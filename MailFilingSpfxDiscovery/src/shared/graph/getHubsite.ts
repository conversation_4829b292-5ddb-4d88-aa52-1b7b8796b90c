import { Site } from "@microsoft/microsoft-graph-types";
import { GraphFI } from "@pnp/graph";

export const getHubsite = async (
  graph: GraphFI,
  sharePointTenant: string,
  hubsitePath: string
): Promise<Site> => {
  // Will need to do something
  // more here when we connect
  // to proper atvero projects

  const hubsite = await graph.sites.getByUrl(sharePointTenant, hubsitePath);
  return hubsite();
};
