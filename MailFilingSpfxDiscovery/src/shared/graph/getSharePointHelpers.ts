import { GraphFI } from "@pnp/graph";
import "@pnp/graph/sites";

export const getSharePointRootUrl = async (graph: GraphFI): Promise<string> => {
  const root = await graph.sites.root();
  const rootUrl = new URL(root.webUrl as string);
  return rootUrl.hostname;
};

export const getSharepointHostName = async (
  graph: GraphFI
): Promise<string | undefined> => {
  const siteInfo = await graph?.sites.getById("root")();
  return siteInfo?.webUrl ?? undefined;
};
