import { GraphFI } from "@pnp/graph";
import "@pnp/graph/search";
import {
  SearchResponse,
  SearchHitsContainer,
  SearchHit,
  Entity,
} from "@microsoft/microsoft-graph-types";
import { Email, EmailResponse } from "../types/Email";
import { Operation, Filter } from "../types/Filter";
import { SortDirection } from "../utils/DataManagment";
import { ListTitles } from "../types/ListTitles";
import { formatDateForQuery } from "../utils/FormatDate";
import { extractProjectCode } from "../utils/ExtractProjectCode";

interface ExtendedSearchHitResource extends Entity {
  listItem: {
    fields: {
      listitemid: string;
      refinableString31: string;
      refinableString35: string;
      refinableDate00: string;
      driveId: string;
      sitename: string;
      refinableString51: string;
      refinableString52: string;
      refinableDecimal09: string;
    };
  };
  webUrl: string;
  id: string;
}

const getEmailContentPaths = (
  tenantUrl: string,
  sites: string[],
  isConfidentialFilterApplied: boolean | undefined
): string[][] => {
  const getPathsForSite = (site: string = "*"): string[] => {
    const basePath = `${tenantUrl}/sites/${site}`;
    const confidentialPath = `${basePath}/Confidential%20Filed%20Email%20Content`;
    const regularPath = `${basePath}/Filed%20Email%20Content`;

    return isConfidentialFilterApplied ? [confidentialPath] : [regularPath];
  };

  if (!sites.length) {
    return [getPathsForSite()];
  }

  return sites.map((site) => getPathsForSite(site));
};

const mapFieldToRefinableString = (field: string): string => {
  switch (field) {
    case "EmailReceived":
      return "RefinableDate00";
    case "EmailSubject":
      return "RefinableString31";
    case "EmailTo":
      return "RefinableString32";
    case "EmailCC":
      return "RefinableString33";
    case "EmailFrom":
      return "RefinableString35";
    case "AttachmentCount":
      return "RefinableDecimal09";
    case "EmailTags":
      return "RefinableString51";
    case "EmailImportant":
      return "RefinableString52";
    default:
      return field;
  }
};

export const extractEmailsFromSearchResults = (
  results: SearchResponse[]
): Email[] => {
  return results.reduce((acc: Array<Email>, result: SearchResponse) => {
    result.hitsContainers?.forEach((container: SearchHitsContainer) => {
      container.hits?.forEach((hit: SearchHit) => {
        const extendedHit = hit.resource as ExtendedSearchHitResource;
        acc.push({
          id: hit.hitId ?? "",
          subject: extendedHit.listItem.fields.refinableString31 ?? "",
          sentBy: extendedHit.listItem.fields.refinableString35 ?? "",
          dateFiled: extendedHit.listItem.fields.refinableDate00 ?? "",
          tag: extendedHit.listItem.fields.refinableString51 ?? "",
          attachmentCount:
            extendedHit.listItem.fields.refinableDecimal09 ?? "0",
          driveItemID: hit.resource?.id ?? undefined,
          driveID: extendedHit.listItem.fields.driveId!,
          projectCode: extractProjectCode(extendedHit.listItem.fields.sitename),
          fileRef: extendedHit.webUrl,
          emailTextSummary: hit.summary ?? "",
          emailImportant: extendedHit.listItem.fields.refinableString52 ?? "No",
          emailConfidential: extendedHit.webUrl.includes(
            ListTitles.Confidential
          ),
        });
      });
    });
    return acc;
  }, []);
};

export const setFilterQuery = (filters: Filter[]): string => {
  return filters
    .filter((filter) => filter.values.some((value) => value !== ""))
    .map((filter) => {
      const field = mapFieldToRefinableString(filter.field);
      const filterValues = filter.values
        .filter((value) => value !== "")
        .map((value) => {
          switch (filter.application) {
            case Operation.Equals:
              return `${field}:${value}`;
            case Operation.Contains:
              return `${field}:${value}`;
            case Operation.Boolean:
              return `${field} eq ${value}`;
            case Operation.NotBoolean:
              return `${field} neq ${value}`;
            case Operation.NotBooleanOrNull:
              return `(${field} neq ${value} or ${field} eq null)`;
            case Operation.IsNull:
              return `${field} eq null`;
            case Operation.GreaterThanDateTime:
              return `${field}>=${formatDateForQuery(value)}`;
            case Operation.LessThanDateTime:
              return `${field}<=${formatDateForQuery(value)}`;
            case Operation.GreaterThan:
              return `${field}>0`;
            default:
              return "";
          }
        })
        .filter((query) => query !== "");

      return filterValues.length > 1
        ? `(${filterValues.join(" OR ")})`
        : filterValues[0];
    })
    .filter((query) => query !== "")
    .join(" AND ");
};

export const getEmailsFromSearch = async (
  graph: GraphFI,
  sites: string[],
  tenantUrl: string,
  searchTerm: string,
  page: number,
  pageSize: number,
  orderColumn: string | undefined,
  orderDirection?: SortDirection,
  filters?: Filter[],
  hubsiteId?: string
): Promise<EmailResponse> => {
  const sortProperties =
    orderColumn && orderDirection
      ? [
          {
            name: orderColumn,
            isDescending: orderDirection === SortDirection.Descending,
          },
        ]
      : [];

  const firstFilters = [
    {
      field: "ContentType",
      values: ["Document"],
      application: Operation.Equals,
    },
  ];

  // Determine whether the confidential filter is applied
  const isConfidentialFilterApplied = filters?.some(
    (filter) =>
      filter.field === "EmailConfidential" && filter.values.includes("true")
  );

  // Remove "EmailConfidential" and "ProjectCode" filter from the filters array
  const filtersWithoutConfidentialAndProjectCode = filters?.filter(
    (filter) =>
      filter.field !== "EmailConfidential" && filter.field !== "ProjectCode"
  );

  // Process the filters without EmailConfidential
  const intermedFilters = filtersWithoutConfidentialAndProjectCode ?? [];

  const values = getEmailContentPaths(
    tenantUrl,
    sites,
    isConfidentialFilterApplied
  );
  const flatValues = values.reduce((acc, val) => acc.concat(val), []);

  // Modify finalFilters based on the confidential filter selection
  const finalFilters = [
    {
      field: "Path",
      values: flatValues,
      application: Operation.Equals,
    },
  ];
  // This query only works if content type is the first filter and path is the last filter
  const combinedFilters: Filter[] = [
    ...firstFilters,
    ...intermedFilters,
    ...finalFilters,
  ];

  const filterQuery = setFilterQuery(combinedFilters);

  // If a hubsiteId is provided, add it to the query string
  let hubsiteFilter = "";
  if (hubsiteId) {
    hubsiteFilter = ` AND DepartmentId:${hubsiteId}`; // Add the filter for DepartmentId
  }

  // Build the final query template with hubsiteFIlter
  const query = {
    queryString: searchTerm || "*", // Default to wildcard search if no search term
    queryTemplate: `({searchTerms}) ${filterQuery}${hubsiteFilter}`, // Combine filter query and hubsite filter
  };

  const results: SearchResponse[] = await graph.query({
    entityTypes: ["driveItem"],
    query,
    fields: [
      "id",
      "Title",
      "listitemid",
      "RefinableDate00", // EmailReceivedOn
      "RefinableString31", // EmailSubject
      "RefinableString32", // EmailTo
      "RefinableString33", // EmailCC
      "RefinableString35", // EmailFrom
      "driveId",
      "sitename",
      "webUrl",
      "RefinableString51", // EmailTags
      "RefinableString52", // EmailImportant
      "RefinableDecimal09", // AttachmentCount
    ],
    from: page * pageSize,
    size: pageSize,
    sortProperties,
  });

  const emails: Array<Email> = extractEmailsFromSearchResults(results);
  // Determine if more results are available
  const moreResultsAvailable = results.some((result) =>
    result.hitsContainers?.some((container) => container.moreResultsAvailable)
  );
  const total =
    results
      .map(
        (result) =>
          result?.hitsContainers?.find(
            (container) => container?.total !== undefined
          )?.total
      )
      .find((total) => total !== null && total !== undefined) ?? 0; // Default to 0 if no valid total is found
  return {
    emails,
    nextLink: undefined, // or the next link if available
    page,
    pageSize,
    sortField: orderColumn,
    sortDirection: orderDirection ?? SortDirection.Descending,
    moreResultsAvailable,
    total,
  };
};
