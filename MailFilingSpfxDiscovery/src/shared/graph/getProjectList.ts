import { GraphFI } from "@pnp/graph";
import "@pnp/graph/search";
import {
  SearchResponse,
  SearchHitsContainer,
  SearchHit,
  Entity,
  SearchRequest,
} from "@microsoft/microsoft-graph-types";

import { getSiteGuidFromGraphGuid } from "../utils/GraphHelpers";
import { Project } from "../types/Project";

interface ExtendedProjectEntity extends Entity {
  name?: string;
  description?: string;
  favorite?: boolean;
}

export const extractProjectsFromSearchResults = (
  hubsiteId: string,
  results: SearchResponse[]
): Project[] => {
  return results.reduce((acc: Array<Project>, result: SearchResponse) => {
    result.hitsContainers?.forEach((container: SearchHitsContainer) => {
      container.hits?.forEach((hit: SearchHit) => {
        const resource = hit.resource as ExtendedProjectEntity;
        if (hubsiteId !== getSiteGuidFromGraphGuid(resource.id ?? "")) {
          acc.push({
            code: resource.name ?? "",
            title: resource.description ?? "",
            id: resource.id ?? "",
            favorite: resource.favorite ?? false,
          });
        }
      });
    });
    return acc;
  }, []);
};

export const getProjectList = async (
  graph: GraphFI,
  hubsiteId: string,
  searchTerm: string
): Promise<Array<Project>> => {
  let projectQuery = {
    entityTypes: ["site"],
    query: {
      queryString: `* DepartmentId:${hubsiteId}`,
    },
    size: 190,
  } as SearchRequest;

  if (searchTerm) {
    projectQuery = {
      entityTypes: ["site"],
      query: {
        queryString: `DepartmentId:${hubsiteId} AND (Description:"${searchTerm}*")`,
      },
      size: 190,
    };
  }

  const results: SearchResponse[] = await graph.query(projectQuery);

  const projects: Array<Project> = extractProjectsFromSearchResults(
    hubsiteId,
    results
  );

  return projects;
};
