import React from "react";
import { ICommandBarItemProps } from "@fluentui/react/lib/CommandBar";
import SearchBoxComponent from "../../webparts/AtveroMailSpfxDiscovery/components/components/SearchBoxComponent";
import { Filter } from "../types/Filter";
import { Email } from "../types/Email";

export const getFarItems = (
  filters: Filter[],
  emailData: (Email | undefined)[] | undefined,
  isConfidential: boolean,
  toggleDriveFilter: () => void,
  setFilters: (filters: Filter[]) => void,
  setIsConfidential: (value: boolean) => void,
  openFilters: () => void
): ICommandBarItemProps[] => {
  const confidentialItem = emailData
    ? [
        {
          key: "showconfidential",
          text: isConfidential
            ? "Show Regular Emails"
            : "Show Confidential Emails",
          iconProps: { iconName: isConfidential ? "Mail" : "Lock" },
          onClick: toggleDriveFilter,
        },
      ]
    : [];

  const filterItems =
    filters.length > 0
      ? [
          {
            key: "clearfilters",
            text: "",
            iconProps: { iconName: "ClearFilter" },
            onClick: () => {
              setFilters([]);
              setIsConfidential(false);
            },
          },
        ]
      : [];

  const openFilterItem = {
    key: "filters",
    text: "",
    iconProps: { iconName: "Filter" },
    onClick: openFilters,
  };

  return [...confidentialItem, ...filterItems, openFilterItem];
};

export const getCommandBarConfig = (
  searchTerm: string,
  setSearchTerm: (term: string) => void,
  doSearch: (term: string) => void,
  filters: Filter[],
  emailData: (Email | undefined)[] | undefined,
  isConfidential: boolean,
  toggleDriveFilter: () => void,
  setFilters: (filters: Filter[]) => void,
  setIsConfidential: (value: boolean) => void,
  openFilters: () => void
): {
  searchButton: ICommandBarItemProps;
  _farItems: ICommandBarItemProps[];
} => {
  const searchButton = {
    key: "search",
    onRender: () => (
      <SearchBoxComponent
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        doSearch={doSearch}
      />
    ),
    onClick: () => {},
    text: "Search",
    ariaLabel: "Search",
    iconOnly: false,
    iconProps: {
      iconName: "Search",
    },
  };

  return {
    searchButton,
    _farItems: getFarItems(
      filters,
      emailData,
      isConfidential,
      toggleDriveFilter,
      setFilters,
      setIsConfidential,
      openFilters
    ),
  };
};
