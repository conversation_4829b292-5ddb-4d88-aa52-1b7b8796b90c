export enum Operation {
  Equals,
  Contains,
  GreaterThanDateTime,
  LessThanDateTime,
  Boolean,
  NotBoolean,
  NotBooleanOrNull,
  IsNull,
  GreaterThan,
}

export type Filter = {
  field: string;
  values: string[];
  application: Operation;
};

export const makeCAMLFilter = (
  field: string,
  application: Operation,
  value: string
): string => {
  if (value === "") {
    return "";
  } else {
    switch (application) {
      case Operation.Equals:
        return (
          "<Eq>" +
          "<FieldRef Name='" +
          field +
          "'/>" +
          "<Value Type='Text'>" +
          value +
          "</Value>" +
          "</Eq>"
        );
      case Operation.GreaterThanDateTime:
        return (
          "<Gt>" +
          "<FieldRef Name='" +
          field.replace("After", "") +
          "'/>" +
          "<Value IncludeTimeValue=\"FALSE\" Type='DateTime'>" +
          value +
          "</Value>" +
          "</Gt>"
        );
      case Operation.LessThanDateTime:
        return (
          "<Lt>" +
          "<FieldRef Name='" +
          field.replace("Before", "") +
          "'/>" +
          "<Value IncludeTimeValue=\"FALSE\" Type='DateTime'>" +
          value +
          "</Value>" +
          "</Lt>"
        );
      case Operation.Contains:
        return (
          "<Contains>" +
          "<FieldRef Name='" +
          field +
          "'/>" +
          "<Value Type='Text'>" +
          value +
          "</Value>" +
          "</Contains>"
        );
      case Operation.Boolean:
        return (
          "<Eq>" +
          "<FieldRef Name='" +
          field +
          "'/>" +
          "<Value Type='Boolean'>" +
          value +
          "</Value>" +
          "</Eq>"
        );
      case Operation.NotBoolean:
        return (
          "<Neq>" +
          "<FieldRef Name='" +
          field +
          "'/>" +
          "<Value Type='Boolean'>" +
          value +
          "</Value>" +
          "</Neq>"
        );
      case Operation.GreaterThan:
        return (
          "<Gt>" +
          "<FieldRef Name='" +
          field +
          "'/>" +
          "<Value Type='Number'>" +
          value +
          "</Value>" +
          "</Gt>"
        );
      case Operation.NotBooleanOrNull:
        return (
          "<Or>" +
          "<Neq>" +
          "<FieldRef Name='" +
          field +
          "'/>" +
          "<Value Type='Boolean'>" +
          value +
          "</Value>" +
          "</Neq>" +
          "<IsNull>" +
          "<FieldRef Name='" +
          field +
          "'/>" +
          "</IsNull>" +
          "</Or>"
        );
      case Operation.IsNull:
        return "<IsNull><FieldRef Name='" + field + "'/></IsNull>";
      default:
        return "";
    }
  }
};

export const createCAMLValuesInner = (
  field: string,
  application: Operation,
  values: string[]
): string => {
  switch (values.length) {
    case 0:
      return "";
    case 1:
      return makeCAMLFilter(field, application, values[0]);
    default: {
      const head = values[0];
      const tail = values.slice(1);

      const merged =
        makeCAMLFilter(field, application, head) +
        createCAMLValuesInner(field, application, tail);
      return "<Or>" + merged + "</Or>";
    }
  }
};

export const createCAMLValues = (
  field: string,
  application: Operation,
  values: string[]
): string => {
  const merged = createCAMLValuesInner(field, application, values);
  return merged;
};

const createCAMLFiltersInner = (filterList: Filter[]): string => {
  switch (filterList.length) {
    case 0:
      return "";
    case 1:
      return createCAMLValues(
        filterList[0].field,
        filterList[0].application,
        filterList[0].values
      );
    default: {
      const head = filterList[0];
      const tail = filterList.slice(1);

      const merged =
        createCAMLValues(head.field, head.application, head.values) +
        createCAMLFiltersInner(tail);

      return "<And>" + merged + "</And>";
    }
  }
};

export const keepFiltersWithValues = (filters: Filter[]): Filter[] => {
  return filters.filter((filter) => {
    if (filter.values.length === 0) {
      return false;
    }

    return filter.values.some((fv) => fv !== "");
  });
};

export const createCAMLFilters = (filters: Filter[]): string => {
  const filtersWithValues = keepFiltersWithValues(filters);
  const merged = createCAMLFiltersInner(filtersWithValues);
  return merged;
};
