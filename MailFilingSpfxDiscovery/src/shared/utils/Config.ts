import config from "../../config.json";

export const ClientId = (): string => {
  return config.appid;
};

export const RedirectUri = (): string => {
  return config.redirectUrl;
};

export const Scopes = (): string[] => {
  return [
    "User.Read",
    "Sites.Read.All",
    "Contacts.Read",
    "Files.ReadWrite",
    "Group.Read.All",
    "Mail.ReadWrite",
    "ProfilePhoto.Read.All",
  ];
};

export const DefaultHubsitePath = (): string => {
  return "/sites/AtveroMail";
};
