import addressParser, { ParsedMailbox } from "email-addresses";

export const getNameAndEmail = (recipient: string): [string, string] => {
  const parts: ParsedMailbox | addressParser.ParsedGroup | null =
    addressParser.parseOneAddress(recipient) as ParsedMailbox;
  let name = recipient;
  let email = "";

  if (parts?.name && parts?.address) {
    name = parts.name;
    email = parts.address;
  } else if (parts?.name) {
    name = parts.name;
  } else if (parts?.address) {
    email = parts.address;
  }
  return [name, email];
};
