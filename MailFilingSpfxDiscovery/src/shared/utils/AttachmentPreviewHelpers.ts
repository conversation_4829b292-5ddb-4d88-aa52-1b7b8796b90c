import { Attachment } from "../types/Attachments";
import { saveAs } from "file-saver";

export const base64ToArrayBuffer = (base64: string): Uint8Array => {
  const binaryString = window.atob(base64);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
};

export const handleAttachmentAction = (
  attachment: Attachment,
  action: "download" | "preview"
): void => {
  if (attachment.content) {
    let blob: Blob;
    if (typeof attachment.content === "string") {
      // If the content is a base64 string, convert it to an array buffer
      const byteArray = base64ToArrayBuffer(attachment.content);
      blob = new Blob([byteArray], { type: attachment.contentType });
    } else if (attachment.content instanceof Uint8Array) {
      // If the content is already binary data, create a Blob directly
      blob = new Blob([attachment.content], { type: attachment.contentType });
    } else {
      console.error("Unsupported attachment content format");
      return;
    }

    const objectUrl = URL.createObjectURL(blob);

    if (action === "download") {
      saveAs(blob, attachment.filename ?? "unnamed-attachment");
    } else if (action === "preview") {
      const win = window.open();
      if (win) {
        win.document.write(
          `<iframe src="${objectUrl}" frameborder="0" style="width:100%; height:100%;"></iframe>`
        );
      }
    }

    // Clean up the object URL after opening
    setTimeout(() => URL.revokeObjectURL(objectUrl), 100);
  } else {
    console.error("Attachment content is undefined");
  }
};
