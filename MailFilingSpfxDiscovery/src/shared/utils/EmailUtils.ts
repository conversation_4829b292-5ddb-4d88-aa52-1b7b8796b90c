import { Filter, createCAMLFilters } from "./CamlHelpers";
import { IRenderListDataParameters } from "@pnp/sp/lists";
import { EmailResponse } from "../types/Email";
import { GraphFI } from "@pnp/graph";
import { IWeb } from "@pnp/sp/webs";
import { ListTitles } from "../types/ListTitles";

export enum SortDirection {
  Ascending = "ascending",
  Descending = "descending",
}

interface IEmailRow {
  ID: string;
  EmailSubject: string;
  EmailFrom: string;
  EmailReceivedOn: string;
  AttachmentCount: string;
  FileRef: string;
  EmailTextSummary: string;
  EmailImportant: string;
  EmailTags: string;
}

export const handleFilter = async (
  filters: Filter[],
  sortColumn: string | undefined,
  sortDirection: SortDirection,
  getSharePointWeb: (projectCode: string) => Promise<IWeb> | undefined,
  graph: GraphFI,
  projectCode: string,
  nextLink: string | undefined,
  page: number,
  pageSize: number,
  resetPaging: boolean,
  shareRootUrl: string,
  isConfidential: boolean
): Promise<EmailResponse> => {
  // Exclude "EmailConfidential" and "ProjectCode" filter from the filters array for ViewXml
  const sanitizedFilters = filters.filter(
    (filter) =>
      filter.field !== "EmailConfidential" && filter.field !== "ProjectCode"
  );

  const whereClause = createCAMLFilters(sanitizedFilters);
  let orderByClause = "";

  if (sortColumn) {
    const ascending = sortDirection === "ascending" ? "True" : "False";
    let fieldRef = sortColumn;

    if (sortColumn === "subject") fieldRef = "EmailSubject";
    else if (sortColumn === "sentBy") fieldRef = "EmailFrom";
    else if (sortColumn === "dateFiled") fieldRef = "EmailReceivedOn";

    orderByClause = `<OrderBy><FieldRef Name="${fieldRef}" Ascending="${ascending}"/></OrderBy>`;
  }

  const web = await getSharePointWeb(projectCode);

  if (web && graph) {
    const excludeFoldersCondition =
      '<Eq><FieldRef Name="FSObjType" /><Value Type="Integer">0</Value></Eq>';

    const combinedWhereClause = whereClause
      ? `<And>${excludeFoldersCondition}${whereClause}</And>`
      : excludeFoldersCondition;

    const viewXml = `<View Scope="RecursiveAll">
      <Query>
        <Where>${combinedWhereClause}</Where>
        ${orderByClause}
      </Query>
      <ViewFields>
        <FieldRef Name="EmailSubject"/>
        <FieldRef Name="EmailFrom"/>
        <FieldRef Name="EmailTo"/>
        <FieldRef Name="EmailCC"/>
        <FieldRef Name="EmailReceivedOn"/>
        <FieldRef Name="EmailTextSummary"/>
        <FieldRef Name="AttachmentCount"/>
        <FieldRef Name="EmailTextSummary"/>
        <FieldRef Name="EmailTags"/>
        <FieldRef Name="EmailImportant"/>
        <FieldRef Name="FileRef"/>
      </ViewFields>
      <RowLimit Paged="TRUE">${pageSize}</RowLimit>
    </View>`;

    const renderListDataParams: IRenderListDataParameters = {
      ViewXml: viewXml,
      Paging: nextLink && !resetPaging ? nextLink : "Paged=TRUE",
    };

    try {
      const listTitle = isConfidential
        ? ListTitles.Confidential
        : ListTitles.NonConfidential;
      const r = await web.lists
        .getByTitle(listTitle)
        .renderListDataAsStream(renderListDataParams);

      // Construct the path for the project and fetch the site information
      const projectPath = `/sites/${projectCode}`;
      const sitePromise = await graph.sites.getByUrl(shareRootUrl, projectPath);
      const site = await sitePromise();

      return {
        page: page,
        pageSize: pageSize,
        nextLink: r.NextHref ? r.NextHref.slice(1) : undefined,
        sortField: sortColumn,
        sortDirection: sortDirection,
        emails: r.Row.map((item: IEmailRow) => ({
          id: item.ID,
          subject: item.EmailSubject,
          sentBy: item.EmailFrom,
          dateFiled: item.EmailReceivedOn,
          projectCode: projectCode,
          attachmentCount: item.AttachmentCount,
          fileRef: `https://${shareRootUrl}${item.FileRef}`,
          emailTextSummary: item.EmailTextSummary,
          emailImportant: item.EmailImportant,
          tag: item.EmailTags,
          emailConfidential: item.FileRef.includes(
            "Confidential Filed Email Content"
          ),
          driveID: site.id ?? "",
          driveItemID: undefined, //This will be implmented as part of attachments tickets
        })),
      };
    } catch (error) {
      console.error("Error fetching filtered and sorted data:", error);
    }
  }

  return {
    page: 0,
    pageSize: 0,
    nextLink: undefined,
    emails: [],
    sortField: sortColumn,
    sortDirection: sortDirection,
  };
};
