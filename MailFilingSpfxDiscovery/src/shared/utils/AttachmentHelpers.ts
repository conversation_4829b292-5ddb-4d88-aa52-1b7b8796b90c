import { Email } from "../types/Email";

import PostalMime from "postal-mime";

import MsgReader from "@kenjiuno/msgreader";

import { Attachment, AttachmentResult } from "../types/Attachments";

import { IForEmailPreview } from "../../adapters/IForEmailPreview";

export const handleAttachments = async (
  email: Email,
  previewAdapter: IForEmailPreview
): Promise<AttachmentResult> => {
  const emailWithDriveID = email.driveItemID
    ? email
    : await previewAdapter.getSelectedEmailWithDriveItemID(email);

  if (email.fileRef) {
    const fileType = determineFileType(email.fileRef);
    const fileContent = await previewAdapter.readFileContent(emailWithDriveID);
    if (fileType === "eml") {
      const emlContent = await parseEmlFileAttachments(fileContent);
      return { ...emlContent, fileRef: email.fileRef };
    } else if (fileType === "msg") {
      const msgContent = await parseMsgFileAttachments(fileContent);
      return { ...msgContent, fileRef: email.fileRef };
    } else {
      console.error("Unsupported file type");
      return { attachments: [], fileRef: email.fileRef };
    }
  }

  return { attachments: [] };
};

export function determineFileType(fileRef: string): "eml" | "msg" | "unknown" {
  const lowercaseFileRef = fileRef.toLowerCase();
  if (lowercaseFileRef.endsWith(".eml")) {
    return "eml";
  } else if (lowercaseFileRef.endsWith(".msg")) {
    return "msg";
  } else {
    return "unknown";
  }
}

export async function parseEmlFileAttachments(
  fileContent: ArrayBuffer
): Promise<AttachmentResult> {
  try {
    const decoder = new TextDecoder("utf-8");
    const parsed = await PostalMime.parse(decoder.decode(fileContent));

    const filteredAttachments = parsed.attachments.filter((attachment) => {
      const isInline =
        attachment.disposition === "inline" && !!attachment.contentId;
      return !isInline;
    });

    const attachments: Attachment[] = filteredAttachments.map((attachment) => ({
      filename: attachment.filename ?? "unknown",
      size: attachment.content.byteLength,
      contentType: attachment.mimeType,
      content: new Uint8Array(attachment.content),
    }));

    return { attachments };
  } catch (error) {
    console.error("Error parsing EML file:", error);
    throw error;
  }
}

export async function parseMsgFileAttachments(
  fileContent: ArrayBuffer
): Promise<AttachmentResult> {
  try {
    const msgReader = new MsgReader(fileContent);
    const fileData = msgReader.getFileData();

    const attachments: Attachment[] = [];

    for (const rawAttachment of fileData.attachments || []) {
      const isInline =
        rawAttachment.innerMsgContent || !!rawAttachment.pidContentId;

      if (isInline) continue;

      const contentResult = msgReader.getAttachment(rawAttachment);
      const contentBuffer = contentResult?.content;

      if (!contentBuffer) continue;

      const fileName =
        rawAttachment.fileName ?? rawAttachment.name ?? "unknown";
      const mimeType =
        rawAttachment.attachMimeTag ?? "application/octet-stream";
      const size = rawAttachment.contentLength ?? contentBuffer.byteLength;

      attachments.push({
        filename: fileName,
        size,
        contentType: mimeType,
        content: contentBuffer,
      });
    }

    return { attachments };
  } catch (error) {
    console.error("Error parsing MSG file:", error);
    throw error;
  }
}
