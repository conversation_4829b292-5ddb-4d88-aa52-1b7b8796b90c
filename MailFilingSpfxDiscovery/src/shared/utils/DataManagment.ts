import { getSharePointRootUrl } from "../graph/getSharePointHelpers";
import { getHubsite } from "../graph/getHubsite";
import { getProjectList } from "../graph/getProjectList";
import { getSiteGuidFromGraphGuid } from "./GraphHelpers";
import { DefaultHubsitePath } from "./Config";

import { EmailResponse } from "../types/Email";
import { Project } from "../types/Project";
import { Site } from "@microsoft/microsoft-graph-types";
import { GraphFI } from "@pnp/graph";
export enum SortDirection {
  Ascending = "ascending",
  Descending = "descending",
}

export const shouldAppendEmails = (
  sortColumn: string,
  sortDirection: SortDirection,
  response: EmailResponse
): boolean => {
  return (
    sortColumn === response.sortField &&
    sortDirection === response.sortDirection
  );
};

export async function fetchShareRootURL(graph: GraphFI): Promise<string> {
  try {
    return await getSharePointRootUrl(graph);
  } catch (error) {
    console.error("Error fetching sharepoint root url: ", error);
    return "";
  }
}

/**
 * Function to extract the SharePoint root URL (shareRootUrl) from the absoluteUrl.
 * @param {string} absoluteUrl - The full absolute URL, e.g. "https://atverodevs.sharepoint.com/sites/Mail2".
 * @returns {string} - The SharePoint root URL, e.g. "atverodevs.sharepoint.com".
 */
export function parseShareRootUrl(absoluteUrl: string): string {
  try {
    // Create a new URL object from the absoluteUrl
    const url = new URL(absoluteUrl);

    // Return the hostname, which will be "atverodevs.sharepoint.com"
    return url.hostname;
  } catch (error) {
    console.error("Invalid absoluteUrl:", absoluteUrl, error);
    return ""; // Return an empty string in case of an error
  }
}

/**
 * Function to extract the project name from the relativeUrl.
 * @param {string} relativeUrl - The server relative URL, e.g. "/sites/Mail2".
 * @returns {string} - The project name, e.g. "Mail2".
 */
export function parseProjectFromRelativeUrl(relativeUrl: string): string {
  try {
    // Check if the relativeUrl contains "/sites/" and split it to extract the project name
    const parts = relativeUrl.split("/sites/");

    // If there's a valid part after "/sites/", return it as the project name
    if (parts.length > 1) {
      return parts[1].split("/")[0]; // Extract the first part after "/sites/"
    }

    return ""; // Return an empty string if the format is incorrect
  } catch (error) {
    console.error("Invalid relativeUrl:", relativeUrl, error);
    return ""; // Return an empty string in case of an error
  }
}

export async function fetchHubsites(
  graph: GraphFI,
  shareRootUrl: string
): Promise<Site> {
  try {
    const hubsite = await getHubsite(graph, shareRootUrl, DefaultHubsitePath());
    if (!hubsite) {
      throw new Error("No hubsite found");
    }
    return hubsite;
  } catch (error) {
    console.error("Error fetching hubsites: ", error);
    throw new Error("Failed to fetch hubsite");
  }
}

export async function fetchProjects(
  graph: GraphFI,
  hubsite: Site,
  searchTerm: string
): Promise<Project[]> {
  try {
    return await getProjectList(
      graph,
      getSiteGuidFromGraphGuid(hubsite.id),
      searchTerm
    );
  } catch (error) {
    console.error("Error fetching projects: ", error);
    return [];
  }
}
