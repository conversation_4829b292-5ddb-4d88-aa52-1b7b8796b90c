import {
  parseIS<PERSON>,
  isValid,
  isThisWeek,
  isToday,
  isYesterday,
  format,
} from "date-fns";
import { cs } from "date-fns/locale";

export function formatDate(inputDate: string | Date): string {
  let date: Date;
  if (!inputDate) {
    return "No Date";
  }

  // Check if the input is already a Date object
  if (inputDate instanceof Date) {
    date = inputDate;
  } else {
    // Try parsing as ISO string
    date = parseISO(inputDate);

    // If parsing fails, try creating a Date object directly
    if (!isValid(date)) {
      date = new Date(inputDate);
    }
  }

  // Check if the resulting date is valid
  if (!isValid(date)) {
    return "Invalid Date";
  }

  // default to UK as that's where most of our customers are from
  const langCode =
    navigator.language || document.documentElement.lang || "en-GB";

  // Check if the date is today
  if (isToday(date)) {
    const formatter = new Intl.DateTimeFormat(langCode, {
      minute: "2-digit",
      hour: "2-digit",
      hour12: Intl.DateTimeFormat().resolvedOptions().hour12,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    });
    return "Today " + formatter.format(date);
  }

  // Check if the date is yesterday
  if (isYesterday(date)) {
    const formatter = new Intl.DateTimeFormat(langCode, {
      minute: "2-digit",
      hour: "2-digit",
      hour12: Intl.DateTimeFormat().resolvedOptions().hour12,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    });
    return "Yesterday " + formatter.format(date);
  }

  // Check if the date is within this week
  if (isThisWeek(date, { weekStartsOn: 1 })) {
    const formatter = new Intl.DateTimeFormat(langCode, {
      minute: "2-digit",
      hour: "2-digit",
      hour12: Intl.DateTimeFormat().resolvedOptions().hour12,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      weekday: "long",
    });
    return formatter.format(date);
  }

  // If none of the above conditions are met, return the date in the specified format

  const formatter = new Intl.DateTimeFormat(langCode, {
    dateStyle: "short",
    timeStyle: "short",
    hour12: Intl.DateTimeFormat().resolvedOptions().hour12,
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  });

  return formatter.format(date);
}

export const formatDateForQuery = (date: string): string => {
  return format(new Date(date), "yyyy-MM-dd'T'HH:mm:ss'Z'", { locale: cs });
};
