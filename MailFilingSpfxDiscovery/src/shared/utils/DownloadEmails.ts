import JSZ<PERSON> from "jszip";
import { saveAs } from "file-saver";
import { Email } from "../types/Email";
import { parseFileRef } from "./ParseFileRef";

import "@pnp/graph/users";
import "@pnp/graph/files";
import { Graph<PERSON> } from "@pnp/graph";

/**
 * Downloads the 1 email.
 * @param {Email | undefined} email - The attachment object containing file references.
 */
export const handleDownloadEmail = (email: Email | undefined): void => {
  const fileRef = email?.fileRef;

  if (fileRef) {
    // Opens the file reference in a new tab to trigger the download
    window.open(fileRef, "_blank");
  } else {
    console.error("No attachments available for download");
  }
};

/**
 * Downloads a zip containing multiple email (.eml) files.
 * @param emails - An array of Email objects containing file references.
 * @param zipFilename - The name of the zip file to be downloaded.
 */
export const handleDownloadMultipleEmails = async (
  graph: GraphFI,
  emails: Email[],
  zipFilename: string = "emails-archive.zip"
): Promise<void> => {
  if (!emails || emails.length === 0) {
    console.error("No emails available for download");
    return;
  }

  const zip = new JSZip();

  // Use Promise.all to ensure all email files are fetched and added to the zip
  const fetchPromises = emails.map(async (email) => {
    const fileRef = email.fileRef;

    if (fileRef) {
      if (!email.driveID || !email.driveItemID) {
        console.error(`No drive or Item ID found for email: ${email.id}`);
        return;
      }

      try {
        const fileContents: Blob = await graph.drives
          .getById(email.driveID)
          .getItemById(email.driveItemID)
          .getContent();

        if (!fileContents) {
          console.error(`Error fetching email file: ${fileRef}`);
          return;
        }

        // Create a filename for each email, or use the subject as a default filename
        const filename = parseFileRef(fileRef);

        // Add the .eml file to the zip
        zip.file(filename, fileContents);
      } catch (error) {
        console.error(`Error fetching email file: ${fileRef}`, error);
      }
    } else {
      console.error(`No file reference found for email: ${email.id}`);
    }
  });

  // Wait for all fetches to complete
  await Promise.all(fetchPromises);

  try {
    // Generate the zip file after all files have been added
    const content = await zip.generateAsync({ type: "blob" });

    console.log(`Zip generated, size: ${content.size}`);

    // Trigger the download of the zip file
    saveAs(content, zipFilename);
  } catch (error) {
    console.error("Error generating zip file:", error);
  }
};
