import { SortDirection } from "../utils/DataManagment";

export type EmailResponse = {
  emails: Email[];
  page: number;
  pageSize: number;
  nextLink: string | undefined;
  sortField: string | undefined;
  sortDirection: SortDirection;
  moreResultsAvailable?: boolean;
  total?: number;
  shareRootUrl?: string;
};

export type Email = {
  projectCode: string | undefined;
  id: string;
  subject: string;
  sentBy: string;
  dateFiled: string;
  tag?: string;
  driveItemID: string | undefined;
  driveID: string;
  attachmentCount?: string;
  fileRef?: string;
  emailTextSummary?: string;
  emailImportant?: string;
  emailConfidential?: boolean;
};
