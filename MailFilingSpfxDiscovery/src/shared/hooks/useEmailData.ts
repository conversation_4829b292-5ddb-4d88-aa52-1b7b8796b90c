import { useState, useEffect } from "react";
import { Filter } from "../types/Filter";
import {
  parseProjectFromRelativeUrl,
  SortDirection,
} from "../utils/DataManagment";
import { Project } from "../types/Project";
import { Email, EmailResponse } from "../types/Email";
import { IForEmailsFromSearch } from "../../adapters/IForEmailsFromSearch";
import { IForEmailPreview } from "../../adapters/IForEmailPreview";

interface UseEmailDataProps {
  dataAdapter: IForEmailsFromSearch;
  previewAdapter: IForEmailPreview;
  isHubsite: boolean;
  relativeUrl: string;
  filters: Filter[];
  activeSearchTerm: string;
  columnCode: string;
  sortDirection: SortDirection;
  projects: Project[];
  absoluteUrl: string;
}

interface IAvatar {
  name: string | undefined;
  image: string | undefined;
}

export const useEmailData = ({
  dataAdapter,
  previewAdapter,
  isHubsite,
  relativeUrl,
  filters,
  activeSearchTerm,
  columnCode,
  sortDirection,
  projects,
  absoluteUrl,
}: UseEmailDataProps): {
  emailData: (Email | undefined)[] | undefined;
  loading: boolean;
  page: number;
  avatars: Map<string, IAvatar>;
  getNextPage: (currentPage: number) => Promise<void>;
  pageSize: number;
} => {
  const [emailData, setEmailData] = useState<(Email | undefined)[] | undefined>(
    undefined
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [page, setPage] = useState<number>(0);
  const [hasMoreItems, setHasMoreItems] = useState<boolean>(true);
  const [nextLink, setNextLink] = useState<string | undefined>(undefined);
  const [avatars, setAvatars] = useState<Map<string, IAvatar>>(
    new Map<string, IAvatar>()
  );
  const pageSize = 100;
  const localdomain = "atverodev.com";

  const columns = [
    { columnKey: "project", label: "Project", code: undefined },
    { columnKey: "subject", label: "Subject", code: "RefinableString31" },
    { columnKey: "sentBy", label: "Sent By", code: "RefinableString35" },
    { columnKey: "dateFiled", label: "Received On", code: "RefinableDate00" },
    { columnKey: "emailTag", label: "Tag", code: "RefinableString51" },
  ];

  useEffect(() => {
    setPage(0);
    setNextLink(undefined);
  }, []);

  const extractDomainFromUrl = (url: string): string => {
    try {
      return new URL(url).host;
    } catch {
      console.error("Invalid URL:", url);
      throw new Error("Invalid URL");
    }
  };

  const shareRootUrl = extractDomainFromUrl(absoluteUrl);

  const updateAvatars = async (emails: Email[]): Promise<void> => {
    const uniques: string[] = [];
    for (const email of emails) {
      if (
        email.sentBy.includes(localdomain) &&
        !uniques.includes(email.sentBy)
      ) {
        uniques.push(email.sentBy);
      }
    }

    const needed: string[] = [];
    for (const email of uniques) {
      if (!avatars.get(email)) {
        needed.push(email);
      }
    }

    const promises = needed.map(async (sender) => {
      return await previewAdapter.getPhoto(sender);
    });

    const results = await Promise.all<(string | undefined)[]>(promises);

    results.forEach((res) => {
      const [name, email, url] = res;
      const avatar: IAvatar = {
        name: name,
        image: url,
      };
      if (email) avatars.set(email, avatar);
    });
  };

  const getNextPage = async (currentPage: number): Promise<void> => {
    if (hasMoreItems) {
      const sites =
        filters?.find((item) => item.field === "ProjectCode")?.values ?? [];
      const nextPage = currentPage + 1;
      setLoading(true);
      const columnKey = columns.find(
        (col) => col.code === columnCode
      )?.columnKey;

      let currentProject = "";
      if (!isHubsite) {
        currentProject = parseProjectFromRelativeUrl(relativeUrl);
      }

      const emailResponse: EmailResponse | undefined =
        await dataAdapter.fetchEmails(
          isHubsite ? sites : [currentProject],
          shareRootUrl,
          activeSearchTerm,
          nextPage,
          pageSize,
          nextLink,
          columnCode,
          columnKey,
          sortDirection,
          filters,
          false
        );

      if (emailResponse) {
        await updateAvatars(emailResponse.emails);

        setEmailData((old) => {
          const emails = emailResponse.emails as (Email | undefined)[];
          if (
            emailResponse.moreResultsAvailable ||
            emailResponse.nextLink !== undefined
          )
            emails.push(undefined);
          if (!old) return emails;
          else return [...old.slice(0, -1), ...emails];
        });

        if (
          emailResponse.moreResultsAvailable ||
          emailResponse.nextLink !== undefined
        ) {
          setHasMoreItems(true);
        }
        setNextLink(emailResponse.nextLink);

        setPage((oldPage) => oldPage + 1);
      }

      setLoading(false);
    }
  };

  useEffect(() => {
    const sites =
      filters?.find((item) => item.field === "ProjectCode")?.values ?? [];
    // This function is only called for getting
    // the very first batch of results from a fresh query
    const fetchEmails = async (): Promise<void> => {
      setAvatars((old) => old);
      setLoading(true);
      setEmailData(undefined);

      // We do this because stream API needs the column key and search API will need the column code.
      // Logic for this is handled in EmailsFromSearchAdapter.
      const columnKey = columns.find(
        (col) => col.code === columnCode
      )?.columnKey;

      let currentProject = "";
      if (!isHubsite) {
        currentProject = parseProjectFromRelativeUrl(relativeUrl);
      }

      const emailResponse: EmailResponse | undefined =
        await dataAdapter.fetchEmails(
          isHubsite ? sites : [currentProject],
          shareRootUrl,
          activeSearchTerm,
          0,
          pageSize,
          undefined,
          columnCode,
          columnKey,
          sortDirection,
          filters,
          false
        );

      if (emailResponse) {
        await updateAvatars(emailResponse.emails);
        const emails = emailResponse.emails as (Email | undefined)[];
        if (
          emailResponse.moreResultsAvailable ||
          emailResponse.nextLink !== undefined
        ) {
          emails.push(undefined);
          setHasMoreItems(true);
        }
        setNextLink(emailResponse.nextLink);

        setPage(0);
        setEmailData(emails);
      }

      setLoading(false);
    };

    if (!isHubsite) {
      return void fetchEmails();
    }

    if (sites.length > 0 || activeSearchTerm.length > 0) {
      void fetchEmails();
    }
  }, [filters, activeSearchTerm, columnCode, sortDirection, projects]);

  return {
    emailData,
    loading,
    page,
    avatars,
    getNextPage,
    pageSize,
  };
};
