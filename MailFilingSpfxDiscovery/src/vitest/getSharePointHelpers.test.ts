import { describe, it, expect, vi } from "vitest";
import { GraphFI } from "@pnp/graph";
import {
  getSharePointRootUrl,
  getSharepointHostName,
} from "../shared/graph/getSharePointHelpers";

describe("getSharePointRootUrl", () => {
  it("returns the hostname of the SharePoint root URL", async () => {
    const mockGraph = {
      sites: {
        root: vi.fn().mockResolvedValue({
          webUrl: "https://example.sharepoint.com/sites/root",
        }),
      },
    } as unknown as GraphFI;

    const result = await getSharePointRootUrl(mockGraph);
    expect(result).toBe("example.sharepoint.com");
  });

  it("throws if webUrl is undefined", async () => {
    const mockGraph = {
      sites: {
        root: vi.fn().mockResolvedValue({
          webUrl: undefined,
        }),
      },
    } as unknown as GraphFI;

    await expect(getSharePointRootUrl(mockGraph)).rejects.toThrow(TypeError);
  });
});

describe("getSharepointHostName", () => {
  it("returns the hostname from getById('root')", async () => {
    const mockGraph = {
      sites: {
        getById: vi.fn().mockReturnValue(
          vi.fn().mockResolvedValue({
            webUrl: "https://example.sharepoint.com/sites/root",
          })
        ),
      },
    } as unknown as GraphFI;

    const result = await getSharepointHostName(mockGraph);
    expect(result).toBe("https://example.sharepoint.com/sites/root");
  });

  it("returns undefined when webUrl is not present", async () => {
    const mockGraph = {
      sites: {
        getById: vi.fn().mockReturnValue(
          vi.fn().mockResolvedValue({
            webUrl: undefined,
          })
        ),
      },
    } as unknown as GraphFI;

    const result = await getSharepointHostName(mockGraph);
    expect(result).toBe(undefined);
  });

  it("returns undefined when getById fails", async () => {
    let result;
    expect(result).toBe(undefined);
  });
});
