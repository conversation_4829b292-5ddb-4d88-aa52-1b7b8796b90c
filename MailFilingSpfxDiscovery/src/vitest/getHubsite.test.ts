import { describe, it, expect, vi } from "vitest";
import { GraphFI } from "@pnp/graph";
import { getHubsite } from "../shared/graph/getHubsite";

describe("getHubsite", () => {
  it("should return the hubsite information", async () => {
    // Mock the return value of getByUrl to be a function that returns the expected site info
    const mockGetByUrl = vi.fn().mockReturnValue(async () => ({
      webUrl: "https://example.sharepoint.com/sites/AtveroMail",
    }));

    const mockGraph: GraphFI = {
      sites: {
        getByUrl: mockGetByUrl,
      },
    } as unknown as GraphFI;

    const sharePointTenant = "https://example.sharepoint.com";
    const hubsitePath = "/sites/AtveroMail";

    const hubsite = await getHubsite(mockGraph, sharePointTenant, hubsitePath);

    expect(mockGraph.sites.getByUrl).toHaveBeenCalledWith(
      sharePointTenant,
      hubsitePath
    );
    expect(hubsite.webUrl).toBe(
      "https://example.sharepoint.com/sites/AtveroMail"
    );
  });
});
