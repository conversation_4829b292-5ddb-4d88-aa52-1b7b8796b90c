import { describe, it, expect } from "vitest";
import { calculateEmailCount } from "../shared/utils/EmailCount";

describe("calculateEmailCount", () => {
  it('should return "0 emails" when both itemsLength and emailsShown are 0', () => {
    const result = calculateEmailCount(0, 0, 0);
    expect(result).toBe("0");
  });

  it("should return the items length if itemsLength is less than emailsShown", () => {
    const result = calculateEmailCount(50, 100, 200);
    expect(result).toBe("50");
  });

  it("should return emailsShown when totalEmails is 0 and emailsShown is greater than 0", () => {
    const result = calculateEmailCount(100, 100, 0);
    expect(result).toBe("100");
  });

  it("should return totalEmails when totalEmails is 100 or fewer", () => {
    const result = calculateEmailCount(100, 100, 100);
    expect(result).toBe("100");
  });

  it("should return a range when totalEmails is greater than 100", () => {
    const result = calculateEmailCount(150, 100, 200);
    expect(result).toBe("100 - 200");
  });

  it('should return "0 emails" if totalEmails is 0 and emailsShown is 0', () => {
    const result = calculateEmailCount(0, 0, 0);
    expect(result).toBe("0");
  });

  it("should return emailsShown if totalEmails is 0 and emailsShown is greater than 0", () => {
    const result = calculateEmailCount(100, 100, 0);
    expect(result).toBe("100");
  });

  it("should return totalEmails if totalEmails is less than or equal to 100", () => {
    const result = calculateEmailCount(80, 80, 80);
    expect(result).toBe("80");
  });

  it("should return the range of emails shown and totalEmails if totalEmails is greater than 100", () => {
    const result = calculateEmailCount(200, 100, 250);
    expect(result).toBe("100 - 250");
  });
});
