import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook } from "@testing-library/react-hooks";
import { waitFor } from "@testing-library/react";
import { useEmailData } from "../shared/hooks/useEmailData";
import { SortDirection } from "../shared/utils/DataManagment";
import { Filter, Operation } from "../shared/types/Filter";
import { Email, EmailResponse } from "../shared/types/Email";

vi.mock("../shared/utils/DataManagment", () => ({
  parseProjectFromRelativeUrl: vi.fn((url) => "TEST-PROJECT"),
  SortDirection: {
    Ascending: "Ascending",
    Descending: "Descending",
  },
}));

describe("useEmailData", () => {
  const mockEmail: Email = {
    id: "1",
    driveID: "drive1",
    driveItemID: undefined,
    fileRef: "ref1",
    sentBy: "<EMAIL>",
    subject: "Test Email",
    emailTextSummary: "Summary",
    tag: "Important",
    projectCode: "TEST-1",
    emailImportant: "Yes",
    emailConfidential: true,
    attachmentCount: "1",
    dateFiled: "2024-01-01",
  };

  const createMockEmailResponse = (
    emails: Email[],
    moreResults: boolean,
    nextLink?: string
  ): EmailResponse => ({
    emails,
    moreResultsAvailable: moreResults,
    page: 0,
    pageSize: 100,
    nextLink,
    sortField: "RefinableDate00",
    sortDirection: SortDirection.Descending,
    total: emails.length,
  });

  const mockDataAdapter = {
    fetchEmails: vi.fn(),
  };

  const mockPhotoResponse = [
    "Test User",
    "<EMAIL>",
    "photo-url",
  ] as const;

  const mockPreviewAdapter = {
    getPhoto: vi.fn().mockResolvedValue(mockPhotoResponse),
    readFileContent: vi.fn(),
    getPreviewUrl: vi.fn(),
    getSelectedEmailWithDriveItemID: vi.fn(),
  };

  const defaultProps = {
    dataAdapter: mockDataAdapter,
    previewAdapter: mockPreviewAdapter,
    isHubsite: false,
    relativeUrl: "/sites/test",
    absoluteUrl: "https://atverodevs.sharepoint.com/sites/test",
    filters: [] as Filter[],
    activeSearchTerm: "",
    columnCode: "RefinableDate00",
    sortDirection: SortDirection.Descending,
    projects: [],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockPreviewAdapter.getPhoto.mockResolvedValue(mockPhotoResponse);
  });

  it("initializes with updated pageSize of 100", () => {
    const { result } = renderHook(() => useEmailData(defaultProps));

    expect(result.current.loading).toBe(true);
    expect(result.current.page).toBe(0);
    expect(result.current.avatars.size).toBe(0);
    expect(result.current.pageSize).toBe(100);
  });

  it("fetches emails on mount for non-hubsite", async () => {
    const initialResponse = createMockEmailResponse([mockEmail], false);
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(initialResponse);

    const { result } = renderHook(() => useEmailData(defaultProps));

    await waitFor(() => {
      expect(mockDataAdapter.fetchEmails).toHaveBeenCalledWith(
        ["TEST-PROJECT"],
        "atverodevs.sharepoint.com",
        "",
        0,
        100,
        undefined,
        "RefinableDate00",
        "dateFiled",
        SortDirection.Descending,
        [],
        false
      );
    });

    await waitFor(() => {
      expect(result.current.emailData).toEqual([mockEmail]);
      expect(result.current.loading).toBe(false);
    });
  });

  it("fetches emails for hubsite when filters exist", async () => {
    const initialResponse = createMockEmailResponse([mockEmail], false);
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(initialResponse);

    const hubsiteProps = {
      ...defaultProps,
      isHubsite: true,
      filters: [
        {
          field: "ProjectCode",
          values: ["TEST-1"],
          application: Operation.Equals,
        },
      ],
    };

    const { result } = renderHook(() => useEmailData(hubsiteProps));

    await waitFor(() => {
      expect(mockDataAdapter.fetchEmails).toHaveBeenCalledWith(
        ["TEST-1"],
        "atverodevs.sharepoint.com",
        "",
        0,
        100,
        undefined,
        "RefinableDate00",
        "dateFiled",
        SortDirection.Descending,
        hubsiteProps.filters,
        false
      );
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
  });

  it("updates avatars for domain emails", async () => {
    const initialResponse = createMockEmailResponse([mockEmail], false);
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(initialResponse);

    const { result } = renderHook(() => useEmailData(defaultProps));

    await waitFor(() => {
      expect(mockPreviewAdapter.getPhoto).toHaveBeenCalledWith(
        "<EMAIL>"
      );
    });

    await waitFor(() => {
      expect(result.current.avatars.get("<EMAIL>")).toEqual({
        name: "Test User",
        image: "photo-url",
      });
    });
  });

  it("handles getNextPage correctly", async () => {
    const initialResponse = createMockEmailResponse([mockEmail], true);
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(initialResponse);

    const { result } = renderHook(() => useEmailData(defaultProps));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    const nextPageEmail: Email = {
      ...mockEmail,
      id: "2",
      subject: "Test Email 2",
    };

    const nextPageResponse = createMockEmailResponse([nextPageEmail], false);
    nextPageResponse.page = 1;

    mockDataAdapter.fetchEmails.mockResolvedValueOnce(nextPageResponse);

    await result.current.getNextPage(0);

    await waitFor(() => {
      expect(result.current.page).toBe(1);
      expect(result.current.emailData?.length).toBe(2);
    });
  });

  it("refreshes data when filters change", async () => {
    const initialResponse = createMockEmailResponse([mockEmail], false);
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(initialResponse);

    const { rerender } = renderHook((props) => useEmailData(props), {
      initialProps: defaultProps,
    });

    const newFilters: Filter[] = [
      {
        field: "ProjectCode",
        values: ["NEW-PROJECT"],
        application: Operation.Equals,
      },
    ];

    const filteredResponse = createMockEmailResponse(
      [{ ...mockEmail, projectCode: "NEW-PROJECT" }],
      false
    );
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(filteredResponse);

    rerender({
      ...defaultProps,
      filters: newFilters,
    });

    await waitFor(() => {
      expect(mockDataAdapter.fetchEmails).toHaveBeenCalledWith(
        ["TEST-PROJECT"],
        "atverodevs.sharepoint.com",
        "",
        0,
        100,
        undefined,
        "RefinableDate00",
        "dateFiled",
        SortDirection.Descending,
        newFilters,
        false
      );
    });
  });

  it("handles nextLink in pagination for non-hubsite", async () => {
    const initialResponse = createMockEmailResponse(
      [mockEmail],
      false,
      "next-page-link"
    );
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(initialResponse);

    const { result } = renderHook(() => useEmailData(defaultProps));

    await waitFor(() => {
      expect(result.current.emailData?.length).toBe(2); // Including undefined for more results
    });

    const nextPageEmail: Email = { ...mockEmail, id: "2" };
    const nextPageResponse = createMockEmailResponse([nextPageEmail], false);
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(nextPageResponse);

    await result.current.getNextPage(0);

    await waitFor(() => {
      expect(mockDataAdapter.fetchEmails).toHaveBeenLastCalledWith(
        ["TEST-PROJECT"],
        "atverodevs.sharepoint.com",
        "",
        1,
        100,
        "next-page-link",
        "RefinableDate00",
        "dateFiled",
        SortDirection.Descending,
        [],
        false
      );
    });
  });

  it("correctly maps column codes to keys", async () => {
    const initialResponse = createMockEmailResponse([mockEmail], false);
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(initialResponse);

    const propsWithDifferentColumn = {
      ...defaultProps,
      columnCode: "RefinableString31", // Subject column
    };

    const { result } = renderHook(() => useEmailData(propsWithDifferentColumn));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(mockDataAdapter.fetchEmails).toHaveBeenCalledWith(
        ["TEST-PROJECT"],
        "atverodevs.sharepoint.com",
        "",
        0,
        100,
        undefined,
        "RefinableString31",
        "subject",
        SortDirection.Descending,
        [],
        false
      );
    });
  });

  it("handles hubsite project selection correctly", async () => {
    const initialResponse = createMockEmailResponse([mockEmail], false);
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(initialResponse);

    const hubsiteProps = {
      ...defaultProps,
      isHubsite: true,
      filters: [
        {
          field: "ProjectCode",
          values: ["HUBSITE-PROJECT"],
          application: Operation.Equals,
        },
      ],
    };

    const { result } = renderHook(() => useEmailData(hubsiteProps));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(mockDataAdapter.fetchEmails).toHaveBeenCalledWith(
        ["HUBSITE-PROJECT"],
        "atverodevs.sharepoint.com",
        "",
        0,
        100,
        undefined,
        "RefinableDate00",
        "dateFiled",
        SortDirection.Descending,
        hubsiteProps.filters,
        false
      );
    });
  });

  it("handles both moreResultsAvailable and nextLink for pagination", async () => {
    const initialResponse = createMockEmailResponse(
      [mockEmail],
      true,
      "next-page-link"
    );
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(initialResponse);

    const { result } = renderHook(() => useEmailData(defaultProps));

    await waitFor(() => {
      expect(result.current.emailData?.length).toBe(2); // Including undefined for more results
    });

    const nextPageResponse = createMockEmailResponse(
      [{ ...mockEmail, id: "2" }],
      true,
      "another-link"
    );
    mockDataAdapter.fetchEmails.mockResolvedValueOnce(nextPageResponse);

    await result.current.getNextPage(0);

    await waitFor(() => {
      expect(mockDataAdapter.fetchEmails).toHaveBeenLastCalledWith(
        ["TEST-PROJECT"],
        "atverodevs.sharepoint.com",
        "",
        1,
        100,
        "next-page-link",
        "RefinableDate00",
        "dateFiled",
        SortDirection.Descending,
        [],
        false
      );
    });
  });
});
