import { describe, it, expect } from "vitest";
import {
  isFiltersDisabled,
  isClearFiltersDisabled,
} from "../shared/utils/FilterButtonHelpers";
import { Filter, Operation } from "../shared/types/Filter";

describe("isFiltersDisabled", () => {
  it("should return true when selectedProjects is empty and searchTerm is empty", () => {
    expect(isFiltersDisabled("", [])).toBe(true);
  });

  it("should return false when searchTerm is not empty", () => {
    expect(isFiltersDisabled("test", [])).toBe(false);
  });

  it("should return false when selectedProjects has one item", () => {
    expect(isFiltersDisabled("", ["project1"])).toBe(false);
  });

  it("should return false when both selectedProjects and searchTerm are not empty", () => {
    expect(isFiltersDisabled("test", ["project1"])).toBe(false);
  });

  it("should return true when selectedProjects has multiple items and searchTerm is empty", () => {
    expect(isFiltersDisabled("", ["project1", "project2"])).toBe(true);
  });

  it("should return false when selectedProjects has exactly one item and searchTerm is empty", () => {
    expect(isFiltersDisabled("", ["project1"])).toBe(false);
  });
});

describe("isClearFiltersDisabled", () => {
  it("should return true when filters array is empty", () => {
    expect(isClearFiltersDisabled([])).toBe(true);
  });

  it("should return false when filters array has items", () => {
    const filters: Filter[] = [
      {
        field: "EmailSubject",
        values: ["test"],
        application: Operation.Contains,
      },
    ];
    expect(isClearFiltersDisabled(filters)).toBe(false);
  });
});
