import { describe, it, expect } from "vitest";
import { getNameAndEmail } from "../shared/utils/useRecipient";

describe("getNameAndEmail", () => {
  it("should return both name and email when both are present", () => {
    const [name, email] = getNameAndEmail("<PERSON> <<EMAIL>>");
    expect(name).toBe("John Doe");
    expect(email).toBe("<EMAIL>");
  });

  it("should return only name when email is not present", () => {
    const [name, email] = getNameAndEmail("John Doe");
    expect(name).toBe("<PERSON> Do<PERSON>");
    expect(email).toBe("");
  });

  it("should return only email when name is not present", () => {
    const [name, email] = getNameAndEmail("<EMAIL>");
    expect(name).toBe("<EMAIL>");
    expect(email).toBe("<EMAIL>");
  });

  it("should return the recipient as name and empty email when input is invalid", () => {
    const [name, email] = getNameAndEmail("invalid input");
    expect(name).toBe("invalid input");
    expect(email).toBe("");
  });
});
