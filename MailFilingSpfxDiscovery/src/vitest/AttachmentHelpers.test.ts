import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  handleAttachments,
  determineFileType,
  parseMsgFileAttachments,
  parseEmlFileAttachments,
} from "../shared/utils/AttachmentHelpers";
import MsgReader from "@kenjiuno/msgreader";
import { IForEmailPreview } from "../adapters/IForEmailPreview";
import { Email } from "../shared/types/Email";

// Mock MsgReader implementation
vi.mock("@kenjiuno/msgreader", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      getFileData: vi.fn(),
      getAttachment: vi.fn(),
    })),
  };
});

vi.mock("postal-mime", () => ({
  default: {
    parse: vi.fn(),
  },
}));

describe("AttachmentHelpers", () => {
  let mockPreviewAdapter: IForEmailPreview;
  let mockEmail: Email;

  beforeEach(() => {
    vi.resetAllMocks();

    mockPreviewAdapter = {
      readFileContent: vi.fn(),
      getPreviewUrl: vi.fn(),
      getSelectedEmailWithDriveItemID: vi.fn(),
      getPhoto: vi.fn(),
    };

    mockEmail = {
      id: "1",
      projectCode: "testProject",
      subject: "Test Subject",
      sentBy: "<EMAIL>",
      dateFiled: "2023-11-12",
      driveID: "drive123",
      driveItemID: "item123",
      fileRef: "test.msg",
    };
  });

  describe("handleAttachments", () => {
    it("handles MSG file attachments", async () => {
      const mockMsgReaderInstance: Partial<MsgReader> = {
        getFileData: vi.fn().mockReturnValue({
          attachments: [
            {
              fileName: "test.msg",
              contentLength: 1024,
              attachMimeTag: "application/octet-stream",
            },
          ],
        }),
        getAttachment: vi
          .fn()
          .mockReturnValue({ content: new Uint8Array([1, 2, 3]) }),
      };

      vi.mocked(MsgReader).mockImplementation(
        () => mockMsgReaderInstance as MsgReader
      );
      vi.mocked(mockPreviewAdapter.readFileContent).mockResolvedValue(
        new ArrayBuffer(8)
      );

      const emailWithMsgFile = { ...mockEmail, fileRef: "test.msg" };
      const result = await handleAttachments(
        emailWithMsgFile,
        mockPreviewAdapter
      );

      expect(result.attachments).toHaveLength(1);
      expect(result.attachments[0].filename).toBe("test.msg");
    });

    it("logs an error for unsupported file types", async () => {
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});
      const emailWithUnknownFile = { ...mockEmail, fileRef: "unsupported.xyz" };

      const result = await handleAttachments(
        emailWithUnknownFile,
        mockPreviewAdapter
      );

      expect(consoleSpy).toHaveBeenCalledWith("Unsupported file type");
      expect(result).toEqual({
        attachments: [],
        fileRef: emailWithUnknownFile.fileRef,
      });
      consoleSpy.mockRestore();
    });

    it("handles unsupported file type gracefully", async () => {
      const emailWithUnsupportedFile = {
        ...mockEmail,
        fileRef: "file.unsupported",
      };
      vi.mocked(
        mockPreviewAdapter.getSelectedEmailWithDriveItemID
      ).mockResolvedValue(emailWithUnsupportedFile);

      const result = await handleAttachments(
        emailWithUnsupportedFile,
        mockPreviewAdapter
      );

      expect(result).toEqual({ attachments: [], fileRef: "file.unsupported" });
    });
  });

  describe("determineFileType", () => {
    it("identifies EML files correctly", () => {
      expect(determineFileType("file.eml")).toBe("eml");
    });

    it("identifies MSG files correctly", () => {
      expect(determineFileType("file.msg")).toBe("msg");
    });

    it("returns unknown for unsupported file types", () => {
      expect(determineFileType("file.txt")).toBe("unknown");
    });

    it("handles file names without extensions", () => {
      expect(determineFileType("file")).toBe("unknown");
    });

    it("handles empty file names", () => {
      expect(determineFileType("")).toBe("unknown");
    });

    it("handles filenames with special characters", () => {
      expect(determineFileType("file@name#$.eml")).toBe("eml");
      expect(determineFileType("file@name#$.msg")).toBe("msg");
    });
  });

  describe("parseMsgFileAttachments", () => {
    beforeEach(() => {
      vi.resetAllMocks();
    });

    it("correctly parses MSG file attachments", async () => {
      const mockAttachment = {
        fileName: "test.txt",
        contentLength: 1024,
        attachMimeTag: "text/plain",
      };

      const mockMsgReaderInstance: Partial<MsgReader> = {
        getFileData: vi.fn().mockReturnValue({
          attachments: [mockAttachment],
        }),
        getAttachment: vi.fn().mockReturnValue({
          content: new Uint8Array([1, 2, 3]),
        }),
      };

      vi.mocked(MsgReader).mockImplementation(
        () => mockMsgReaderInstance as MsgReader
      );

      const result = await parseMsgFileAttachments(new ArrayBuffer(8));

      expect(result.attachments).toHaveLength(1);
      expect(result.attachments[0].filename).toBe("test.txt");
    });

    it("handles MSG files with no attachments", async () => {
      const mockMsgReaderInstance: Partial<MsgReader> = {
        getFileData: vi.fn().mockReturnValue({
          attachments: [],
        }),
        getAttachment: vi.fn(),
      };

      vi.mocked(MsgReader).mockImplementation(
        () => mockMsgReaderInstance as MsgReader
      );

      const result = await parseMsgFileAttachments(new ArrayBuffer(8));

      expect(result.attachments).toEqual([]);
    });

    it("throws an error when MSG parsing fails", async () => {
      const mockMsgReaderInstance: Partial<MsgReader> = {
        getFileData: vi.fn().mockImplementation(() => {
          throw new Error("MSG parsing error");
        }),
      };

      vi.mocked(MsgReader).mockImplementation(
        () => mockMsgReaderInstance as MsgReader
      );

      await expect(parseMsgFileAttachments(new ArrayBuffer(8))).rejects.toThrow(
        "MSG parsing error"
      );
    });

    it("returns empty array if attachments property is missing", async () => {
      const mockMsgReaderInstance: Partial<MsgReader> = {
        getFileData: vi.fn().mockReturnValue({}),
        getAttachment: vi.fn(),
      };

      vi.mocked(MsgReader).mockImplementation(
        () => mockMsgReaderInstance as MsgReader
      );

      const result = await parseMsgFileAttachments(new ArrayBuffer(8));

      expect(result.attachments).toEqual([]);
    });
  });

  describe("parseEmlFileAttachments", () => {
    it("filters out inline attachments", async () => {
      const PostalMime = await import(
        /* webpackChunkName: 'postal-mime' */
        "postal-mime"
      );
      const mockContent = new Uint8Array([1, 2, 3]).buffer;

      vi.mocked(PostalMime.default.parse).mockResolvedValue({
        attachments: [
          {
            filename: "inline.png",
            mimeType: "image/png",
            content: mockContent,
            disposition: "inline",
            contentId: "abc123",
          },
          {
            filename: "attached.txt",
            mimeType: "text/plain",
            content: mockContent,
            disposition: "attachment",
          },
        ],
        headers: [],
        from: { name: "Tester" },
        messageId: "<msg-id>",
      });

      const result = await parseEmlFileAttachments(new ArrayBuffer(8));
      expect(result.attachments).toHaveLength(1);
      expect(result.attachments[0].filename).toBe("attached.txt");
    });

    it("uses 'unknown' as fallback filename", async () => {
      const PostalMime = await import(
        /* webpackChunkName: 'postal-mime' */
        "postal-mime"
      );
      const mockContent = new Uint8Array([1, 2, 3]).buffer;

      vi.mocked(PostalMime.default.parse).mockResolvedValue({
        attachments: [
          {
            filename: null,
            mimeType: "application/octet-stream",
            content: mockContent,
            disposition: "attachment",
          },
        ],
        headers: [],
        from: { name: "Tester" },
        messageId: "<msg-id>",
      });

      const result = await parseEmlFileAttachments(new ArrayBuffer(8));
      expect(result.attachments[0].filename).toBe("unknown");
    });

    it("throws error when parsing fails", async () => {
      const PostalMime = await import(
        /* webpackChunkName: 'postal-mime' */
        "postal-mime"
      );
      vi.mocked(PostalMime.default.parse).mockRejectedValue(new Error("fail"));

      await expect(parseEmlFileAttachments(new ArrayBuffer(8))).rejects.toThrow(
        "fail"
      );
    });
  });

  it("correctly sets attachment size using content byteLength", async () => {
    const mockContent = new Uint8Array([1, 2, 3, 4, 5]);

    const PostalMime = await import(
      /* webpackChunkName: 'postal-mime' */
      "postal-mime"
    );
    vi.mocked(PostalMime.default.parse).mockResolvedValue({
      headers: [
        { key: "From", value: "<EMAIL>" },
        { key: "To", value: "<EMAIL>" },
      ],
      from: { name: "Sender Name" },
      to: [{ name: "Recipient Name" }],
      subject: "Test Email",
      messageId: "<<EMAIL>>",
      attachments: [
        {
          filename: "test.txt",
          mimeType: "text/plain",
          content: mockContent.buffer,
          disposition: "attachment",
          contentId: "test-id",
        },
      ],
    });

    const result = await parseEmlFileAttachments(new ArrayBuffer(8));

    expect(result.attachments[0].size).toBe(5);
  });
});
