import { vi, describe, it, expect, beforeEach } from "vitest";
import { GraphFI } from "@pnp/graph";
import { IWeb } from "@pnp/sp/webs";
import { Filter, Operation } from "../shared/types/Filter";
import { handleFilter } from "../shared/utils/EmailUtils";
import { SortDirection } from "../shared/utils/DataManagment";
import { ListTitles } from "../shared/types/ListTitles";
import { IList, IRenderListDataAsStreamResult } from "@pnp/sp/lists";

describe("handleFilter", () => {
  let mockGraph: GraphFI;
  let mockWeb: IWeb;
  let mockGetSharePointWeb: (projectCode: string) => Promise<IWeb>;
  let mockList: IList;

  const mockEmailRow = {
    ID: "123",
    EmailSubject: "Test Email",
    EmailFrom: "<EMAIL>",
    EmailReceivedOn: "2024-01-01",
    AttachmentCount: "2",
    FileRef: "/sites/project1/Filed Email Content/doc.msg",
    EmailTextSummary: "Summary",
    EmailImportant: "Yes",
    EmailTags: "Important",
  };

  const mockListResponse: IRenderListDataAsStreamResult = {
    Row: [mockEmailRow],
    NextHref: undefined,
    CurrentFolderSpItemUrl: "",
    FilterLink: "",
    FirstRow: 1,
    FolderPermissions: "",
    ForceNoHierarchy: "",
    HierarchyHasIndention: "",
    LastRow: 1,
    RowLimit: 1,
  };

  beforeEach(() => {
    mockList = {
      renderListDataAsStream: vi.fn(),
    } as unknown as IList;

    mockWeb = {
      lists: {
        getByTitle: vi.fn().mockReturnValue(mockList),
      },
    } as unknown as IWeb;

    mockGraph = {
      sites: {
        getByUrl: vi
          .fn()
          .mockReturnValue(() => Promise.resolve({ id: "site-123" })),
      },
    } as unknown as GraphFI;

    mockGetSharePointWeb = vi.fn().mockResolvedValue(mockWeb);
  });

  it("should handle basic filtering", async () => {
    const filters: Filter[] = [
      {
        field: "EmailImportant",
        values: ["true"],
        application: Operation.Boolean,
      },
    ];

    vi.mocked(mockList.renderListDataAsStream).mockResolvedValue(
      mockListResponse
    );

    const result = await handleFilter(
      filters,
      undefined,
      SortDirection.Ascending,
      mockGetSharePointWeb,
      mockGraph,
      "project1",
      undefined,
      0,
      10,
      false,
      "example.com",
      false
    );

    expect(result.emails[0]).toMatchObject({
      id: "123",
      subject: "Test Email",
      sentBy: "<EMAIL>",
    });
    expect(mockWeb.lists.getByTitle).toHaveBeenCalledWith(
      ListTitles.NonConfidential
    );
  });

  it("should handle confidential filtering", async () => {
    const confidentialResponse = {
      ...mockListResponse,
      Row: [
        {
          ...mockEmailRow,
          FileRef: "/sites/project1/Confidential Filed Email Content/doc.msg",
        },
      ],
    };

    vi.mocked(mockList.renderListDataAsStream).mockResolvedValue(
      confidentialResponse
    );

    const result = await handleFilter(
      [],
      undefined,
      SortDirection.Ascending,
      mockGetSharePointWeb,
      mockGraph,
      "project1",
      undefined,
      0,
      10,
      false,
      "example.com",
      true
    );

    expect(result.emails[0].emailConfidential).toBe(true);
    expect(mockWeb.lists.getByTitle).toHaveBeenCalledWith(
      ListTitles.Confidential
    );
  });

  it("should handle sorting", async () => {
    vi.mocked(mockList.renderListDataAsStream).mockResolvedValue(
      mockListResponse
    );

    await handleFilter(
      [],
      "subject",
      SortDirection.Descending,
      mockGetSharePointWeb,
      mockGraph,
      "project1",
      undefined,
      0,
      10,
      false,
      "example.com",
      false
    );

    const renderListParams = vi.mocked(mockList.renderListDataAsStream).mock
      .calls[0][0];
    expect(renderListParams.ViewXml).toContain(
      '<FieldRef Name="EmailSubject" Ascending="False"'
    );
  });

  it("should handle pagination", async () => {
    const nextLink = "Paged=TRUE&p_ID=100";
    const responseWithNextPage = {
      ...mockListResponse,
      NextHref: nextLink,
    };

    vi.mocked(mockList.renderListDataAsStream).mockResolvedValue(
      responseWithNextPage
    );

    const result = await handleFilter(
      [],
      undefined,
      SortDirection.Ascending,
      mockGetSharePointWeb,
      mockGraph,
      "project1",
      nextLink,
      1,
      10,
      false,
      "example.com",
      false
    );

    expect(result.nextLink).toBe(nextLink.slice(1));
    expect(result.page).toBe(1);
    expect(result.pageSize).toBe(10);
  });

  it("should handle reset paging", async () => {
    vi.mocked(mockList.renderListDataAsStream).mockResolvedValue(
      mockListResponse
    );

    await handleFilter(
      [],
      undefined,
      SortDirection.Ascending,
      mockGetSharePointWeb,
      mockGraph,
      "project1",
      "some-next-link",
      0,
      10,
      true,
      "example.com",
      false
    );

    const renderListParams = vi.mocked(mockList.renderListDataAsStream).mock
      .calls[0][0];
    expect(renderListParams.Paging).toBe("Paged=TRUE");
  });

  it("should handle multiple filters", async () => {
    const filters: Filter[] = [
      {
        field: "EmailImportant",
        values: ["true"],
        application: Operation.Boolean,
      },
      {
        field: "EmailSubject",
        values: ["urgent"],
        application: Operation.Contains,
      },
    ];

    vi.mocked(mockList.renderListDataAsStream).mockResolvedValue(
      mockListResponse
    );

    await handleFilter(
      filters,
      undefined,
      SortDirection.Ascending,
      mockGetSharePointWeb,
      mockGraph,
      "project1",
      undefined,
      0,
      10,
      false,
      "example.com",
      false
    );

    const renderListParams = vi.mocked(mockList.renderListDataAsStream).mock
      .calls[0][0];
    expect(renderListParams.ViewXml).toContain("EmailImportant");
    expect(renderListParams.ViewXml).toContain("EmailSubject");
  });

  it("should handle invalid SharePoint web", async () => {
    const result = await handleFilter(
      [],
      undefined,
      SortDirection.Ascending,
      mockGetSharePointWeb,
      mockGraph,
      "project1",
      undefined,
      0,
      10,
      false,
      "example.com",
      false
    );

    expect(result).toEqual({
      page: 0,
      pageSize: 0,
      nextLink: undefined,
      emails: [],
      sortField: undefined,
      sortDirection: SortDirection.Ascending,
      moreResultsAvailable: undefined,
    });
  });

  it("should handle SharePoint errors", async () => {
    vi.mocked(mockList.renderListDataAsStream).mockRejectedValue(
      new Error("SharePoint Error")
    );

    const result = await handleFilter(
      [],
      undefined,
      SortDirection.Ascending,
      mockGetSharePointWeb,
      mockGraph,
      "project1",
      undefined,
      0,
      10,
      false,
      "example.com",
      false
    );

    expect(result).toEqual({
      page: 0,
      pageSize: 0,
      nextLink: undefined,
      emails: [],
      sortField: undefined,
      sortDirection: SortDirection.Ascending,
      moreResultsAvailable: undefined,
    });
  });

  it("should format file references correctly", async () => {
    vi.mocked(mockList.renderListDataAsStream).mockResolvedValue(
      mockListResponse
    );

    const result = await handleFilter(
      [],
      undefined,
      SortDirection.Ascending,
      mockGetSharePointWeb,
      mockGraph,
      "project1",
      undefined,
      0,
      10,
      false,
      "example.com",
      false
    );

    expect(result.emails[0].fileRef).toBe(
      `https://example.com${mockEmailRow.FileRef}`
    );
  });
});
