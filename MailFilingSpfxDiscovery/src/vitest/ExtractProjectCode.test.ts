import { describe, expect, it } from "vitest";
import { extractProjectCode } from "../shared/utils/ExtractProjectCode";

describe("extractProjectCode", () => {
  it("should extract the project code from a valid site path", () => {
    const input = "sites/project1/folder";
    const result = extractProjectCode(input);
    expect(result).toBe("folder");
  });

  it("should return undefined for invalid site path", () => {
    const emptyInput = "";
    const result = extractProjectCode(emptyInput);
    expect(result).toBeUndefined();
  });
});
