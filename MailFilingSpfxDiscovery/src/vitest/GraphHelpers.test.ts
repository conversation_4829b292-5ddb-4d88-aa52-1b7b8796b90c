import { getSiteGuidFromGraphGuid } from "../shared/utils/GraphHelpers";
import { describe, it, expect } from "vitest";

describe("getSiteGuidFromGraphGuid", () => {
  it("returns the correct site guid", () => {
    const graphSiteId = "test.sharepoint.com, 12345,67890";
    const expectedSiteGuid = "12345";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });

  it("returns an empty string if graphSiteId is empty", () => {
    const graphSiteId = "";
    const expectedSiteGuid = "";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });

  it("returns the first part if graphSiteId does not follow the right format (one part in id)", () => {
    const graphSiteId = "12345";
    const expectedSiteGuid = "";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });

  it("returns the first part if graphSiteId does not follow the right format (two parts in id)", () => {
    const graphSiteId = "test.sharepoint.com,12345";
    const expectedSiteGuid = "";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });

  it("returns the first part if graphSiteId does not follow the right format (,)", () => {
    const graphSiteId = ",";
    const expectedSiteGuid = "";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });
});
