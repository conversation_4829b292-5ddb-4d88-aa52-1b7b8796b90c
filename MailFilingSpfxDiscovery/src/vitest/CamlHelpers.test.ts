import { describe, it, expect } from "vitest";
import {
  makeCAMLFilter,
  createCAMLValuesInner,
  createCAMLValues,
  keepFiltersWithValues,
  createCAMLFilters,
  Filter,
  Operation,
} from "../shared/utils/CamlHelpers";

describe("CAMLUtils", () => {
  describe("makeCAMLFilter", () => {
    it("returns empty string for empty value", () => {
      expect(makeCAMLFilter("Field", Operation.Equals, "")).toBe("");
    });

    it("creates a valid CAML filter for Equals operation", () => {
      expect(makeCAMLFilter("Field", Operation.Equals, "Value")).toBe(
        "<Eq><FieldRef Name='Field'/><Value Type='Text'>Value</Value></Eq>"
      );
    });

    it("creates a valid CAML filter for Contains operation", () => {
      expect(makeCAMLFilter("Field", Operation.Contains, "Value")).toBe(
        "<Contains><FieldRef Name='Field'/><Value Type='Text'>Value</Value></Contains>"
      );
    });

    it("creates a valid CAML filter for Boolean operation", () => {
      expect(makeCAMLFilter("Field", Operation.Boolean, "1")).toBe(
        "<Eq><FieldRef Name='Field'/><Value Type='Boolean'>1</Value></Eq>"
      );
    });

    it("creates a valid CAML filter for NotBooleanOrNull operation", () => {
      expect(makeCAMLFilter("Field", Operation.NotBooleanOrNull, "1")).toBe(
        "<Or><Neq><FieldRef Name='Field'/><Value Type='Boolean'>1</Value></Neq><IsNull><FieldRef Name='Field'/></IsNull></Or>"
      );
    });

    it("creates a valid CAML filter for GreaterThan operation", () => {
      expect(makeCAMLFilter("Field", Operation.GreaterThan, "10")).toBe(
        "<Gt><FieldRef Name='Field'/><Value Type='Number'>10</Value></Gt>"
      );
    });

    it("returns empty string for unsupported operations", () => {
      expect(makeCAMLFilter("Field", 99 as Operation, "Value")).toBe("");
    });
  });

  describe("createCAMLValuesInner", () => {
    it("returns empty string for no values", () => {
      expect(createCAMLValuesInner("Field", Operation.Equals, [])).toBe("");
    });

    it("creates a single CAML filter for one value", () => {
      expect(createCAMLValuesInner("Field", Operation.Equals, ["Value"])).toBe(
        "<Eq><FieldRef Name='Field'/><Value Type='Text'>Value</Value></Eq>"
      );
    });

    it("creates OR combined filters for multiple values", () => {
      expect(
        createCAMLValuesInner("Field", Operation.Equals, ["Value1", "Value2"])
      ).toBe(
        "<Or><Eq><FieldRef Name='Field'/><Value Type='Text'>Value1</Value></Eq><Eq><FieldRef Name='Field'/><Value Type='Text'>Value2</Value></Eq></Or>"
      );
    });
  });

  describe("createCAMLValues", () => {
    it("wraps the inner values correctly", () => {
      expect(
        createCAMLValues("Field", Operation.Equals, ["Value1", "Value2"])
      ).toBe(
        "<Or><Eq><FieldRef Name='Field'/><Value Type='Text'>Value1</Value></Eq><Eq><FieldRef Name='Field'/><Value Type='Text'>Value2</Value></Eq></Or>"
      );
    });
  });

  describe("keepFiltersWithValues", () => {
    const filters: Filter[] = [
      { field: "Field1", values: ["Value1"], application: Operation.Equals },
      { field: "Field2", values: [""], application: Operation.Contains },
      { field: "Field3", values: [], application: Operation.GreaterThan },
    ];

    it("filters out filters with no valid values", () => {
      const result = keepFiltersWithValues(filters);
      expect(result).toEqual([filters[0]]);
    });
  });

  describe("createCAMLFilters", () => {
    const filters: Filter[] = [
      { field: "Field1", values: ["Value1"], application: Operation.Equals },
      { field: "Field2", values: [""], application: Operation.Contains },
    ];

    it("returns empty string for no valid filters", () => {
      expect(createCAMLFilters([])).toBe("");
    });

    it("creates combined CAML filters for valid filters", () => {
      expect(createCAMLFilters(filters)).toBe(
        "<Eq><FieldRef Name='Field1'/><Value Type='Text'>Value1</Value></Eq>"
      );
    });
  });
});
