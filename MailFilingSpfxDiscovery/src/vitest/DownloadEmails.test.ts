import {
  handleDownloadEmail,
  handleDownloadMultipleEmails,
} from "../shared/utils/DownloadEmails";
import { vi, describe, beforeEach, afterEach, it, expect } from "vitest";
import JSZip from "jszip";
import { saveAs } from "file-saver";
import { GraphFI } from "@pnp/graph";
import { Email } from "../shared/types/Email";

// Mock external dependencies
vi.mock("file-saver", () => ({
  saveAs: vi.fn(),
}));

vi.mock("./ParseFileRef", () => ({
  parseFileRef: vi.fn((_fileRef) => "parsed-filename.eml"),
}));

const mockEmail: Email = {
  projectCode: "Project1",
  id: "email-1",
  subject: "Mock Email Subject",
  sentBy: "<EMAIL>",
  dateFiled: "2024-11-12T12:00:00.000Z",
  driveItemID: "mock-drive-item-id",
  driveID: "mock-drive-id",
  attachmentCount: 2,
  fileRef: "http://example.com/mock-email-file.eml",
  emailTextSummary: "This is a mock email summary.",
  emailImportant: false,
  emailConfidential: false,
};

describe("handleDownloadEmail", () => {
  beforeEach(() => {
    global.open = vi.fn();
    URL.createObjectURL = vi.fn(() => "blob:http://example.com/blob");
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should open a new tab with the file reference if the fileRef exists", () => {
    handleDownloadEmail(mockEmail);

    expect(window.open).toHaveBeenCalledWith(
      "http://example.com/mock-email-file.eml",
      "_blank"
    );
  });
});

describe("handleDownloadMultipleEmails", () => {
  let graph: GraphFI;
  let emails: Email[];

  beforeEach(() => {
    const getContentMock = vi.fn(() =>
      Promise.resolve(new Blob(["Test content"], { type: "text/plain" }))
    );
    const getItemByIdMock = vi.fn(() => ({ getContent: getContentMock }));
    const getByIdMock = vi.fn(() => ({ getItemById: getItemByIdMock }));

    graph = {
      drives: {
        getById: getByIdMock,
      },
    } as unknown as GraphFI;

    emails = [
      {
        id: "email-1",
        subject: "Email 1",
        sentBy: "<EMAIL>",
        dateFiled: "2024-09-06T11:16:32.0000000+00:00",
        projectCode: "Mail",
        fileRef:
          "https://example.sharepoint.com/sites/Mail/2024-09/06-10-Email-1.eml",
        driveID: "test-drive-id",
        driveItemID: "test-drive-item-id",
        attachmentCount: 3,
        emailTextSummary: "",
        emailConfidential: false,
        emailImportant: false,
      },
      {
        id: "email-2",
        subject: "Email 2",
        sentBy: "<EMAIL>",
        dateFiled: "2024-09-06T10:59:07.0000000+00:00",
        projectCode: "Mail",
        fileRef:
          "https://example.sharepoint.com/sites/Mail/2024-09/06-10-Email-2.eml",
        driveID: "test-drive-id",
        driveItemID: "test-drive-item-id",
        attachmentCount: 3,
        emailTextSummary: "",
        emailConfidential: false,
        emailImportant: false,
      },
    ];

    vi.clearAllMocks();
  });

  it("should log an error if no emails are provided", async () => {
    console.error = vi.fn();
    await handleDownloadMultipleEmails(graph, []);

    expect(console.error).toHaveBeenCalledWith(
      "No emails available for download"
    );
    expect(saveAs).not.toHaveBeenCalled();
  });

  it("should log an error if zip generation fails", async () => {
    vi.spyOn(JSZip.prototype, "generateAsync").mockRejectedValue(
      new Error("Zip generation failed")
    );
    console.error = vi.fn();

    await handleDownloadMultipleEmails(graph, emails);

    expect(console.error).toHaveBeenCalledWith(
      "Error generating zip file:",
      expect.any(Error)
    );
  });

  it("should fetch the content for each email and add to the zip", async () => {
    const mockGenerateAsync = vi
      .spyOn(JSZip.prototype, "generateAsync")
      .mockResolvedValue(
        new Blob(["zip content"], { type: "application/zip" })
      );

    await handleDownloadMultipleEmails(graph, emails);

    expect(graph.drives.getById).toHaveBeenCalledWith("test-drive-id");
    expect(saveAs).toHaveBeenCalled(); // Ensure saveAs is called after zip is generated
    expect(mockGenerateAsync).toHaveBeenCalled();
  });

  // it("should log an error if no fileRef is found for an email", async () => {
  //   const faultyEmail: Email = { ...emails[0], fileRef: "Filed Email Content" };
  //   console.error = vi.fn();

  //   await handleDownloadMultipleEmails(graph, [faultyEmail]);

  //   expect(console.error).toHaveBeenCalledWith(
  //     `No file reference found for email: ${faultyEmail.id}`
  //   );
  // });

  it("should log an error if fetching email content fails", async () => {
    graph.drives.getById = vi.fn(() => ({
      getItemById: vi.fn(() => ({
        getContent: vi.fn(() => {
          throw new Error("Fetch error");
        }),
      })),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    })) as any;

    console.error = vi.fn();

    await handleDownloadMultipleEmails(graph, emails);

    expect(console.error).toHaveBeenCalledWith(
      `Error fetching email file: ${emails[0].fileRef}`,
      expect.any(Error)
    );
  });

  // it("should not call saveAs if zip content is empty", async () => {
  //   // Mock the generateAsync method to return an empty Blob

  //   await handleDownloadMultipleEmails(graph, emails);

  //   expect(console.error).toHaveBeenCalledWith(
  //     "Zip content is empty, nothing to download"
  //   );
  //   expect(saveAs).not.toHaveBeenCalled();
  // });

  // it("should log an error if email does not have driveID", async () => {
  //   const faultyEmail: Email = {
  //     ...emails[0],
  //     driveID: undefined,
  //   };
  //   console.error = vi.fn();

  //   await handleDownloadMultipleEmails(graph, [faultyEmail]);

  //   expect(console.error).toHaveBeenCalledWith(
  //     `No drive or Item ID found for email: ${faultyEmail.id}`
  //   );
  //   expect(saveAs).not.toHaveBeenCalled();
  // });

  it("should skip emails with missing driveItemID and continue", async () => {
    const faultyEmail = { ...emails[0], driveItemID: undefined }; // First email has missing driveItemID
    const mockGenerateAsync = vi
      .spyOn(JSZip.prototype, "generateAsync")
      .mockResolvedValue(
        new Blob(["zip content"], { type: "application/zip" })
      );

    // Pass a faulty email and a valid one
    await handleDownloadMultipleEmails(graph, [faultyEmail, emails[1]]);

    expect(graph.drives.getById).toHaveBeenCalledWith("test-drive-id"); // Ensure the valid email is processed
    expect(mockGenerateAsync).toHaveBeenCalled(); // Ensure zip is generated for the valid email
    expect(saveAs).toHaveBeenCalled(); // Ensure saveAs is called for the valid email
  });

  it("should log an error if no file contents are returned", async () => {
    graph.drives.getById = vi.fn(() => ({
      getItemById: vi.fn(() => ({
        getContent: vi.fn(() => undefined), // Simulate no content returned
      })),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    })) as any;

    console.error = vi.fn();

    await handleDownloadMultipleEmails(graph, emails);

    expect(console.error).toHaveBeenCalledWith(
      `Error fetching email file: ${emails[0].fileRef}`
    );
  });
});
