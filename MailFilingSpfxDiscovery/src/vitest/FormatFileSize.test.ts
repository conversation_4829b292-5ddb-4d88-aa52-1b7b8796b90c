import { describe, it, expect } from "vitest";
import { formatFileSize } from "../shared/utils/FormatFileSize";

describe("formatFileSize", () => {
  it("returns 'Unknown size' for undefined input", () => {
    expect(formatFileSize(undefined)).toBe("Unknown size");
  });

  it("returns '0 Byte' for 0 bytes", () => {
    expect(formatFileSize(0)).toBe("0 Byte");
  });

  it("formats bytes correctly", () => {
    expect(formatFileSize(500)).toBe("500 Bytes");
  });

  it("formats kilobytes correctly", () => {
    expect(formatFileSize(1024)).toBe("1 KB");
    expect(formatFileSize(1536)).toBe("2 KB");
  });

  it("formats megabytes correctly", () => {
    expect(formatFileSize(1024 * 1024)).toBe("1 MB");
  });

  it("formats gigabytes correctly", () => {
    expect(formatFileSize(1024 * 1024 * 2.5)).toBe("3 MB"); // rounds to nearest
    expect(formatFileSize(1024 * 1024 * 1024)).toBe("1 GB");
  });

  it("formats terabytes correctly", () => {
    expect(formatFileSize(1024 ** 4)).toBe("1 TB");
  });

  it("handles very large numbers gracefully", () => {
    const result = formatFileSize(1024 ** 6); // beyond TB
    expect(result).toMatch(/^\d+ /); // it still returns a string with a number and unit
  });
});
