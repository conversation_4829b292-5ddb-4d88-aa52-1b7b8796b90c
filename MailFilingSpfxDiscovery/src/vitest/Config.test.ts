import { describe, it, expect } from "vitest";
import * as ConfigModule from "../shared/utils/Config";

describe("Config utilities", () => {
  it("returns correct RedirectUri", () => {
    expect(ConfigModule.RedirectUri()).toBe("https://localhost:3001");
  });

  it("returns correct Scopes array", () => {
    const scopes = ConfigModule.Scopes();
    expect(scopes).toContain("User.Read");
    expect(scopes).toContain("Sites.Read.All");
    expect(scopes).toContain("Contacts.Read");
    expect(scopes).toContain("Files.ReadWrite");
    expect(scopes).toContain("Group.Read.All");
    expect(scopes).toContain("Mail.ReadWrite");
    expect(scopes).toContain("ProfilePhoto.Read.All");
    expect(scopes).toHaveLength(7);
  });

  it("returns default hubsite path", () => {
    expect(ConfigModule.DefaultHubsitePath()).toBe("/sites/AtveroMail");
  });
});
