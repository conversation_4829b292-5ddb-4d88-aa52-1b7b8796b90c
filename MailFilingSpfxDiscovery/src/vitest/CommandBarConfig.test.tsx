import React from "react";
import { describe, it, expect, vi } from "vitest";
import { getCommandBarConfig } from "../shared/utils/CommandBarConfig";
import { Operation } from "../shared/types/Filter";

// We don't need to mock SearchBoxComponent as we want to test the real component
vi.mock("@fluentui/react/lib/SearchBox", () => ({
  SearchBox: ({ placeholder, value }) => (
    <input
      data-testid="fluent-searchbox"
      placeholder={placeholder}
      value={value}
    />
  ),
}));

describe("getCommandBarConfig", () => {
  const defaultProps = {
    searchTerm: "test",
    setSearchTerm: vi.fn(),
    doSearch: vi.fn(),
    filters: [],
    emailData: undefined,
    isConfidential: false,
    toggleDriveFilter: vi.fn(),
    setFilters: vi.fn(),
    setIsConfidential: vi.fn(),
    openFilters: vi.fn(),
  };

  describe("searchButton", () => {
    it("should return searchButton with correct properties", () => {
      const config = getCommandBarConfig(
        defaultProps.searchTerm,
        defaultProps.setSearchTerm,
        defaultProps.doSearch,
        defaultProps.filters,
        defaultProps.emailData,
        defaultProps.isConfidential,
        defaultProps.toggleDriveFilter,
        defaultProps.setFilters,
        defaultProps.setIsConfidential,
        defaultProps.openFilters
      );

      expect(config.searchButton.key).toBe("search");
      expect(config.searchButton.text).toBe("Search");
      expect(config.searchButton.ariaLabel).toBe("Search");
      expect(config.searchButton.iconOnly).toBe(false);
      expect(config.searchButton.iconProps?.iconName).toBe("Search");
    });
  });

  describe("_farItems", () => {
    it("should return only filter button when no filters and no emailData", () => {
      const config = getCommandBarConfig(
        defaultProps.searchTerm,
        defaultProps.setSearchTerm,
        defaultProps.doSearch,
        [],
        undefined,
        defaultProps.isConfidential,
        defaultProps.toggleDriveFilter,
        defaultProps.setFilters,
        defaultProps.setIsConfidential,
        defaultProps.openFilters
      );

      expect(config._farItems).toHaveLength(1);
      expect(config._farItems[0].key).toBe("filters");
      expect(config._farItems[0].iconProps?.iconName).toBe("Filter");
    });

    it("should include showconfidential button when emailData exists", () => {
      const config = getCommandBarConfig(
        defaultProps.searchTerm,
        defaultProps.setSearchTerm,
        defaultProps.doSearch,
        [],
        [
          {
            projectCode: "123",
            id: "1",
            subject: "Test Email",
            sentBy: "<EMAIL>",
            dateFiled: "27/03/2024",
            driveID: "1234567",
            attachmentCount: "0",
            fileRef: "",
            emailTextSummary: "",
            emailImportant: "No",
            emailConfidential: false,
          },
        ],
        defaultProps.isConfidential,
        defaultProps.toggleDriveFilter,
        defaultProps.setFilters,
        defaultProps.setIsConfidential,
        defaultProps.openFilters
      );

      expect(config._farItems).toHaveLength(2);
      expect(config._farItems[0].key).toBe("showconfidential");
      expect(config._farItems[0].text).toBe("Show Confidential Emails");
    });

    it("should include clearfilters button when filters exist", () => {
      const config = getCommandBarConfig(
        defaultProps.searchTerm,
        defaultProps.setSearchTerm,
        defaultProps.doSearch,
        [
          {
            field: "test",
            values: [""],
            application: Operation.Boolean,
          },
        ],
        undefined,
        defaultProps.isConfidential,
        defaultProps.toggleDriveFilter,
        defaultProps.setFilters,
        defaultProps.setIsConfidential,
        defaultProps.openFilters
      );

      expect(config._farItems).toHaveLength(2);
      expect(config._farItems.some((item) => item.key === "clearfilters")).toBe(
        true
      );
    });

    it("should show correct text for confidential toggle", () => {
      const config = getCommandBarConfig(
        defaultProps.searchTerm,
        defaultProps.setSearchTerm,
        defaultProps.doSearch,
        [],
        [
          {
            projectCode: "123",
            id: "1",
            subject: "Test Email",
            sentBy: "<EMAIL>",
            dateFiled: "27/03/2024",
            driveID: "1234567",
            attachmentCount: "0",
            fileRef: "",
            emailTextSummary: "",
            emailImportant: "No",
            emailConfidential: true,
          },
        ],
        true,
        defaultProps.toggleDriveFilter,
        defaultProps.setFilters,
        defaultProps.setIsConfidential,
        defaultProps.openFilters
      );

      const confidentialButton = config._farItems.find(
        (item) => item.key === "showconfidential"
      );
      expect(confidentialButton?.text).toBe("Show Regular Emails");
      expect(confidentialButton?.iconProps?.iconName).toBe("Mail");
    });

    it("should call correct functions on button clicks", () => {
      const config = getCommandBarConfig(
        defaultProps.searchTerm,
        defaultProps.setSearchTerm,
        defaultProps.doSearch,
        [
          {
            field: "test",
            values: [""],
            application: Operation.Boolean,
          },
        ],
        [
          {
            projectCode: "123",
            id: "1",
            subject: "Test Email",
            sentBy: "<EMAIL>",
            dateFiled: "27/03/2024",
            driveID: "1234567",
            attachmentCount: "0",
            fileRef: "",
            emailTextSummary: "",
            emailImportant: "No",
            emailConfidential: true,
          },
        ],
        defaultProps.isConfidential,
        defaultProps.toggleDriveFilter,
        defaultProps.setFilters,
        defaultProps.setIsConfidential,
        defaultProps.openFilters
      );

      // Test confidential toggle
      const confidentialButton = config._farItems.find(
        (item) => item.key === "showconfidential"
      );
      confidentialButton?.onClick?.();
      expect(defaultProps.toggleDriveFilter).toHaveBeenCalled();

      // Test clear filters
      const clearFiltersButton = config._farItems.find(
        (item) => item.key === "clearfilters"
      );
      clearFiltersButton?.onClick?.();
      expect(defaultProps.setFilters).toHaveBeenCalledWith([]);
      expect(defaultProps.setIsConfidential).toHaveBeenCalledWith(false);

      // Test filter button
      const filterButton = config._farItems.find(
        (item) => item.key === "filters"
      );
      filterButton?.onClick?.();
      expect(defaultProps.openFilters).toHaveBeenCalled();
    });
  });
  it("should render SearchBoxComponent with correct props", () => {
    const mockSetSearchTerm = vi.fn();
    const mockDoSearch = vi.fn();

    const config = getCommandBarConfig(
      "test-search",
      mockSetSearchTerm,
      mockDoSearch,
      [],
      undefined,
      false,
      vi.fn(),
      vi.fn(),
      vi.fn(),
      vi.fn()
    );

    const mockDismissMenu = vi.fn();
    const searchBox = config.searchButton.onRender?.(
      null,
      mockDismissMenu
    ) as React.ReactElement;

    expect(searchBox?.props).toEqual({
      searchTerm: "test-search",
      setSearchTerm: mockSetSearchTerm,
      doSearch: mockDoSearch,
    });
  });
});
