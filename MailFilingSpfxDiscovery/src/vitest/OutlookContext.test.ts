import { describe, it, expect } from "vitest";
import {
  OutlookInfo,
  parseOutlookSource,
} from "../shared/utils/OutlookContext";

describe("parseOutlookSource", () => {
  it("correctly parses Mac source", () => {
    const url = "https://example.com?_host_info=mac$16.0$en-US";
    const expected: OutlookInfo = {
      source: "mac",
      version: "16.0",
      language: "en-us",
    };
    expect(parseOutlookSource(url)).toEqual(expected);
  });

  it("correctly parses Web source", () => {
    const url = "https://example.com?_host_info=web$16.0$en-US";
    const expected: OutlookInfo = {
      source: "web",
      version: "16.0",
      language: "en-us",
    };
    expect(parseOutlookSource(url)).toEqual(expected);
  });

  it("correctly parses Windows source", () => {
    const url = "https://example.com?_host_info=win32$16.0$en-US";
    const expected: OutlookInfo = {
      source: "windows",
      version: "16.0",
      language: "en-us",
    };
    expect(parseOutlookSource(url)).toEqual(expected);
  });

  it("returns web for unrecognized source", () => {
    const url = "https://example.com?_host_info=linux$16.0$en-US";
    const expected: OutlookInfo = {
      source: "web",
      version: "16.0",
      language: "en-us",
    };
    expect(parseOutlookSource(url)).toEqual(expected);
  });

  it("handles missing _host_info parameter", () => {
    const url = "https://example.com";
    const expected: OutlookInfo = {
      source: "web",
      version: "",
      language: "",
    };
    expect(parseOutlookSource(url)).toEqual(expected);
  });
});
