import { vi, describe, it, expect, beforeEach } from "vitest";
import { GraphFI } from "@pnp/graph";
import { Filter, Operation } from "../shared/types/Filter";
import { getEmailsFromSearch } from "../shared/graph/getEmailsFromSearch";
import { SortDirection } from "../shared/utils/DataManagment";

describe("getEmailsFromSearch", () => {
  let mockGraph: GraphFI;
  const tenantUrl = "https://example.com";

  beforeEach(() => {
    mockGraph = {
      query: vi.fn(),
    } as unknown as GraphFI;
  });

  const mockSearchResponse = {
    hitsContainers: [
      {
        hits: [
          {
            hitId: "123",
            resource: {
              id: "drive-id-1",
              webUrl:
                "https://example.com/sites/project1/Filed%20Email%20Content",
              listItem: {
                fields: {
                  refinableString31: "Test Subject",
                  refinableString35: "<EMAIL>",
                  refinableDate00: "2024-01-01",
                  refinableString51: "Important",
                  refinableDecimal09: "2",
                  driveId: "drive-123",
                  sitename: "sites/project1",
                  refinableString52: "Yes",
                },
              },
            },
            summary: "Email summary",
          },
        ],
        total: 1,
        moreResultsAvailable: false,
      },
    ],
  };

  it("should fetch emails with default parameters", async () => {
    vi.mocked(mockGraph.query).mockResolvedValueOnce([mockSearchResponse]);

    const result = await getEmailsFromSearch(
      mockGraph,
      [],
      tenantUrl,
      "",
      0,
      10,
      undefined
    );

    expect(result.emails).toHaveLength(1);
    expect(result.emails[0]).toMatchObject({
      id: "123",
      subject: "Test Subject",
      sentBy: "<EMAIL>",
      projectCode: "project1",
    });
  });

  it("should handle confidential email filtering", async () => {
    vi.mocked(mockGraph.query).mockResolvedValueOnce([mockSearchResponse]);

    const confidentialFilter: Filter = {
      field: "EmailConfidential",
      values: ["true"],
      application: Operation.Boolean,
    };

    await getEmailsFromSearch(
      mockGraph,
      ["project1"],
      tenantUrl,
      "",
      0,
      10,
      undefined,
      undefined,
      [confidentialFilter]
    );

    const queryCall = vi.mocked(mockGraph.query).mock.calls[0][0];
    const queryTemplate = queryCall.query?.queryTemplate;
    expect(queryTemplate).toContain("Confidential%20Filed%20Email%20Content");
  });

  it("should apply sorting correctly", async () => {
    vi.mocked(mockGraph.query).mockResolvedValueOnce([mockSearchResponse]);

    await getEmailsFromSearch(
      mockGraph,
      [],
      tenantUrl,
      "",
      0,
      10,
      "EmailReceived",
      SortDirection.Ascending
    );

    const queryCall = vi.mocked(mockGraph.query).mock.calls[0][0];
    expect(queryCall.sortProperties).toEqual([
      {
        name: "EmailReceived",
        isDescending: false,
      },
    ]);
  });

  it("should handle pagination parameters", async () => {
    vi.mocked(mockGraph.query).mockResolvedValueOnce([mockSearchResponse]);

    const page = 2;
    const pageSize = 15;

    await getEmailsFromSearch(
      mockGraph,
      [],
      tenantUrl,
      "",
      page,
      pageSize,
      undefined
    );

    const queryCall = vi.mocked(mockGraph.query).mock.calls[0][0];
    expect(queryCall.from).toBe(page * pageSize);
    expect(queryCall.size).toBe(pageSize);
  });

  it("should apply hubsite filtering when provided", async () => {
    vi.mocked(mockGraph.query).mockResolvedValueOnce([mockSearchResponse]);

    const hubsiteId = "hub123";

    await getEmailsFromSearch(
      mockGraph,
      [],
      tenantUrl,
      "",
      0,
      10,
      undefined,
      undefined,
      undefined,
      hubsiteId
    );

    const queryCall = vi.mocked(mockGraph.query).mock.calls[0][0];
    const queryTemplate = queryCall.query?.queryTemplate;
    expect(queryTemplate).toContain(`DepartmentId:${hubsiteId}`);
  });

  it("should handle search terms correctly", async () => {
    vi.mocked(mockGraph.query).mockResolvedValueOnce([mockSearchResponse]);

    const searchTerm = "important email";

    await getEmailsFromSearch(
      mockGraph,
      [],
      tenantUrl,
      searchTerm,
      0,
      10,
      undefined
    );

    const queryCall = vi.mocked(mockGraph.query).mock.calls[0][0];
    const queryString = queryCall.query?.queryString;
    expect(queryString).toBe(searchTerm);
  });

  it("should handle multiple filters", async () => {
    vi.mocked(mockGraph.query).mockResolvedValueOnce([mockSearchResponse]);

    const filters: Filter[] = [
      {
        field: "EmailImportant",
        values: ["true"],
        application: Operation.Boolean,
      },
      {
        field: "EmailSubject",
        values: ["urgent"],
        application: Operation.Contains,
      },
    ];

    await getEmailsFromSearch(
      mockGraph,
      [],
      tenantUrl,
      "",
      0,
      10,
      undefined,
      undefined,
      filters
    );

    const queryCall = vi.mocked(mockGraph.query).mock.calls[0][0];
    const queryTemplate = queryCall.query?.queryTemplate;
    expect(queryTemplate).toContain("RefinableString52 eq true");
    expect(queryTemplate).toContain("RefinableString31:urgent");
  });
});
