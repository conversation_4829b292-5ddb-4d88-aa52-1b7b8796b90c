import * as React from "react";
import { describe, expect, it, vi, beforeEach } from "vitest";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import { act } from "react-dom/test-utils";
import "@testing-library/jest-dom/vitest";
import "@testing-library/jest-dom";
import userEvent from "@testing-library/user-event";
import { SortDirection } from "../shared/utils/DataManagment";
import { Operation } from "../shared/types/Filter";
import Filters, {
  fluentSelectStyles,
} from "../webparts/AtveroMailSpfxDiscovery/components/components/Filters";
import { Project } from "../shared/types/Project";
import { CSSObjectWithLabel } from "react-select";

beforeEach(() => {
  vi.resetAllMocks();
});

const mockProjects: Project[] = [
  { code: "proj1", title: "Project One", id: "1", favorite: false },
  { code: "proj2", title: "Project Two", id: "2", favorite: true },
  { code: "proj3", title: "Another Project", id: "3", favorite: false },
];

const defaultProps = {
  onFilter: vi.fn(),
  changeFilter: vi.fn(),
  removeFilter: vi.fn(),
  onClear: vi.fn(),
  currentColumnCode: "",
  currentSortDirection: SortDirection.Ascending,
  onSortChangeColumn: vi.fn(),
  onSortChangeDirection: vi.fn(),
  filters: [],
  dismissPanel: vi.fn(),
  projects: mockProjects,
  isHubsite: false,
};

describe("Single Filters", () => {
  it("Simple Render", async () => {
    render(<Filters {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Filters")).toBeInTheDocument();
      expect(screen.getByPlaceholderText("Subject")).toBeInTheDocument();
    });
  });

  it("Filter by subject", async () => {
    userEvent.setup();
    const changeFilter = vi.fn();

    await act(async () => {
      render(<Filters {...defaultProps} changeFilter={changeFilter} />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("Subject")).toBeInTheDocument();
      });

      const subjectBox = screen.getByPlaceholderText("Subject");
      const subjectText = "Filter by subject";

      await userEvent.type(subjectBox, subjectText);
      fireEvent.keyDown(subjectBox, { key: "enter", keyCode: 13 });

      await waitFor(() => {
        expect(screen.getByDisplayValue(subjectText)).toBeInTheDocument();
        expect(changeFilter).toHaveBeenCalledWith({
          field: "EmailSubject",
          values: [subjectText],
          application: Operation.Contains,
        });
      });
    });
  });

  it("Filter by sender", async () => {
    userEvent.setup();
    const changeFilter = vi.fn();

    await act(async () => {
      render(<Filters {...defaultProps} changeFilter={changeFilter} />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("Sent by")).toBeInTheDocument();
      });

      const sentByBox = screen.getByPlaceholderText("Sent by");
      const sentByText = "Typed sent by text";

      await userEvent.type(sentByBox, sentByText);
      fireEvent.keyDown(sentByBox, { key: "enter", keyCode: 13 });

      await waitFor(() => {
        expect(screen.getByDisplayValue(sentByText)).toBeInTheDocument();
        expect(changeFilter).toHaveBeenCalledWith({
          field: "EmailFrom",
          values: [sentByText],
          application: Operation.Contains,
        });
      });
    });
  });

  it("Filter by recipients", async () => {
    userEvent.setup();
    const changeFilter = vi.fn();

    await act(async () => {
      render(<Filters {...defaultProps} changeFilter={changeFilter} />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("Recipients")).toBeInTheDocument();
      });

      const toBox = screen.getByPlaceholderText("Recipients");
      const toText = "Filter by recipients typed value";

      await userEvent.type(toBox, toText);
      fireEvent.keyDown(toBox, { key: "enter", keyCode: 13 });

      await waitFor(() => {
        expect(screen.getByDisplayValue(toText)).toBeInTheDocument();
        expect(changeFilter).toHaveBeenCalledWith({
          field: "EmailTo",
          values: [toText],
          application: Operation.Contains,
        });
      });
    });
  });

  it("Filter by tags", async () => {
    userEvent.setup();
    const changeFilter = vi.fn();

    await act(async () => {
      render(<Filters {...defaultProps} changeFilter={changeFilter} />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("Tags")).toBeInTheDocument();
      });

      const tagBox = screen.getByPlaceholderText("Tags");
      const tagText = "Filter by a types tag";

      await userEvent.type(tagBox, tagText);
      fireEvent.keyDown(tagBox, { key: "enter", keyCode: 13 });

      await waitFor(() => {
        expect(screen.getByDisplayValue(tagText)).toBeInTheDocument();
        expect(changeFilter).toHaveBeenCalledWith({
          field: "EmailTags",
          values: [tagText],
          application: Operation.Contains,
        });
      });
    });
  });
});

describe("Apply Filters", () => {
  it("Filter by applied filters", async () => {
    userEvent.setup();
    const applyFilter = vi.fn();

    await act(async () => {
      render(<Filters {...defaultProps} onFilter={applyFilter} />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("Subject")).toBeInTheDocument();
      });

      const subjectBox = screen.getByPlaceholderText("Subject");
      const subjectText = "Filter by subject";
      fireEvent.change(subjectBox, { target: { value: subjectText } });

      const sentByBox = screen.getByPlaceholderText("Sent by");
      const sentByText = "Typed sent by text";
      fireEvent.change(sentByBox, { target: { value: sentByText } });

      const toBox = screen.getByPlaceholderText("Recipients");
      const toText = "Filter by recipients typed value";
      fireEvent.change(toBox, { target: { value: toText } });

      const tagBox = screen.getByPlaceholderText("Tags");
      const tagText = "Filter by a types tag";
      fireEvent.change(tagBox, { target: { value: tagText } });

      const applyButton = screen.getByText("Apply");
      await userEvent.click(applyButton);

      await waitFor(() => {
        expect(screen.getByDisplayValue(subjectText)).toBeInTheDocument();
        expect(screen.getByDisplayValue(tagText)).toBeInTheDocument();
        expect(applyFilter).toHaveBeenCalledWith([
          {
            field: "EmailSubject",
            values: [subjectText],
            application: Operation.Contains,
          },
          {
            field: "EmailFrom",
            values: [sentByText],
            application: Operation.Contains,
          },
          {
            field: "EmailTo",
            values: [toText],
            application: Operation.Contains,
          },
          {
            field: "EmailTags",
            values: [tagText],
            application: Operation.Contains,
          },
        ]);
      });
    });
  });
});

describe("Project Selection", () => {
  interface ProjectOption {
    value: string;
    label: string;
  }

  it("clears selected projects when clear button is clicked", async () => {
    userEvent.setup();

    render(
      <Filters
        {...defaultProps}
        isHubsite={true}
        filters={[
          {
            field: "ProjectCode",
            values: ["proj1"],
            application: Operation.Equals,
          },
        ]}
      />
    );

    const clearButton = screen.getByRole("button", { name: /clear/i });
    await userEvent.click(clearButton);

    await waitFor(() => {
      expect(screen.queryByText("Project One")).not.toBeInTheDocument();
    });
  });

  it("loadOptions filters and formats projects correctly", async () => {
    const testLoadOptions = (inputValue: string): Promise<ProjectOption[]> =>
      Promise.resolve(
        mockProjects
          .filter((project) =>
            project.title.toLowerCase().includes(inputValue.toLowerCase())
          )
          .map((project) => ({
            value: project.code,
            label: project.title,
          }))
      );

    const cases = [
      {
        input: "one",
        expected: [{ value: "proj1", label: "Project One" }],
      },
      {
        input: "two",
        expected: [{ value: "proj2", label: "Project Two" }],
      },
      {
        input: "project",
        expected: [
          { value: "proj1", label: "Project One" },
          { value: "proj2", label: "Project Two" },
          { value: "proj3", label: "Another Project" },
        ],
      },
      {
        input: "ANOTHER",
        expected: [{ value: "proj3", label: "Another Project" }],
      },
      {
        input: "nonexistent",
        expected: [],
      },
    ];

    for (const testCase of cases) {
      const result = await testLoadOptions(testCase.input);
      expect(result).toEqual(testCase.expected);
    }
  });

  it("should apply correct styles to menu, menuList, and option components", () => {
    const baseStyles: CSSObjectWithLabel = {};
    const baseProps = {
      selectProps: {},
      theme: {},
    };

    // @ts-expect-error - Minimal props needed for style testing
    expect(fluentSelectStyles.menu?.(baseStyles, baseProps)).toMatchObject({
      zIndex: 1000,
      boxShadow: "0 6.4px 14.4px rgba(0, 0, 0, 0.132)",
      borderRadius: "2px",
    });

    // @ts-expect-error - Minimal props needed for style testing
    expect(fluentSelectStyles.menuList?.(baseStyles, baseProps)).toMatchObject({
      padding: "4px",
    });

    const getOptionStyles = (overrides = {}): CSSObjectWithLabel | undefined =>
      // @ts-expect-error - Minimal props needed for style testing
      fluentSelectStyles.option?.(baseStyles, {
        ...baseProps,
        isDisabled: false,
        isFocused: false,
        isSelected: false,
        ...overrides,
      });

    expect(getOptionStyles({ isSelected: true })).toMatchObject({
      backgroundColor: "#0078d4",
      color: "white",
    });

    expect(getOptionStyles({ isFocused: true })).toMatchObject({
      backgroundColor: "#f3f2f1",
      color: "#323130",
    });

    expect(getOptionStyles()).toMatchObject({
      backgroundColor: "white",
      color: "#323130",
    });
  });
});
