import { describe, it, expect, vi, beforeEach } from "vitest";
import { EmailsAdapter } from "../adapters/EmailsFromSearchAdapter";
import { GraphFI } from "@pnp/graph";
import { SPFI } from "@pnp/sp";
import { SortDirection } from "../shared/utils/DataManagment";
import { Filter, Operation } from "../shared/types/Filter";
import * as EmailUtils from "../shared/utils/EmailUtils";
import * as graphApi from "../shared/graph/getEmailsFromSearch";
import { Email, EmailResponse } from "../shared/types/Email";

describe("EmailsAdapter", () => {
  let adapter: EmailsAdapter;
  let mockGraph: GraphFI;
  let mockSharepoint: SPFI;
  const mockRelativeURL = "/sites/AtveroMail";
  const mockHubsiteID = "521540af-aecb-4883-9551-9ae1bbdbac98";

  const mockEmail: Email = {
    id: "1",
    driveID: "drive1",
    fileRef: "ref1",
    subject: "Test Email",
    sentBy: "<EMAIL>",
    dateFiled: "2024-01-01",
    emailTextSummary: "Summary",
    tag: "Important",
    projectCode: "TEST-1",
    emailImportant: "Yes",
    emailConfidential: true,
    attachmentCount: "1",
    driveItemID: undefined,
  };

  const createMockEmailResponse = (
    emails: Email[],
    page: number = 0,
    moreResults: boolean = false
  ): EmailResponse => ({
    emails,
    page,
    pageSize: 20,
    nextLink: moreResults ? "next-page-token" : undefined,
    sortField: "RefinableDate00",
    sortDirection: SortDirection.Descending,
    moreResultsAvailable: moreResults,
    total: emails.length,
    shareRootUrl: "test.sharepoint.com",
  });

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup mock Graph API
    mockGraph = {
      sites: {
        getByUrl: vi
          .fn()
          .mockReturnValue(() => Promise.resolve({ id: "site1" })),
        getById: vi.fn().mockReturnValue({
          drives: vi
            .fn()
            .mockResolvedValue([{ id: "drive1", name: "Filed Email Content" }]),
        }),
      },
    } as unknown as GraphFI;

    // Setup mock Sharepoint
    mockSharepoint = {
      web: {},
      site: {
        rootWeb: vi
          .fn()
          .mockResolvedValue({ Url: "https://test.sharepoint.com" }),
      },
    } as unknown as SPFI;

    adapter = new EmailsAdapter(
      mockGraph,
      mockSharepoint,
      mockRelativeURL,
      mockHubsiteID
    );
  });

  it("should handle search term case correctly", async () => {
    // Mock getEmailsFromSearch with proper EmailResponse
    const mockSearchResponse = createMockEmailResponse([mockEmail]);

    vi.spyOn(graphApi, "getEmailsFromSearch").mockResolvedValue(
      mockSearchResponse
    );

    const result = await adapter.fetchEmails(
      ["TEST-PROJECT"],
      "test.sharepoint.com",
      "search term",
      0,
      20,
      undefined,
      "RefinableDate00",
      "dateFiled",
      SortDirection.Descending,
      [],
      false
    );

    expect(graphApi.getEmailsFromSearch).toHaveBeenCalledWith(
      mockGraph,
      ["TEST-PROJECT"],
      "https://test.sharepoint.com",
      "search term",
      0,
      20,
      "RefinableDate00",
      SortDirection.Descending,
      [],
      mockHubsiteID
    );
    expect(result).toEqual(mockSearchResponse);
  });

  it("should handle filtered results for single project without search term", async () => {
    // Mock handleFilter with proper EmailResponse
    const mockFilterResponse = createMockEmailResponse([mockEmail]);

    vi.spyOn(EmailUtils, "handleFilter").mockResolvedValue(mockFilterResponse);

    const filters: Filter[] = [
      {
        field: "EmailConfidential",
        values: ["true"],
        application: Operation.Equals,
      },
    ];

    const result = await adapter.fetchEmails(
      ["TEST-PROJECT"],
      "test.sharepoint.com",
      "",
      0,
      20,
      undefined,
      "RefinableDate00",
      "dateFiled",
      SortDirection.Descending,
      filters,
      false
    );

    expect(EmailUtils.handleFilter).toHaveBeenCalledWith(
      filters,
      "dateFiled",
      SortDirection.Descending,
      expect.any(Function),
      mockGraph,
      "TEST-PROJECT",
      undefined,
      0,
      20,
      false,
      "test.sharepoint.com",
      true
    );

    // Check that the response includes all required fields
    expect(result).toMatchObject({
      emails: [
        expect.objectContaining({
          driveID: "drive1",
          projectCode: "TEST-PROJECT",
        }),
      ],
      page: 0,
      pageSize: 20,
      sortField: "RefinableDate00",
      sortDirection: SortDirection.Descending,
      moreResultsAvailable: false,
    });
  });

  it("should handle pagination correctly", async () => {
    // Mock first page response
    const firstPageResponse = createMockEmailResponse([mockEmail], 0, true);
    const secondPageEmail = { ...mockEmail, id: "2" };
    const secondPageResponse = createMockEmailResponse(
      [secondPageEmail],
      1,
      false
    );

    vi.spyOn(EmailUtils, "handleFilter")
      .mockResolvedValueOnce(firstPageResponse)
      .mockResolvedValueOnce(secondPageResponse);

    // Fetch first page
    const firstResult = await adapter.fetchEmails(
      ["TEST-PROJECT"],
      "test.sharepoint.com",
      "",
      0,
      20,
      undefined,
      "RefinableDate00",
      "dateFiled",
      SortDirection.Descending,
      [],
      false
    );

    expect(firstResult?.nextLink).toBe("next-page-token");
    expect(firstResult?.moreResultsAvailable).toBe(true);

    // Fetch second page
    const secondResult = await adapter.fetchEmails(
      ["TEST-PROJECT"],
      "test.sharepoint.com",
      "",
      1,
      20,
      "next-page-token",
      "RefinableDate00",
      "dateFiled",
      SortDirection.Descending,
      [],
      false
    );

    expect(secondResult?.nextLink).toBeUndefined();
    expect(secondResult?.moreResultsAvailable).toBe(false);
  });

  it("should return undefined when graph is not initialized", async () => {
    adapter = new EmailsAdapter(
      undefined as unknown as GraphFI,
      mockSharepoint,
      mockRelativeURL,
      mockHubsiteID
    );

    const result = await adapter.fetchEmails(
      ["TEST-PROJECT"],
      "test.sharepoint.com",
      "",
      0,
      20,
      undefined,
      "RefinableDate00",
      "dateFiled",
      SortDirection.Descending,
      [],
      false
    );

    expect(result).toBeUndefined();
  });

  it("should handle errors gracefully", async () => {
    // Mock a failure in graph API
    mockGraph.sites.getByUrl = vi.fn().mockImplementation(() => {
      throw new Error("API Error");
    });

    const result = await adapter.fetchEmails(
      ["TEST-PROJECT"],
      "test.sharepoint.com",
      "",
      0,
      20,
      undefined,
      "RefinableDate00",
      "dateFiled",
      SortDirection.Descending,
      [],
      false
    );

    expect(result).toBeUndefined();
  });
});
