import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import { Preview } from "../webparts/AtveroMailSpfxDiscovery/components/components/Preview";
import "@testing-library/jest-dom";
import { Email } from "../shared/types/Email";
import { IForEmailPreview } from "../adapters/IForEmailPreview";
import type { Mock } from "vitest";
import { IButtonProps, IPanelProps, IStackProps } from "@fluentui/react";

interface FluentUIModule {
  Panel: React.FC<IPanelProps>;
  PanelType: { large: "large" };
  DefaultButton: React.FC<IButtonProps>;
  Stack: React.FC<IStackProps>;
  mergeStyleSets: (styles: Record<string, unknown>) => Record<string, unknown>;
}

vi.mock("@fluentui/react", async (importOriginal) => {
  const actual = (await importOriginal()) as FluentUIModule;
  return {
    ...actual,
    Panel: ({
      children,
      headerText,
    }: {
      children: React.ReactNode;
      headerText?: string;
    }) => (
      <div data-testid="mock-panel">
        {headerText && <div>{headerText}</div>}
        {children}
      </div>
    ),
    PanelType: { large: "large" },
    DefaultButton: ({
      children,
      onClick,
    }: {
      children: React.ReactNode;
      onClick?: () => void;
    }) => (
      <button onClick={onClick} data-testid="mock-button">
        {children}
      </button>
    ),
    Stack: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="mock-stack">{children}</div>
    ),
    mergeStyleSets: (styles: Record<string, unknown>) => styles,
  };
});

vi.mock("@fluentui/react-hooks", async (importOriginal) => {
  const actual = (await importOriginal()) as FluentUIModule;
  return {
    ...actual,
    useBoolean: () => [false, { setTrue: vi.fn(), setFalse: vi.fn() }],
  };
});

vi.mock(
  "../webparts/AtveroMailSpfxDiscovery/components/components/Attachments",
  () => ({
    default: () => (
      <div data-testid="mock-attachments">Attachments Component</div>
    ),
  })
);

vi.mock("../shared/utils/AttachmentHelpers", () => ({
  handleAttachments: vi.fn().mockResolvedValue({ attachments: [] }),
}));

vi.mock("../shared/utils/DownloadEmails", () => ({
  handleDownloadEmail: vi.fn(),
}));

describe("Preview Component", () => {
  let mockEmail: Email;
  let mockPreviewAdapter: IForEmailPreview;
  let mockDismissPanel: () => void;

  beforeEach(() => {
    mockEmail = {
      projectCode: "TEST123",
      id: "email-123",
      subject: "Test Email",
      sentBy: "<EMAIL>",
      dateFiled: "2024-03-21",
      driveID: "test-drive",
      attachmentCount: "0",
      emailConfidential: false,
    } as Email;

    mockPreviewAdapter = {
      getSelectedEmailWithDriveItemID: vi.fn(),
      getPreviewUrl: vi.fn(),
      readFileContent: vi.fn(),
      getPhoto: vi.fn(),
    } as unknown as IForEmailPreview;

    mockDismissPanel = vi.fn();

    vi.spyOn(console, "error").mockImplementation(() => {});
  });

  describe("getPreview function", () => {
    it("should fetch preview URL directly when driveItemID exists", async () => {
      const emailWithDriveID = {
        ...mockEmail,
        driveItemID: "existing-drive-id",
      };

      (mockPreviewAdapter.getPreviewUrl as Mock).mockResolvedValue(
        "https://test.com/preview"
      );

      render(
        <Preview
          email={emailWithDriveID}
          previewAdapter={mockPreviewAdapter}
          dismissPanel={mockDismissPanel}
        />
      );

      await waitFor(() => {
        expect(
          mockPreviewAdapter.getSelectedEmailWithDriveItemID
        ).not.toHaveBeenCalled();
        expect(mockPreviewAdapter.getPreviewUrl).toHaveBeenCalledWith(
          emailWithDriveID
        );
      });

      const iframe = await screen.findByTitle("email-preview");
      expect(iframe).toHaveAttribute("src", "https://test.com/preview");
    });

    it("should fetch driveItemID first when it does not exist", async () => {
      const emailWithoutDriveID = {
        ...mockEmail,
        driveItemID: undefined,
      };

      const emailWithDriveID = {
        ...emailWithoutDriveID,
        driveItemID: "fetched-drive-id",
      };

      (
        mockPreviewAdapter.getSelectedEmailWithDriveItemID as Mock
      ).mockResolvedValue(emailWithDriveID);
      (mockPreviewAdapter.getPreviewUrl as Mock).mockResolvedValue(
        "https://test.com/preview"
      );

      render(
        <Preview
          email={emailWithoutDriveID}
          previewAdapter={mockPreviewAdapter}
          dismissPanel={mockDismissPanel}
        />
      );

      await waitFor(() => {
        expect(
          mockPreviewAdapter.getSelectedEmailWithDriveItemID
        ).toHaveBeenCalledWith(emailWithoutDriveID);
        expect(mockPreviewAdapter.getPreviewUrl).toHaveBeenCalledWith(
          emailWithDriveID
        );
      });

      const iframe = await screen.findByTitle("email-preview");
      expect(iframe).toHaveAttribute("src", "https://test.com/preview");
    });

    it("should handle undefined preview URL", async () => {
      const emailWithDriveID = {
        ...mockEmail,
        driveItemID: "existing-drive-id",
      };

      (mockPreviewAdapter.getPreviewUrl as Mock).mockResolvedValue(undefined);

      render(
        <Preview
          email={emailWithDriveID}
          previewAdapter={mockPreviewAdapter}
          dismissPanel={mockDismissPanel}
        />
      );

      await waitFor(() => {
        expect(screen.getByText("Loading Email")).toBeInTheDocument();
      });
    });

    it("should handle error when fetching driveItemID", async () => {
      const emailWithoutDriveID = {
        ...mockEmail,
        driveItemID: undefined,
      };

      (
        mockPreviewAdapter.getSelectedEmailWithDriveItemID as Mock
      ).mockRejectedValue(new Error("Failed to fetch"));

      render(
        <Preview
          email={emailWithoutDriveID}
          previewAdapter={mockPreviewAdapter}
          dismissPanel={mockDismissPanel}
        />
      );

      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith(
          "Error fetching preview URL:",
          expect.any(Error)
        );
        expect(screen.getByText("Loading Email")).toBeInTheDocument();
      });
    });

    it("should handle error when fetching preview URL", async () => {
      const emailWithDriveID = {
        ...mockEmail,
        driveItemID: "existing-drive-id",
      };

      (mockPreviewAdapter.getPreviewUrl as Mock).mockRejectedValue(
        new Error("Failed to fetch preview")
      );

      render(
        <Preview
          email={emailWithDriveID}
          previewAdapter={mockPreviewAdapter}
          dismissPanel={mockDismissPanel}
        />
      );

      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith(
          "Error fetching preview URL:",
          expect.any(Error)
        );
        expect(screen.getByText("Loading Email")).toBeInTheDocument();
      });
    });
  });
});
