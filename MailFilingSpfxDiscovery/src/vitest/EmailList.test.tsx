import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { EmailList } from "../webparts/AtveroMailSpfxDiscovery/components/components/EmailList";
import * as FormatDate from "../shared/utils/FormatDate";

vi.mock("../shared/utils/useRecipient", () => ({
  getNameAndEmail: vi.fn().mockReturnValue(["Test User", "<EMAIL>"]),
}));

vi.mock("../shared/utils/FormatDate", () => ({
  formatDate: vi.fn().mockReturnValue("Jan 1, 2024"),
}));

vi.mock("@fluentui/react", () => ({
  Persona: ({ text, size }) => (
    <div data-testid="mock-persona" data-size={size}>
      {text}
    </div>
  ),
  PersonaSize: {
    size32: 32,
  },
}));

vi.mock("@fluentui/react-icons", () => ({
  Attach20Regular: () => <div data-testid="attachment-icon">Attachment</div>,
  Important20Regular: () => <div data-testid="important-icon">Important</div>,
  LockClosed20Regular: () => (
    <div data-testid="confidential-icon">Confidential</div>
  ),
}));

vi.mock(
  "../webparts/AtveroMailSpfxDiscovery/components/components/TagBadge",
  () => ({
    TagBadge: ({ tagName }: { tagName: string }) => (
      <div data-testid="tag-badge">{tagName}</div>
    ),
  })
);

const mockStyles = {
  stackedEmailView: "stackedEmailView",
  emailAvatar: "emailAvatar",
  emailContent: "emailContent",
  contentRow: "contentRow",
  flex: "flex",
  emailSenderName: "emailSenderName",
  rhsContent: "rhsContent",
  tagsProjectContainer: "tagsProjectContainer",
  confidentialIcon: "confidentialIcon",
  importantIcon: "importantIcon",
  attachmentIcon: "attachmentIcon",
  projectCode: "projectCode",
  emailReceived: "emailReceived",
  emailSubject: "emailSubject",
  emailTextSummary: "emailTextSummary",
};

describe("EmailList", () => {
  const mockEmail = {
    id: "1",
    driveID: "drive1",
    fileRef: "ref1",
    sentBy: "<EMAIL>",
    subject: "Test Subject",
    emailTextSummary: "Test Summary",
    tag: "Important",
    projectCode: "PRJ-001",
    emailImportant: "Yes",
    emailConfidential: true,
    attachmentCount: "2",
    dateFiled: "2024-01-01",
    driveItemID: undefined,
  };

  const mockAvatars = new Map([
    ["<EMAIL>", { name: "Test User", image: "test-image.jpg" }],
  ]);

  describe("getAvatar", () => {
    it("renders Persona with correct props", () => {
      render(EmailList.getAvatar(mockEmail, mockAvatars));

      const persona = screen.getByTestId("mock-persona");
      expect(persona).toBeDefined();
      expect(persona.textContent).toBe("Test User");
    });
  });

  describe("renderItemColumn", () => {
    const renderItem = EmailList.renderItemColumn(mockStyles, mockAvatars);

    beforeEach(() => {
      render(renderItem(mockEmail, 0));
    });

    it("renders sender name", () => {
      expect(screen.getByText("<EMAIL>")).toBeDefined();
    });

    it("renders subject", () => {
      expect(screen.getByText("Test Subject")).toBeDefined();
    });

    it("renders email summary", () => {
      expect(screen.getByText("Test Summary")).toBeDefined();
    });

    it("renders project code", () => {
      expect(screen.getByText("PRJ-001")).toBeDefined();
    });

    it("renders confidential icon when email is confidential", () => {
      expect(screen.getByTestId("confidential-icon")).toBeDefined();
    });

    it("renders important icon when email is important", () => {
      expect(screen.getByTestId("important-icon")).toBeDefined();
    });

    it("renders attachment icon when email has attachments", () => {
      expect(screen.getByTestId("attachment-icon")).toBeDefined();
    });

    it("renders formatted date", () => {
      expect(FormatDate.formatDate).toHaveBeenCalledWith("2024-01-01");
      expect(screen.getByText("Jan 1, 2024")).toBeDefined();
    });
  });

  describe("renderItemColumn with no tag", () => {
    it('passes "No tag" to TagBadge when email has no tag', () => {
      const emailWithoutTag = { ...mockEmail, tag: "" };
      const renderItem = EmailList.renderItemColumn(mockStyles, mockAvatars);

      render(renderItem(emailWithoutTag, 0));
      const tagBadge = screen.getByTestId("tag-badge");
      expect(tagBadge).toBeDefined();
    });
  });

  describe("getRowStyles", () => {
    it("returns correct style object", () => {
      const styles = EmailList.getRowStyles();

      expect(styles.check).toEqual({
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: 0,
        height: "100%",
      });

      expect(styles.root).toEqual({
        display: "flex",
        alignItems: "center",
      });
    });
  });
});
