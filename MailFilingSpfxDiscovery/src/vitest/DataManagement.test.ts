import { describe, it, expect, vi } from "vitest";
import {
  fetchShareRootURL,
  shouldAppendEmails,
  SortDirection,
  parseShareRootUrl,
  parseProjectFromRelativeUrl,
  fetchHubsites,
  fetchProjects,
} from "../shared/utils/DataManagment";
import { EmailResponse } from "../shared/types/Email";
import { Site } from "@microsoft/microsoft-graph-types";
import { GraphFI } from "@pnp/graph";

import * as GetSharePointHelpers from "../shared/graph/getSharePointHelpers";
import * as HubsiteModule from "../shared/graph/getHubsite";
import * as ProjectListModule from "../shared/graph/getProjectList";
import * as GraphHelperModule from "../shared/utils/GraphHelpers";
import { Project } from "../shared/types/Project";

vi.mock("../shared/graph/getSharePointHelpers", () => ({
  getSharePointRootUrl: vi.fn(),
}));

vi.mock("../shared/graph/getHubsite", () => ({
  getHubsite: vi.fn(),
}));

vi.mock("../shared/graph/getProjectList", () => ({
  getProjectList: vi.fn(),
}));

vi.mock("../shared/utils/GraphHelpers", () => ({
  getSiteGuidFromGraphGuid: vi.fn(),
}));

describe("DataManagment utils", () => {
  describe("shouldAppendEmails", () => {
    const baseResponse: EmailResponse = {
      emails: [],
      sortField: "subject",
      sortDirection: SortDirection.Ascending,
      page: 1,
      pageSize: 10,
      nextLink: undefined,
    };

    it("returns true when column and direction match", () => {
      expect(
        shouldAppendEmails("subject", SortDirection.Ascending, baseResponse)
      ).toBe(true);
    });

    it("returns false when column does not match", () => {
      expect(
        shouldAppendEmails("date", SortDirection.Ascending, baseResponse)
      ).toBe(false);
    });

    it("returns false when direction does not match", () => {
      expect(
        shouldAppendEmails("subject", SortDirection.Descending, baseResponse)
      ).toBe(false);
    });
  });

  describe("fetchShareRootURL", () => {
    it("returns empty string on error", async () => {
      vi.mocked(
        GetSharePointHelpers.getSharePointRootUrl
      ).mockRejectedValueOnce(new Error("boom"));
      const mockGraph = {} as GraphFI;
      const result = await fetchShareRootURL(mockGraph);
      expect(result).toBe("");
    });

    it("returns url when successful", async () => {
      vi.mocked(
        GetSharePointHelpers.getSharePointRootUrl
      ).mockResolvedValueOnce("https://example.sharepoint.com");
      const mockGraph = {} as GraphFI;
      const result = await fetchShareRootURL(mockGraph);
      expect(result).toBe("https://example.sharepoint.com");
    });
  });

  describe("parseShareRootUrl", () => {
    it("extracts hostname from valid URL", () => {
      const result = parseShareRootUrl(
        "https://atverodevs.sharepoint.com/sites/Mail2"
      );
      expect(result).toBe("atverodevs.sharepoint.com");
    });

    it("returns empty string on invalid URL", () => {
      const result = parseShareRootUrl("not-a-valid-url");
      expect(result).toBe("");
    });
  });

  describe("parseProjectFromRelativeUrl", () => {
    it("extracts project name from relative URL", () => {
      const result = parseProjectFromRelativeUrl("/sites/Mail2");
      expect(result).toBe("Mail2");
    });

    it("returns empty string on invalid format", () => {
      const result = parseProjectFromRelativeUrl("/badformat");
      expect(result).toBe("");
    });
  });

  describe("fetchHubsites", () => {
    const graph = {} as GraphFI;
    const hub: Site = { id: "123", name: "Hub" };

    it("returns hubsite when found", async () => {
      vi.mocked(HubsiteModule.getHubsite).mockResolvedValueOnce(hub);
      const result = await fetchHubsites(graph, "url");
      expect(result).toEqual(hub);
    });

    it("throws when no hubsite found", async () => {
      // @ts-expect-error: mocking `getHubsite` to return undefined for test case
      vi.mocked(HubsiteModule.getHubsite).mockResolvedValueOnce(undefined);
      await expect(fetchHubsites(graph, "url")).rejects.toThrow(
        "Failed to fetch hubsite"
      );
    });
  });

  describe("fetchProjects", () => {
    const graph = {} as GraphFI;
    const hubsite: Site = { id: "site-id" };

    it("returns project list", async () => {
      const projects = [{ id: "1", title: "Test Project" } as Project];
      vi.mocked(GraphHelperModule.getSiteGuidFromGraphGuid).mockReturnValue(
        "guid"
      );
      vi.mocked(ProjectListModule.getProjectList).mockResolvedValueOnce(
        projects
      );

      const result = await fetchProjects(graph, hubsite, "term");
      expect(result).toEqual(projects);
    });

    it("returns empty array on error", async () => {
      vi.mocked(GraphHelperModule.getSiteGuidFromGraphGuid).mockReturnValue(
        "guid"
      );
      vi.mocked(ProjectListModule.getProjectList).mockRejectedValueOnce(
        new Error("fail")
      );
      const result = await fetchProjects(graph, hubsite, "term");
      expect(result).toEqual([]);
    });
  });
});
