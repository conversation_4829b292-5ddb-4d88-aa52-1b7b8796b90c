import { IForEmailPreview } from "../../../adapters/IForEmailPreview";
import { IForEmailsFromSearch } from "../../../adapters/IForEmailsFromSearch";
import { IProjectsList } from "../../../adapters/IProjectsList";
import { IBackendAdapter } from "../../../adapters/IBackendAdapter";

export interface IAtveroMailSpfxDiscoveryProps {
  dataAdapter: IForEmailsFromSearch;
  previewAdapter: IForEmailPreview;
  relativeUrl: string;
  projectListAdapter: IProjectsList;
  isHubsite: boolean;
  absoluteUrl: string;
  backendAdapter: IBackendAdapter;
}
