import React, { useEffect, useState } from "react";
import { IPreviewProps } from "./IPreviewProps";
import { Panel, PanelType, DefaultButton, Stack } from "@fluentui/react";
import { useBoolean } from "@fluentui/react-hooks";
import Attachments from "./Attachments";
import { AttachmentResult } from "../../../../shared/types/Attachments";
import { handleAttachments } from "../../../../shared/utils/AttachmentHelpers";
import { handleDownloadEmail } from "../../../../shared/utils/DownloadEmails";

export const Preview: React.FC<IPreviewProps> = ({
  email,
  previewAdapter,
  dismissPanel,
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(undefined);
  const [
    isAttachmentsOpen,
    { setTrue: openAttachments, setFalse: closeAttachments },
  ] = useBoolean(false);
  const [attachmentCount, setAttachmentCount] = useState<number>(0);

  useEffect(() => {
    const getPreview = async (): Promise<void> => {
      try {
        // If email doesn't have driveItemID, fetch it first
        const emailWithDriveID = email.driveItemID
          ? email
          : await previewAdapter.getSelectedEmailWithDriveItemID(email);

        const preview = await previewAdapter.getPreviewUrl(emailWithDriveID);
        setPreviewUrl(preview ?? undefined);
      } catch (error) {
        console.error("Error fetching preview URL:", error);
        setPreviewUrl(undefined);
      }
    };

    const fetchAttachments = async (): Promise<void> => {
      try {
        const result: AttachmentResult | undefined = await handleAttachments(
          email,
          previewAdapter
        );
        if (result) {
          setAttachmentCount(result.attachments.length);
        }
      } catch (error) {
        console.error("Error fetching attachments:", error);
        setAttachmentCount(0);
      }
    };

    void getPreview();
    void fetchAttachments();
  }, [previewAdapter, email]);

  const handleDownload = (): void => {
    handleDownloadEmail(email);
  };

  const attachmentButton = (): JSX.Element | null => {
    if (attachmentCount > 0) {
      return (
        <DefaultButton
          iconProps={{ iconName: "Attach" }}
          onClick={openAttachments}
        >
          Attachments ({attachmentCount})
        </DefaultButton>
      );
    } else {
      return null;
    }
  };

  return (
    <>
      <Panel
        onDismiss={dismissPanel}
        type={PanelType.large}
        headerText={email.subject}
        isLightDismiss={!isAttachmentsOpen}
        isOpen={true}
        onOuterClick={!isAttachmentsOpen ? undefined : () => undefined}
      >
        <Stack
          horizontal
          horizontalAlign="end"
          verticalAlign="center"
          styles={{
            root: { paddingTop: "10px", paddingBottom: "10px", gap: "8px" },
          }}
        >
          {attachmentButton()}
          <DefaultButton
            iconProps={{ iconName: "Download" }}
            onClick={handleDownload}
          >
            Download Email
          </DefaultButton>
        </Stack>

        {!previewUrl ? (
          <div>Loading Email</div>
        ) : (
          <div style={{ height: "800px" }}>
            <iframe
              title="email-preview"
              src={previewUrl}
              style={{
                width: "100%",
                height: "100%",
                border: "none",
              }}
            />
          </div>
        )}
      </Panel>

      {isAttachmentsOpen && (
        <Attachments
          previewAdapter={previewAdapter}
          email={email}
          dismissPanel={closeAttachments}
        />
      )}
    </>
  );
};

export default Preview;
