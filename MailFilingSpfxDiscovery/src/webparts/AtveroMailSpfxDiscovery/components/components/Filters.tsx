import React, { useState } from "react";
import {
  De<PERSON>ultButton,
  PrimaryButton,
  SearchBox,
  Checkbox,
  Panel,
  PanelType,
  ChoiceGroup,
  IChoiceGroupOption,
  mergeStyleSets,
  DatePicker,
  DayOfWeek,
} from "@fluentui/react";
import { Filter, Operation } from "../../../../shared/types/Filter";
import { SortDirection } from "../../../../shared/utils/DataManagment";
import { IFiltersProps } from "./IFiltersProps";
import AsyncSelect from "react-select/async";
import { ActionMeta, StylesConfig } from "react-select";

interface ProjectOption {
  value: string;
  label: string;
}

const useStyles = mergeStyleSets({
  outerContainer: {
    margin: "8px",
  },
  container: {
    minWidth: "250px",
    padding: "0px",
    display: "flex",
    flexDirection: "column",
    gap: "10px",
    margin: "0px",
  },
  checkboxContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
    margin: "0px",
    padding: "0px",
  },
  sortContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "5px",
  },
  sortTitle: {
    fontWeight: "bold",
  },
  buttonContainer: {
    display: "flex",
    justifyContent: "space-between",
    marginTop: "10px",
  },
  divider: {
    border: "1px solid #ccc",
    margin: "10px 0",
  },
  label: {
    fontWeight: "600",
    marginBottom: "5px",
    display: "block",
  },
  projectSelectContainer: {
    display: "block",
  },
  inputContainer: {
    display: "block",
  },
});

export const fluentSelectStyles: StylesConfig<ProjectOption, true> = {
  control: (baseStyles, state) => ({
    ...baseStyles,
    maxWidth: "300px",
    minHeight: "32px",
    backgroundColor: "white",
    borderColor: state.isFocused ? "#323130" : "#605e5c",
    borderRadius: "2px",
    boxShadow: "none",
    "&:hover": {
      borderColor: "#323130",
    },
  }),
  menu: (baseStyles) => ({
    ...baseStyles,
    zIndex: 1000,
    boxShadow: "0 6.4px 14.4px rgba(0, 0, 0, 0.132)",
    borderRadius: "2px",
  }),
  menuList: (baseStyles) => ({
    ...baseStyles,
    padding: "4px",
  }),
  option: (baseStyles, state) => ({
    ...baseStyles,
    padding: "8px",
    backgroundColor: state.isSelected
      ? "#0078d4"
      : state.isFocused
        ? "#f3f2f1"
        : "white",
    color: state.isSelected ? "white" : "#323130",
    cursor: "pointer",
    ":active": {
      backgroundColor: "#0078d4",
      color: "white",
    },
  }),
  multiValue: (baseStyles) => ({
    ...baseStyles,
    backgroundColor: "#f3f2f1",
    borderRadius: "2px",
    margin: "2px",
  }),
  multiValueLabel: (baseStyles) => ({
    ...baseStyles,
    color: "#323130",
    padding: "2px 6px",
  }),
  multiValueRemove: (baseStyles) => ({
    ...baseStyles,
    borderRadius: "0 2px 2px 0",
    paddingLeft: "4px",
    paddingRight: "4px",
    ":hover": {
      backgroundColor: "#e1dfdd",
      color: "#323130",
    },
  }),
  placeholder: (baseStyles) => ({
    ...baseStyles,
    color: "#605e5c",
  }),
  input: (baseStyles) => ({
    ...baseStyles,
    color: "#323130",
    margin: "0",
    padding: "0",
  }),
  valueContainer: (baseStyles) => ({
    ...baseStyles,
    padding: "0 8px",
    maxHeight: "150px",
    overflow: "auto",
    "&::-webkit-scrollbar": {
      width: "6px",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: "#c8c8c8",
      borderRadius: "3px",
    },
    "&::-webkit-scrollbar-track": {
      backgroundColor: "#f4f4f4",
    },
  }),
  dropdownIndicator: (baseStyles) => ({
    ...baseStyles,
    padding: "6px",
    ":hover": {
      color: "#323130",
    },
  }),
  clearIndicator: (baseStyles) => ({
    ...baseStyles,
    padding: "6px",
    ":hover": {
      color: "#323130",
    },
  }),
  indicatorSeparator: (baseStyles) => ({
    ...baseStyles,
    backgroundColor: "#605e5c",
  }),
};

const getFilterFromFilters = (filters: Filter[], filter: string): string => {
  const matching = filters.find((f) => f.field === filter);

  if (matching && matching.values.length > 0) {
    return matching.values[0];
  } else return "";
};

const Filters: React.FC<IFiltersProps> = ({
  onFilter,
  changeFilter,
  removeFilter,
  onClear,
  currentColumnCode,
  currentSortDirection,
  onSortChangeColumn,
  onSortChangeDirection,
  filters,
  dismissPanel,
  projects,
  isHubsite,
}) => {
  const styles = useStyles;
  const [subject, setSubject] = useState(
    getFilterFromFilters(filters, "EmailSubject")
  );
  const [from, setFrom] = useState(getFilterFromFilters(filters, "EmailFrom"));
  const [recipients, setRecipients] = useState(
    getFilterFromFilters(filters, "EmailTo")
  );
  const [startDate, setStartDate] = useState<Date | null | undefined>(null);
  const [endDate, setEndDate] = useState<Date | null | undefined>(null);
  const [hasAttachments, setHasAttachments] = useState(
    getFilterFromFilters(filters, "AttachmentCount") === "0"
  );
  const [tags, setTags] = useState(getFilterFromFilters(filters, "EmailTags"));
  const [isImportant, setIsImportant] = useState(
    getFilterFromFilters(filters, "EmailImportant") === "Yes"
  );
  const [isConfidential, setIsConfidential] = useState(
    getFilterFromFilters(filters, "EmailConfidential") === "true"
  );
  const [selectedProjects, setSelectedProjects] = useState<string[]>(
    filters.find((f) => f.field === "ProjectCode")?.values || []
  );

  // Sorting state synced with props
  const [selectedSortColumn, setSelectedSortColumn] =
    useState(currentColumnCode);
  const [selectedSortDirection, setSelectedSortDirection] =
    useState(currentSortDirection);

  const applyFilters = (): void => {
    const filters: Filter[] = [];
    // This is added as a filter, so when the filters are cleared, the
    // selection of filters are cleared too
    if (selectedProjects.length > 0 && isHubsite) {
      filters.push({
        field: "ProjectCode",
        values: selectedProjects,
        application: Operation.Equals,
      });
    }

    if (subject)
      filters.push({
        field: "EmailSubject",
        values: [subject],
        application: Operation.Contains,
      });

    if (from)
      filters.push({
        field: "EmailFrom",
        values: [from],
        application: Operation.Contains,
      });

    if (recipients)
      filters.push({
        field: "EmailTo",
        values: [recipients],
        application: Operation.Contains,
      });

    if (tags) {
      filters.push({
        field: "EmailTags",
        values: [tags],
        application: Operation.Contains,
      });
    }

    if (startDate)
      filters.push({
        field: "EmailReceived",
        values: [startDate?.toISOString() ?? ""],
        application: Operation.GreaterThanDateTime,
      });

    if (endDate)
      filters.push({
        field: "EmailReceived",
        values: [endDate?.toISOString() ?? ""],
        application: Operation.LessThanDateTime,
      });

    if (hasAttachments) {
      filters.push({
        field: "AttachmentCount",
        values: ["0"],
        application: Operation.GreaterThan,
      });
    }

    if (isImportant) {
      filters.push({
        field: "EmailImportant",
        values: ["Yes"],
        application: Operation.Equals,
      });
    }

    if (isConfidential) {
      filters.push({
        field: "EmailConfidential",
        values: ["true"],
        application: Operation.Equals,
      });
    }

    onFilter(filters);
  };

  const clearFilters = (): void => {
    removeFilter("ProjectCode");
    setSelectedProjects([]);
    setSubject("");
    setFrom("");
    setRecipients("");
    setStartDate(null);
    setEndDate(null);
    setTags("");
    setHasAttachments(false);
    setIsImportant(false);
    setIsConfidential(false);
    onClear();
  };

  const handleSortColumnChange = (
    ev?: React.FormEvent<HTMLElement | HTMLInputElement>,
    option?: IChoiceGroupOption
  ): void => {
    const selectedColumn = option?.key ?? "";
    setSelectedSortColumn(selectedColumn);
    onSortChangeColumn(selectedColumn);
  };

  const handleSortDirectionChange = (
    ev?: React.FormEvent<HTMLElement | HTMLInputElement>,
    option?: IChoiceGroupOption
  ): void => {
    const direction =
      option?.key === "ascending"
        ? SortDirection.Ascending
        : SortDirection.Descending;
    setSelectedSortDirection(direction);
    onSortChangeDirection(direction);
  };

  const onRenderFooterContent = (): JSX.Element => {
    return (
      <div className={styles.buttonContainer}>
        <DefaultButton onClick={clearFilters}>Clear</DefaultButton>
        <PrimaryButton onClick={applyFilters}>Apply</PrimaryButton>
      </div>
    );
  };

  return (
    <Panel
      onDismiss={dismissPanel}
      type={PanelType.smallFixedFar}
      headerText="Filters"
      isLightDismiss
      isOpen={true}
      onRenderFooterContent={onRenderFooterContent}
    >
      <div className={styles.container}>
        {/* This will need to be changed so it works across all sites not just the hubsite*/}

        {isHubsite && (
          <div>
            <label htmlFor="project-select" className={styles.label}>
              Select Projects
            </label>
            <AsyncSelect<ProjectOption, true>
              id="project-select"
              isMulti
              placeholder="Choose projects"
              defaultOptions={projects.map((project) => ({
                value: project.code,
                label: `${project.code} : ${project.title}`,
              }))}
              value={selectedProjects.map((code) => ({
                value: code,
                label: projects.find((p) => p.code === code)?.title ?? code,
              }))}
              onChange={(
                newValue: readonly ProjectOption[],
                actionMeta: ActionMeta<ProjectOption>
              ) => {
                setSelectedProjects(newValue.map((option) => option.value));
              }}
              loadOptions={(inputValue: string) => {
                const searchTerm = inputValue.toLowerCase();
                return Promise.resolve(
                  projects
                    .filter(
                      (project) =>
                        project.title.toLowerCase().includes(searchTerm) ||
                        project.code.toLowerCase().includes(searchTerm)
                    )
                    .map((project) => ({
                      value: project.code,
                      label: `${project.code} : ${project.title}`,
                    }))
                );
              }}
              styles={fluentSelectStyles}
            />
          </div>
        )}
        <label htmlFor="subject-searchbox">
          <span className={styles.label}>Subject</span>
        </label>
        <SearchBox
          id="subject-searchbox"
          placeholder="Subject"
          value={subject}
          onChange={(_, newValue) => setSubject(newValue ?? "")}
          onSearch={(newValue) => {
            setSubject(newValue || "");
            changeFilter({
              field: "EmailSubject",
              values: [newValue || ""],
              application: Operation.Contains,
            });
          }}
          onClear={() => removeFilter("EmailSubject")}
        />

        <label htmlFor="sent-by-searchbox">
          <span className={styles.label}>Sent by</span>
        </label>
        <SearchBox
          id="sent-by-searchbox"
          placeholder="Sent by"
          value={from}
          onChange={(_, newValue) => setFrom(newValue ?? "")}
          onSearch={(newValue) => {
            setFrom(newValue || "");
            changeFilter({
              field: "EmailFrom",
              values: [newValue || ""],
              application: Operation.Contains,
            });
          }}
          onClear={() => removeFilter("EmailFrom")}
        />

        <label htmlFor="recipients-searchbox">
          <span className={styles.label}>Recipients</span>
        </label>
        <SearchBox
          id="recipients-searchbox"
          placeholder="Recipients"
          value={recipients}
          onChange={(_, newValue) => setRecipients(newValue ?? "")}
          onSearch={(newValue) => {
            setRecipients(newValue || "");
            changeFilter({
              field: "EmailTo",
              values: [newValue || ""],
              application: Operation.Contains,
            });
          }}
          onClear={() => removeFilter("EmailTo")}
        />

        <label htmlFor="tags-searchbox">
          <span className={styles.label}>Tags</span>
        </label>
        <SearchBox
          id="tags-searchbox"
          placeholder="Tags"
          value={tags}
          onChange={(_, newValue) => setTags(newValue ?? "")}
          onSearch={(newValue) => {
            setTags(newValue || "");
            changeFilter({
              field: "EmailTags",
              values: [newValue || ""],
              application: Operation.Contains,
            });
          }}
          onClear={() => removeFilter("EmailTags")}
        />

        <DatePicker
          label="Start Date"
          firstDayOfWeek={DayOfWeek.Monday}
          placeholder="Select a date..."
          value={startDate || undefined}
          onSelectDate={setStartDate}
        />
        <DatePicker
          label="End Date"
          firstDayOfWeek={DayOfWeek.Monday}
          placeholder="Select a date..."
          value={endDate || undefined}
          onSelectDate={setEndDate}
        />

        <div className={styles.checkboxContainer}>
          <Checkbox
            label="Has attachments"
            checked={hasAttachments}
            onChange={(_, checked) => {
              if (checked) {
                setHasAttachments(checked);
                changeFilter({
                  field: "AttachmentCount",
                  values: ["0"],
                  application: Operation.GreaterThan,
                });
              } else {
                setHasAttachments(checked ?? false);
                removeFilter("AttachmentCount");
              }
            }}
          />
          <Checkbox
            label="Important Emails Only"
            checked={isImportant}
            onChange={(_, checked) => {
              if (checked) {
                setIsImportant(checked);
                changeFilter({
                  field: "EmailImportant",
                  values: ["Yes"],
                  application: Operation.Equals,
                });
              } else {
                setIsImportant(checked ?? false);
                removeFilter("EmailImportant");
              }
            }}
          />
          <Checkbox
            label="Confidential Emails Only"
            checked={isConfidential}
            onChange={(_, checked) => {
              if (checked) {
                setIsConfidential(checked);
                changeFilter({
                  field: "EmailConfidential",
                  values: ["true"],
                  application: Operation.Equals,
                });
              } else {
                setIsConfidential(checked ?? false);
                removeFilter("EmailConfidential");
              }
            }}
          />
        </div>

        <hr className={styles.divider} />

        <div className={styles.sortContainer}>
          <div className={styles.sortTitle}>Sort by:</div>
          <ChoiceGroup
            selectedKey={selectedSortColumn}
            options={[
              { key: "RefinableString31", text: "Subject" },
              { key: "RefinableString35", text: "Sent By" },
              { key: "RefinableDate00", text: "Received On" },
            ]}
            onChange={handleSortColumnChange}
          />
        </div>

        <hr className={styles.divider} />

        <div className={styles.sortContainer}>
          <div className={styles.sortTitle}>Sort Direction:</div>
          <ChoiceGroup
            selectedKey={selectedSortDirection.toString()}
            options={[
              {
                key: SortDirection.Descending.toString(),
                text: "Descending",
              },
              { key: SortDirection.Ascending.toString(), text: "Ascending" },
            ]}
            onChange={handleSortDirectionChange}
          />
        </div>
      </div>
    </Panel>
  );
};

export default Filters;
