import { Filter } from "../../../../shared/types/Filter";
import { SortDirection } from "../../../../shared/utils/DataManagment";
import { Project } from "../../../../shared/types/Project";

export interface IFiltersProps {
  onFilter: (filters: Filter[]) => void;
  changeFilter: (filter: Filter) => void;
  removeFilter: (filter: string) => void;

  onClear: () => void;
  currentColumnCode: string;
  currentSortDirection: SortDirection;
  onSortChangeColumn: (columnKey: string) => void;
  onSortChangeDirection: (direction: SortDirection) => void;

  filters: Filter[];
  dismissPanel: () => void;
  projects: Project[];
  isHubsite: boolean;
}
