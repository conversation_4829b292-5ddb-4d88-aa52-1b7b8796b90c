import React from "react";
import { SearchBox } from "@fluentui/react";

interface SearchBoxComponentProps {
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  doSearch: (searchTerm: string) => void;
}

const SearchBoxComponent: React.FC<SearchBoxComponentProps> = ({
  searchTerm,
  setSearchTerm,
  doSearch,
}) => (
  <SearchBox
    placeholder="Search emails"
    value={searchTerm}
    onChange={(_, newValue) => setSearchTerm(newValue ?? "")}
    onSearch={(newValue) => {
      doSearch(newValue ?? "");
    }}
    onClear={() => doSearch("")}
    styles={{ root: { maxWidth: "300px", width: "100%" } }}
    underlined={true}
  />
);

export default SearchBoxComponent;
