import React from "react";
import { IAttachmentsProps } from "./IAttachmentsProps";
import {
  Panel,
  PanelType,
  PrimaryButton,
  TooltipHost,
  Text,
  mergeStyleSets,
} from "@fluentui/react";
import { AttachmentResult } from "../../../../shared/types/Attachments";
import { handleAttachments } from "../../../../shared/utils/AttachmentHelpers";
// import { formatFileSize } from "../../../../shared/utils/FormatFileSize";
import { clampText } from "../../../../shared/utils/ClampText";
import { handleAttachmentAction } from "../../../../shared/utils/AttachmentPreviewHelpers";
import { Email } from "../../../../shared/types/Email";
import { formatFileSize } from "../../../../shared/utils/FormatFileSize";

const styles = mergeStyleSets({
  container: {
    paddingTop: "16px",
  },
  labelContainer: {
    display: "flex",
    alignItems: "center",
    marginBottom: "12px",
  },
  attachmentList: {
    display: "flex",
    flexDirection: "column",
    gap: "12px",
    maxHeight: "80vh", // Dynamically adjusts to 80% of the viewport height
    overflowY: "auto",
  },
  attachmentItem: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "8px",
    border: "1px solid #E1E1E1",
    borderRadius: "4px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
  },
  attachmentInfo: {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
    gap: "4px",
  },
  attachmentActions: {
    display: "flex",
    gap: "8px",
  },
  icon: {
    fontSize: "20px",
    color: "#0F6CBD",
  },
  title: {
    fontSize: "14px",
    fontWeight: "600",
  },
  size: {
    fontSize: "12px",
    color: "#6A6A6A",
  },
});

const Attachments: React.FC<IAttachmentsProps> = ({
  email,
  previewAdapter,
  dismissPanel,
}) => {
  const [attachments, setAttachments] = React.useState<
    AttachmentResult | undefined
  >(undefined);
  const [isAttachmentLoading, setIsAttachmentLoading] = React.useState(false);

  const fetchAttachments = async (email: Email): Promise<void> => {
    setIsAttachmentLoading(true);
    try {
      const attachmentResult = await handleAttachments(email, previewAdapter);
      setAttachments(attachmentResult);
    } catch (error) {
      console.error("Error handling attachments:", error);
      setAttachments(undefined);
    } finally {
      setIsAttachmentLoading(false);
    }
  };

  React.useEffect(() => {
    void fetchAttachments(email);
  }, [email]);

  const attachmentCount = attachments?.attachments.length ?? 0;

  return (
    <Panel
      onDismiss={dismissPanel}
      type={PanelType.medium}
      headerText={`Attachments (${attachmentCount})`}
      isLightDismiss
      isOpen
    >
      <div className={styles.container}>
        {isAttachmentLoading ? (
          <Text variant="medium">Loading Attachment List...</Text>
        ) : (
          <div className={styles.attachmentList}>
            {attachments?.attachments.map((attachment) => (
              <div key={attachment.filename} className={styles.attachmentItem}>
                <div className={styles.attachmentInfo}>
                  <TooltipHost content={attachment.filename}>
                    <Text className={styles.title}>
                      {clampText(attachment.filename as string, 30)}
                    </Text>
                  </TooltipHost>
                  <Text className={styles.size}>
                    {formatFileSize(attachment.size)}
                  </Text>
                </div>
                <div className={styles.attachmentActions}>
                  <PrimaryButton
                    iconProps={{ iconName: "Download" }}
                    text="Download"
                    onClick={() =>
                      handleAttachmentAction(attachment, "download")
                    }
                  />
                  <PrimaryButton
                    iconProps={{ iconName: "View" }}
                    text="Preview"
                    onClick={() =>
                      handleAttachmentAction(attachment, "preview")
                    }
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Panel>
  );
};

export default Attachments;
