import React from "react";
import { Persona, PersonaSize } from "@fluentui/react";
import { TagBadge } from "./TagBadge";
import {
  Attach20Regular,
  Important20Regular,
  LockClosed20Regular,
} from "@fluentui/react-icons";
import { Email } from "../../../../shared/types/Email";
import { getNameAndEmail } from "../../../../shared/utils/useRecipient";
import { formatDate } from "../../../../shared/utils/FormatDate";
import { Tag } from "../../../../shared/types/Tags";

interface IAvatar {
  name: string | undefined;
  image: string | undefined;
}

export interface EmailStyles {
  stackedEmailView: string;
  emailAvatar: string;
  emailContent: string;
  contentRow: string;
  flex: string;
  emailSenderName: string;
  rhsContent: string;
  tagsProjectContainer: string;
  confidentialIcon: string;
  importantIcon: string;
  attachmentIcon: string;
  projectCode: string;
  emailReceived: string;
  emailSubject: string;
  emailTextSummary: string;
}

export const EmailList = {
  getAvatar: (item: Email, avatars: Map<string, IAvatar>): JSX.Element => {
    const [name, email] = getNameAndEmail(item.sentBy);
    const avatar: IAvatar | undefined = avatars.get(email);

    return (
      <Persona
        text={name}
        size={PersonaSize.size32}
        hidePersonaDetails={true}
        imageAlt={name}
        imageUrl={avatar?.image ?? ""}
      />
    );
  },

  renderItemColumn:
    (styles: EmailStyles, avatars: Map<string, IAvatar>, tags: Tag[]) =>
    (item: Email, index: number): JSX.Element => {
      const tagToDisplay = item.tag && item.tag !== "" ? item.tag : "No tag";
      const isImportant = item.emailImportant === "Yes";
      const isConfidential = item.emailConfidential;
      const attachmentCount = item.attachmentCount ?? "0";
      const hasAttachments = attachmentCount !== "0";

      return (
        <div className={styles.stackedEmailView}>
          <div className={styles.emailAvatar}>
            {EmailList.getAvatar(item, avatars)}
          </div>
          <div className={styles.emailContent}>
            <div className={`${styles.contentRow} ${styles.flex}`}>
              <div className={styles.emailSenderName}>{item.sentBy}</div>
              <div className={styles.rhsContent}>
                <div className={styles.tagsProjectContainer}>
                  <div className={styles.confidentialIcon}>
                    {isConfidential && (
                      <LockClosed20Regular data-testid="confidential-icon" />
                    )}
                  </div>
                  <div className={styles.importantIcon}>
                    {isImportant && (
                      <Important20Regular data-testid="important-icon" />
                    )}
                  </div>
                  <div className={styles.attachmentIcon}>
                    {hasAttachments && (
                      <Attach20Regular data-testid="attachment-icon" />
                    )}
                  </div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "4px",
                    }}
                  >
                    <TagBadge tags={tags} tagName={tagToDisplay} />
                    <span className={styles.projectCode}>
                      {item.projectCode}
                    </span>
                  </div>
                </div>
                <div className={styles.emailReceived}>
                  {formatDate(item.dateFiled)}
                </div>
              </div>
            </div>
            <div className={styles.contentRow}>
              <div className={styles.emailSubject}>{item.subject}</div>
            </div>
            <div className={styles.emailTextSummary}>
              {item.emailTextSummary}
            </div>
          </div>
        </div>
      );
    },

  getRowStyles: () => ({
    check: {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      padding: 0,
      height: "100%",
    },
    root: {
      display: "flex",
      alignItems: "center",
    },
  }),
};
