@import '~@fluentui/react/dist/sass/mixins/_Font.Mixins.scss';



.flex {
  display: flex;
}

.headerContainer {
  padding-left: 24px;
}

.messageContainer {
  padding-left: 24px;
  font-size: 12px;
}

.stackedEmailView {
  display: flex;

  .emailAvatar {
    padding: 12px;
  }

  .emailContent {
    display: "flex";
    flex: 1;
    flex-Direction: "column";
    gap: "0px";
  }

  .rhsContent {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 11px;
    right: 18px;
    align-items: flex-end;
  }

  .tagsProjectContainer {
    display: flex;
    flex-direction: row;
    margin-bottom: 2px;
  }

  .contentRow {
    display: flex;
    justify-content: space-between;
    align-items: "center";
    width: "100%";
    margin-right: 16px;
  }

  .emailTextSummary{
    width: 85vw;
    overflow: auto;
  }

  .emailSenderName {
    color: #333333;
    font-size: 14px;
    line-height: 1.3;
    min-height: 28px;
    align-content: center;
  }

  .emailSubject {
    color: #333333;
    font-size: 14px;
    line-height: 1.3;
  }

  .emailReceived {
    color: #242424;
    font-size: 12px;
    line-height: 1.2;
  }

  .emailSummary {
    color: #666666;
    font-size: 12px;
    line-height: 1.2
  }

  .confidentialIcon {
    padding: 2px;
  }

  .importantIcon {
    color: "red";
    padding: 2px;
  }

  .attachmentIcon {
    padding: 2px;
  }

  .tagName {
    padding: 2px;
  }

  .projectCode {
    font-Weight: "600";
    color: #242424;
    padding: 2px;
  }
}


.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: "1";
  margin: "0";
  padding: "0";
}