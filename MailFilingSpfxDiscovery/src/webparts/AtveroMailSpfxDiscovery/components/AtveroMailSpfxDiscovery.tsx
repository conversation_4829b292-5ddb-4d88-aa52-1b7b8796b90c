import React, { FC, ReactNode, useEffect, useState } from "react";
import styles from "./AtveroMailSpfxDiscovery.module.scss";
import type { IAtveroMailSpfxDiscoveryProps } from "./IAtveroMailSpfxDiscoveryProps";
import { Email } from "../../../shared/types/Email";
import { Filter, Operation } from "../../../shared/types/Filter";
import { Project } from "../../../shared/types/Project";
import { SortDirection } from "../../../shared/utils/DataManagment";
import { useBoolean } from "@fluentui/react-hooks";
import { Selection, IObjectWithKey } from "@fluentui/react/lib/DetailsList";
import {
  ShimmeredDetailsList,
  IColumn,
  SelectionMode,
  IDetailsRowProps,
} from "@fluentui/react";
import { Sticky, StickyPositionType } from "@fluentui/react/lib/Sticky";
import { CommandBar } from "@fluentui/react/lib/CommandBar";
import { ScrollablePane } from "@fluentui/react/lib/ScrollablePane";
import Filters from "./components/Filters";
import Preview from "./components/Preview";
import { useEmailData } from "../../../shared/hooks/useEmailData";
import { getCommandBarConfig } from "../../../shared/utils/CommandBarConfig";
import { EmailList } from "./components/EmailList";
import { Tag, DEFAULT_TAGS } from "../../../shared/types/Tags";

interface IMailColumn extends IColumn {
  sortName: string;
}

const AtveroMailSpfxDiscovery: FC<IAtveroMailSpfxDiscoveryProps> = (props) => {
  const {
    dataAdapter,
    previewAdapter,
    projectListAdapter,
    isHubsite,
    relativeUrl,
    absoluteUrl,
    backendAdapter,
  } = props;

  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filters, setFilters] = useState<Filter[]>([]);
  const [columnCode, setColumnCode] = useState<string>("RefinableDate00");
  const [sortDirection, setSortDirection] = useState<SortDirection>(
    SortDirection.Descending
  );
  const [projects, setProjects] = useState<Project[]>([]);
  const [activeSearchTerm, setActiveSearchTerm] = useState("");
  const [isFiltersOpen, { setTrue: openFilters, setFalse: closeFilters }] =
    useBoolean(false);
  const [
    isEmailSelected,
    { setTrue: openEmailPreview, setFalse: closeEmailPreview },
  ] = useBoolean(false);
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [isConfidential, setIsConfidential] = useState(false);
  const [selectionDetails, setSelectionDetails] = useState<IObjectWithKey[]>(
    []
  );

  const [tags, setTags] = useState<Tag[]>([]);

  const [selectionState] = useState(
    new Selection({
      onSelectionChanged: () => {
        const selections = selectionState.getSelection();
        setSelectionDetails(selections);
        if (selections.length > 0) {
          openEmailPreview();
        } else {
          closeEmailPreview();
        }
      },
    })
  );

  const { emailData, loading, page, avatars, getNextPage, pageSize } =
    useEmailData({
      dataAdapter,
      previewAdapter,
      isHubsite,
      relativeUrl,
      filters,
      activeSearchTerm,
      columnCode,
      sortDirection,
      projects,
      absoluteUrl,
    });

  useEffect(() => {
    if (isHubsite) {
      const fetchProjects = async (): Promise<void> => {
        const projectList = await projectListAdapter.getProjectList("");
        setProjects(projectList);
      };

      void fetchProjects();
    }
  }, [projectListAdapter]);

  useEffect(() => {
    const getCustomTags = async (): Promise<void> => {
      const useCustomTags = await backendAdapter.getSetting("useCustomTags");

      if (useCustomTags) {
        try {
          const getTagsList = await backendAdapter.getTags();

          if (
            getTagsList.error === undefined &&
            getTagsList.tags !== undefined
          ) {
            setTags(getTagsList.tags);
          }
        } catch (error) {
          console.error("Error fetching custom tags:", error);
          // fall back to the standard set

          setTags(DEFAULT_TAGS);
        }
      } else {
        // fall back to the standard set

        setTags(DEFAULT_TAGS);
      }
    };

    void getCustomTags();
  }, [backendAdapter]);

  const fluentUiColumns: IMailColumn[] = [
    {
      key: "email",
      name: "Email",
      fieldName: "subject",
      sortName: "RefinableString31",
      minWidth: 1024,
      maxWidth: 1200,
      isResizable: true,
    },
  ];

  useEffect(() => {
    const sites =
      filters?.find((item) => item.field === "ProjectCode")?.values ?? [];
    setSelectedProjects(sites);
  }, [filters]);

  const toggleDriveFilter = (): void => {
    setIsConfidential((prevConfidential) => {
      const newConfidentialState = !prevConfidential;
      const updatedFilters = newConfidentialState
        ? [
            ...filters,
            {
              field: "EmailConfidential",
              values: ["true"],
              application: Operation.Equals,
            },
          ]
        : filters.filter((filter) => filter.field !== "EmailConfidential");

      setFilters(updatedFilters);
      return newConfidentialState;
    });
  };

  const doSearch = (finalSearchTerm: string): void => {
    setSearchTerm(finalSearchTerm);
    setActiveSearchTerm(finalSearchTerm);
  };

  const selectedEmail =
    isEmailSelected && selectionDetails.length > 0
      ? (selectionDetails[0] as Email)
      : undefined;

  const dismissPanelAndUnselect = (): void => {
    selectionState.setAllSelected(false);
    closeEmailPreview();
  };

  const _renderCustomPlaceholder = (
    rowProps: IDetailsRowProps,
    index?: number,
    defaultRender?: (props: IDetailsRowProps) => ReactNode
  ): ReactNode => {
    if (!loading && index && index > pageSize - 1) {
      void getNextPage(page);
    }
    if (defaultRender) return defaultRender(rowProps);
  };

  const renderMessage = (): string | null => {
    if (selectedProjects.length === 0 && !(activeSearchTerm.length > 0)) {
      return "Select a project or search a keyword to find your emails.";
    } else if (selectedProjects.length > 1 && !(activeSearchTerm.length > 0)) {
      return `You're searching from the following projects: ${selectedProjects.join(
        ", "
      )}`;
    }
    return null;
  };

  const message = renderMessage();

  const { searchButton, _farItems } = getCommandBarConfig(
    searchTerm,
    setSearchTerm,
    doSearch,
    filters,
    emailData,
    isConfidential,
    toggleDriveFilter,
    setFilters,
    setIsConfidential,
    openFilters
  );

  const modifyFilters = (changedFilter: Filter): void => {
    const newFilters = filters.filter(
      (filter) => filter.field !== changedFilter.field
    );
    setFilters([...newFilters, changedFilter]);
    if (
      changedFilter.field === "EmailConfidential" &&
      changedFilter.values.includes("true")
    ) {
      setIsConfidential(true);
    }
  };

  const removeFilter = (changedFilter: string): void => {
    const newFilters = filters.filter(
      (filter) => filter.field !== changedFilter
    );
    setIsConfidential(false);
    setFilters(newFilters);
  };

  return (
    <>
      <div>
        <ScrollablePane>
          <Sticky stickyPosition={StickyPositionType.Header}>
            <CommandBar
              items={[searchButton]}
              farItems={_farItems}
              ariaLabel="Task actions"
              primaryGroupAriaLabel="Task actions"
              farItemsGroupAriaLabel="More actions"
            />
          </Sticky>
          {message && isHubsite ? (
            <div className={styles.messageContainer}>
              <Sticky stickyPosition={1}> {message}</Sticky>
            </div>
          ) : (
            <ShimmeredDetailsList
              skipViewportMeasures={false}
              setKey="emailData"
              items={emailData || []}
              columns={fluentUiColumns}
              selectionMode={SelectionMode.single}
              onRenderItemColumn={EmailList.renderItemColumn(
                styles,
                avatars,
                tags
              )}
              enableShimmer={!emailData}
              ariaLabelForShimmer="Emails are being fetched"
              ariaLabelForGrid="Emails details"
              selection={selectionState}
              selectionPreservedOnEmptyClick={true}
              enterModalSelectionOnTouch={true}
              ariaLabelForSelectionColumn="Toggle selection"
              ariaLabelForSelectAllCheckbox="Toggle selection for all items"
              checkButtonAriaLabel="select row"
              onShouldVirtualize={() => true}
              onRenderDetailsHeader={() => <div />} // We would like to keep this blank to disable the default header rendering
              onRenderCustomPlaceholder={_renderCustomPlaceholder}
              onRenderRow={(props, defaultRender) => {
                if (props) {
                  props.styles = EmailList.getRowStyles();
                  return defaultRender ? defaultRender(props) : null;
                }
                return null;
              }}
            />
          )}
        </ScrollablePane>
      </div>

      {isFiltersOpen && (
        <Filters
          projects={projects}
          onFilter={(filters) => {
            setFilters(filters);
            closeFilters();
          }}
          changeFilter={modifyFilters}
          removeFilter={removeFilter}
          onClear={() => {
            setFilters([]);
          }}
          currentColumnCode={columnCode}
          currentSortDirection={sortDirection}
          onSortChangeColumn={(columnKey: string) => {
            setColumnCode(columnKey);
          }}
          onSortChangeDirection={(direction: SortDirection) => {
            setSortDirection(direction);
          }}
          filters={filters}
          dismissPanel={() => closeFilters()}
          isHubsite={isHubsite}
        />
      )}
      {selectedEmail && (
        <Preview
          previewAdapter={previewAdapter}
          email={selectedEmail}
          dismissPanel={dismissPanelAndUnselect}
        />
      )}
    </>
  );
};

export default AtveroMailSpfxDiscovery;
