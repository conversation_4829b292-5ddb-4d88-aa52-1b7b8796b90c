/* v8 ignore next 100 */
import * as React from "react";
import * as ReactDom from "react-dom";
import { Version } from "@microsoft/sp-core-library";
import {
  IPropertyPaneConfiguration,
  PropertyPaneTextField,
} from "@microsoft/sp-property-pane";
import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";
//import { IReadonlyTheme } from "@microsoft/sp-component-base";

import * as strings from "AtveroMailSpfxDiscoveryWebPartStrings";
import AtveroMailSpfxDiscovery from "./components/AtveroMailSpfxDiscovery";
import { IAtveroMailSpfxDiscoveryProps } from "./components/IAtveroMailSpfxDiscoveryProps";

import { SPFx as graphSPFx, graphfi } from "@pnp/graph"; // PnP JS Graph
import { SPFx as spSPFx, spfi } from "@pnp/sp"; // PnP JS SharePoint
import { EmailPreviewAdapter } from "../../adapters/EmailPreviewAdapter";
import { ProjectsListAdapter } from "../../adapters/ProjectsListAdapter";
import { EmailsAdapter } from "../../adapters/EmailsFromSearchAdapter";
export interface IAtveroMailSpfxDiscoveryWebPartProps {
  description: string;
}

import { AadHttpClient } from "@microsoft/sp-http";
import { getAtveroClientId } from "./AtveroConstants";

import { BackendAdapter } from "../../adapters/BackendAdapter";

export default class AtveroMailSpfxDiscoveryWebPart extends BaseClientSideWebPart<IAtveroMailSpfxDiscoveryWebPartProps> {
  // private _isDarkTheme: boolean = false;
  // private _environmentMessage: string = "";

  private callAuthenticatedAzure = (url: string): Promise<string> => {
    return this.context.aadHttpClientFactory
      .getClient(getAtveroClientId())
      .then((client) => {
        return client
          .get(url, AadHttpClient.configurations.v1)
          .then((response) => {
            if (response.status >= 200 && response.status < 400) {
              return response.text();
            } else {
              return Promise.reject(
                "Azure function (" +
                  url +
                  ") failed with code: " +
                  response.statusText
              );
            }
          });
      });
  };

  public render(): void {
    // Initialize PnP JS Graph and SharePoint clients
    const graphClient = graphfi().using(graphSPFx(this.context)); // Graph API
    const spClient = spfi().using(spSPFx(this.context)); // SharePoint API
    const absoluteUrl = this.context.pageContext.web.absoluteUrl;
    const relativeUrl = this.context.pageContext.web.serverRelativeUrl;
    const isHubsite = this.context.pageContext.legacyPageContext.isHubSite;
    const hubSiteId = this.context.pageContext.legacyPageContext.hubSiteId;
    const portalUrl = this.context.pageContext.legacyPageContext.portalUrl;

    const dataAdapter: EmailsAdapter = new EmailsAdapter(
      graphClient,
      spClient,
      relativeUrl,
      hubSiteId
    );

    const previewAdapter: EmailPreviewAdapter = new EmailPreviewAdapter(
      graphClient,
      portalUrl
    );

    const projectListAdapter: ProjectsListAdapter = new ProjectsListAdapter(
      graphClient,
      spClient
    );

    const backendAdapter: BackendAdapter = new BackendAdapter(
      this.callAuthenticatedAzure
    );

    const element: React.ReactElement<IAtveroMailSpfxDiscoveryProps> =
      React.createElement(AtveroMailSpfxDiscovery, {
        dataAdapter: dataAdapter,
        relativeUrl: relativeUrl,
        previewAdapter: previewAdapter,
        projectListAdapter: projectListAdapter,
        isHubsite: isHubsite,
        absoluteUrl: absoluteUrl,
        backendAdapter: backendAdapter,
      });

    ReactDom.render(element, this.domElement);
  }

  protected onDispose(): void {
    ReactDom.unmountComponentAtNode(this.domElement);
  }

  protected get dataVersion(): Version {
    return Version.parse("1.0");
  }

  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: {
            description: strings.PropertyPaneDescription,
          },
          groups: [
            {
              groupName: strings.BasicGroupName,
              groupFields: [
                PropertyPaneTextField("description", {
                  label: strings.DescriptionFieldLabel,
                }),
              ],
            },
          ],
        },
      ],
    };
  }
}
