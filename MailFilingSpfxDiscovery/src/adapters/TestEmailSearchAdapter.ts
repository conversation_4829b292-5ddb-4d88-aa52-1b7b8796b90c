import { IForEmailsFromSearch } from "./IForEmailsFromSearch";
import { Email, EmailResponse } from "../shared/types/Email";
import { SortDirection } from "../shared/utils/DataManagment";
import { Filter } from "../shared/types/Filter";

export class TestEmailSearchAdapter implements IForEmailsFromSearch {
  fetchEmails(
    selectedProjects: string[],
    shareRootUrl: string,
    searchTerm: string,
    page: number,
    pageSize: number,
    nextLink: string | undefined,
    columnCode: string | undefined,
    sortColumn: string | undefined,
    sortDirection: SortDirection,
    filters: Filter[],
    resetPaging: boolean
  ): Promise<EmailResponse> {
    const email: Email = {
      projectCode: "123",
      id: "123",
      subject: "This is a test email",
      sentBy: "<Test User> <EMAIL>",
      dateFiled: Date.now().toLocaleString(),
      emailTextSummary: "Some email body text",
      emailConfidential: false,
      emailImportant: "Yes",
      attachmentCount: "0",
      driveID: "",
      driveItemID: "",
      fileRef: "",
    };
    const response: EmailResponse = {
      emails: [email],
      nextLink: undefined,
      page: 0,
      pageSize: 20,
      sortField: "",
      sortDirection: SortDirection.Ascending,
      total: 1,
      moreResultsAvailable: false,
    };
    return Promise.resolve(response);
  }
}
