import { IForEmailPreview } from "./IForEmailPreview";
import { Email } from "../shared/types/Email";

export class TestPreviewAdapter implements IForEmailPreview {
  readFileContent(email: Email): Promise<ArrayBuffer> {
    return Promise.resolve(new ArrayBuffer(1));
  }

  getPhoto(recipient: string): Promise<(string | undefined)[]> {
    return Promise.resolve([]);
  }

  getPreviewUrl(selectedEmail: Email): Promise<string | undefined> {
    return Promise.resolve(undefined);
  }

  getSelectedEmailWithDriveItemID(selectedEmail: Email): Promise<Email> {
    return Promise.resolve(selectedEmail);
  }

  ensureDriveItemID(email: Email): Promise<Email> {
    return Promise.resolve(email);
  }
}
