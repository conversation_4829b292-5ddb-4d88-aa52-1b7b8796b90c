import { <PERSON>raph<PERSON> } from "@pnp/graph";
import { SPFI } from "@pnp/sp";
import { IProjectsList } from "./IProjectsList";
// import { Site } from "@microsoft/microsoft-graph-types";
import { Project } from "../shared/types/Project";
import { IRenderListDataParameters } from "@pnp/sp/lists";
import "@pnp/sp/webs";
import "@pnp/sp/lists";
// import { Web } from "@pnp/sp/webs";
// import { getHubsite } from "../shared/graph/getHubsite";
interface IRenderListDataAsStreamProps {
  ID: string;
  ProjectCode: string | undefined;
  ProjectNumber: string | undefined;
  Description: string;
  ATVImportedSourceID: string | undefined;
}

export class ProjectsListAdapter implements IProjectsList {
  graphClient: GraphFI;
  spClient: SPFI;

  constructor(client: GraphFI, spclient: SPFI) {
    this.graphClient = client;
    this.spClient = spclient;
  }

  getProjectList = async (
    // hubsite: Site,
    searchString?: string
  ): Promise<Project[]> => {
    // const hubsite = await getHubsite(this.graphClient, "atverodevs.sharepoint.com", "/sites/AtveroMail");
    let whereClause = "";
    if (searchString) {
      whereClause = `
            <Or>
              <Contains>
                <FieldRef Name='ProjectCode'/>
                  <Value Type='Text'>${searchString}</Value>
              </Contains>
              <Contains>
                <FieldRef Name='Description'/><Value Type='Text'>${searchString}</Value>
              </Contains>
            </Or>
          `;
    }

    if (this.spClient) {
      const viewXml = `<View Scope="RecursiveAll">
            <Query>
              <Where>${whereClause}</Where>
              <OrderBy><FieldRef Name="ProjectCode" Ascending="True"/></OrderBy>
            </Query>
             <ViewFields>
              <FieldRef Name="ProjectCode"/>          
              <FieldRef Name="ProjectNumber"/>
              <FieldRef Name="Description"/>
              <FieldRef Name="ATVImportedSourceID"/>
            </ViewFields>
            <RowLimit Paged="TRUE">${200}</RowLimit>
          </View>`;

      const renderListDataParams: IRenderListDataParameters = {
        ViewXml: viewXml,
        Paging: "Paged=TRUE",
      };

      //   const projectWeb = Web([this.spClient.web, hubsite?.webUrl as string]);

      // render list data as stream
      const r = await this.spClient.web.lists
        .getByTitle("Projects")
        .renderListDataAsStream(renderListDataParams);
      // log array of items in response

      const projects = r.Row.map((project: IRenderListDataAsStreamProps) => ({
        code: project.ProjectCode ?? project.ProjectNumber ?? "",
        title: project.Description,
        id: project.ID,
        favorite: false,
      }));

      return projects;
    }
    return [];
  };
}
