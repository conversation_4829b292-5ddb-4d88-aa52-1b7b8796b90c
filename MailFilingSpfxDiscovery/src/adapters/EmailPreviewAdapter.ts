import { <PERSON><PERSON><PERSON><PERSON> } from "@pnp/graph";
import { IForEmailPreview } from "./IForEmailPreview";
import { Email } from "../shared/types/Email";
import { getNameAndEmail } from "../shared/utils/useRecipient";
import { FieldValueSet, ListItem } from "@microsoft/microsoft-graph-types";
import { ListTitles } from "../shared/types/ListTitles";
import "@pnp/graph/list-item";
import "@pnp/graph/lists";
import "@pnp/graph/users";
import "@pnp/graph/files";
import "@pnp/graph/photos";

interface CustomFieldValueSet extends FieldValueSet {
  EmailSubject?: string;
  EmailFrom?: string;
  EmailReceived?: string;
}

export class EmailPreviewAdapter implements IForEmailPreview {
  graphClient: GraphFI;
  portalUrl: string;

  constructor(client: GraphFI, portalUrl: string) {
    this.graphClient = client;
    this.portalUrl = portalUrl;
  }

  async getPreviewUrl(selectedEmail: Email): Promise<string | undefined> {
    const preview = await this.graphClient.me.drives
      .getById(selectedEmail.driveID)
      .getItemById(selectedEmail.driveItemID as string)
      .preview();

    if (preview.getUrl) {
      return `${preview.getUrl}&nb=true`;
    }
    return undefined;
  }

  async getSelectedEmailWithDriveItemID(selectedEmail: Email): Promise<Email> {
    const projectPath = `/sites/${selectedEmail.projectCode}`;
    const cleanPortalUrl = this.portalUrl
      .replace("https://", "")
      .replace("http://", "")
      .replace(/\/+$/, "");

    // Determine the list type based on the emailConfidential property
    const listType = selectedEmail.emailConfidential
      ? ListTitles.Confidential
      : ListTitles.NonConfidential;

    // Get the site details
    const site = await (
      await this.graphClient.sites.getByUrl(cleanPortalUrl, projectPath)
    )();
    const lists = await this.graphClient.sites
      .getById(site.id as string)
      .lists();
    const filteredLists = lists.filter(
      (list: ListItem) => list.name === listType
    );

    if (filteredLists.length === 0) {
      throw new Error(`List of type "${listType}" not found`);
    }

    const selectedEmailWithDriveItems = await this.graphClient.sites
      .getById(site.id as string)
      .lists.getById(filteredLists[0].id as string)
      .items.getById(selectedEmail?.id ?? "")
      .expand("driveItem")();

    const fields = selectedEmailWithDriveItems.fields as CustomFieldValueSet;
    return {
      ...selectedEmail,
      id: fields?.id ?? "",
      subject: fields?.EmailSubject ?? "",
      sentBy: fields?.EmailFrom ?? "",
      dateFiled: fields?.EmailReceived ?? "",
      driveItemID: selectedEmailWithDriveItems.driveItem?.id,
      driveID: selectedEmail.driveID,
      projectCode: selectedEmail.projectCode,
    };
  }

  readFileAsync(file: Blob): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        resolve(reader.result as ArrayBuffer);
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  }

  async readFileContent(email: Email): Promise<ArrayBuffer> {
    const fileContents: Blob = await this.graphClient.me.drives
      .getById(email.driveID ?? "")
      .getItemById(email.driveItemID as string)
      .getContent();
    const content: ArrayBuffer = await this.readFileAsync(fileContents);
    return content;
  }

  //  get the avatar image

  async getPhoto(recipient: string): Promise<(string | undefined)[]> {
    const [name, email] = getNameAndEmail(recipient);

    let urlEncodedObject = undefined;

    try {
      const blob: Blob = await this.graphClient.users
        .getById(email)
        .photo.getBlob();
      urlEncodedObject = URL.createObjectURL(blob);
    } catch (e) {
      console.error("Failed to fetch photo. Failed with error: ", e);
      urlEncodedObject = undefined;
    }

    return [name, email, urlEncodedObject];
  }
}
