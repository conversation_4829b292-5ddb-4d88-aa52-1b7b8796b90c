import { <PERSON><PERSON><PERSON><PERSON> } from "@pnp/graph";
import { SP<PERSON> } from "@pnp/sp";
import { Filter } from "../shared/types/Filter";
import { EmailResponse } from "../shared/types/Email";
import { SortDirection } from "../shared/utils/DataManagment";
import { handleFilter } from "../shared/utils/EmailUtils";
import { IForEmailsFromSearch } from "./IForEmailsFromSearch";
import { getEmailsFromSearch } from "../shared/graph/getEmailsFromSearch";
import { IWeb, Web } from "@pnp/sp/webs";
import { Drive } from "@microsoft/microsoft-graph-types";

import "@pnp/sp/lists";
import "@pnp/sp/sites";

export const getWebFromSite = async (
  sharepoint: SPFI,
  projectCode: string,
  relativeUrl: string
): Promise<IWeb> => {
  const rootwebData = await sharepoint.site.rootWeb();
  const cleanRootUrl = rootwebData.Url.replace(relativeUrl, "");

  // We're passing the root web around and we want one for the current site
  // use the web factory to one for our selected site.
  // Need to pass the any queryable sharepoit object to get a queryable web object
  // out again. https://pnp.github.io/pnpjs/sp/webs/#access-a-web
  const web = Web([sharepoint.web, cleanRootUrl + "/sites/" + projectCode]);
  return web;
};

export class EmailsAdapter implements IForEmailsFromSearch {
  readonly graph: GraphFI;
  readonly sharepoint: SPFI | undefined;
  readonly relativeUrl: string;
  readonly hubSiteId: string;

  constructor(
    graph: GraphFI,
    sharepoint: SPFI | undefined,
    relativeUrl: string,
    hubSiteId: string
  ) {
    this.graph = graph;
    this.sharepoint = sharepoint;
    this.relativeUrl = relativeUrl;
    this.hubSiteId = hubSiteId;
  }

  async fetchEmails(
    selectedProjects: string[],
    shareRootUrl: string,
    searchTerm: string,
    page: number,
    pageSize: number,
    nextLink: string | undefined,
    columnCode: string | undefined,
    columnKey: string | undefined,
    sortDirection: SortDirection,
    filters: Filter[],
    resetPaging: boolean
  ): Promise<EmailResponse | undefined> {
    // Check if the graph object is defined
    if (!this.graph) return;

    try {
      // Case 1: When there is a search term, fetch filtered and sorted emails using the search API
      if (searchTerm) {
        const emails = await getEmailsFromSearch(
          this.graph,
          selectedProjects,
          `https://${shareRootUrl}`,
          searchTerm,
          page,
          pageSize,
          columnCode,
          sortDirection,
          filters,
          this.hubSiteId
        );
        return emails;
      }

      // Case 2: When no search term but there are selected projects
      if (selectedProjects.length > 0) {
        const projectPath = `/sites/${selectedProjects[0]}`;

        const sitePromise = await this.graph.sites.getByUrl(
          shareRootUrl,
          projectPath
        );

        const site = await sitePromise();

        // Fetch drives from the site and find the "Filed Email Content" drive
        const allSiteDrives = (await this.graph.sites
          .getById(site.id as string)
          .drives()) as Drive[];

        const filteredDrives = allSiteDrives.filter(
          (drive) => drive.name === "Filed Email Content"
        );

        const isConfidentialFilterApplied = filters?.some(
          (filter) =>
            filter.field === "EmailConfidential" &&
            filter.values.includes("true")
        );

        // Case 2a: Handle filters or sorting without a search term
        if (this.sharepoint && selectedProjects.length === 1) {
          // stream api
          const emailResponse = await handleFilter(
            filters,
            columnKey,
            sortDirection,
            (selectedProject: string) => {
              if (this.sharepoint) {
                return getWebFromSite(
                  this.sharepoint,
                  selectedProject,
                  this.relativeUrl
                );
              } else {
                return undefined;
              }
            },
            this.graph,
            selectedProjects[0],
            nextLink,
            page,
            pageSize,
            resetPaging,
            shareRootUrl,
            isConfidentialFilterApplied
          );

          const updatedResponse = {
            ...emailResponse,
            emails: emailResponse.emails.map((item) => ({
              ...item,
              driveID: filteredDrives[0].id!,
              projectCode: selectedProjects[0],
            })),
          };

          return updatedResponse;
        }
      }
    } catch (error) {
      console.error("Error fetching emails: ", error);
    }

    return undefined;
  }
}
