import { IBackendAdapter } from "./IBackendAdapter";
import { Tag } from "../shared/types/Tags";

interface SettingResponse {
  IsSuccess: boolean;
  Message: string;
}

interface CustomTagResponse {
  IsSuccess: boolean;
  Message: string;
  Data: Array<{
    Name: string;
    Colour: string;
    BackgroundColour: string;
  }>;
}

export class BackendAdapter implements IBackendAdapter {
  loadedTags: Tag[] | undefined;

  callAuthenticatedAzure: (url: string) => Promise<string>;

  constructor(callAuthenticatedAzure: (url: string) => Promise<string>) {
    this.callAuthenticatedAzure = callAuthenticatedAzure;
  }

  async getTags(): Promise<{
    tags: Tag[] | undefined;
    error?: string;
  }> {
    if (this.loadedTags !== undefined) return { tags: this.loadedTags };

    try {
      const response = await this.callAuthenticatedAzure("/api/customtags");

      if (response) {
        const customTagsResponse: CustomTagResponse = JSON.parse(response);

        if (customTagsResponse.IsSuccess) {
          const tags = customTagsResponse.Data.map((tag) => ({
            Name: tag.Name,
            Colour: tag.Colour,
            BackgroundColour: tag.BackgroundColour,
          }));

          return { tags: tags };
        } else {
          return { tags: undefined, error: customTagsResponse.Message };
        }
      }

      return { tags: undefined, error: "Failed to fetch custom tags" };
    } catch {
      return {
        tags: undefined,
        error: "An error occurred while fetching tags",
      };
    }
  }

  async getSetting(setting: string): Promise<string | undefined> {
    try {
      const response = await this.callAuthenticatedAzure(
        "/api/AppSettings?setting=" + setting
      );

      const settingVal: SettingResponse = JSON.parse(response);

      if (settingVal.Message === "") return undefined;

      return settingVal.Message;
    } catch (error: unknown) {
      if (error instanceof Error && error.message !== null) {
        console.log(error.message);
        return undefined;
      } else {
        return undefined;
      }
    }
  }
}
