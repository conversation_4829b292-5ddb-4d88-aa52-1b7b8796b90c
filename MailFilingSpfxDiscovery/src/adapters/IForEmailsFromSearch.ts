import { SortDirection } from "../shared/utils/DataManagment";
import { Filter } from "../shared/types/Filter";
import { EmailResponse } from "../shared/types/Email";

export interface IForEmailsFromSearch {
  fetchEmails(
    selectedProjects: string[],
    shareRootUrl: string,
    searchTerm: string,
    page: number,
    pageSize: number,
    nextLink: string | undefined,
    columnCode: string | undefined,
    sortColumn: string | undefined,
    sortDirection: SortDirection,
    filters: Filter[],
    resetPaging: boolean
  ): Promise<EmailResponse | undefined>;
}
