{
  "extends": "./node_modules/@microsoft/rush-stack-compiler-4.7/includes/tsconfig-web.json",
  "compilerOptions": {
    "target": "es5",
    "forceConsistentCasingInFileNames": true,
    "module": "esnext",
    "moduleResolution": "node",
    "jsx": "react",
    "declaration": true,
    "sourceMap": true,
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "outDir": "lib",
    "inlineSources": false,
    "noImplicitAny": true,
    "allowSyntheticDefaultImports": true, // Add this line
    "esModuleInterop": true, // Add this line to ensure module compatibility
    "resolveJsonModule": true, // Add this line
    "typeRoots": ["./node_modules/@types", "./node_modules/@microsoft"],
    "types": ["webpack-env"],
    "lib": ["es5", "dom", "es2015.collection", "es2015.promise"]
  },
  "include": ["src/**/*.ts", "src/**/*.tsx"],
  "exclude": ["src/vitest/**/*", "src/vitest/*"]
}
