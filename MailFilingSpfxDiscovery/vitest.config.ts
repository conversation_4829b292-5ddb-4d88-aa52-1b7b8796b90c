import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react";

//
export default defineConfig({
  plugins: [react()],
  test: {
    environment: "jsdom",
    setupFiles: "./src/setupTests.ts",
    // speed up since tests don't rely on css
    // https://github.com/vitest-dev/vitest/blob/main/examples/react-testing-lib/vite.config.ts#L14-L16
    css: false,
    include: ["./src/vitest/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
    globals: true,
    reporters: [
      "default", // Vitest's default reporter so that terminal output is still visible
    ],

    coverage: {
      reporter: ["text", "lcov", "html"],

      include: ["src/webparts/AtveroMailSpfxDiscovery/**/*", "src/shared/**/*"],
      exclude: ["**/*.scss.ts", "./src/adapters/**/*"],
    },
  },
});
