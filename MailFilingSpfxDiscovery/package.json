{"name": "atvero-mail-spfx-discovery", "version": "0.0.1", "private": true, "engines": {"node": ">=18.17.1 <19.0.0"}, "main": "lib/index.js", "scripts": {"build": "gulp bundle && gulp package-solution", "clean": "gulp clean", "test": "vitest run", "coverage": "vitest run --coverage", "mrproper": "rm -rf node_modules && rm package-lock.json && npm cache clean --force", "check:formatting": "npx prettier 'src/**/*.{js,ts,tsx,jsx}' --check"}, "dependencies": {"@fluentui/react": "^8.106.4", "@kenjiuno/msgreader": "^1.22.0", "@microsoft/sp-component-base": "1.20.0", "@microsoft/sp-core-library": "1.20.0", "@microsoft/sp-http": "^1.20.0", "@microsoft/sp-lodash-subset": "1.20.0", "@microsoft/sp-office-ui-fabric-core": "1.20.0", "@microsoft/sp-property-pane": "1.20.0", "@microsoft/sp-webpart-base": "1.20.0", "@pnp/common": "^2.15.0", "@pnp/graph": "^4.6.0", "@pnp/logging": "^4.6.0", "@pnp/odata": "^2.15.0", "@pnp/sp": "^4.6.0", "date-fns-tz": "^3.2.0", "email-addresses": "^5.0.0", "file-saver": "^2.0.5", "postal-mime": "^2.3.2", "react": "17.0.1", "react-dom": "17.0.1", "react-select": "^5.9.0", "tslib": "2.3.1"}, "devDependencies": {"@microsoft/eslint-config-spfx": "1.20.2", "@microsoft/eslint-plugin-spfx": "1.20.2", "@microsoft/microsoft-graph-types": "^2.40.0", "@microsoft/rush-stack-compiler-4.7": "0.1.0", "@microsoft/sp-build-web": "1.20.2", "@microsoft/sp-module-interfaces": "1.20.2", "@pnp/cli-microsoft365": "^10.0.0", "@rushstack/eslint-config": "4.0.1", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@types/date-fns": "^2.6.0", "@types/file-saver": "^2.0.7", "@types/react": "17.0.45", "@types/react-dom": "17.0.17", "@types/react-test-renderer": "17.0.1", "@types/testing-library__jest-dom": "^6.0.0", "@types/webpack-env": "~1.15.2", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "ajv": "^6.12.5", "eslint": "8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react-hooks": "4.3.0", "gulp": "4.0.2", "jsdom": "^24.1.0", "node-polyfill-webpack-plugin": "^4.0.0", "prettier": "^3.4.2", "react-select-event": "^5.5.1", "react-test-renderer": "17.0.1", "typescript": "4.7.4", "vitest": "^1.6.0", "vitest-sonar-reporter": "^2.0.0", "vitest-when": "^0.4.1"}}