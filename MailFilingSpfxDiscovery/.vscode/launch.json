{"version": "0.2.0", "configurations": [{"name": "Hosted workbench", "type": "msedge", "request": "launch", "url": "https://{tenantDomain}/_layouts/workbench.aspx", "webRoot": "${workspaceRoot}", "sourceMaps": true, "sourceMapPathOverrides": {"webpack:///.././src/*": "${webRoot}/src/*", "webpack:///../../../src/*": "${webRoot}/src/*", "webpack:///../../../../src/*": "${webRoot}/src/*", "webpack:///../../../../../src/*": "${webRoot}/src/*"}, "runtimeArgs": ["--remote-debugging-port=9222", "-incognito"]}]}