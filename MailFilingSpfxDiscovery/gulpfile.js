"use strict";

const build = require("@microsoft/sp-build-web");

const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");

build.addSuppression(
  `Warning - [sass] The local CSS class '-ms-flex' is not camelCase and will not be type-safe.`
);

const getTasks = build.rig.getTasks;
build.rig.getTasks = function () {
  const result = getTasks.call(build.rig);

  result.set("serve", result.get("serve-deprecated"));

  return result;
};

build.configureWebpack.mergeConfig({
  additionalConfiguration: (generatedConfiguration) => {
    generatedConfiguration.plugins.push(new NodePolyfillPlugin());
    generatedConfiguration.module.rules.push({
      test: /\.md$/,
      use: [
        {
          loader: "html-loader",
        },
        {
          loader: "markdown-loader",
        },
      ],
    });

    return generatedConfiguration;
  },
});

build.initialize(require("gulp"));
