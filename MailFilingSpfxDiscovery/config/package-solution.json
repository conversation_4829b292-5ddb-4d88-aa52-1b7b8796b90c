{"$schema": "https://developer.microsoft.com/json-schemas/spfx-build/package-solution.schema.json", "solution": {"name": "atvero-mail-spfx-discovery-client-side-solution", "id": "e7772fc0-a23d-45dc-ad4d-6601e5d60595", "version": "*******", "includeClientSideAssets": true, "skipFeatureDeployment": true, "isDomainIsolated": false, "developer": {"name": "", "websiteUrl": "", "privacyUrl": "", "termsOfUseUrl": "", "mpnId": "Undefined-1.20.0"}, "metadata": {"shortDescription": {"default": "AtveroMailSpfxDiscovery description"}, "longDescription": {"default": "AtveroMailSpfxDiscovery description"}, "screenshotPaths": [], "videoUrl": "", "categories": []}, "features": [{"title": "atvero-mail-spfx-discovery Feature", "description": "The feature that activates elements of the atvero-mail-spfx-discovery solution.", "id": "a36fcf03-a117-4216-8522-055a83d157e5", "version": "*******"}], "webApiPermissionRequests": [{"resource": "Atvero Mail", "scope": "user_impersonation"}, {"resource": "Windows Azure Active Directory", "scope": "User.Read"}, {"resource": "Microsoft Graph", "scope": "User.Read"}, {"resource": "Microsoft Graph", "scope": "Sites.Read.All"}, {"resource": "Microsoft Graph", "scope": "Contacts.Read"}, {"resource": "Microsoft Graph", "scope": "Files.ReadWrite.All"}, {"resource": "Microsoft Graph", "scope": "Group.Read.All"}, {"resource": "Microsoft Graph", "scope": "Mail.ReadWrite"}, {"resource": "Microsoft Graph", "scope": "ProfilePhoto.Read.All"}]}, "paths": {"zippedPackage": "solution/atvero-mail-spfx-discovery.sppkg"}}