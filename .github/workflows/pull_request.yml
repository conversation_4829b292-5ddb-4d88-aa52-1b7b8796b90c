name: Pull Request Install Build and Test

env:
  NODE_VERSION: "20.12.1"

on:
  pull_request:
    branches:
      - main
      - staging

permissions:
  actions: write
  checks: write
  contents: write
  pull-requests: write

jobs:
  discovery-build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Change Directory to MailFilingDiscovery
        run: cd MailFilingDiscovery

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingDiscovery

      - name: Build Prep
        run: npm run build:prep
        working-directory: MailFilingDiscovery

      - name: Build
        run: npm run build
        working-directory: MailFilingDiscovery

      - name: Test
        run: npm run test
        working-directory: MailFilingDiscovery

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingDiscovery

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.DISCOVERY_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingDiscovery
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-discovery
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.branch.name="${{ github.event.pull_request.head.ref }}" 
            -Dsonar.branch.target="${{ github.event.pull_request.base.ref }}"
            -Dsonar.test.inclusions=src/**/*.test.ts,src/**/*.test.tsx
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.qualitygate.wait=true
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=*.config.js,src/main.tsx,**/*.d.ts,src/**/*.test.ts,src/**/*.test.tsx,src/**/*.test.js,src/**/*.test.jsx,src/adapters/**,src/App.tsx

  filing-build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Change Directory to MailFilingPlugin
        run: cd MailFilingPlugin

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingPlugin

      - name: Build Prep
        run: npm run build:prep
        working-directory: MailFilingPlugin

      - name: Test Config
        run: npm run test-config
        working-directory: MailFilingPlugin

      - name: Build
        run: npm run build
        working-directory: MailFilingPlugin

      - name: Test
        run: npm run test
        working-directory: MailFilingPlugin

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingPlugin

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.FILING_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingPlugin
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-filing
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.branch.name="${{ github.event.pull_request.head.ref }}" 
            -Dsonar.branch.target="${{ github.event.pull_request.base.ref }}"
            -Dsonar.test.inclusions=src/**/*.test.js,src/**/*.test.jsx
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.qualitygate.wait=true
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=*.config.js,src/main.tsx",**/*.d.ts,src/**/*.test.ts,src/**/*.test.tsx,src/**/*.test.js,src/**/*.test.jsx,src/taskpane/adapters/**/*
            -Dsonar.exclusions=*.config.js,src/taskpane/index.tsx,**/*.d.ts,src/**/*.test.ts,src/**/*.test.tsx,src/**/*.test.js,src/**/*.test.jsx,src/taskpane/adapters/**/*

  admin-build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Change Directory to MailFilingAdmin
        run: cd MailFilingAdmin

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingAdmin

      - name: Build Prep
        run: npm run build:prep
        working-directory: MailFilingAdmin

      - name: Build
        run: npm run build
        working-directory: MailFilingAdmin

      - name: Test
        run: npm run test
        working-directory: MailFilingAdmin

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingAdmin

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.ADMIN_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingAdmin
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-admin
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.branch.name="${{ github.event.pull_request.head.ref }}" 
            -Dsonar.branch.target="${{ github.event.pull_request.base.ref }}"
            -Dsonar.test.inclusions=src/**/*.test.js,src/**/*.test.jsx
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=*.config.js,src/main.tsx,**/*.d.ts,tests/**/*.ts,tests/**/*.tsx,src/adapters/**,src/services/backendCall/backendCall.ts
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.qualitygate.wait=true

  bootstrap-build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Change Directory to MailFilingBootstrap
        run: cd MailFilingBootstrap

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingBootstrap
      - name: Build Prep
        run: npm run build:prep
        working-directory: MailFilingBootstrap

      - name: Build
        run: npm run build
        working-directory: MailFilingBootstrap

      - name: Test
        run: npm run test
        working-directory: MailFilingBootstrap

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingBootstrap

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.BOOTSTRAP_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingBootstrap
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-bootstrap
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.branch.name="${{ github.event.pull_request.head.ref }}" 
            -Dsonar.branch.target="${{ github.event.pull_request.base.ref }}"
            -Dsonar.qualitygate.wait=true
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.test.inclusions=tests/**/*.test.ts,tests/**/*.test.tsx
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=src/routes/**/*,src/routeTree.gen.ts,src/main.tsx,**/*.d.ts,src/adapters/**,src/api/**,tests/**/*.ts,tests/**/*.tsx

  spfx-build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Change Directory to MailFilingSpfxDiscovery
        run: cd MailFilingSpfxDiscovery

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingSpfxDiscovery

      - name: Build
        run: npm run build
        working-directory: MailFilingSpfxDiscovery

      - name: Make release
        # Make release checks that the build runs clean, a clean build is needed
        # to make a release later.
        run: ./make_release.sh 0.0.0.0
        working-directory: MailFilingSpfxDiscovery

      - name: Test
        run: npm run test
        working-directory: MailFilingSpfxDiscovery

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingSpfxDiscovery

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.SPFX_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingSpfxDiscovery
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-discovery-spfx
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.branch.name="${{ github.event.pull_request.head.ref }}" 
            -Dsonar.branch.target="${{ github.event.pull_request.base.ref }}"
            -Dsonar.test.inclusions=src/**/*.test.ts,src/**/*.test.tsx
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.qualitygate.wait=true
            -Dsonar.coverage.exclusions=src/**/*.test.ts,src/**/*.test.tsx,
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=*.config.js,src/main.tsx,**/*.d.ts,src/**/*.test.ts,src/**/*.test.tsx,src/**/*.test.js,src/adapters/*.ts,src/adapters/*.tsx,src/adapters/*.js
            -Dsonar.qualitygate.wait=true
