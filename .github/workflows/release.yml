name: Deploy to production

env:
  NODE_VERSION: "20.12.1"

on:
  release:
    types:
      - created

permissions:
  checks: write
  contents: write
  pull-requests: write

jobs:
  deploy_discovery:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Change Directory to MailFilingDiscovery
        run: cd MailFilingDiscovery

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingDiscovery

      - name: Release Config
        id: create-json
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingDiscovery/config.json"
          json: |
            {                
              "version": "${{ github.event.release.tag_name }}",
              "product": "cmap",
              "appid": "${{vars.DISCOVERY_CLIENT_ID}}",
              "redirectUrl": "${{ vars.CMAPMAIL_DISCOVERY_REDIRECT_URI }}",
              "backendUrl": "${{ vars.FILING_APP_BACKEND_URL }}",
              "backendScope": "${{ vars.FILING_APP_ADDRESS}}"              
            }
      - name: Show config
        run: cat config.json
        working-directory: MailFilingDiscovery

      - name: Release Build
        run: npm run build
        working-directory: MailFilingDiscovery

      - name: Build And Deploy
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.DISCOVERY_DEPOYMENT_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          skip_app_build: true
          ###### Repository/Build Configurations ######
          app_location: "MailFilingDiscovery/dist" # App source code path relative to repository root
        env: 
          VITE_CLIENT_ID: ${{ vars.VITE_CLIENT_ID }}
          VITE_REDIRECT_URI: ${{ vars.VITE_REDIRECT_URI }}

  deploy_plugin:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Change Directory to MailFilingPlugin
        run: cd MailFilingPlugin

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingPlugin

      - name: Generate Atvero Mail Manifest config
        run: |
          echo "{\"productName\":\"Atvero\", \"id\":\"f91922be-8587-4565-8f11-ee5353a1066c\",\"fosid\":\"6e8d76ab-d4a6-4bdf-8106-ac6b3e51216f\",\"environment\": \"\", \"version\": \"${{ github.event.release.tag_name }}\",\"appid\": \"${{ vars.FILING_APP_ID }}\",\"server\": \"${{ vars.FILING_APP_ADDRESS}}\",\"discovery\": \"https://discovery.atveromail.com\"}" > manifest/prod.json
        working-directory: MailFilingPlugin

      - name: Generate Atvero Mail manifest file
        run: npx ejs-cli -f manifest/manifest.xml.ejs  -O manifest/prod.json > manifest.xml  
        working-directory: MailFilingPlugin

      - name: Generate Atvero Mail File On Send manifest file
        run: npx ejs-cli -f manifest/manifest-with-file-on-send.xml.ejs  -O manifest/prod.json > manifest-file-on-send.xml  
        working-directory: MailFilingPlugin

      - name: Generate CMap Mail file on send manifest config file
        id: create-json-usa
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingPlugin/manifest/prod.json"
          json: |
            {
              "productName":"CMap", 
              "id":"eb5519f5-f193-4aaa-8db4-9cebfe9ef2c2",
              "fosid":"d93dbdb4-6993-4c69-a730-ca3222c009ad",
              "environment": "", 
              "version": "${{ github.event.release.tag_name }}",
              "appid": "${{ vars.CMAPMAIL_CLIENT_ID }}",
              "server": "${{ vars.CMAPMAIL_FILING_APP_ADDRESS}}",
              "discovery": "https://discovery.cmapmail.com"
            }
      - name: Show manifest config
        run: cat manifest/prod.json
        working-directory: MailFilingPlugin

      - name: Generate CMap Mail manifest file
        run: npx ejs-cli -f manifest/manifest.xml.ejs  -O manifest/prod.json > cmapmail.xml  
        working-directory: MailFilingPlugin
      - name: Generate CMap Mail File On Send manifest file
        run: npx ejs-cli -f manifest/manifest-with-file-on-send.xml.ejs  -O manifest/prod.json > cmapmail-file-on-send.xml  
        working-directory: MailFilingPlugin
        
      - name: Generate CMap Mail US1 manifest config file
        id: create-json
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingPlugin/manifest/us1-prod.json"
          json: |
            {
              "productName":"CMap", 
              "id": "50b8abc3-37de-4dee-9ab0-372f0c20060f",
              "fosid": "2eebd04f-dd05-401a-a4ac-b9b658fd215d",
              "environment": "", 
              "version": "${{ github.event.release.tag_name }}",
              "appid": "${{ vars.CMAPMAIL_CLIENT_ID }}",
              "server": "${{ vars.CMAPMAIL_FILING_APP_ADDRESS}}",
              "discovery": "https://discovery.cmapmail.com"
            }
      - name: Show manifest config
        run: cat manifest/us1-prod.json
        working-directory: MailFilingPlugin

      - name: Generate CMap Mail US1 manifest file
        run: npx ejs-cli -f manifest/manifest-us1.xml.ejs  -O manifest/us1-prod.json > cmapmail-us1.xml
        working-directory: MailFilingPlugin

      - name: Generate CMap Mail US1 File On Send manifest file
        run: npx ejs-cli -f manifest/manifest-us1-file-on-send.xml.ejs  -O manifest/us1-prod.json > cmapmail-us1-file-on-send.xml
        working-directory: MailFilingPlugin

      - name: Generate Production Config
        id: create-json-config
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingPlugin/config.json"
          json: |
            {
              "version": "${{ github.event.release.tag_name }}",
              "atvero": {
                "product": "cmap",
                "id":"462936ca-5551-407f-977e-4f5f26dbe0f4",
                "appId": "${{vars.FILING_APP_ID}}",
                "backendUrl": "${{ vars.FILING_APP_BACKEND_URL }}",
                "backendScope": "${{ vars.FILING_APP_ADDRESS}}",
                "discovery": "https://discovery.atveromail.com"
              },
              "cmap": {
                "product": "cmap",
                "appId": "${{vars.CMAPMAIL_CLIENT_ID}}",
                "backendUrl": "${{vars.FILING_APP_BACKEND_URL}}",
                "backendScope": "${{ vars.CMAPMAIL_FILING_APP_ADDRESS}}",
                "discovery": "${{ vars.CMAPMAIL_DISCOVERY_REDIRECT_URI}}"
              },
              "cmap_us1": {
                "product": "cmap",
                "appId": "${{vars.CMAPMAIL_CLIENT_ID}}",
                "backendUrl": "${{vars.CMAPMAIL_BACKEND_URL_US1}}",
                "backendScope": "${{ vars.CMAPMAIL_FILING_APP_ADDRESS}}",
                "discovery": "${{ vars.CMAPMAIL_DISCOVERY_REDIRECT_URI}}"
              }
            }
      - name: Show config
        run: cat config.json
        working-directory: MailFilingPlugin

      - name: Release Build
        run: npm run build
        working-directory: MailFilingPlugin

      - name: Build And Deploy
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.PLUGIN_DEPLOYMENT_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          skip_app_build: true
          ###### Repository/Build Configurations ######
          app_location: "MailFilingPlugin/dist/" # App source code path relative to repository root

  deploy_admin:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Change Directory to MailFilingAdmin
        run: cd MailFilingAdmin

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingAdmin

      - name: Generate Config
        id: create-json
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingAdmin/config.json"
          json: |
            {
              "version": "${{ github.event.release.tag_name }}",
              "product": "cmap",
              "appid": "${{vars.DISCOVERY_CLIENT_ID}}",
              "redirectUrl": "${{ vars.CMAPMAIL_ADMIN_REDIRECT_URL }}",
              "backendUrl": "${{ vars.FILING_APP_BACKEND_URL }}",
              "backendScope": "${{ vars.FILING_APP_ADDRESS}}"
            }
      - name: Show config
        run: cat config.json
        working-directory: MailFilingAdmin

      - name: Release Build
        run: npm run build
        working-directory: MailFilingAdmin

      - name: Build And Deploy
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.ADMIN_DEPLOYMENT_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          skip_app_build: true
          ###### Repository/Build Configurations ######
          app_location: "MailFilingAdmin/dist" # App source code path relative to repository root
        env: 
          VITE_CLIENT_ID: ${{ vars.VITE_CLIENT_ID }}
          VITE_REDIRECT_URI: ${{ vars.VITE_REDIRECT_URI }}

  deploy_install:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Change Directory to MailFilingBootstrap
        run: cd MailFilingBootstrap

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingBootstrap

      - name: Release Config
        id: create-json
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingBootstrap/config.json"
          json: |
            {
              "version": "${{ github.event.release.tag_name }}",
              "product": "cmap",
              "appid": "${{vars.DISCOVERY_CLIENT_ID}}",
              "redirectUrl": "${{ vars.CMAPMAIL_INSTALL_REDIRECT_URL }}",
              "backendUrl": "${{ vars.FILING_APP_BACKEND_URL }}",
              "backendScope": "${{ vars.FILING_APP_ADDRESS}}"
            }

      - name: Show config
        run: cat config.json
        working-directory: MailFilingBootstrap


      - name: Release Build
        run: npm run build
        working-directory: MailFilingBootstrap

      - name: Build And Deploy
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.INSTALL_DEPLOYMENT_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          skip_app_build: true
          ###### Repository/Build Configurations ######
          app_location: "MailFilingBootstrap/dist" # App source code path relative to repository root
        env: 
          VITE_CLIENT_ID: ${{ vars.VITE_CLIENT_ID }}
          VITE_REDIRECT_URI: ${{ vars.VITE_REDIRECT_URI }}

  deploy_spfx_discovery:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingSpfxDiscovery

      - name: Release Config
        run: |
          echo "{\"appid\":\"${{vars.DISCOVERY_CLIENT_ID}}\",\"backendScope\": \"${{ vars.FILING_APP_ADDRESS}}\",\"backendUrl\": \"${{ vars.FILING_APP_BACKEND_URL }}\", \"version\": \"${{ github.event.release.tag_name }}\",\"redirectUrl\": \"${{ vars.INSTALL_REDIRECT_URI }}\"}" > config.json
          echo Build with the following config:
          cat config.json
        working-directory: MailFilingSpfxDiscovery


      - name: Release Build
        run:  ./make_release.sh ${{ github.event.release.tag_name }}
        working-directory: MailFilingSpfxDiscovery

      - name: "Compressing output"
        run: |  
          mkdir atveromail-${{ github.event.release.tag_name }}
          mv sharepoint/solution/atvero-mail-spfx-discovery.sppkg atveromail-${{ github.event.release.tag_name }}
          zip -9r atveromail-${{ github.event.release.tag_name }}.zip atveromail-${{ github.event.release.tag_name }}
        working-directory: MailFilingSpfxDiscovery

      # Much magic going on here, the upload url is constructed from the
      # the parameters of the release event
      - name: Upload binaries to release
        uses: svenstaro/upload-release-action@v1-release
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: MailFilingSpfxDiscovery/atveromail-${{ github.event.release.tag_name }}.zip
          asset_name: atveromail-${{ github.event.release.tag_name }}.zip
          tag: ${{ github.ref }}

      # using similar to the above, push the binary to a location for the CS team
      - name: Upload release binaries to Netlify
        uses: MrFlynn/upload-to-netlify-action@v3
        with:
          source-file: ./MailFilingSpfxDiscovery/atveromail-${{ github.event.release.tag_name }}.zip
          destination-path: mail/atveromail-${{ github.event.release.tag_name }}.zip
          site-name: atvero-releases
          branch-name: ${{ github.head_ref }}
          netlify-token: ${{ secrets.NETLIFY_TOKEN }}

      - name: Generate html file
        run: |
          echo "<html><body><h1>Atvero Mail PIM ${{ github.event.release.tag_name }}</h1><ul>" > index.html
          echo "<li><a href=\"./mail/atveromail-${{ github.event.release.tag_name }}.zip\">Atvero Mail PIM Spfx</a></li>" >> index.html
          echo "</ul></body></html>" >> index.html   
      - name: Upload release binaries to Netlify
        uses: MrFlynn/upload-to-netlify-action@v3
        with:
          source-file: ./index.html
          destination-path: mail/index.html
          site-name: atvero-releases
          branch-name: ${{ github.head_ref }}
          netlify-token: ${{ secrets.NETLIFY_TOKEN }}
