name: Push to main Install Build and Test

env:
  NODE_VERSION: "20.12.1"

on:
  push:
    branches:
      - main

permissions:
  checks: write
  contents: write
  pull-requests: write

jobs:
  discovery-build-and-test:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Change Directory to MailFilingDiscovery
        run: cd MailFilingDiscovery

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingDiscovery

      - name: Build Prep
        run: npm run build:prep
        working-directory: MailFilingDiscovery

      - name: Preprod Config
        id: create-json
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingDiscovery/config.json"
          json: |
            {                
              "version": "*******",
              "product": "atvero",
              "appid": "${{vars.STAGE_APP_ID}}",
              "redirectUrl": "https://purple-grass-05f1d0c03.5.azurestaticapps.net",
              "backendUrl": "${{ vars.STAGE_APP_BACKEND_URL }}",
              "backendScope": "${{ vars.STAGE_FILING_APP_ADDRESS}}" 
            }

      - name: Show config
        run: cat config.json
        working-directory: MailFilingDiscovery

      - name: Build
        run: npm run build
        working-directory: MailFilingDiscovery

      - name: Test
        run: npm run test
        working-directory: MailFilingDiscovery

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingDiscovery

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.DISCOVERY_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingDiscovery
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-discovery
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.test.inclusions=src/**/*.test.ts,src/**/*.test.tsx
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.coverage.exclusions=src/**/*.test.ts,src/**/*.test.tsx
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=src/main.tsx,**/*.d.ts,src/**/*.test.ts,src/**/*.test.tsx,src/**/*.test.js,src/**/*.test.jsx,src/adapters/**

      - name: Build And Deploy
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.CMAPMAIL_DISCOVERY_PREPROD_DEPLOY }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          skip_app_build: true
          ###### Repository/Build Configurations ######
          app_location: "MailFilingDiscovery/dist" # App source code path relative to repository root

  filing-build-and-test:
    environment: production
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Change Directory to MailFilingPlugin
        run: cd MailFilingPlugin

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingPlugin

      - name: Build Prep
        run: npm run build:prep
        working-directory: MailFilingPlugin

      - name: Build
        run: npm run build
        working-directory: MailFilingPlugin

      - name: Test
        run: npm run test
        working-directory: MailFilingPlugin

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingPlugin

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.FILING_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingPlugin
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-filing
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.test.inclusions=src/**/*.test.js,src/**/*.test.jsx
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=src/main.tsx",**/*.d.ts,src/**/*.test.ts,src/**/*.test.tsx,src/**/*.test.js,src/**/*.test.jsx,src/taskpane/adapters/**/*

      - name: Generate CMap Mail manifest config file
        id: create-json
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingPlugin/manifest/stage.json"
          json: |
            {
              "productName":"CMap", 
              "id":"3ec697cb-0722-443a-a687-c5f185ce8138",
              "fosid":"a7bab182-e1fb-434c-b74b-b6bdb856bd2f",
              "environment": "", 
              "version": "${{ github.event.release.tag_name }}"
              "appid": "${{ vars.CMAPMAIL_CLIENT_ID }}",
              "server": "${{ vars.CMAPMAIL_FILING_APP_ADDRESS}}",
              "discovery": "https://discovery-preprod.atveromail.com"}"
            }
      - name: Show manifest config
        run: cat manifest/stage.json
        working-directory: MailFilingPlugin

      - name: Generate CMap Mail manifest file
        run: npx ejs-cli -f manifest/manifest.xml.ejs  -O manifest/stage.json > cmapmail.xml
        working-directory: MailFilingPlugin

      - name: Generate CMap mailfile on send manifest file
        run: npx ejs-cli -f manifest/manifest-with-file-on-send.xml.ejs  -O manifest/stage.json > cmapmail-with-file-on-send.xml
        working-directory: MailFilingPlugin

      - name: Preprod Config
        id: create-json-config
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingPlugin/config.json"
          json: |
            {
              "version": "${{ github.event.release.tag_name }}",
              "atvero": {
                "product": "cmap",
                "appId": "${{vars.STAGE_APP_ID}}",
                "redirectUrl": "https://purple-grass-05f1d0c03.5.azurestaticapps.net",
                "backendUrl": "${{ vars.STAGE_APP_BACKEND_URL }}",
                "backendScope": "${{ vars.STAGE_FILING_APP_ADDRESS}}",
                "discovery": "https://purple-grass-05f1d0c03.5.azurestaticapps.net"
              },
              "cmap": {
                "product": "cmap",
                "appId": "${{vars.CMAPMAIL_CLIENT_ID}}",
                "redirectUrl": "https://filing-preprod.cmapmail.com",
                "backendUrl": "${{vars.CMAPMAIL_STAGE_BACKEND_URL}}",
                "backendScope": "${{ vars.CMAPMAIL_FILING_APP_ADDRESS}}",
                "discovery": "https://discovery-preprod.cmapmail.com"
              },
              "cmap_us1": {
                "product": "cmap",
                "appId": "${{vars.CMAPMAIL_CLIENT_ID}}",
                "redirectUrl": "https://filing-preprod.cmapmail.com",
                "backendUrl": "${{vars.CMAPMAIL_STAGE_BACKEND_URL}}",
                "backendScope": "${{ vars.CMAPMAIL_FILING_APP_ADDRESS}}",
                "discovery": "https://discovery-preprod.cmapmail.com"
              }
            }
      - name: Show config
        run: cat config.json
        working-directory: MailFilingPlugin

      - name: Release Build
        run: npm run build
        working-directory: MailFilingPlugin

      - name: Build And Deploy
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.CMAPMAIL_FILING_PREPROD_DEPLOY }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          skip_app_build: true
          ###### Repository/Build Configurations ######
          app_location: "MailFilingPlugin/dist/" # App source code path relative to repository root

  admin-build-and-test:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Change Directory to MailFilingAdmin
        run: cd MailFilingAdmin

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingAdmin

      - name: Build Prep
        run: npm run build:prep
        working-directory: MailFilingAdmin

      - name: Preprod Config
        id: create-json
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingAdmin/config.json"
          json: |
            {
              "version": "${{ github.event.release.tag_name }}",
              "product": "atvero",
              "appid": "${{vars.STAGE_APP_ID}}",
              "redirectUrl": "https://purple-grass-05f1d0c03.5.azurestaticapps.net",
              "backendUrl": "${{ vars.STAGE_APP_BACKEND_URL }}",
              "backendScope": "${{ vars.STAGE_FILING_APP_ADDRESS}}",
              "discovery": "https://purple-grass-05f1d0c03.5.azurestaticapps.net"  
            }

      - name: Show config
        run: cat config.json
        working-directory: MailFilingAdmin

      - name: Build
        run: npm run build
        working-directory: MailFilingAdmin

      - name: Test
        run: npm run test
        working-directory: MailFilingAdmin

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingAdmin

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.ADMIN_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingAdmin
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-admin
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.test.inclusions=src/**/*.test.js,src/**/*.test.jsx
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=src/**/index.tsx,src/main.tsx",**/*.d.ts,src/**/*.test.ts,src/**/*.test.tsx,src/**/*.test.js,src/**/*.test.jsx,tests/**/*.test.ts,tests/**/*.test.tsx,src/services/backendCall/backendCall.ts

      - name: Build And Deploy
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.CMAPMAIL_ADMIN_PREPROD_DEPLOY }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          skip_app_build: true
          ###### Repository/Build Configurations ######
          app_location: "MailFilingAdmin/dist" # App source code path relative to repository root

  bootstrap-build-and-test:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Change Directory to MailFilingBootstrap
        run: cd MailFilingBootstrap

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingBootstrap

      - name: Build Prep
        run: npm run build:prep
        working-directory: MailFilingBootstrap

      - name: Preprod Config
        id: create-json
        uses: jsdaniell/create-json@v1.2.3
        with:
          name: "./MailFilingBootstrap/config.json"
          json: |
            {
              "version": "${{ github.event.release.tag_name }}",
              "product": "atvero",
              "appid": "${{vars.STAGE_APP_ID}}",
              "redirectUrl": "https://purple-grass-05f1d0c03.5.azurestaticapps.net",
              "backendUrl": "${{ vars.STAGE_APP_BACKEND_URL }}",
              "backendScope": "${{ vars.STAGE_FILING_APP_ADDRESS}}",
              "discovery": "https://purple-grass-05f1d0c03.5.azurestaticapps.net"
            }

      - name: Show config
        run: cat config.json
        working-directory: MailFilingBootstrap

      - name: Build
        run: npm run build
        working-directory: MailFilingBootstrap

      - name: Test
        run: npm run test
        working-directory: MailFilingBootstrap

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingBootstrap

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.BOOTSTRAP_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingBootstrap
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-bootstrap
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.test.inclusions=tests/**/*.test.ts,tests/**/*.test.tsx
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=src/routes/**/*,src/routeTree.gen.ts,src/main.tsx,**/*.d.ts,src/adapters/**,src/api/**,tests/**/*.ts,tests/**/*.tsx

      - name: Build And Deploy
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.CMAPMAIL_INSTALL_PREPROD_DEPLOY }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          skip_app_build: true
          ###### Repository/Build Configurations ######
          app_location: "MailFilingBootstrap/dist" # App source code path relative to repository root
  spfx-build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Change Directory to MailFilingSpfxDiscovery
        run: cd MailFilingSpfxDiscovery

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: Install Dependencies
        run: npm install
        working-directory: MailFilingSpfxDiscovery

      - name: Build
        run: npm run build
        working-directory: MailFilingSpfxDiscovery

      - name: Test
        run: npm run test
        working-directory: MailFilingSpfxDiscovery

      - name: Coverage
        run: npm run coverage
        working-directory: MailFilingSpfxDiscovery

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.SPFX_SONARCLOUD_TOKEN }}
        with:
          projectBaseDir: MailFilingSpfxDiscovery
          args: >
            -Dsonar.organization=cmap-vsts  
            -Dsonar.projectKey=cmap-vsts_atvero-atveromailfrontend-discovery-spfx
            -Dsonar.sources=.
            -Dsonar.host.url=https://sonarcloud.io
            -Dsonar.test.inclusions=src/**/*.test.ts,src/**/*.test.tsx
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.qualitygate.wait=true
            -Dsonar.coverage.exclusions=src/**/*.test.ts,src/**/*.test.tsx
            -Dsonar.coverage.inclusions=src/**/*.ts,src/**/*.tsx,src/**/*.js,src/**/*.jsx
            -Dsonar.coverage.exclusions=*.config.js,src/main.tsx,**/*.d.ts,src/**/*.test.ts,src/**/*.test.tsx,src/**/*.test.js,src/adapters/*.ts,src/adapters/*.tsx,src/adapters/*.js

      - name: Deploy to atverodev.
        run: |
          cd MailFilingSpfxDiscovery
          npx m365 login --authType password --userName ${{ secrets.ATVERODEV_DEPLOY_USER }} --password ${{ secrets.ATVERODEV_DEPLOY_PASSWORD }} --appId ${{ secrets.ATVERODEV_APP_ID}} --tenant ${{ secrets.ATVERODEV_TENANT}}
          npx m365 spo app add -p sharepoint/solution/atvero-mail-spfx-discovery.sppkg --overwrite
          npx m365 spo app deploy --name atvero.sppkg --appCatalogScope tenant --appCatalogUrl https://atverodevs.sharepoint.com/sites/AppCatalog  --skipFeatureDeployment
