name: Enforce Branch Protection

on:
  pull_request:
    branches:
      - main
      - staging

jobs:
  enforce-branch-policy:
    runs-on: ubuntu-latest
    steps:
      - name: Check if PR targets the staging branch
        if: github.event.pull_request.base.ref == 'staging'
        run: |
          if [[ "${{ github.event.pull_request.head.ref }}" != "main" ]]; then
            echo "Only the main branch can be merged into staging."
            exit 1
          fi

      - name: Check if PR targets the main branch
        if: github.event.pull_request.base.ref == 'main'
        run: |
          if [[ "${{ github.event.pull_request.head.ref }}" == "staging" ]]; then
            echo "Only branches other than staging can be merged into main."
            exit 1
          fi
