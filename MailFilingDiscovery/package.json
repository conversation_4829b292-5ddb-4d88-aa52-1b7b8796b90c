{"name": "@atvero/mailfiling-discovery", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm run dev-config && vite", "build": "tsc && vite build", "dev-config": "cp config/dev.json config.json", "test-config": "cp config/test.json config.json", "build:prep": "npm run dev-config", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "npm run test-config && vitest", "mrproper": "rm -rf node_modules && rm package-lock.json && npm cache clean --force", "coverage": "vitest run --coverage", "check:formatting": "npx prettier 'src/**/*.{js,ts,tsx,jsx}' --check", "fix:formatting": "npx prettier 'src/**/*.{js,ts,tsx,jsx}' --write"}, "dependencies": {"@azure/msal-browser": "^3.17.0", "@azure/msal-react": "^2.0.19", "@fluentui/react-components": "^9.54.2", "@fluentui/react-datepicker-compat": "^0.4.45", "@fluentui/react-icons": "^2.0.249", "@kenjiuno/msgreader": "^1.22.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@microsoft/microsoft-graph-types": "^2.40.0", "@microsoft/office-js": "^1.1.93", "@pnp/common": "^2.15.0", "@pnp/graph": "^4.2.0", "@pnp/logging": "^4.2.0", "@pnp/msaljsclient": "^4.2.0", "@pnp/odata": "^2.15.0", "@pnp/sp": "^4.3.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "^8.0.1", "@types/file-saver": "^2.0.7", "@types/react-virtualized-auto-sizer": "^1.0.4", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "email-addresses": "^5.0.0", "file-saver": "^2.0.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "mailparser": "^3.7.1", "react": "^17.0.1", "react-dom": "^17.0.1", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.10", "react-window-infinite-loader": "^1.0.9", "setimmediate": "^1.0.5", "vite-plugin-node-polyfills": "^0.22.0"}, "devDependencies": {"@azure/static-web-apps-cli": "^2.0.1", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.7", "@types/mailparser": "^3.4.4", "@types/node": "^22.3.0", "@types/react": "^17", "@types/react-dom": "^17", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^2.0.5", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "jsdom": "^24.1.0", "prettier": "3.5.3", "typescript": "^4.7.0", "vite": "^5.2.0", "vite-plugin-static-copy": "^1.0.6", "vitest": "^2.0.5"}}