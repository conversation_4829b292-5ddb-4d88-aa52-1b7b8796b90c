import { useState, useEffect } from "react";
import { ISharepointAdapter } from "./adapters/ISharepointAdapter";
import { SharepointAdapter, options } from "./adapters/SharepointAdapter";
import { spfi, SPBrowser } from "@pnp/sp";
import { MSAL } from "@pnp/msaljsclient";
import AppInitializer from "./AppInitializer";
import { AtveroIcon } from "./components/AtveroIcon";
import { useGraph } from "./adapters/hooks/useGraphAPI";
import { Hubsite } from "./shared/types/Hubsite";
import { MsalAuthenticationTemplate, MsalProvider } from "@azure/msal-react";
import { InteractionType } from "@azure/msal-browser";
import { msalInstance } from "./adapters/SharepointAdapter";

import {
  Button,
  makeStyles,
  Spinner,
  tokens,
} from "@fluentui/react-components";
import { IBackendAdapter } from "./adapters/IBackendAdapter";
import { BackendAdapter } from "./adapters/BackendAdapter";

const useStyles = makeStyles({
  loginContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100vh",
    width: "100vw",
    backgroundColor: tokens.colorNeutralBackground2,
  },
  loaderContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: "100vh",
    flexDirection: "column",
    width: "100vw",
    backgroundColor: tokens.colorNeutralBackground2,
  },
  errorHeading: {
    marginBottom: "16px",
    color: tokens.colorNeutralForeground1,
  },
  errorMessage: {
    marginBottom: "24px",
    marginTop: "0px",
    textAlign: "center",
    color: tokens.colorNeutralForeground2,
  },
  spinner: {
    marginTop: "10px",
  },
  popupHelp: {
    position: "absolute",
    bottom: "20px",
    left: "50%",
    transform: "translateX(-50%)",
    color: tokens.colorNeutralForeground3,
    fontSize: "12px",
    textAlign: "center",
    width: "100%",
    padding: "0 20px",
  },
});

const AppWrapper: React.FC = () => {
  const { graph, loginError, manualLogin, isFirstAttempt } = useGraph();
  const [sharepointAdapter, setSharepointAdapter] = useState<
    ISharepointAdapter | undefined
  >();
  const [selectedHubsite, setSelectedHubsite] = useState<Hubsite | undefined>();
  const backendAdapter: IBackendAdapter = new BackendAdapter();

  useEffect(() => {
    const setupAdapters = async () => {
      const updatedOptions = { ...options };

      if (graph) {
        let sharepointHost: string | undefined;

        if (selectedHubsite !== undefined) {
          sharepointHost = "https://" + new URL(selectedHubsite.url).host;
        } else {
          const siteInfo = await graph?.sites.getById("root")();
          // We make a graph call here as this is required before adapters have been created
          const url = siteInfo?.webUrl;
          sharepointHost = `${url}`;
        }

        if (sharepointHost) {
          updatedOptions.authParams.scopes = [`${sharepointHost}/.default`];
          updatedOptions.authParams.forceRefresh = true;
          const sharepointInstance = spfi(sharepointHost).using(
            SPBrowser(),
            MSAL(updatedOptions)
          );
          const sharepointAdapter = new SharepointAdapter(
            graph,
            sharepointInstance,
            sharepointHost
          );
          setSharepointAdapter(sharepointAdapter);
        }
      }
    };

    setupAdapters();
  }, [graph, selectedHubsite]);

  const styles = useStyles();

  if (loginError) {
    const isPopupError = loginError?.includes("popup_window_error");

    return (
      <div className={styles.loginContainer}>
        <AtveroIcon />
        <h2 className={styles.errorHeading}>
          {isFirstAttempt
            ? "Logging you in..."
            : isPopupError
              ? "Please enable Pop-ups"
              : "Unable to login"}
        </h2>
        <p className={styles.errorMessage}>
          {isFirstAttempt ? (
            "Attempting to authenticate..."
          ) : isPopupError ? (
            "Please enable pop-ups in your browser to continue with the sign in process."
          ) : (
            <>
              We encountered an error while trying to log you in.
              <br />
              Please refresh this page or contact your local Mail support admin
              with the following error message:
              <br />
              {loginError}
            </>
          )}
        </p>
        {!isFirstAttempt && isPopupError && (
          <p className={styles.popupHelp} data-testid="popup-help">
            To enable pop-ups: Click the blocked pop-up icon in your browser's
            address bar or navigate to your pop-ups options in your browsers
            settings and select 'Always allow'.
          </p>
        )}
        {!isFirstAttempt && <Button onClick={manualLogin}>Login</Button>}
      </div>
    );
  }

  if (!sharepointAdapter) {
    return (
      <div className={styles.loaderContainer}>
        <AtveroIcon />
        <Spinner
          className={styles.spinner}
          size="tiny"
          label="Loading Mail..."
        />
      </div>
    );
  }

  return (
    <MsalProvider instance={msalInstance}>
      <MsalAuthenticationTemplate interactionType={InteractionType.Redirect}>
        <AppInitializer
          selectedHubsite={selectedHubsite}
          setSelectedHubsite={(hubsite: Hubsite | undefined) => {
            if (hubsite === undefined && selectedHubsite !== undefined)
              setSelectedHubsite(hubsite);
            else {
              if (hubsite?.url === selectedHubsite?.url) return;
              setSelectedHubsite(hubsite);
            }
          }}
          sharepointAdapter={sharepointAdapter}
          backendAdapter={backendAdapter}
        />
      </MsalAuthenticationTemplate>
    </MsalProvider>
  );
};

export default AppWrapper;
