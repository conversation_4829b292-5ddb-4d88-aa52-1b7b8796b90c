import { Project } from "../shared/types/Project";
import { Hubsite } from "../shared/types/Hubsite";
import {
  Drive,
  EntityType,
  Group,
  ListItem,
  NullableOption,
  SearchResponse,
  Site,
} from "@microsoft/microsoft-graph-types";
import {
  IRenderListDataAsStreamResult,
  IRenderListDataParameters,
} from "@pnp/sp/lists";
import { Email } from "../shared/types/Email";

export interface ISharepointAdapter {
  // Project-related methods
  getProject(key: string, value: string, pathHubsite: string): Promise<Project>;
  getProjects(hubsite: Hubsite, searchTerm: string): Promise<Project[]>;

  // Search-related methods
  getSearchResults(
    query: {
      queryString: string;
      queryTemplate: string;
    },
    entityTypes: EntityType[],
    fields: string[],
    from: number,
    size: number,
    sortProperties: {
      name: string;
      isDescending: boolean;
    }[]
  ): Promise<SearchResponse[]>;

  // Site-related methods
  getSiteById(siteUrl: string, sitePath: string): Promise<Site>;
  getSiteDrives(siteId: string): Promise<Drive[]>;

  // List-related methods
  getListDataAsStream(
    projectCode: string,
    listTitle: string,
    renderListDataParams: IRenderListDataParameters
  ): Promise<IRenderListDataAsStreamResult>;

  // Utility methods
  getSite(sharePointTenant: string, hubsitePath: string): Promise<Site>;
  getRootSite(): Promise<Hubsite>;
  getPreviewUrl(
    selectedEmail: Email
  ): Promise<NullableOption<string> | undefined>;
  getAtveroGroups(): Promise<Group[]>;
  getCmapGroups(): Promise<Group[]>;
  getIsAdmin(filteredGroups: string[]): Promise<boolean>;

  // Preview-related methods
  getSiteFromSelectedEmail(selectedEmail: Email): Promise<Site>;
  getListsFromSite(siteId: string): Promise<ListItem[]>;
  getEmailWithDriveItem(
    siteId: string,
    listId: string,
    itemId: string
  ): Promise<ListItem>;
  getFileContents(email: Email): Promise<Blob>;
  getPhotoBlob(email: string): Promise<Blob>;
}
