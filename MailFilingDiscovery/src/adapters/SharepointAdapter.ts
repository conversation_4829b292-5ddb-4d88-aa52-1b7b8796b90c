import { <PERSON><PERSON><PERSON><PERSON> } from "@pnp/graph";
import { SPFI } from "@pnp/sp";
import { IWeb } from "@pnp/sp/webs";
import { Web } from "@pnp/sp/webs";
import { ISharepointAdapter } from "./ISharepointAdapter";
import { Project } from "../shared/types/Project";
import { Hubsite } from "../shared/types/Hubsite";
import { buildProjectSearchQuery } from "../shared/utils/ProjectSearchHelper";
import { EntityType, ListItem, Site } from "@microsoft/microsoft-graph-types";
import { IRenderListDataParameters } from "@pnp/sp/lists";
import { Email } from "../shared/types/Email";
import "@pnp/sp/lists";
import "@pnp/sp/items";
import "@pnp/sp/sites";
import "@pnp/graph/sites";
import "@pnp/graph/users";
import "@pnp/graph/files";
import "@pnp/graph/lists";
import "@pnp/graph/list-item";
import "@pnp/graph/groups";

import { MSALOptions } from "@pnp/msaljsclient";
import { ClientId, RedirectUri } from "../shared/utils/Config";
import { PublicClientApplication } from "@azure/msal-browser";

export const options: MSALOptions = {
  configuration: {
    auth: {
      authority: "https://login.microsoftonline.com/common",
      clientId: ClientId(),
      redirectUri: RedirectUri(),
    },
    cache: {
      claimsBasedCachingEnabled: true,
    },
  },
  authParams: {
    forceRefresh: false,
    scopes: [],
  },
};
export const msalInstance = new PublicClientApplication(options.configuration);

export class SharepointAdapter implements ISharepointAdapter {
  private graph: GraphFI;
  private sharepoint: SPFI;
  private sharepointHost: string;

  constructor(client: GraphFI, spclient: SPFI, sharepointHost: string) {
    this.graph = client;
    this.sharepoint = spclient;
    this.sharepointHost = sharepointHost;
  }

  async getProject(
    key: string,
    value: string,
    pathHubsite: string
  ): Promise<Project> {
    try {
      const hubWeb = Web([this.sharepoint.web, pathHubsite]);
      const items = await hubWeb.lists
        .getByTitle("Projects")
        .items.filter(`${key} eq '${value}'`)
        .select("Title", "ProjectCode", "Description", "ATVImportedSourceID")
        .top(1)();

      if (items.length === 0) {
        throw new Error(
          `Project not found with ${key}: ${value} at ${this.sharepoint.web}`
        );
      }

      const item = items[0];
      return {
        code: item.ProjectCode,
        title: item.Title ?? null,
        id: item.ATVImportedSourceID,
        favorite: false,
      };
    } catch (error) {
      console.error(
        `Failed to fetch project with ${key}: ${value} at ${pathHubsite}`,
        error
      );
      throw error;
    }
  }

  async getProjects(hubsite: Hubsite, searchTerm: string): Promise<Project[]> {
    try {
      const { renderListDataParams } = buildProjectSearchQuery(searchTerm);

      const projectWeb = Web([this.sharepoint.web, hubsite?.url]);

      const response = await projectWeb.lists
        .getByTitle("Projects")
        .renderListDataAsStream(renderListDataParams);

      const projects = response.Row.map((project) => ({
        code: project.ProjectCode,
        title: project.Description,
        id: project.ID,
        favorite: false,
      }));

      return projects;
    } catch (error) {
      console.error("Error fetching projects: ", error);
      return [];
    }
  }

  async getSearchResults(
    query: {
      queryString: string;
      queryTemplate: string;
    },
    entityTypes: EntityType[],
    fields: string[],
    from: number,
    size: number,
    sortProperties: {
      name: string;
      isDescending: boolean;
    }[]
  ) {
    try {
      // PnPjs call to search API
      return await this.graph.query({
        entityTypes,
        query,
        fields,
        from,
        size,
        sortProperties,
      });
    } catch (error) {
      console.error("Error executing search query:", error);
      throw error;
    }
  }

  async getSiteById(siteUrl: string, sitePath: string) {
    try {
      console.log("getSiteById: ", siteUrl, sitePath);
      const sitePromise = await this.graph.sites.getByUrl(siteUrl, sitePath);
      return await sitePromise();
    } catch (error) {
      console.error("Error getting site by ID:", error);
      throw error;
    }
  }

  async getSiteDrives(siteId: string) {
    try {
      return await this.graph.sites.getById(siteId).drives();
    } catch (error) {
      console.error("Error getting site drives:", error);
      throw error;
    }
  }

  async getListDataAsStream(
    projectCode: string,
    listTitle: string,
    renderListDataParams: IRenderListDataParameters
  ) {
    const web = await this.getSharepointWeb(projectCode);

    if (!web) {
      throw new Error(
        `Could not find SharePoint web for project code: ${projectCode}`
      );
    }

    try {
      return await web.lists
        .getByTitle(listTitle)
        .renderListDataAsStream(renderListDataParams);
    } catch (error) {
      console.error("Error rendering list data as stream:", error);
      throw error;
    }
  }

  async getSite(sharePointTenant: string, hubsitePath: string) {
    try {
      const hubsite = await this.graph.sites.getByUrl(
        sharePointTenant,
        hubsitePath
      );
      return hubsite();
    } catch (error) {
      console.error("Error getting hubsite:", error);
      throw error;
    }
  }

  async getRootSite(): Promise<Hubsite> {
    try {
      const site = await this.graph.sites.root();
      return {
        id: site.id,
        displayName: site.displayName,
        name: site.name,
        url: site.webUrl,
      } as Hubsite;
    } catch (error) {
      console.error("Error getting root site:", error);
      throw error;
    }
  }

  private readonly getWebFromSite = async (
    sharepoint: SPFI,
    projectCode: string
  ): Promise<IWeb> => {
    const rootwebData = await sharepoint.site.rootWeb();

    // We're passing the root web around and we want one for the current site
    // use the web factory to one for our selected site.
    // Need to pass the any queryable sharepoit object to get a queryable web object
    // out again. https://pnp.github.io/pnpjs/sp/webs/#access-a-web
    const web = Web([
      sharepoint.web,
      rootwebData.Url + "/sites/" + projectCode,
    ]);
    return web;
  };

  private async getSharepointWeb(projectCode: string): Promise<IWeb | null> {
    try {
      return await this.getWebFromSite(this.sharepoint, projectCode);
    } catch (error) {
      console.error("getSharepointWeb: Failed to fetch sharepoint web", error);
      return null;
    }
  }

  getPreviewUrl = async (selectedEmail: Email) => {
    try {
      const site = await this.getSiteFromSelectedEmail(selectedEmail);

      if (!site.id) {
        return undefined;
      } else
        return (
          (
            await this.graph.sites
              .getById(site.id)
              .drives.getById(selectedEmail?.driveID ?? "")
              .getItemById(selectedEmail?.driveItemID as string)
              .preview()
          ).getUrl + "&nb=true"
        );
    } catch (error) {
      console.error("getPreviewUrl: Failed to fetch preview URL", error);
      return undefined;
    }
  };

  getAtveroGroups = async () => {
    try {
      return this.graph.groups.filter(
        "startswith(displayName, 'Atvero Mail Admins')"
      )();
    } catch (error) {
      console.error("getAtveroGroups: Failed to fetch Atvero Groups", error);
      return [];
    }
  };

  getCmapGroups = async () => {
    try {
      return this.graph.groups.filter(
        "startswith(displayName, 'CMAP Mail Admins')"
      )();
    } catch (error) {
      console.error("getCmapGroups: Failed to fetch Cmap Groups", error);
      return [];
    }
  };

  getIsAdmin = async (filteredGroups: string[]) => {
    try {
      const userGroups = await this.graph.me.checkMemberGroups(filteredGroups);
      if (userGroups.length > 0) {
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error checking admin level", error);
      return false;
    }
  };

  getSiteFromSelectedEmail = async (selectedEmail: Email): Promise<Site> => {
    try {
      const shareRootUrl = new URL(this.sharepointHost);
      const projectPath = `/sites/${selectedEmail.projectCode}`;
      console.log(
        "Lookup up site with host",
        shareRootUrl.hostname,
        projectPath
      );
      return (
        await this.graph.sites.getByUrl(shareRootUrl.hostname, projectPath)
      )();
    } catch (error) {
      console.error("getSiteFromSelectedEmail: Failed to fetch site", error);
      throw error;
    }
  };

  getListsFromSite = async (siteId: string): Promise<ListItem[]> => {
    try {
      return await this.graph.sites.getById(siteId).lists();
    } catch (error) {
      console.error("getListsFromSite: Failed to fetch lists", error);
      throw error;
    }
  };

  getEmailWithDriveItem = async (
    siteId: string,
    listId: string,
    itemId: string
  ): Promise<ListItem> => {
    try {
      return await this.graph.sites
        .getById(siteId)
        .lists.getById(listId)
        .items.getById(itemId)
        .expand("driveItem")();
    } catch (error) {
      console.error(
        "getEmailWithDriveItem: Failed to fetch email with drive item",
        error
      );
      throw error;
    }
  };

  getFileContents = async (email: Email): Promise<Blob> => {
    try {
      const site = await this.getSiteFromSelectedEmail(email);
      if (!site.id) {
        throw new Error("Failed to fetch site ID");
      }
      const siteId = site.id;
      return await this.graph.sites
        .getById(siteId)
        .drives.getById(email.driveID ?? "")
        .getItemById(email.driveItemID as string)
        .getContent();
    } catch (error) {
      console.error("getFileContents: Failed to fetch files content", error);
      throw error;
    }
  };

  getPhotoBlob = async (email: string): Promise<Blob> => {
    try {
      return await this.graph.users.getById(email).photo.getBlob();
    } catch (error) {
      console.error("getPhotoBlob: Failed to fetch photo", error);
      throw error;
    }
  };
}
