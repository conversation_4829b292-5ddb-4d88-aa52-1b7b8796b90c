import { BackendScope, BackendUrl } from "../../shared/utils/Config";
import { msalInstance } from "../SharepointAdapter";
import { IApiRequest } from "./IApiRequest";

const getApiUrl = (apiCall: string): string => {
  return `${BackendUrl()}${apiCall}`;
};

export const getApiRequest = (apiCall: string): IApiRequest => {
  return {
    url: getApiUrl(apiCall),
    scopes: [BackendScope()],
  };
};

export async function bearerToken(apiCall: string): Promise<string> {
  const account = msalInstance.getActiveAccount();
  if (!account) {
    throw Error(
      "No active account! Verify a user has been signed in and setActiveAccount has been called."
    );
  }
  const apiRequest = getApiRequest(apiCall);

  const response = await msalInstance.acquireTokenSilent({
    ...apiRequest,
    account: account,
  });

  return response.accessToken;
}

export async function atveroMailBackend(
  apiCall: string,
  method: string
): Promise<unknown> {
  const token = await bearerToken(apiCall);

  const headers = new Headers();
  const bearer = `Bearer ${token}`;

  headers.append("Authorization", bearer);
  headers.append("ngrok-skip-browser-warning", "true");

  const url = getApiUrl(apiCall);

  const options = {
    method: method,
    headers: headers,
  };
  try {
    const response = await fetch(url, options);
    return await response.json();
  } catch (error) {
    return undefined;
  }
}

export async function atveroMailBackendPostJson(
  apiCall: string,
  body: any
): Promise<unknown> {
  const token = await bearerToken(apiCall);

  const headers = new Headers();
  const bearer = `Bearer ${token}`;

  headers.append("Authorization", bearer);
  headers.append("Content-Type", "application/json");
  headers.append("ngrok-skip-browser-warning", "true");

  const url = getApiUrl(apiCall);

  const options = {
    method: "POST",
    headers: headers,
    body: body,
  };
  try {
    const response = await fetch(url, options);
    return await response.json();
  } catch (error) {
    console.log(`Exception POSTing to the back end: ${error}`);
    return undefined;
  }
}
