import { Hubsite } from "../shared/types/Hubsite";
import {
  atveroMailBackend,
  atveroMailBackendPostJson,
} from "./backendCall/backendCall";
import { IBackendAdapter } from "./IBackendAdapter";
import { getSitePath } from "../shared/utils/SiteHelpers";
import { Tag } from "../shared/types/Tags";

interface HubsiteResponse {
  IsSuccess: boolean;
  Message: string;
  Data: Array<{
    Name: string;
    SiteUrl: string;
    DisplayName: string;
  }>;
}

interface UnfileResponse {
  IsSuccess: boolean;
  Message: string;
}
interface ForwardResponse {
  IsSuccess: boolean;
  Message: string;
}

interface SettingResponse {
  IsSuccess: boolean;
  Message: string;
}

interface CustomTagResponse {
  IsSuccess: boolean;
  Message: string;
  Data: Array<{
    Name: string;
    Colour: string;
    BackgroundColour: string;
  }>;
}

export class BackendAdapter implements IBackendAdapter {
  loadedTags: Tag[] | undefined;

  async getHubsites(): Promise<{
    hubsites: Hubsite[] | undefined;
    error?: string;
  }> {
    try {
      const response = await atveroMailBackend("/api/Hubsites", "GET");

      if (response) {
        const hubsiteResponse = response as HubsiteResponse;

        if (hubsiteResponse.IsSuccess) {
          const mappedHubsites: Hubsite[] = hubsiteResponse.Data.map(
            (site) => ({
              displayName: site.DisplayName || "No Hubsite Name",
              name: site.Name,
              url: site.SiteUrl,
              path: getSitePath(site.SiteUrl) ?? "/",
            })
          );

          return { hubsites: mappedHubsites };
        } else {
          return { hubsites: undefined, error: hubsiteResponse.Message };
        }
      }

      return { hubsites: undefined, error: "Failed to fetch hubsites" };
    } catch (error) {
      return {
        hubsites: undefined,
        error: "An error occurred while fetching hubsites",
      };
    }
  }
  async forwardEmail(
    driveId: string,
    driveItemID: string
  ): Promise<{ error?: string }> {
    try {
      const body = {
        driveId: driveId,
        driveItemID: driveItemID,
      };

      const response = await atveroMailBackendPostJson(
        "/api/ForwardMail",
        JSON.stringify(body)
      );

      if (response) {
        const forwardResponse = response as ForwardResponse;

        if (forwardResponse.IsSuccess) {
          return {};
        } else {
          return { error: forwardResponse.Message };
        }
      }

      return { error: "Failed to forward email" };
    } catch (error) {
      return {
        error: "An error occurred while forwarding the email",
      };
    }
  }

  async unfileEmail(
    driveId: string,
    id: string,
    messageId: string | undefined,
    conversationId: string | undefined,
    driveItemID: string,
    projectCode: string
  ): Promise<{ error?: string }> {
    try {
      const body = {
        driveId: driveId,
        id: id,
        messageId: messageId,
        conversationId: conversationId,
        driveItemID: driveItemID,
        projectCode: projectCode,
      };

      const response = await atveroMailBackendPostJson(
        "/api/UnfileEmail",
        JSON.stringify(body)
      );

      if (response) {
        const unfileResponse = response as UnfileResponse;

        if (unfileResponse.IsSuccess) {
          return {};
        } else {
          return { error: unfileResponse.Message };
        }
      }

      return { error: "Failed to unfile email" };
    } catch (error) {
      return {
        error: "An error occurred while unfiling the email",
      };
    }
  }

  async getTags(): Promise<{
    tags: Tag[] | undefined;
    error?: string;
  }> {
    if (this.loadedTags !== undefined) return { tags: this.loadedTags };

    try {
      const response = await atveroMailBackend("/api/customtags", "GET");

      if (response) {
        const customTagsResponse = response as CustomTagResponse;

        if (customTagsResponse.IsSuccess) {
          const tags = customTagsResponse.Data.map((tag) => ({
            Name: tag.Name,
            Colour: tag.Colour,
            BackgroundColour: tag.BackgroundColour,
          }));

          return { tags: tags };
        } else {
          return { tags: undefined, error: customTagsResponse.Message };
        }
      }

      return { tags: undefined, error: "Failed to fetch custom tags" };
    } catch (error) {
      return {
        tags: undefined,
        error: "An error occurred while fetching tags",
      };
    }
  }

  async getSetting(setting: string): Promise<string | undefined> {
    try {
      const response = await atveroMailBackend(
        "/api/AppSettings?setting=" + setting,
        "GET"
      );

      const settingVal = response as SettingResponse;

      if (settingVal.Message === "") return undefined;

      return settingVal.Message;
    } catch (error: unknown) {
      if (error instanceof Error && error.message !== null) {
        console.log(error.message);
        return undefined;
      } else {
        return undefined;
      }
    }
  }
}
