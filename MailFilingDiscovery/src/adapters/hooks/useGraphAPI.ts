import { useEffect, useState, useCallback } from "react";
import { graph<PERSON>, GraphBrowser, GraphFI } from "@pnp/graph";
import { MSAL, MSALOptions } from "@pnp/msaljsclient";
import { ClientId, RedirectUri, Scopes } from "../../shared/utils/Config";

export interface IUseGraph {
  graph: GraphFI | null;
  loginError: string | null;
  manualLogin: () => void;
  isFirstAttempt: boolean;
}

export const useGraph = (): IUseGraph => {
  const [graph, setGraph] = useState<GraphFI | null>(null);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [isFirstAttempt, setIsFirstAttempt] = useState<boolean>(true);

  const initializeGraph = useCallback(async () => {
    const options: MSALOptions = {
      configuration: {
        auth: {
          authority: "https://login.microsoftonline.com/common",
          clientId: ClientId(),
          redirectUri: RedirectUri(),
        },
        cache: {
          claimsBasedCachingEnabled: true,
        },
      },
      authParams: {
        forceRefresh: false,
        scopes: Scopes(),
      },
    };

    try {
      const graph = graphfi().using(GraphBrowser(), MSAL(options));
      await graph.me(); // This will trigger the login process
      setGraph(graph);
      setLoginError(null);
      setIsFirstAttempt(false);
    } catch (error) {
      console.error("Login failed:", error);
      setLoginError(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
      setIsFirstAttempt(false);
    }
  }, []);

  useEffect(() => {
    initializeGraph();
  }, [initializeGraph]);

  const manualLogin = useCallback(() => {
    initializeGraph();
  }, [initializeGraph]);

  return { graph, loginError, manualLogin, isFirstAttempt };
};
