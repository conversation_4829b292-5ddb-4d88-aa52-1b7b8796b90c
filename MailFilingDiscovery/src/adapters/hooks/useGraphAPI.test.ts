import { renderHook, act } from "@testing-library/react-hooks";
import { useGraph } from "./useGraphAPI";
import { graphfi } from "@pnp/graph";
import { describe, it, expect, vi, beforeEach } from "vitest";

// Mocking modules
vi.mock("@pnp/graph", () => ({
  graphfi: vi.fn(),
  GraphBrowser: vi.fn(),
}));

vi.mock("@pnp/msaljsclient", () => ({
  MSAL: vi.fn(),
}));

vi.mock("../utils/Config", () => ({
  ClientId: vi.fn(),
  RedirectUri: vi.fn(),
  Scopes: vi.fn(),
}));

describe("useGraph", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("initializes graph and sets state correctly on successful login", async () => {
    const mockGraph = {
      me: vi.fn().mockResolvedValue({}),
    };

    (graphfi as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      using: vi.fn().mockReturnValue(mockGraph),
    });

    const { result, waitForNextUpdate } = renderHook(() => useGraph());

    await waitForNextUpdate();

    expect(result.current.graph).toBe(mockGraph);
    expect(result.current.loginError).toBeNull();
    expect(result.current.isFirstAttempt).toBe(false);
  });

  it("sets loginError and isFirstAttempt correctly on failed login", async () => {
    const mockError = new Error("Login failed");
    const mockGraph = {
      me: vi.fn().mockRejectedValue(mockError),
    };

    (graphfi as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      using: vi.fn().mockReturnValue(mockGraph),
    });

    const { result, waitForNextUpdate } = renderHook(() => useGraph());

    await waitForNextUpdate();

    expect(result.current.graph).toBeNull();
    expect(result.current.loginError).toBe(mockError.message);
    expect(result.current.isFirstAttempt).toBe(false);
  });

  it("retries login when manualLogin is called", async () => {
    const mockGraph = {
      me: vi.fn().mockResolvedValue({}),
    };

    (graphfi as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      using: vi.fn().mockReturnValue(mockGraph),
    });

    const { result, waitForNextUpdate } = renderHook(() => useGraph());

    await waitForNextUpdate();

    expect(result.current.graph).toBe(mockGraph);

    act(() => {
      result.current.manualLogin();
    });

    await waitForNextUpdate();

    expect(result.current.graph).toBe(mockGraph);
    expect(result.current.loginError).toBeNull();
  });
});
