import { Hubsite } from "../shared/types/Hubsite";
import { Tag } from "../shared/types/Tags";

export interface IBackendAdapter {
  getHubsites(): Promise<{ hubsites: Hubsite[] | undefined; error?: string }>;
  forwardEmail(
    driveId: string,
    driveItemID: string
  ): Promise<{ error?: string }>;
  unfileEmail(
    driveId: string,
    id: string,
    messageId: string | undefined,
    conversationId: string | undefined,
    driveItemID: string,
    projectCode: string
  ): Promise<{ error?: string }>;
  getTags(): Promise<{
    tags: Tag[] | undefined;
    error?: string;
  }>;
  getSetting(setting: string): Promise<string | undefined>;
}
