import { ISharepointAdapter } from "./ISharepointAdapter";
import { Project } from "../shared/types/Project";
import { Hubsite } from "../shared/types/Hubsite";
import {
  EntityType,
  ListItem,
  Site,
  Drive,
  SearchResponse,
} from "@microsoft/microsoft-graph-types";
import { IRenderListDataParameters } from "@pnp/sp/lists";
import { Email } from "../shared/types/Email";

export class TestSharepointAdapter implements ISharepointAdapter {
  async getProject(
    _key: string,
    _value: string,
    _pathHubsite: string
  ): Promise<Project> {
    throw new Error("Method not implemented.");
  }

  async getProjects(
    _hubsite: Hubsite,
    _searchTerm: string
  ): Promise<Project[]> {
    throw new Error("Method not implemented.");
  }

  async getSearchResults(
    _query: { queryString: string; queryTemplate: string },
    _entityTypes: EntityType[],
    _fields: string[],
    _from: number,
    _size: number,
    _sortProperties: { name: string; isDescending: boolean }[]
  ): Promise<SearchResponse[]> {
    throw new Error("Method not implemented.");
  }

  async getSiteById(_siteUrl: string, _sitePath: string): Promise<Site> {
    throw new Error("Method not implemented.");
  }

  async getSiteDrives(_siteId: string): Promise<Drive[]> {
    throw new Error("Method not implemented.");
  }

  async getListDataAsStream(
    _projectCode: string,
    _listTitle: string,
    _renderListDataParams: IRenderListDataParameters
  ): Promise<any> {
    throw new Error("Method not implemented.");
  }

  async getSite(
    _sharePointTenant: string,
    _hubsitePath: string
  ): Promise<Site> {
    throw new Error("Method not implemented.");
  }

  async getRootSite(): Promise<Hubsite> {
    throw new Error("Method not implemented.");
  }

  getPreviewUrl = async (_selectedEmail: Email) => {
    throw new Error("Method not implemented.");
  };

  getAtveroGroups = async () => {
    throw new Error("Method not implemented.");
  };

  getCmapGroups = async () => {
    throw new Error("Method not implemented.");
  };

  getIsAdmin = async (_filteredGroups: string[]) => {
    throw new Error("Method not implemented.");
  };

  getSiteFromSelectedEmail = async (_selectedEmail: Email): Promise<Site> => {
    throw new Error("Method not implemented.");
  };

  getListsFromSite = async (_siteId: string): Promise<ListItem[]> => {
    throw new Error("Method not implemented.");
  };

  getEmailWithDriveItem = async (
    _siteId: string,
    _listId: string,
    _itemId: string
  ): Promise<ListItem> => {
    throw new Error("Method not implemented.");
  };

  getFileContents = async (_email: Email): Promise<Blob> => {
    throw new Error("Method not implemented.");
  };

  getPhotoBlob = async (_email: string): Promise<Blob> => {
    throw new Error("Method not implemented.");
  };
}
