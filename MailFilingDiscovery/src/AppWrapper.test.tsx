import { describe, it, expect, vi, beforeEach, beforeAll } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { act } from "react-dom/test-utils";

import { useGraph } from "./adapters/hooks/useGraphAPI";
import { GraphFI } from "@pnp/graph";

vi.mock("./adapters/hooks/useGraphAPI");
import AppWrapper from "./AppWrapper";

vi.mock("./components/AtveroIcon", () => ({
  AtveroIcon: () => <div data-testid="atvero-icon">Atvero Icon</div>,
}));

vi.mock("@azure/msal-react", () => ({
  MsalProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
  MsalAuthenticationTemplate: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

beforeEach(() => {
  window.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
});

beforeAll(() => {
  vi.clearAllMocks();
});

describe("AppWrapper", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Initial Loading States", () => {
    it("renders loading state initially", () => {
      vi.mocked(useGraph).mockImplementation(() => ({
        graph: null,
        loginError: null,
        manualLogin: vi.fn(),
        isFirstAttempt: true,
      }));

      render(<AppWrapper />);
      expect(screen.getByTestId("atvero-icon")).toBeInTheDocument();
      expect(screen.getByText("Loading Mail...")).toBeInTheDocument();
    });
  });

  describe("Authentication Flow", () => {
    const mockManualLogin = vi.fn();

    it("handles popup block error state with retry button and shows popup help", () => {
      vi.mocked(useGraph).mockImplementation(() => ({
        graph: null,
        loginError: "popup_window_error",
        manualLogin: mockManualLogin,
        isFirstAttempt: false,
      }));

      render(<AppWrapper />);

      expect(screen.getByText("Please enable Pop-ups")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Please enable pop-ups in your browser to continue with the sign in process."
        )
      ).toBeInTheDocument();

      const popupHelp = screen.getByTestId("popup-help");
      expect(popupHelp).toBeInTheDocument();
      expect(popupHelp).toHaveTextContent(
        "To enable pop-ups: Click the blocked pop-up icon in your browser's address bar or navigate to your pop-ups options in your browsers settings and select 'Always allow'."
      );

      const loginButton = screen.getByText("Login");
      expect(loginButton).toBeInTheDocument();
      fireEvent.click(loginButton);
      expect(mockManualLogin).toHaveBeenCalled();
    });

    it("handles generic login error with support admin message", () => {
      const mockManualLogin = vi.fn();
      const genericError = "Failed to authenticate";
      vi.mocked(useGraph).mockImplementation(() => ({
        graph: null,
        loginError: genericError,
        manualLogin: mockManualLogin,
        isFirstAttempt: false,
      }));

      render(<AppWrapper />);

      expect(screen.getByText("Unable to login")).toBeInTheDocument();

      const errorMessage = screen.getByText(
        (content) =>
          content.includes(
            "We encountered an error while trying to log you in."
          ) &&
          content.includes(
            "Please refresh this page or contact your local Mail support admin with the following error message:"
          ) &&
          content.includes(genericError)
      );
      expect(errorMessage).toBeInTheDocument();

      expect(screen.queryByTestId("popup-help")).not.toBeInTheDocument();

      const loginButton = screen.getByText("Login");
      expect(loginButton).toBeInTheDocument();
      fireEvent.click(loginButton);
      expect(mockManualLogin).toHaveBeenCalled();
    });

    it("shows first attempt authentication message", () => {
      vi.mocked(useGraph).mockImplementation(() => ({
        graph: null,
        loginError: "Auth Error",
        manualLogin: mockManualLogin,
        isFirstAttempt: true,
      }));

      render(<AppWrapper />);
      expect(screen.getByText("Logging you in...")).toBeInTheDocument();
      expect(
        screen.getByText("Attempting to authenticate...")
      ).toBeInTheDocument();
      expect(screen.queryByTestId("popup-help")).not.toBeInTheDocument();
    });

    it("handles successful login", async () => {
      const mockSite = vi.fn().mockResolvedValue({
        webUrl: "https://example.sharepoint.com/sites/root",
        id: "site-123",
        // Add other properties from ISite as needed
      });

      const mockGraph = {
        sites: {
          getById: vi.fn().mockReturnValue(mockSite),
        },
      } as unknown as GraphFI;

      vi.mocked(useGraph).mockImplementation(() => ({
        graph: mockGraph,
        loginError: null,
        manualLogin: mockManualLogin,
        isFirstAttempt: true,
      }));

      render(<AppWrapper />);
      await act(async () => {
        await waitFor(() => {
          expect(screen.queryByText("No email selected")).toBeInTheDocument();
        });
      });
    });
  });
});
