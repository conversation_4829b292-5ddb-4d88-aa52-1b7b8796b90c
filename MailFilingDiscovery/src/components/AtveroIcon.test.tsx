import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { AtveroIcon } from "./AtveroIcon";

describe("AtveroIcon Component", () => {
  it("renders the Atvero icon image", () => {
    render(<AtveroIcon />);
    const logoImage = screen.getByAltText("Atvero Mail Icon");
    expect(logoImage).toBeDefined();
    expect(logoImage).toHaveAttribute(
      "src",
      expect.stringContaining("AtveroMail_icon_svg.svg")
    );
  });

  it("applies the correct styles to the icon image", () => {
    render(<AtveroIcon />);
    const logoImage = screen.getByAltText("Atvero Mail Icon");
    expect(logoImage).toHaveStyle({
      width: "32px",
      paddingLeft: "6px",
      paddingRight: "6px",
    });
  });
});
