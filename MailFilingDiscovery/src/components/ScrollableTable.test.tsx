import { describe, it, expect, vi } from "vitest";
import { render, fireEvent } from "@testing-library/react";
import { ScrollableTable, renderRow } from "../components/ScrollableTable";
import { Email } from "../shared/types/Email";
import { Hubsite } from "../shared/types/Hubsite";
import { Tag } from "../shared/types/Tags";
import { SortDirection } from "../shared/utils/FilterUtils";
import { TestSharepointAdapter } from "../adapters/TestSharepointAdapter";

const tags: Tag[] = [
  { Name: "No Tag", Colour: "", BackgroundColour: "" },
  { Name: "Client", Colour: "", BackgroundColour: "" },
  { Name: "Internal", Colour: "", BackgroundColour: "" },
];

const mockEmails: Email[] = [
  {
    id: "1",
    subject: "Test Email 1",
    sentBy: "Test User",
    dateFiled: "2023-01-01",
    tag: "",
    driveItemID: "",
    driveID: "",
    attachmentCount: "",
    projectCode: "PR001",
    emailTextSummary: "Summary 1",
    fileRef: "",
  },
  {
    id: "2",
    subject: "Test Email 2",
    sentBy: "<EMAIL>",
    dateFiled: "2023-01-02",
    tag: "",
    driveItemID: "",
    driveID: "",
    attachmentCount: "",
    projectCode: "PR002",
    emailTextSummary: "Summary 2",
    fileRef: "",
  },
];

const mockHubsite: Hubsite = {
  id: "",
  displayName: "hub1",
  name: "Test Hub",
  url: "https://test.sharepoint.com/sites/hub1",
  path: "/sites/hub1",
};

// Mock columns data
const columns = [
  { columnKey: "subject", label: "Subject" },
  { columnKey: "sentBy", label: "Sent By" },
  { columnKey: "dateFiled", label: "Date Filed" },
];

let mockAdapter: TestSharepointAdapter;

const setup = (propsOverride: any = {}) => {
  const defaultProps = {
    graph: null,
    columns,
    items: mockEmails,
    onEmailSelect: vi.fn(),
    selectedEmails: [],
    loadMoreItems: vi.fn(),
    hasMoreItems: false,
    onSortChange: vi.fn(),
    currentSort: { columnKey: "subject", direction: SortDirection.Ascending },
    hasNextPage: false,
    isNextPageLoading: false,
    loadNextPage: vi.fn(),
    emailsShown: 2,
    totalEmails: 2,
    loading: false,
    useStackedRows: false,
    selectedProjectsCount: 0,
    hasSearchTerm: false,
    hideProjectColumn: false,
    selectedHubsite: mockHubsite,
    ...propsOverride,
  };
  return render(<ScrollableTable {...defaultProps} />);
};

describe("ScrollableTable", () => {
  it("renders shimmer rows when loading is true", () => {
    const { getAllByRole } = setup({ loading: true });
    expect(getAllByRole("row")).toHaveLength(15);
  });

  it("calls loadNextPage when reaching the end of the list and hasMoreItems is true", () => {
    const loadNextPage = vi.fn();
    const { container } = setup({
      hasMoreItems: true,
      isNextPageLoading: false,
      loadNextPage,
    });

    const listElement = container.querySelector(".List");
    if (listElement) {
      fireEvent.scroll(listElement, { target: { scrollTop: 1000 } });
      expect(loadNextPage).toHaveBeenCalled();
    }
  });

  it("does not call loadNextPage when isNextPageLoading is true", () => {
    const loadNextPage = vi.fn();
    setup({ hasNextPage: true, isNextPageLoading: true, loadNextPage });
    expect(loadNextPage).not.toHaveBeenCalled();
  });

  it("displays hubsite selection message when no hubsite is selected", () => {
    const { getByText } = setup({ selectedHubsite: undefined });
    expect(
      getByText("Select a hubsite to search filed emails.")
    ).toBeInTheDocument();
  });

  it("displays project selection message when hubsite is selected but no projects", () => {
    const { getByText } = setup({
      selectedProjectsCount: 0,
      hasSearchTerm: false,
      selectedHubsite: mockHubsite,
    });
    expect(
      getByText("Select a project or search a keyword to find your emails.")
    ).toBeInTheDocument();
  });

  it("displays search prompt when multiple projects selected without search term", () => {
    const { getByText } = setup({
      selectedProjectsCount: 2,
      hasSearchTerm: false,
      selectedHubsite: mockHubsite,
    });
    expect(
      getByText(
        "Enter a keyword in your search bar to find your emails from your selected projects."
      )
    ).toBeInTheDocument();
  });

  it("does not render rows when no items are present and hubsite is selected", () => {
    const { queryByRole } = setup({
      items: [],
      selectedHubsite: mockHubsite,
      selectedProjectsCount: 1,
      hasSearchTerm: true,
    });
    expect(queryByRole("row")).toBeNull();
  });

  it("renders correctly with empty state when search yields no results", () => {
    const { queryAllByRole } = setup({
      items: [],
      loading: false,
      selectedHubsite: mockHubsite,
      selectedProjectsCount: 1,
      hasSearchTerm: true,
    });
    expect(queryAllByRole("row")).toHaveLength(0);
  });

  it("applies correct styles to table container", () => {
    const { container } = setup();
    const table = container.firstChild as HTMLElement;
    expect(table).toHaveStyle({
      display: "flex",
      flexDirection: "column",
      flex: "1 1 auto",
      overflow: "hidden",
    });
  });

  it("renders stacked rows when useStackedRows is true", () => {
    const { queryByText } = setup({
      useStackedRows: true,
      selectedHubsite: mockHubsite,
      selectedProjectsCount: 1,
      hasSearchTerm: true,
    });
    expect(queryByText("Subject")).not.toBeInTheDocument();
  });
});

describe("renderRow", () => {
  const mockOnClick = vi.fn();
  const mockOnSingleClick = vi.fn();

  const mockIsItemLoaded = vi.fn().mockReturnValue(true);
  mockAdapter = new TestSharepointAdapter();

  it("renders RenderRow component when useStackedRows is false", () => {
    const { container } = render(
      renderRow(
        0,
        {},
        mockEmails,
        [],
        mockOnClick,
        mockOnSingleClick,
        mockAdapter,
        mockIsItemLoaded,
        false,
        false,
        tags
      )
    );
    expect(
      container.querySelector('[data-testid="table-row"]')
    ).toBeInTheDocument();
  });

  it("renders RenderStackedRow component when useStackedRows is true", () => {
    const { container } = render(
      renderRow(
        0,
        {},
        mockEmails,
        [],
        mockOnClick,
        mockOnSingleClick,
        mockAdapter,
        mockIsItemLoaded,
        false,
        true,
        tags
      )
    );
    expect(
      container.querySelector('[data-testid="stacked-row"]')
    ).not.toBeNull();
  });

  it("calls onClick with the correct email when a row is clicked", () => {
    const { getByText } = render(
      renderRow(
        0,
        {},
        mockEmails,
        [],
        mockOnClick,
        mockOnSingleClick,
        mockAdapter,
        mockIsItemLoaded,
        false,
        false,
        tags
      )
    );
    fireEvent.click(getByText("Test Email 1"));
    expect(mockOnSingleClick).toHaveBeenCalledWith(mockEmails[0]);
  });

  it("does not render project column when hideProjectColumn is true", () => {
    const { queryByText } = render(
      renderRow(
        0,
        {},
        mockEmails,
        [],
        mockOnClick,
        mockOnSingleClick,
        mockAdapter,
        mockIsItemLoaded,
        true,
        false,
        tags
      )
    );
    expect(queryByText("PR001")).not.toBeInTheDocument();
  });

  it("renders project column when hideProjectColumn is false", () => {
    const { getByText } = render(
      renderRow(
        0,
        {},
        mockEmails,
        [],
        mockOnClick,
        mockOnSingleClick,
        mockAdapter,
        mockIsItemLoaded,
        false,
        false,
        tags
      )
    );
    expect(getByText("PR001")).toBeInTheDocument();
  });
});
