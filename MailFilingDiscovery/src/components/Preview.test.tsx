import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi, describe, it, expect } from "vitest";
import { Preview } from "./Preview";
import { GraphFI } from "@pnp/graph";
import { Email } from "../shared/types/Email";
import { handleAttachments } from "../shared/utils/AttachmentHelpers";
import { TestSharepointAdapter } from "../adapters/TestSharepointAdapter";
import { Tenancy } from "../shared/types/Tenancy";

vi.mock("@fluentui/react-icons", () => ({
  AttachRegular: () => <span>Attach Icon</span>,
  Dismiss24Regular: () => <span>Close Icon</span>,
}));

vi.mock("./Attachments", () => ({
  Attachments: () => <div data-testid="attachments">Attachments</div>,
}));

vi.mock("../shared/utils/AttachmentHelpers", () => ({
  handleAttachments: vi.fn(),
}));

describe("Preview Component", () => {
  const mockEmail: Email = {
    projectCode: "123",
    id: "1",
    subject: "Test Subject",
    sentBy: "<EMAIL>",
    dateFiled: "2023-05-01",
  };

  const mockSharepointAdapter = new TestSharepointAdapter();
  mockSharepointAdapter.getFileContents = vi
    .fn()
    .mockResolvedValue(new Blob(["test content"], { type: "message/rfc822" }));

  const mockProps = {
    previewUrl: "http://example.com/preview",
    email: mockEmail,
    graph: {} as GraphFI,
    isPreviewLoading: false,
    onLoadingComplete: vi.fn(),
    onAttachmentsLoaded: vi.fn(),
    selectedEmailCount: 1, // Single email selected by default
    onClearSelectedEmails: vi.fn(), // Mocking the clear emails function
    isMobile: false, // Default to desktop view
    isModalOpen: false, // Not relevant for desktop
    onCloseModal: vi.fn(),
    shareRootUrl: "",
    sharepointAdapter: mockSharepointAdapter,
    tenancy: Tenancy.Atvero,
  };

  it("renders without crashing", async () => {
    render(<Preview {...mockProps} />);
    await waitFor(() => {
      expect(screen.getByTitle("email-preview")).toBeInTheDocument();
    });
  });

  it("renders attachments button if there are attachments", async () => {
    vi.mocked(handleAttachments).mockResolvedValue({
      attachments: [
        {
          filename: "test.pdf",
          size: 1000,
          contentType: "application/pdf",
          content: new Uint8Array(8),
        },
      ],
      fileRef: "http://example.com/file.pdf",
    });
    render(<Preview {...mockProps} />);
    await waitFor(() => {
      expect(screen.getByText("Attachments (1)")).toBeInTheDocument();
    });
  });

  it("opens drawer when attachments button is clicked", async () => {
    vi.mocked(handleAttachments).mockResolvedValue({
      attachments: [
        {
          filename: "test.pdf",
          size: 1000,
          contentType: "application/pdf",
          content: new Uint8Array(8),
        },
      ],
      fileRef: "http://example.com/file.pdf",
    });
    render(<Preview {...mockProps} />);
    const attachButton = await screen.findByText("Attachments (1)");
    fireEvent.click(attachButton);
    expect(screen.getByTestId("attachments")).toBeInTheDocument();
  });

  it("renders spinner when loading", () => {
    render(<Preview {...mockProps} isPreviewLoading={true} />);
    expect(screen.getByRole("progressbar")).toBeInTheDocument();
  });

  it("renders email content correctly when one email is selected", async () => {
    render(<Preview {...mockProps} />);
    const iframe = await screen.findByTitle("email-preview");
    expect(iframe).toHaveAttribute("src", mockProps.previewUrl);
  });

  it("renders subject correctly when one email is selected", async () => {
    render(<Preview {...mockProps} />);
    await waitFor(() => {
      expect(screen.getByText(mockEmail.subject)).toBeInTheDocument();
    });
  });

  it("calls onAttachmentsLoaded when attachments are fetched", async () => {
    const attachmentResult = {
      attachments: [
        {
          filename: "test.pdf",
          size: 1000,
          contentType: "application/pdf",
          content: new Uint8Array(8),
        },
      ],
      fileRef: "http://example.com/file.pdf",
    };
    vi.mocked(handleAttachments).mockResolvedValue(attachmentResult);
    render(<Preview {...mockProps} />);
    await waitFor(() => {
      expect(mockProps.onAttachmentsLoaded).toHaveBeenCalledWith(
        attachmentResult
      );
    });
  });

  it("handles errors when fetching attachments", async () => {
    const consoleErrorSpy = vi
      .spyOn(console, "error")
      .mockImplementation(() => {});
    vi.mocked(handleAttachments).mockRejectedValue(new Error("Fetch error"));
    render(<Preview {...mockProps} />);
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Error handling attachments:",
        expect.any(Error)
      );
      expect(mockProps.onAttachmentsLoaded).toHaveBeenCalledWith(undefined);
    });
    consoleErrorSpy.mockRestore();
  });

  it("does not render attachments button when no attachments are present", async () => {
    vi.mocked(handleAttachments).mockResolvedValue({
      attachments: [],
      fileRef: "",
    });
    render(<Preview {...mockProps} />);
    await waitFor(() => {
      expect(screen.queryByText("Attachments (0)")).not.toBeInTheDocument();
    });
  });

  it('renders "No email selected" when no emails are selected', async () => {
    render(<Preview {...mockProps} selectedEmailCount={0} />);
    await waitFor(() => {
      expect(screen.getByText("No email selected")).toBeInTheDocument();
    });
  });

  it("renders clear selected emails link when multiple emails are selected", async () => {
    render(<Preview {...mockProps} selectedEmailCount={3} />);
    await waitFor(() => {
      expect(screen.getByText("3 emails selected")).toBeInTheDocument();
      expect(screen.getByText("Clear selected emails")).toBeInTheDocument();
    });
  });

  it("calls onClearSelectedEmails when clear selected emails link is clicked", async () => {
    render(<Preview {...mockProps} selectedEmailCount={3} />);
    const clearLink = await screen.findByText("Clear selected emails");
    fireEvent.click(clearLink);
    expect(mockProps.onClearSelectedEmails).toHaveBeenCalled();
  });

  it("renders modal correctly in mobile view when isMobile is true", async () => {
    render(<Preview {...mockProps} isMobile={true} isModalOpen={true} />);
    await waitFor(() => {
      expect(screen.getByText("Test Subject")).toBeInTheDocument();
    });
    expect(screen.getByTitle("email-preview")).toBeInTheDocument();
  });

  it("renders close button in modal for mobile view", async () => {
    render(<Preview {...mockProps} isMobile={true} isModalOpen={true} />);
    await waitFor(() => {
      expect(screen.getByText("Close Icon")).toBeInTheDocument();
    });
  });

  it("calls onCloseModal when close button is clicked in mobile view", async () => {
    render(<Preview {...mockProps} isMobile={true} isModalOpen={true} />);
    const closeButton = await screen.findByText("Close Icon");
    fireEvent.click(closeButton);
    expect(mockProps.onCloseModal).toHaveBeenCalled();
  });
});
