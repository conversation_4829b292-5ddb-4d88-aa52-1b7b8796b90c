import { render, screen, fireEvent } from "@testing-library/react";
import { describe, test, expect, vi } from "vitest";
import RenderStackedRow from "./RenderStackedRow";
import { Email } from "../shared/types/Email";
import { Tag } from "../shared/types/Tags";
import { ShimmerRow, generateShimmerRows } from "./RenderRow";
import { TestSharepointAdapter } from "../adapters/TestSharepointAdapter";

const mockEmail: Email = {
  projectCode: "P001",
  id: "1",
  subject: "Test Stacked Email",
  sentBy: "<EMAIL>",
  dateFiled: "2024-07-08T12:26:00",
  attachmentCount: "1",
  emailTextSummary: "This is a stacked email summary",
  driveItemID: "",
  driveID: "",
  tag: "",
  fileRef: "",
};

const tags: Tag[] = [
  { Name: "No Tag", Colour: "", BackgroundColour: "" },
  { Name: "Client", Colour: "", BackgroundColour: "" },
  { Name: "Internal", Colour: "", BackgroundColour: "" },
];

const mockEmailWithoutAttachments: Email = {
  ...mockEmail,
  attachmentCount: "0",
};

let mockAdapter: TestSharepointAdapter;

vi.spyOn(navigator, "languages", "get").mockReturnValue([]);
vi.spyOn(navigator, "language", "get").mockImplementation(() => "en-GB");

describe("RenderStackedRow Component", () => {
  beforeEach(() => {
    vi.resetAllMocks();

    mockAdapter = new TestSharepointAdapter();
  });

  test("renders a ShimmerRow when the item is not loaded", () => {
    render(<ShimmerRow style={{}} />);

    expect(screen.getByRole("row")).toBeInTheDocument();
    expect(screen.getAllByRole("cell")).toHaveLength(5); // 3 cells and 1 selection cell
  });

  test("renders centered message when no items are loaded and it's the first row", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(false);

    render(
      <RenderStackedRow
        index={0}
        style={{}}
        items={[]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        tags={tags}
      />
    );

    expect(
      screen.getByText(
        "Please select a project or start searching for emails to display results."
      )
    ).toBeInTheDocument();
  });

  test("renders shimmer row when item is not loaded and it's not the first row", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(false);

    render(
      <RenderStackedRow
        index={1}
        style={{}}
        items={[]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        tags={tags}
      />
    );

    // Ensure shimmer row is rendered
    expect(screen.getAllByRole("row")).toHaveLength(1);
  });

  test("renders the stacked row with email data", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderStackedRow
        index={0}
        style={{}}
        items={[mockEmail]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        tags={tags}
      />
    );

    expect(screen.getByText("Test Stacked Email")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(
      screen.getByText("This is a stacked email summary")
    ).toBeInTheDocument();
    expect(screen.getByText("08/07/2024, 12:26")).toBeInTheDocument();
  });
  7;
  test("renders the row as selected when the email is selected", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderStackedRow
        index={0}
        style={{}}
        items={[mockEmail]}
        selectedEmails={[mockEmail]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        tags={tags}
      />
    );

    expect(screen.getByRole("row")).toHaveAttribute("aria-selected", "true");
  });

  test("renders the row without an attachment icon when the email has no attachments", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderStackedRow
        index={0}
        style={{}}
        items={[mockEmailWithoutAttachments]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        tags={tags}
      />
    );

    // Ensure the email content is rendered
    expect(screen.getByText("Test Stacked Email")).toBeInTheDocument();

    // Check that the attachment icon is not rendered
    expect(screen.queryByTestId("attachment-icon")).not.toBeInTheDocument();
  });

  test("renders the row with a project code", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderStackedRow
        index={0}
        style={{}}
        items={[mockEmailWithoutAttachments]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={false}
        tags={tags}
      />
    );

    // Ensure the email content is rendered
    expect(screen.getByText("P001")).toBeInTheDocument();
  });
  test("renders the row without a project code", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderStackedRow
        index={0}
        style={{}}
        items={[mockEmailWithoutAttachments]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    expect(screen.queryAllByText("P001")).toHaveLength(0);
  });
  test("renders the attachment icon when the email has attachments", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderStackedRow
        index={0}
        style={{}}
        items={[mockEmail]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        tags={tags}
      />
    );

    expect(screen.getByText("Test Stacked Email")).toBeInTheDocument();

    // Use getByTestId instead of getByRole since the icon isn't an img element
    expect(screen.getByTestId("attachment-icon")).toBeInTheDocument();
  });

  test("calls onClick handler when a row is clicked", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);
    const mockOnClick = vi.fn();
    const mockOnSingleClick = vi.fn();

    render(
      <RenderStackedRow
        index={0}
        style={{}}
        items={[mockEmail]}
        selectedEmails={[]}
        onClick={mockOnClick}
        onSingleClick={mockOnSingleClick}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        tags={tags}
      />
    );

    fireEvent.click(screen.getByRole("row"));

    expect(mockOnSingleClick).toHaveBeenCalledWith(mockEmail);
  });

  test("does not throw error when optional fields are missing", () => {
    const mockEmailWithMissingFields = {
      projectCode: "P001",
      id: "2",
      subject: "Test Email 2",
      sentBy: "Test Sender 2",
      dateFiled: "2024-07-08T12:26:00",
      // No attachmentCount
    } as Email;

    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderStackedRow
        index={0}
        style={{}}
        items={[mockEmailWithMissingFields]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        tags={tags}
      />
    );

    expect(screen.getByText("Test Email 2")).toBeInTheDocument();
    expect(screen.getByText("Test Sender 2")).toBeInTheDocument();
    expect(screen.getByText("08/07/2024, 12:26")).toBeInTheDocument();
  });
});

describe("ShimmerRow Component", () => {
  test("renders shimmer effect placeholders", () => {
    render(<ShimmerRow style={{}} />);

    expect(screen.getAllByRole("cell")).toHaveLength(5); // Adjust based on the number of skeleton cells
  });
});

describe("generateShimmerRows function", () => {
  test("generates the correct number of ShimmerRow components", () => {
    const rows = generateShimmerRows(5); // Generate 5 shimmer rows

    expect(rows).toHaveLength(5);
    rows.forEach((row) => {
      expect(row.type).toBe(ShimmerRow);
    });
  });

  test("generates ShimmerRow components with unique keys", () => {
    const rows = generateShimmerRows(5);

    const keys = rows.map((row) => row.key);
    const uniqueKeys = new Set(keys);

    expect(uniqueKeys.size).toBe(5); // All keys should be unique
  });
});
