import React, { memo } from "react";
import { Persona, Tooltip } from "@fluentui/react-components";
import { PersonMailRegular } from "@fluentui/react-icons";
import { useRecipient } from "../shared/utils/useRecipient";
import { ISharepointAdapter } from "../adapters/ISharepointAdapter";

interface RecipientPersonaProps {
  recipient: string;
  adapter: ISharepointAdapter;
}

const RecipientPersona: React.FC<RecipientPersonaProps> = memo(
  ({ recipient, adapter }) => {
    const { name, email, img } = useRecipient(recipient, adapter);

    // Memoize the avatar configuration
    const avatar = React.useMemo(
      () => ({
        icon: <PersonMailRegular />,
        color: "colorful" as const,
        image: img ? { src: img } : undefined,
      }),
      [img]
    );

    return (
      <Tooltip content={email} relationship="label">
        <Persona
          name={name}
          primaryText={name === email ? "" : name}
          secondaryText={email}
          size="medium"
          textAlignment="center"
          avatar={avatar}
        />
      </Tooltip>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.recipient === nextProps.recipient &&
      prevProps.adapter === nextProps.adapter
    );
  }
);

RecipientPersona.displayName = "RecipientPersona";

export default RecipientPersona;
