import React, { useMemo } from "react";
import {
  TableRow,
  TableSelectionCell,
  TableCell,
  makeStyles,
  Tooltip,
  Persona,
} from "@fluentui/react-components";
import {
  Attach20Regular,
  PersonMailRegular,
  Important20Regular,
  LockClosed20Regular,
} from "@fluentui/react-icons";
import { Email } from "../shared/types/Email";
import { formatDate } from "../shared/utils/FormatDate";
import { useRecipient } from "../shared/utils/useRecipient";
import { ShimmerRow } from "./RenderRow";
import { TagBadge } from "./TagBadge";
import { Tag } from "../shared/types/Tags";
import { ISharepointAdapter } from "../adapters/ISharepointAdapter";

const useStyles = makeStyles({
  tableRow: {
    "&:hover": {
      cursor: "pointer",
    },
  },
  redIcon: {
    color: "red",
    marginLeft: "4px",
  },
  confidentialIcon: {
    marginLeft: "8px",
  },
  tableCell: {
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    padding: "10px",
    display: "inline-block",
    alignContent: "center",
  },
  dynamicContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "0px",
  },
  contentRow: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  ellipsis: {
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    flex: "1",
    margin: "0",
    padding: "0",
  },
  iconRight: {
    marginLeft: "0px",
    flexShrink: "0",
  },
  rightAligned: {
    textAlign: "right",
    flexShrink: "0",
    marginLeft: "8px",
  },
  senderName: {
    color: "#333333",
    fontSize: "14px",
    lineHeight: "1.3",
  },
  subjectText: {
    color: "#333333",
    fontSize: "14px",
    lineHeight: "1.3",
  },
  emailSummary: {
    color: "#666666",
    fontSize: "12px",
    lineHeight: "1.2",
  },
  secondaryText: {
    color: "#242424",
    fontSize: "12px",
    lineHeight: "1.2",
  },
  centeredMessage: {
    textAlign: "center",
    padding: "50px",
    color: "#888",
    fontSize: "16px",
  },
  projectCode: {
    fontWeight: "600",
  },
});

interface RenderStackedRowProps {
  index: number;
  style: React.CSSProperties;
  items: Email[];
  selectedEmails: Email[];
  onClick: (item: Email) => void;
  onSingleClick: (item: Email) => void;
  adapter: ISharepointAdapter;
  isItemLoaded: (index: number) => boolean;
  hideProjectColumn?: boolean;
  tags: Tag[];
}

const RenderStackedRow: React.FC<RenderStackedRowProps> = ({
  index,
  style,
  items,
  selectedEmails,
  onClick,
  onSingleClick,
  adapter,
  isItemLoaded,
  hideProjectColumn = false,
  tags,
}) => {
  const classes = useStyles();

  const item = items[index] ?? {};
  const selected = selectedEmails.some((email) => email.id === item.id);
  const { name, img } = useRecipient(item.sentBy || "", adapter);

  const attachmentCount = useMemo(
    () => parseInt(item.attachmentCount ?? "0", 10),
    [item.attachmentCount]
  );
  const hasAttachments = attachmentCount > 0;

  const isImportant = item.emailImportant === "Yes";

  const isConfidential = item.emailConfidential;

  if (!isItemLoaded(index)) {
    return index === 0 ? (
      <div className={classes.centeredMessage}>
        <p>
          Please select a project or start searching for emails to display
          results.
        </p>
      </div>
    ) : (
      <ShimmerRow style={style} />
    );
  }

  return (
    <TableRow
      style={style}
      key={item.id || index}
      onClick={() => {
        onSingleClick(item);
      }}
      aria-selected={selected}
      appearance={selected ? "brand" : "none"}
      data-testid="stacked-row"
      className={classes.tableRow}
    >
      <TableSelectionCell
        onClick={(event) => {
          event.stopPropagation();
          onClick(item);
          return false;
        }}
        checked={selected}
        checkboxIndicator={{
          "aria-label": selected ? "Deselect row" : "Select row",
        }}
      />
      <Persona
        name={name}
        primaryText={""}
        avatar={{
          icon: <PersonMailRegular />,
          color: "colorful",
          image: {
            src: img ?? "",
          },
        }}
      />
      <TableCell className={classes.tableCell}>
        <div className={classes.dynamicContainer}>
          <div className={classes.contentRow}>
            <span className={`${classes.ellipsis} ${classes.senderName}`}>
              {name}
            </span>
            {isConfidential && (
              <LockClosed20Regular
                data-testid="confidential-icon"
                className={classes.confidentialIcon}
              />
            )}
            {isImportant && (
              <Important20Regular
                data-testid="important-icon"
                className={classes.redIcon}
              />
            )}
            {hasAttachments && (
              <Attach20Regular
                className={classes.iconRight}
                data-testid="attachment-icon"
              />
            )}

            <TagBadge tags={tags} tagName={item.tag} />

            {!hideProjectColumn && (
              <span
                className={`${classes.rightAligned} ${classes.secondaryText}`}
              >
                <span className={classes.projectCode}>{item.projectCode}</span>
              </span>
            )}
          </div>

          <div className={classes.contentRow}>
            <Tooltip content={item.subject} relationship="label">
              <span className={`${classes.ellipsis} ${classes.subjectText}`}>
                {item.subject}
              </span>
            </Tooltip>
            <span
              className={`${classes.rightAligned} ${classes.secondaryText}`}
            >
              {formatDate(item.dateFiled)}
            </span>
          </div>

          <div className={classes.contentRow}>
            <span className={`${classes.ellipsis} ${classes.emailSummary}`}>
              {item.emailTextSummary ?? ""}
            </span>
          </div>
        </div>
      </TableCell>
    </TableRow>
  );
};

export default RenderStackedRow;
