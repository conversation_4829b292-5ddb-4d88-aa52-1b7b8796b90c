import React, { useEffect, useState } from "react";
import {
  make<PERSON><PERSON><PERSON>,
  Spinner,
  Button,
  tokens,
  Subtitle1,
  Subtitle2,
  <PERSON><PERSON>,
  <PERSON>,
} from "@fluentui/react-components";
import { AttachRegular, Dismiss24Regular } from "@fluentui/react-icons";
import { Attachments } from "./Attachments";
import { Email } from "../shared/types/Email";
import { AttachmentResult } from "../shared/types/Attachments";
import { handleAttachments } from "../shared/utils/AttachmentHelpers";
import { AtveroLogo } from "./AtveroLogo";
import { ISharepointAdapter } from "../adapters/ISharepointAdapter";
import { Tenancy } from "../shared/types/Tenancy";

interface PreviewProps {
  previewUrl?: string;
  email: Email;
  isPreviewLoading: boolean;
  onLoadingComplete: () => void;
  onAttachmentsLoaded: (attachments: AttachmentResult | undefined) => void;
  selectedEmailCount: number;
  onClearSelectedEmails: () => void;
  isMobile: boolean;
  isModalOpen: boolean;
  onCloseModal: () => void;
  shareRootUrl: string;
  sharepointAdapter: ISharepointAdapter;
  tenancy: Tenancy;
}

export const Preview: React.FC<PreviewProps> = ({
  previewUrl,
  email,
  isPreviewLoading,
  onLoadingComplete,
  onAttachmentsLoaded,
  selectedEmailCount,
  onClearSelectedEmails,
  isMobile,
  isModalOpen,
  onCloseModal,
  sharepointAdapter,
  tenancy,
}) => {
  const styles = useStyles();
  const [attachments, setAttachments] = useState<AttachmentResult | undefined>(
    undefined
  );
  const [isAttachmentLoading, setIsAttachmentLoading] = useState(false);
  const [subject, setSubject] = useState("");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  useEffect(() => {
    if (email) {
      fetchAttachments(email);
    }
  }, [email, selectedEmailCount]);

  const fetchAttachments = async (email: Email) => {
    setIsAttachmentLoading(true);
    try {
      const attachmentResult = await handleAttachments(
        email,
        sharepointAdapter
      );
      setAttachments(attachmentResult);
      onAttachmentsLoaded(attachmentResult);
      setSubject(email.subject);
    } catch (error) {
      console.error("Error handling attachments:", error);
      setAttachments(undefined);
    } finally {
      setIsAttachmentLoading(false);
      onLoadingComplete();
    }
  };

  const isLoading = isPreviewLoading || isAttachmentLoading;

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const hasAttachments = attachments && attachments.attachments.length > 0;

  const renderMobileView = () => (
    <div className={styles.fullScreenModal}>
      <div className={styles.modalHeader}>
        <Button
          icon={<Dismiss24Regular />}
          onClick={onCloseModal}
          className={styles.dismissButton}
        />
        <Subtitle1>{subject}</Subtitle1>
      </div>
      <div className={styles.modalContent}>
        {isLoading ? (
          <div className={styles.spinnerContainer}>
            <Spinner size="large" />
          </div>
        ) : (
          <iframe
            title="email-preview"
            src={previewUrl}
            className={styles.fullScreenIframe}
          />
        )}
      </div>
    </div>
  );

  const renderDesktopView = () => (
    <div className={styles.previewContainer}>
      {isLoading ? (
        <div className={styles.spinnerContainer}>
          <Spinner size="large" />
        </div>
      ) : (
        <>
          {previewUrl && selectedEmailCount === 1 ? (
            <div className={styles.outerContainer}>
              <div className={`${styles.section} ${styles.topBanner}`}>
                <Subtitle1>{subject}</Subtitle1>
                {hasAttachments && (
                  <Button
                    icon={<AttachRegular />}
                    onClick={toggleDrawer}
                    className={styles.attachmentButton}
                  >
                    Attachments ({attachments.attachments.length})
                  </Button>
                )}
              </div>
              <div className={`${styles.section} ${styles.middleContent}`}>
                <iframe
                  title="email-preview"
                  src={previewUrl}
                  className={styles.iframe}
                />
              </div>
              {hasAttachments && renderAttachmentDrawer()}
            </div>
          ) : (
            renderNoEmailMessage()
          )}
        </>
      )}
    </div>
  );

  const renderAttachmentDrawer = () => (
    <Drawer
      open={isDrawerOpen}
      onOpenChange={(_, { open }) => setIsDrawerOpen(open)}
      position="end"
      modalType="non-modal"
    >
      <div className={styles.drawerContent}>
        <div className={styles.drawerHeader}>
          <Button
            appearance="subtle"
            aria-label="Close"
            icon={<Dismiss24Regular />}
            onClick={() => setIsDrawerOpen(false)}
            className={styles.dismissButton}
          />
        </div>
        <Attachments attachments={attachments} />
      </div>
    </Drawer>
  );

  const renderNoEmailMessage = () => (
    <div className={styles.noEmailMessage}>
      <AtveroLogo tenancy={tenancy} />
      <br />
      <br />
      {selectedEmailCount === 0 ? (
        <Subtitle2>No email selected</Subtitle2>
      ) : (
        <>
          <Subtitle2>{selectedEmailCount} emails selected</Subtitle2>
          <Link
            onClick={onClearSelectedEmails}
            className={styles.clearSelection}
          >
            Clear selected emails
          </Link>
        </>
      )}
    </div>
  );

  if (isMobile && isModalOpen) return renderMobileView();
  if (!isMobile) return renderDesktopView();

  return null;
};

const useStyles = makeStyles({
  fullScreenModal: {
    position: "fixed",
    top: 0,
    left: 0,
    width: "100vw",
    height: "100vh",
    backgroundColor: "#FFFFFF",
    display: "flex",
    flexDirection: "column",
    zIndex: 1000,
    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.2)",
  },
  modalHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: tokens.spacingVerticalS,
    boxShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
    zIndex: 10,
    backgroundColor: "#FFFFFF",
  },
  modalContent: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    overflow: "hidden",
  },
  dismissButton: {
    marginRight: tokens.spacingVerticalS,
  },
  fullScreenIframe: {
    flex: 1,
    border: "none",
    width: "100%",
    height: "100%",
  },
  previewContainer: {
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
  },
  spinnerContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
  },
  outerContainer: {
    height: "100%",
    display: "flex",
    flexDirection: "column",
  },
  section: {
    padding: tokens.spacingVerticalS,
  },
  topBanner: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  middleContent: {
    flex: 1,
    overflow: "auto",
  },
  iframe: {
    width: "100%",
    height: "100%",
    border: "none",
  },
  noEmailMessage: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
    width: "100%",
  },
  attachmentButton: {
    marginLeft: "auto",
  },
  drawerContent: {
    padding: tokens.spacingVerticalM,
  },
  drawerHeader: {
    display: "flex",
    justifyContent: "flex-end",
    alignItems: "center",
    marginBottom: tokens.spacingVerticalS,
  },
  clearSelection: {
    marginTop: tokens.spacingVerticalS,
    cursor: "pointer",
    color: tokens.colorBrandForeground1,
  },
});
