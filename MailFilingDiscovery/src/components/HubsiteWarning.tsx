import React from "react";
import {
  MessageBar,
  makeStyles,
  tokens,
  mergeClasses,
} from "@fluentui/react-components";

interface HubsiteWarningProps {
  className?: string;
}

const useStyles = makeStyles({
  root: {
    width: "96%",
  },
  messageBar: {
    backgroundColor: tokens.colorNeutralBackground1,
  },
  icon: {
    color: tokens.colorStatusWarningForeground2,
  },
});

const HubsiteWarning: React.FC<HubsiteWarningProps> = ({ className }) => {
  const styles = useStyles();

  return (
    <div className={mergeClasses(styles.root, className)}>
      <MessageBar intent="warning" style={{ paddingRight: "12px" }}>
        No hubsites were found for your account. Please contact your
        administrator to get access to the required hubsites.
      </MessageBar>
    </div>
  );
};

export default HubsiteWarning;
