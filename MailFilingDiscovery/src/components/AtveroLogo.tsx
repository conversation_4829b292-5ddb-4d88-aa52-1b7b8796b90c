import { makeStyles } from "@fluentui/react-components";
import CMap<PERSON>ogo_black from "../../assets/CMap PIM & CMap Mail/CMap Mail/Black_Text/SVG/CMap_Mail_black_text_svg.svg";
import AtveroLogo_black from "../../assets/Atvero_Mail_black_text.svg";
import { Tenancy } from "../shared/types/Tenancy";

interface AtveroLogoProps {
  tenancy: Tenancy;
}

export const AtveroLogo = ({ tenancy }: AtveroLogoProps) => {
  const styles = useStyles();

  const logoSrc = tenancy === Tenancy.CMap ? CMapLogo_black : AtveroLogo_black;
  const altText = tenancy === Tenancy.CMap ? "CMap Mail" : "Atvero Mail";
  return <img src={logoSrc} alt={altText} className={styles.logoSmall} />;
};

const useStyles = makeStyles({
  logoSmall: { width: "300px", paddingLeft: "6px", paddingRight: "6px" },
});
