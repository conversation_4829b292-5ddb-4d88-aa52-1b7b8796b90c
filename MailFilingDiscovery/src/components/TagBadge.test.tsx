// TagBadge.test.tsx
import { render } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { TagBadge } from "./TagBadge";
import { Tag, DEFAULT_TAGS } from "../shared/types/Tags";

const tags: Tag[] = DEFAULT_TAGS;

// Mock the useStyles hook
vi.mock("./useStyles", () => ({
  useStyles: () => ({
    tag: "mocked-tag-class",
  }),
}));

describe("TagBadge Component", () => {
  it("renders correctly with a valid tagName", () => {
    const { getByText } = render(<TagBadge tags={tags} tagName="Internal" />);
    const label = getByText("Internal");
    expect(label).toBeInTheDocument();
  });

  it("renders null when tagName is undefined", () => {
    const { container } = render(<TagBadge tags={tags} tagName={undefined} />);
    expect(container.firstChild).toBeNull();
  });

  it("renders null when tagName results in NoTag", () => {
    const { container } = render(<TagBadge tags={tags} tagName="invalidTag" />);
    expect(container.firstChild).toBeNull();
  });
});
