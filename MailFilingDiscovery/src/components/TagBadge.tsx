import React from "react";
import { Label, makeStyles, tokens } from "@fluentui/react-components";
import { Tag } from "../shared/types/Tags";

export const TagBadge: React.FC<{
  tags: Tag[];
  tagName: string | undefined;
}> = ({ tags, tagName }) => {
  const styles = useStyles();

  if (
    tagName === undefined ||
    tagName === "No Tag" ||
    tagName === "" ||
    tagName == "NoTag"
  )
    return null; // display nothing for no tag

  const tagInfo = tags.filter((t) => t.Name === tagName);

  if (tagInfo.length < 1) {
    return null;
  }

  const defaultTag: Tag = {
    Name: tagName,
    BackgroundColour: "Silver",
    Colour: "Black",
  };

  const displayTag = tagInfo.length > 0 ? tagInfo[0] : defaultTag;
  return (
    <Label
      className={styles.tag}
      style={{
        backgroundColor: displayTag.BackgroundColour,
        color: displayTag.Colour,
      }}
    >
      {displayTag.Name}
    </Label>
  );
};

const useStyles = makeStyles({
  tag: {
    borderRadius: "4px",
    padding: "2px 8px",
    fontSize: tokens.fontSizeBase200,
  },
});
