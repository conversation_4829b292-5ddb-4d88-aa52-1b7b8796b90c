import { describe, it, expect, beforeAll } from "vitest";
import { render, screen } from "@testing-library/react";
import HubsiteWarning from "./HubsiteWarning";

// Mock ResizeObserver before tests
beforeAll(() => {
  class ResizeObserverMock {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  global.ResizeObserver = ResizeObserverMock;
});

describe("HubsiteWarning", () => {
  it("renders the warning message", () => {
    render(<HubsiteWarning />);
    expect(
      screen.getByText(/No hubsites were found for your account/i)
    ).toBeInTheDocument();
  });

  it("renders MessageBar component with warning intent", () => {
    render(<HubsiteWarning />);
    // Looking for the MessageBar by its class and role
    const messageBar = screen.getByRole("group", {
      // The aria-labelledby attribute is present but no name is needed
    });
    expect(messageBar).toHaveClass("fui-MessageBar");
  });

  it("renders with custom className when provided", () => {
    const customClass = "custom-class";
    render(<HubsiteWarning className={customClass} />);
    const container = document.querySelector(`[class*="${customClass}"]`);
    expect(container).toBeInTheDocument();
  });

  it("includes the full warning message", () => {
    render(<HubsiteWarning />);
    const message =
      "No hubsites were found for your account. Please contact your administrator to get access to the required hubsites.";
    expect(screen.getByText(message)).toBeInTheDocument();
  });
});
