import React, { useState, useEffect } from "react";
import {
  Button,
  Input,
  Popover,
  PopoverTrigger,
  PopoverSurface,
  makeStyles,
  Field,
  tokens,
  RadioGroup,
  Radio,
  Checkbox,
} from "@fluentui/react-components";
import {
  Filter24Regular,
  Tag20Regular,
  TextCaseTitle20Regular,
  MailOpenPerson20Regular,
  ArrowForwardDownPerson20Regular,
} from "@fluentui/react-icons";
import { DatePicker } from "@fluentui/react-datepicker-compat";
import { Filter, Operation } from "../shared/types/Filter";
import { SortDirection } from "../shared/utils/FilterUtils";

interface FiltersProps {
  onFilter: (filters: Filter[]) => void;
  onClear: () => void;
  disabledFilters: boolean;
  currentSortColumn: string;
  currentSortDirection: SortDirection;
  onSortChange: (columnKey: string, direction: SortDirection) => void;
  filters: Filter[];
  isConfidential: boolean;
  setIsConfidential: (value: boolean) => void;
}

const Filters: React.FC<FiltersProps> = ({
  onFilter,
  onClear,
  disabledFilters,
  currentSortColumn,
  currentSortDirection,
  onSortChange,
  filters,
  isConfidential,
  setIsConfidential,
}) => {
  const styles = useStyles();
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [subject, setSubject] = useState("");
  const [from, setFrom] = useState("");
  const [recipients, setRecipients] = useState("");
  const [startDate, setStartDate] = useState<Date | null | undefined>(null);
  const [endDate, setEndDate] = useState<Date | null | undefined>(null);
  const [hasAttachments, setHasAttachments] = useState(false);
  const [tags, setTags] = useState("");
  const [isImportant, setIsImportant] = useState(false);

  // Sorting state
  const [selectedSortColumn, setSelectedSortColumn] =
    useState(currentSortColumn);
  const [selectedSortDirection, setSelectedSortDirection] =
    useState(currentSortDirection);

  // Reset fields when filters are cleared
  useEffect(() => {
    if (filters.length === 0) {
      setSubject("");
      setFrom("");
      setRecipients("");
      setStartDate(null);
      setEndDate(null);
      setHasAttachments(false);
      setTags("");
      setIsImportant(false);
      setIsConfidential(false);
    }
  }, [filters]);

  const applyFilters = () => {
    const filters: Filter[] = [
      {
        field: "EmailSubject",
        values: [subject],
        application: Operation.Contains,
      },
      { field: "EmailFrom", values: [from], application: Operation.Contains },
      {
        field: "EmailTo",
        values: [recipients],
        application: Operation.Contains,
      },
      {
        field: "EmailReceived",
        values: [startDate?.toISOString() ?? ""],
        application: Operation.GreaterThanDateTime,
      },
      {
        field: "EmailReceived",
        values: [endDate?.toISOString() ?? ""],
        application: Operation.LessThanDateTime,
      },
    ];

    // Add a new filter for tags if it's not empty
    if (tags) {
      filters.push({
        field: "EmailTags",
        values: [tags],
        application: Operation.Contains,
      });
    }

    if (hasAttachments) {
      filters.push({
        field: "AttachmentCount",
        values: ["0"],
        application: Operation.GreaterThan,
      });
    }

    if (isImportant) {
      filters.push({
        field: "EmailImportant",
        values: ["Yes"],
        application: Operation.Equals,
      });
    }

    if (isConfidential) {
      filters.push({
        field: "EmailConfidential",
        values: ["true"],
        application: Operation.Equals,
      });
    }

    onFilter(filters);
    setIsPopoverOpen(false);
  };

  const clearFilters = () => {
    setSubject("");
    setFrom("");
    setRecipients("");
    setStartDate(null);
    setEndDate(null);
    setTags("");
    setHasAttachments(false);
    onClear();
    setIsPopoverOpen(false);
    setIsImportant(false);
    setIsConfidential(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      applyFilters();
    }
  };

  const handleSortColumnChange = (columnKey: string) => {
    setSelectedSortColumn(columnKey);
    onSortChange(columnKey, selectedSortDirection);
  };

  const handleSortDirectionChange = (direction: SortDirection) => {
    setSelectedSortDirection(direction);
    onSortChange(selectedSortColumn, direction);
  };

  return (
    <div className={styles.outerContainer}>
      <Popover
        open={isPopoverOpen}
        onOpenChange={(_, { open }) => setIsPopoverOpen(open)}
      >
        <PopoverTrigger>
          <Button
            disabled={disabledFilters}
            icon={<Filter24Regular />}
            onClick={() => setIsPopoverOpen(!isPopoverOpen)}
            aria-label="Open filters"
          />
        </PopoverTrigger>
        <PopoverSurface>
          <div className={styles.container} onKeyDown={handleKeyDown}>
            <Input
              placeholder="Subject"
              value={subject}
              onChange={(_, data) => setSubject(data.value)}
              contentBefore={<TextCaseTitle20Regular />}
            />
            <Input
              placeholder="Sent by"
              value={from}
              onChange={(_, data) => setFrom(data.value)}
              contentBefore={<ArrowForwardDownPerson20Regular />}
            />
            <Input
              placeholder="Recipients"
              value={recipients}
              onChange={(_, data) => setRecipients(data.value)}
              contentBefore={<MailOpenPerson20Regular />}
            />
            <Input
              placeholder="Tags"
              value={tags}
              onChange={(_, data) => setTags(data.value)}
              contentBefore={<Tag20Regular />}
            />
            <Field label="Start Date">
              <DatePicker
                className={styles.control}
                placeholder="Select a date..."
                value={startDate}
                onSelectDate={setStartDate}
              />
            </Field>
            <Field label="End Date">
              <DatePicker
                className={styles.control}
                placeholder="Select a date..."
                value={endDate}
                onSelectDate={setEndDate}
              />
            </Field>
            <div className={styles.checkboxContainer}>
              <Checkbox
                label="Has attachments"
                checked={hasAttachments}
                onChange={(_, data) => setHasAttachments(!!data.checked)}
              />
              <Checkbox
                label="Important Emails Only"
                checked={isImportant}
                onChange={(_, data) => setIsImportant(!!data.checked)}
              />
            </div>

            <hr className={styles.divider} />

            <div className={styles.sortContainer}>
              <div className={styles.sortTitle}>Sort by:</div>
              <RadioGroup
                value={selectedSortColumn}
                onChange={(_, data) => handleSortColumnChange(data.value)}
              >
                <Radio value="subject" label="Subject" />
                <Radio value="sentBy" label="Sent By" />
                <Radio value="dateFiled" label="Received On" />
              </RadioGroup>
            </div>

            <hr className={styles.divider} />

            <div className={styles.sortContainer}>
              <div className={styles.sortTitle}>Sort Direction:</div>
              <RadioGroup
                value={selectedSortDirection}
                onChange={(_, data) =>
                  handleSortDirectionChange(data.value as SortDirection)
                }
              >
                <Radio value={SortDirection.Descending} label="Descending" />
                <Radio value={SortDirection.Ascending} label="Ascending" />
              </RadioGroup>
            </div>

            <div className={styles.buttonContainer}>
              <Button onClick={clearFilters}>Clear</Button>
              <Button appearance={"primary"} onClick={applyFilters}>
                Apply
              </Button>
            </div>
          </div>
        </PopoverSurface>
      </Popover>
    </div>
  );
};

const useStyles = makeStyles({
  outerContainer: {
    margin: tokens.spacingHorizontalS,
  },
  container: {
    minWidth: "250px",
    padding: "20px",
    display: "flex",
    flexDirection: "column",
    gap: "10px",
    margin: "10px",
  },
  checkboxContainer: {
    display: "flex",
    flexDirection: "column",
    margin: "0px",
    padding: "0px",
  },
  sortContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "5px",
  },
  sortTitle: {
    fontWeight: "bold",
  },
  buttonContainer: {
    display: "flex",
    justifyContent: "space-between",
    marginTop: "10px",
  },
  control: {
    maxWidth: "300px",
  },
  divider: {
    border: "1px solid #ccc",
    margin: "10px 0",
  },
});

export default Filters;
