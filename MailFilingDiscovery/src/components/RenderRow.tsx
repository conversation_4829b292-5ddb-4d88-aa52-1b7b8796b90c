import React from "react";
import {
  TableRow,
  TableSelectionCell,
  TableCell,
  makeStyles,
  SkeletonItem,
  Tooltip,
} from "@fluentui/react-components";
import {
  Attach20Regular,
  Important20Regular,
  LockClosed20Regular,
} from "@fluentui/react-icons";
import { Email } from "../shared/types/Email";
import { formatDate } from "../shared/utils/FormatDate";
import RecipientPersona from "./RecipientPersona";
import { TagBadge } from "./TagBadge";
import { Tag } from "../shared/types/Tags";
import { ISharepointAdapter } from "../adapters/ISharepointAdapter";

const useStyles = makeStyles({
  tableRow: {
    "&:hover": {
      cursor: "pointer",
    },
  },
  redIcon: {
    color: "red",
  },
  confidentialIcon: {
    marginLeft: "8px",
  },
  tableCell: {
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    padding: "10px",
    display: "inline-block",
    alignContent: "center",
  },
  tableCellHidden: {
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    padding: "10px",
    alignContent: "center",
    display: "none",
  },
  dynamicContainer: {
    display: "grid",
    gridTemplateColumns: "auto 1fr",
    alignItems: "center",
    gap: "8px",
    width: "100%",
  },
  fixedColumn: {
    whiteSpace: "nowrap",
  },
  dynamicColumn: {
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  centeredMessage: {
    textAlign: "center",
    padding: "50px",
    color: "#888",
    fontSize: "16px",
  },
});

interface RenderRowProps {
  hideProjectColumn: boolean;
  index: number;
  style: React.CSSProperties;
  items: Email[];
  selectedEmails: Email[];
  onClick: (item: Email) => void;
  onSingleClick: (item: Email) => void;
  adapter: ISharepointAdapter;
  isItemLoaded: (index: number) => boolean;
  tags: Tag[];
}

export const generateShimmerRows = (count: number) => {
  const rows = [];
  for (let i = 0; i < count; i++) {
    rows.push(<ShimmerRow key={i} style={{}} />);
  }
  return rows;
};

export const ShimmerRow: React.FC<{ style: React.CSSProperties }> = ({
  style,
}) => (
  <TableRow style={style} className={useStyles().tableRow}>
    <TableSelectionCell>
      <SkeletonItem shape="circle" size={24} />
    </TableSelectionCell>
    <TableCell>
      <SkeletonItem shape="rectangle" size={24} />
    </TableCell>
    <TableCell>
      <SkeletonItem shape="rectangle" size={24} />
    </TableCell>
    <TableCell>
      <SkeletonItem shape="rectangle" size={24} />
    </TableCell>
    <TableCell>
      <SkeletonItem shape="rectangle" size={24} />
    </TableCell>
  </TableRow>
);

const RenderRow: React.FC<RenderRowProps> = ({
  index,
  style,
  items,
  selectedEmails,
  onClick,
  onSingleClick,
  adapter,
  isItemLoaded,
  hideProjectColumn,
  tags,
}) => {
  const classes = useStyles();
  const item = items[index];

  if (!isItemLoaded(index) || !item) {
    return (
      <div>
        {index === 0 ? (
          <div className={classes.centeredMessage}>
            <p>
              Please select a project or start searching for emails to display
              results.
            </p>
          </div>
        ) : (
          <ShimmerRow style={style} />
        )}
      </div>
    );
  } else {
    const isConfidential = item.emailConfidential;
    const selected = selectedEmails.some((email) => email.id === item.id);

    const attachmentCount = parseInt(item.attachmentCount ?? "0", 10);
    const hasAttachments = attachmentCount > 0;

    const projectClass = hideProjectColumn
      ? classes.tableCellHidden
      : classes.tableCell;

    // Determine the tag to display, use "No tag" as the default if tag is empty or undefined
    const tagToDisplay = item.tag && item.tag !== "" ? item.tag : "No tag";

    const isImportant = item.emailImportant === "Yes";

    return (
      <TableRow
        data-testid="table-row"
        style={style}
        key={index || item.subject}
        onClick={() => {
          onSingleClick(item);
        }}
        aria-selected={selected}
        appearance={selected ? "brand" : "none"}
        className={classes.tableRow}
      >
        <TableSelectionCell
          onClick={(event) => {
            event.stopPropagation();
            onClick(item);
            return false;
          }}
          checked={selected}
          checkboxIndicator={{
            "aria-label": selected ? "Deselect row" : "Select row",
          }}
        />

        {!hideProjectColumn && (
          <TableCell className={projectClass}>
            <div className={classes.dynamicContainer}>
              <span className={classes.fixedColumn}>{item.projectCode}</span>
            </div>
          </TableCell>
        )}
        <TableCell className={classes.tableCell}>
          <div className={classes.dynamicContainer}>
            <Tooltip content={item.subject} relationship="label">
              <span className={classes.dynamicColumn}>{item.subject}</span>
            </Tooltip>
            <span className={classes.fixedColumn}>
              {isConfidential && (
                <LockClosed20Regular
                  data-testid="confidential-icon"
                  className={classes.confidentialIcon}
                />
              )}
              {isImportant && (
                <Important20Regular
                  data-testid="important-icon"
                  className={classes.redIcon}
                />
              )}
              {hasAttachments && (
                <Attach20Regular data-testid="attachment-icon" />
              )}
            </span>
          </div>
        </TableCell>
        <TableCell className={classes.tableCell}>
          <RecipientPersona adapter={adapter} recipient={item.sentBy} />
        </TableCell>
        <TableCell className={classes.tableCell}>
          <div className={classes.dynamicContainer}>
            <Tooltip content={formatDate(item.dateFiled)} relationship="label">
              <span className={classes.dynamicColumn}>
                {formatDate(item.dateFiled)}
              </span>
            </Tooltip>
          </div>
        </TableCell>
        <TableCell className={classes.tableCell}>
          <div className={classes.dynamicContainer}>
            <div className={classes.fixedColumn}>
              {tagToDisplay !== undefined && (
                <TagBadge tags={tags} tagName={tagToDisplay} />
              )}
            </div>
          </div>
        </TableCell>
      </TableRow>
    );
  }
};

export default RenderRow;
