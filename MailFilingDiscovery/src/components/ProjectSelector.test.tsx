import { describe, it, expect } from "vitest";
import { partitionProjects } from "./ProjectSelector";
import { Project } from "../shared/types/Project";

describe("partitionProjects", () => {
  it("the partitions are all empry if the projects list is empty", () => {
    const { selectedPartition, favouritePartition, regularPartition } =
      partitionProjects([], []);
    expect(selectedPartition).toHaveLength(0);
    expect(favouritePartition).toHaveLength(0);
    expect(regularPartition).toHaveLength(0);
  });

  it("all projects are selected", () => {
    const projects: Project[] = [
      { code: "1234", id: "1", title: "Project 1", favorite: false },
      { code: "1235", id: "2", title: "Project 2", favorite: false },
    ];
    const selectedProjects = ["1234", "1235"];
    const { selectedPartition, favouritePartition, regularPartition } =
      partitionProjects(projects, selectedProjects);
    expect(selectedPartition.map((p: Project) => p.id)).toEqual(["1", "2"]);
    expect(favouritePartition).toHaveLength(0);
    expect(regularPartition).toHaveLength(0);
  });

  it("no projects are selected", () => {
    const projects: Project[] = [
      { code: "1234", id: "1", title: "Project 1", favorite: false },
      { code: "1235", id: "2", title: "Project 2", favorite: false },
    ];
    const selectedProjects: string[] = [];
    const { selectedPartition, favouritePartition, regularPartition } =
      partitionProjects(projects, selectedProjects);
    expect(selectedPartition).toHaveLength(0);
    expect(favouritePartition).toHaveLength(0);
    expect(regularPartition.map((p: Project) => p.id)).toEqual(
      projects.map((p) => p.id)
    );
  });

  it("all projects are favourites", () => {
    const projects: Project[] = [
      { code: "1234", id: "1", title: "Project 1", favorite: true },
      { code: "1235", id: "2", title: "Project 2", favorite: true },
    ];
    const selectedProjects: string[] = [];
    const { selectedPartition, favouritePartition, regularPartition } =
      partitionProjects(projects, selectedProjects);
    expect(selectedPartition).toHaveLength(0);
    expect(favouritePartition.map((p: Project) => p.id)).toEqual(
      projects.map((p) => p.id)
    );
    expect(regularPartition).toHaveLength(0);
  });

  it("no projects are favourites", () => {
    const projects: Project[] = [
      { code: "1234", id: "1", title: "Project 1", favorite: false },
      { code: "1235", id: "2", title: "Project 2", favorite: false },
    ];
    const selectedProjects: string[] = [];
    const { selectedPartition, favouritePartition, regularPartition } =
      partitionProjects(projects, selectedProjects);
    expect(selectedPartition).toHaveLength(0);
    expect(favouritePartition).toHaveLength(0);
    expect(regularPartition.map((p: Project) => p.id)).toEqual(
      projects.map((p) => p.id)
    );
  });

  it("one of each and nothing else", () => {
    const projects: Project[] = [
      { code: "1234", id: "1", title: "Project 1", favorite: false },
      { code: "1235", id: "2", title: "Project 2", favorite: true },
      { code: "1236", id: "3", title: "Project 3", favorite: false },
    ];
    const selectedProjects: string[] = ["1236"];
    const { selectedPartition, favouritePartition, regularPartition } =
      partitionProjects(projects, selectedProjects);
    expect(selectedPartition[0]?.id).toBe("3");
    expect(favouritePartition[0]?.id).toBe("2");
    expect(regularPartition[0]?.id).toBe("1");
  });
});
