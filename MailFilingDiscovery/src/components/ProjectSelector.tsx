import React, { useState, useEffect, useCallback } from "react";
import { debounce } from "lodash";
import {
  Combobox,
  Option,
  OptionGroup,
  makeStyles,
  useId,
  Spinner,
} from "@fluentui/react-components";
import { Project } from "../shared/types/Project";
import { clampText } from "../shared/utils/ClampText";

const useStyles = makeStyles({
  test: {
    "& ::-webkit-scrollbar": {
      width: "8px",
    },
    "& ::-webkit-scrollbar-track": {
      backgroundColor: "#f0f0f0",
    },
    "& ::-webkit-scrollbar-thumb": {
      backgroundColor: "#cccccc",
      borderRadius: "10px",
    },
    "& ::-webkit-scrollbar-thumb:hover": {
      backgroundColor: "#bbbbbb",
    },
  },
  root: {
    display: "grid",
    gridTemplateRows: "repeat(1fr)",
    justifyItems: "start",
    gap: "2px",
    maxWidth: "300px",
    "& ::-webkit-scrollbar": {
      width: "8px",
    },
    "& ::-webkit-scrollbar-track": {
      backgroundColor: "#f0f0f0",
    },
    "& ::-webkit-scrollbar-thumb": {
      backgroundColor: "#cccccc",
      borderRadius: "10px",
    },
    "& ::-webkit-scrollbar-thumb:hover": {
      backgroundColor: "#bbbbbb",
    },
  },
  options: {
    maxHeight: "40vh",
    "& ::-webkit-scrollbar": {
      width: "8px",
    },
    "& ::-webkit-scrollbar-track": {
      backgroundColor: "#f0f0f0",
    },
    "& ::-webkit-scrollbar-thumb": {
      backgroundColor: "#cccccc",
      borderRadius: "10px",
    },
    "& ::-webkit-scrollbar-thumb:hover": {
      backgroundColor: "#bbbbbb",
    },
  },
  spinner: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100px",
  },
});

type ProjectPartition = {
  selectedPartition: Project[];
  favouritePartition: Project[];
  regularPartition: Project[];
};

export const partitionProjects = (
  projects: Project[],
  selectedProjects: string[]
): ProjectPartition => {
  return projects.reduce(
    (acc: ProjectPartition, project) => {
      if (selectedProjects.includes(project.code)) {
        acc.selectedPartition.push(project);
      } else if (project.favorite) {
        acc.favouritePartition.push(project);
      } else {
        acc.regularPartition.push(project);
      }
      return acc;
    },
    { selectedPartition: [], favouritePartition: [], regularPartition: [] }
  );
};

interface ProjectSelectorProps {
  projects: Project[];
  selectedProjects: string[];
  onProjectSelect: (selectedProjects: string[]) => void;
  onSearchProjects: (searchTerm: string) => Promise<void>;
  isLoading: boolean;
  disabled: boolean;
}

export const ProjectSelector: React.FC<ProjectSelectorProps> = ({
  projects,
  selectedProjects,
  onProjectSelect,
  onSearchProjects,
  isLoading,
  disabled,
}) => {
  const styles = useStyles();
  const comboId = useId("combo-multi");
  const [selected, setSelected] = useState<string[]>(selectedProjects);
  const [inputValue, setInputValue] = useState<string>("");

  useEffect(() => {
    setSelected(selectedProjects);
  }, [selectedProjects]);

  const debouncedSearchProjects = useCallback(
    debounce((searchTerm: string) => onSearchProjects(searchTerm), 300),
    [onSearchProjects]
  );

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newInputValue = event.target.value;
    setInputValue(newInputValue);
    debouncedSearchProjects(newInputValue);
  };

  const handleOptionSelect = (
    _event: any,
    data: { optionValue?: string; selectedOptions: string[] }
  ) => {
    setSelected(data.selectedOptions);
    onProjectSelect(data.selectedOptions);
  };

  const { selectedPartition, favouritePartition, regularPartition } =
    partitionProjects(projects, selectedProjects);

  const renderOption = (project: Project, index: number) => (
    <Option
      data-testid={`project-item-${project.code}`}
      text={project.title ?? ""}
      key={index}
      value={project.code}
    >
      {project.code} - {clampText(project.title ?? "", 40)}
    </Option>
  );

  return (
    <div className={styles.test}>
      <Combobox
        aria-labelledby={comboId}
        multiselect
        value={inputValue}
        placeholder="Select a Project"
        selectedOptions={selected}
        className={styles.root}
        onInput={handleInputChange}
        onOptionSelect={handleOptionSelect}
        disabled={disabled}
      >
        {isLoading ? (
          <div className={styles.spinner}>
            <Spinner size="small" />
          </div>
        ) : (
          <>
            {selectedProjects.length > 0 && (
              <OptionGroup label="Selected Projects">
                {selectedPartition.map((project, index) =>
                  renderOption(project, index)
                )}
              </OptionGroup>
            )}
            {favouritePartition.length > 0 && (
              <OptionGroup label="Favorite Projects">
                {favouritePartition.map((project, index) =>
                  renderOption(project, index)
                )}
              </OptionGroup>
            )}

            <OptionGroup label="Projects" className={styles.options}>
              {regularPartition.map((project, index) =>
                renderOption(project, index)
              )}
            </OptionGroup>
          </>
        )}
      </Combobox>
    </div>
  );
};
