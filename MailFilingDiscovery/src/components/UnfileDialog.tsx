import {
  Button,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
} from "@fluentui/react-components";

interface UnfileDialogProps {
  handleUnfile: () => void;
  isUnfiling: boolean;
}

export const UnfileDialog: React.FC<UnfileDialogProps> = ({
  handleUnfile,
  isUnfiling,
}) => {
  return (
    <DialogSurface>
      <DialogBody>
        <DialogTitle data-testid="dialog-title">Unfile Email</DialogTitle>
        <DialogContent>
          Unfiling an email will permanently delete the email from the filing
          area and stop all future filing of emails in the conversation. The
          unfile cannot be undone.
        </DialogContent>

        <DialogActions>
          <Button
            appearance="primary"
            onClick={handleUnfile}
            disabled={isUnfiling}
          >
            Unfile Email
          </Button>
          {/* DialogTrigger inside of a Dialog still works properly */}
          <DialogTrigger disableButtonEnhancement>
            <Button appearance="secondary" disabled={isUnfiling}>
              Cancel
            </Button>
          </DialogTrigger>
        </DialogActions>
      </DialogBody>
    </DialogSurface>
  );
};
