import React from "react";
import {
  Combobox,
  Option,
  makeStyles,
  useId,
  Spinner,
  tokens,
  Avatar,
} from "@fluentui/react-components";
import { Hubsite } from "../shared/types/Hubsite";

interface HubSiteSelectorProps {
  hubsites?: Hubsite[];
  selectedHubsite: Hubsite | undefined;
  onHubsiteSelect: (selectedHubsite: Hubsite | undefined) => void;
  isLoading?: boolean;
}

const useStyles = makeStyles({
  root: {
    display: "grid",
    gridTemplateRows: "repeat(1fr)",
    justifyItems: "start",
    gap: "2px",
    maxWidth: "300px",
    backgroundColor: tokens.colorNeutralBackground1,
    scrollbarWidth: "thin",
    scrollbarColor: `${tokens.colorNeutralBackground4} ${tokens.colorNeutralBackground1}`,
  },
  spinner: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100px",
  },
  optionContent: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    width: "100%",
  },
  avatar: {
    flexShrink: 0,
  },
});

const HubSiteSelector: React.FC<HubSiteSelectorProps> = ({
  hubsites = [],
  selectedHubsite,
  onHubsiteSelect,
  isLoading = false,
}) => {
  const styles = useStyles();
  const comboId = useId("hubsite-selector");

  const handleHubsiteSelect = (
    _event: any,
    data: { optionValue?: string; selectedOptions: string[] }
  ) => {
    const selectedHubPath = data.selectedOptions[0];
    const selectedHub = hubsites.find((hub) => hub.path === selectedHubPath);
    onHubsiteSelect(selectedHub);
  };

  const renderOption = (hubsite: Hubsite) => (
    <Option key={hubsite.path} value={hubsite.path} text={hubsite.displayName}>
      <div className={styles.optionContent}>
        <Avatar
          shape="square"
          name={hubsite.displayName}
          color={hubsite.url === "" ? "neutral" : "colorful"}
          size={28}
          className={styles.avatar}
        />

        {hubsite.displayName}
      </div>
    </Option>
  );

  if (isLoading) {
    return (
      <div className={styles.spinner}>
        <Spinner size="small" />
      </div>
    );
  }

  return (
    <Combobox
      data-testid="hubsite-combobox"
      aria-labelledby={comboId}
      placeholder="Select a Hubsite"
      value={selectedHubsite?.displayName}
      selectedOptions={selectedHubsite ? [selectedHubsite.path] : []}
      className={styles.root}
      onOptionSelect={handleHubsiteSelect}
    >
      {hubsites.map(renderOption)}
    </Combobox>
  );
};

export default HubSiteSelector;
