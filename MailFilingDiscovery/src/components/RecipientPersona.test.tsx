import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import RecipientPersona from "./RecipientPersona";
import { useRecipient } from "../shared/utils/useRecipient";
import { TestSharepointAdapter } from "../adapters/TestSharepointAdapter";

// Mocking useRecipient
vi.mock("../shared/utils/useRecipient", () => ({
  useRecipient: vi.fn(),
}));

describe("RecipientPersona", () => {
  let mockAdapter: TestSharepointAdapter;

  beforeEach(() => {
    mockAdapter = new TestSharepointAdapter();

    vi.clearAllMocks();
  });

  it("renders the recipient persona with name and email", () => {
    const recipient = "<PERSON> <<EMAIL>>";

    vi.mocked(useRecipient).mockReturnValue({
      name: "<PERSON>",
      email: "<EMAIL>",
      img: null,
    });

    render(<RecipientPersona adapter={mockAdapter} recipient={recipient} />);

    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("renders with only email when name is same as email", () => {
    const recipient = "<EMAIL>";

    vi.mocked(useRecipient).mockReturnValue({
      name: "<EMAIL>",
      email: "<EMAIL>",
      img: null,
    });

    render(<RecipientPersona adapter={mockAdapter} recipient={recipient} />);

    const primaryText = screen.queryByText("<EMAIL>");
    expect(primaryText).toBeInTheDocument();
  });

  it("renders avatar with image when available", () => {
    const recipient = "<EMAIL>";
    const mockImageUrl = "mock-image-url";

    vi.mocked(useRecipient).mockReturnValue({
      name: "John Doe",
      email: "<EMAIL>",
      img: mockImageUrl,
    });

    render(<RecipientPersona adapter={mockAdapter} recipient={recipient} />);

    // Test that the avatar is rendered with the correct aria-label
    const avatar = screen.getByRole("img", { name: "John Doe" });
    expect(avatar).toBeInTheDocument();
  });

  it("renders avatar with initials when no image is available", () => {
    const recipient = "<EMAIL>";

    vi.mocked(useRecipient).mockReturnValue({
      name: "John Doe",
      email: "<EMAIL>",
      img: null,
    });

    render(<RecipientPersona adapter={mockAdapter} recipient={recipient} />);

    // Check for initials in the avatar
    const initials = screen.getByText("JD");
    expect(initials).toBeInTheDocument();
  });

  it("renders with email in aria-label", () => {
    const recipient = "John Doe <<EMAIL>>";
    const email = "<EMAIL>";

    vi.mocked(useRecipient).mockReturnValue({
      name: "John Doe",
      email,
      img: null,
    });

    render(<RecipientPersona adapter={mockAdapter} recipient={recipient} />);

    // Check that the Persona div has the email in its aria-label
    const persona = screen.getByLabelText(email);
    expect(persona).toBeInTheDocument();
  });

  it("renders with correct primary and secondary text", () => {
    const recipient = "John Doe <<EMAIL>>";

    vi.mocked(useRecipient).mockReturnValue({
      name: "John Doe",
      email: "<EMAIL>",
      img: null,
    });

    render(<RecipientPersona adapter={mockAdapter} recipient={recipient} />);

    // Check for both primary and secondary text
    const primaryText = screen.getByText("John Doe");
    const secondaryText = screen.getByText("<EMAIL>");

    expect(primaryText).toHaveClass("fui-Persona__primaryText");
    expect(secondaryText).toHaveClass("fui-Persona__secondaryText");
  });
});
