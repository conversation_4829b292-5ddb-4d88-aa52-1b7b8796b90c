import React, { useEffect, useState } from "react";
import {
  Button,
  makeStyles,
  tokens,
  Tooltip,
  Subtitle2,
} from "@fluentui/react-components";
import { ArrowDownload24Regular, Eye24Regular } from "@fluentui/react-icons";
import { AttachmentResult } from "../shared/types/Attachments";
import { formatFileSize } from "../shared/utils/FormatFileSize";
import { clampText } from "../shared/utils/ClampText";
import {
  OutlookInfo,
  parseOutlookSource,
} from "../shared/utils/OutlookContext";
import { getFileIconClass } from "../shared/utils/FileIconClass";
import { handleAttachmentAction } from "../shared/utils/AttachmentPreviewHelpers";

interface AttachmentsProps {
  attachments: AttachmentResult | undefined;
}

export const Attachments: React.FC<AttachmentsProps> = ({ attachments }) => {
  const styles = useStyles();
  const [outlookInfo, setOutlookInfo] = useState<OutlookInfo | null>(null);

  useEffect(() => {
    const url = window.location.href;
    const parsedInfo = parseOutlookSource(url);
    setOutlookInfo(parsedInfo);
  }, []);

  if (
    !attachments ||
    !attachments.attachments ||
    attachments.attachments.length === 0
  ) {
    return null;
  }

  const attachmentCount = attachments.attachments.length;

  return (
    <div className={styles.container}>
      <div className={styles.labelContainer}>
        <Subtitle2>Attachments - ({attachmentCount})</Subtitle2>
      </div>
      <div className={styles.attachmentList}>
        {attachments.attachments.map((attachment, index) => (
          <div key={index} className={styles.attachmentItem}>
            <div className={styles.attachmentInfo}>
              <i
                className={`ms-Icon ${getFileIconClass(attachment.contentType)} ${styles.icon}`}
                aria-hidden="true"
              ></i>
              <Tooltip
                content={attachment.filename as string}
                relationship="label"
              >
                <span className={styles.title}>
                  {clampText(attachment.filename as string, 20) ||
                    "Unnamed Attachment"}{" "}
                  -{" "}
                </span>
              </Tooltip>
              <span className={styles.size}>
                {formatFileSize(attachment.size)}
              </span>
            </div>
            {outlookInfo?.source === "web" && (
              <div className={styles.attachmentActions}>
                <Tooltip content="Download" relationship="label">
                  <Button
                    icon={
                      <ArrowDownload24Regular className={styles.actionIcon} />
                    }
                    appearance="subtle"
                    size="small"
                    onClick={() =>
                      handleAttachmentAction(attachment, "download")
                    }
                    className={styles.actionButton}
                  />
                </Tooltip>

                <Tooltip content="Preview" relationship="label">
                  <Button
                    icon={<Eye24Regular className={styles.actionIcon} />}
                    appearance="subtle"
                    size="small"
                    onClick={() =>
                      handleAttachmentAction(attachment, "preview")
                    }
                    className={styles.actionButton}
                  />
                </Tooltip>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
const useStyles = makeStyles({
  container: {
    borderBottom: `1px solid ${tokens.colorNeutralStroke1}`,
  },
  labelContainer: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalXS,
    marginBottom: tokens.spacingVerticalS,
  },
  count: {
    color: tokens.colorNeutralForeground2,
  },
  attachmentList: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalS,
  },
  attachmentItem: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: tokens.spacingHorizontalS,
    borderRadius: tokens.borderRadiusMedium,
    border: `1px solid ${tokens.colorNeutralStroke1}`,
  },
  attachmentInfo: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalXS,
  },
  attachmentActions: {
    display: "flex",
    gap: tokens.spacingHorizontalXS,
  },
  icon: {
    height: "20px",
    width: "20px",
  },
  actionIcon: {
    color: "#0F6CBD",
  },
  actionButton: {
    padding: "5px 15px 5px 15px",
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    borderRadius: tokens.borderRadiusMedium,
  },
  title: {
    fontSize: tokens.fontSizeBase400,
  },
  size: {
    color: tokens.colorNeutralForeground2,
    fontSize: tokens.fontSizeBase400,
  },
});
