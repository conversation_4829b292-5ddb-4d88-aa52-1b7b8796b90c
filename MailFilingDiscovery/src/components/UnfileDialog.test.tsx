// UnfileDialog.test.tsx
import { render, screen } from "@testing-library/react";

import { describe, it, expect } from "vitest";
import { UnfileDialog } from "./UnfileDialog";

describe("UnfileDialog Component", () => {
  it("renders correctly", () => {
    render(<UnfileDialog handleUnfile={() => {}} isUnfiling={false} />);
    expect(screen.getByTestId("dialog-title")).toBeInTheDocument();
  });
});
