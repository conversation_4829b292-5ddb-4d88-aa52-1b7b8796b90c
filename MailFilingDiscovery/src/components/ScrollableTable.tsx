import React from "react";
import { FixedSizeList as List } from "react-window";
import InfiniteLoader from "react-window-infinite-loader";
import AutoSizer from "react-virtualized-auto-sizer";
import {
  TableRow,
  TableCell,
  TableBody,
  Table,
  makeStyles,
  tokens,
} from "@fluentui/react-components";
import { Email } from "../shared/types/Email";
import { Column } from "../shared/types/Column";
import RenderRow, { generateShimmerRows } from "./RenderRow";
import RenderStackedRow from "./RenderStackedRow";
import { Hubsite } from "../shared/types/Hubsite";
import { Tag } from "../shared/types/Tags";
import { SortDirection } from "../shared/utils/FilterUtils";
import { ISharepointAdapter } from "../adapters/ISharepointAdapter";

type AutoSizerChildProps = { height: number; width: number };

interface ScrollableTableProps {
  adapter: ISharepointAdapter;
  columns: Column[];
  items: Email[];
  onEmailSelect: (email: Email) => void;
  onSingleEmailSelect: (email: Email) => void;
  selectedEmails: Email[];
  loadMoreItems: () => void;
  hasMoreItems: boolean;
  onSortChange: (columnKey: string, direction: SortDirection) => void;
  currentSort: { columnKey: string; direction: SortDirection };
  hasNextPage: boolean;
  isNextPageLoading: boolean;
  loadNextPage: (startIndex: number, endIndex: number) => void;
  emailsShown: number;
  totalEmails: number;
  loading: boolean;
  useStackedRows: boolean;
  selectedProjectsCount: number;
  hasSearchTerm: boolean;
  hideProjectColumn: boolean;
  selectedHubsite?: Hubsite;
  tags: Tag[];
}

export const renderRow = (
  index: number,
  style: React.CSSProperties,
  items: Email[],
  selectedEmails: Email[],
  onClick: (item: Email) => void,
  onSingleClick: (item: Email) => void,
  adapter: ISharepointAdapter,
  isItemLoaded: (index: number) => boolean,
  hideProjectColumn: boolean,
  useStackedRows: boolean,
  tags: Tag[]
) => {
  const RowComponent = useStackedRows ? RenderStackedRow : RenderRow;
  return (
    <RowComponent
      index={index}
      style={style}
      items={items}
      selectedEmails={selectedEmails}
      onClick={onClick}
      onSingleClick={onSingleClick}
      adapter={adapter}
      isItemLoaded={isItemLoaded}
      hideProjectColumn={hideProjectColumn}
      tags={tags}
    />
  );
};

export const ScrollableTable: React.FC<ScrollableTableProps> = ({
  adapter,

  // Are there more items to load?
  // (This information comes from the most recent API request.)
  columns,
  hasNextPage,
  isNextPageLoading,
  items,
  loadNextPage,
  onEmailSelect,
  onSingleEmailSelect,
  selectedEmails,
  loading,
  useStackedRows,
  selectedProjectsCount,
  hasSearchTerm,
  hideProjectColumn,
  selectedHubsite,
  tags,
}) => {
  const classes = useStyles();

  const itemCount = hasNextPage ? items.length + 1 : items.length;
  const loadMoreItemsCallback = isNextPageLoading ? () => {} : loadNextPage;
  const isItemLoaded = (index: number) => !hasNextPage || index < items.length;

  const renderMessage = () => {
    if (!selectedHubsite) {
      return "Select a hubsite to search filed emails.";
    }
    if (selectedProjectsCount === 0 && !hasSearchTerm) {
      return "Select a project or search a keyword to find your emails.";
    } else if (selectedProjectsCount > 1 && !hasSearchTerm) {
      return "Enter a keyword in your search bar to find your emails from your selected projects.";
    }
    return null;
  };

  const message = renderMessage();

  const renderTableContent = () => {
    if (loading) {
      return generateShimmerRows(15);
    }

    if (message) {
      return (
        <TableRow>
          <TableCell
            colSpan={columns.length + 1}
            className={classes.messageCell}
          >
            {message}
          </TableCell>
        </TableRow>
      );
    }

    return (
      <AutoSizer>
        {({ height, width }: AutoSizerChildProps) => (
          <InfiniteLoader
            isItemLoaded={isItemLoaded}
            itemCount={itemCount}
            loadMoreItems={loadMoreItemsCallback}
          >
            {({ onItemsRendered, ref }) => (
              <List
                className="List"
                height={height}
                itemCount={itemCount}
                itemSize={72}
                onItemsRendered={onItemsRendered}
                ref={ref}
                width={width}
              >
                {({ index, style }) =>
                  renderRow(
                    index,
                    style,
                    items,
                    selectedEmails,
                    onEmailSelect,
                    onSingleEmailSelect,
                    adapter,
                    isItemLoaded,
                    hideProjectColumn,
                    useStackedRows,
                    tags
                  )
                }
              </List>
            )}
          </InfiniteLoader>
        )}
      </AutoSizer>
    );
  };

  return (
    <Table
      noNativeElements={true}
      aria-label="Custom table"
      className={classes.tableContainer}
      sortable
    >
      <TableBody className={classes.tableScroll}>
        {renderTableContent()}
      </TableBody>
    </Table>
  );
};

const useStyles = makeStyles({
  tableContainer: {
    display: "flex",
    flexDirection: "column",
    flex: "1 1 auto",
    overflow: "hidden",
  },
  tableScroll: {
    overflow: "hidden",
    flex: "1 1 auto",
    overflowY: "auto",
    minHeight: "150px",
    minWidth: "250px",
    width: "100%",
    "& ::-webkit-scrollbar": {
      width: "8px",
    },
    "& ::-webkit-scrollbar-track": {
      backgroundColor: "#f0f0f0",
    },
    "& ::-webkit-scrollbar-thumb": {
      backgroundColor: "#cccccc",
      borderRadius: "10px",
    },
    "& ::-webkit-scrollbar-thumb:hover": {
      backgroundColor: "#bbbbbb",
    },
  },
  messageCell: {
    justifyContent: "center",
    textAlign: "center",
    padding: "20px",
    color: tokens.colorNeutralForeground3,
    fontSize: tokens.fontSizeBase300,
  },
  headerText: {
    color: "#888",
    marginLeft: tokens.spacingHorizontalS,
    fontSize: "16px",
  },
  centeredMessage: {
    textAlign: "center",
    padding: "50px",
    color: "#888",
    fontSize: "16px",
  },
  tableRow: {
    "&:hover": {
      cursor: "pointer",
    },
  },
});
