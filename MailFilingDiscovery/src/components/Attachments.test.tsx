import { render, screen, fireEvent } from "@testing-library/react";
import { vi, describe, it, expect } from "vitest";
import { Attachments } from "./Attachments";
import { AttachmentResult } from "../shared/types/Attachments";
import * as OutlookContext from "../shared/utils/OutlookContext";
import * as FormatFileSize from "../shared/utils/FormatFileSize";
import * as ClampText from "../shared/utils/ClampText";
import { saveAs } from "file-saver";

beforeAll(() => {
  // Mock global URL.createObjectURL and URL.revokeObjectURL
  global.URL.createObjectURL = vi.fn(() => "mocked-object-url");
  global.URL.revokeObjectURL = vi.fn();

  vi.mock("@fluentui/react-icons", () => ({
    ArrowDownload24Regular: () => <span>Download Icon</span>,
    Eye24Regular: () => <span>Preview Icon</span>,
  }));

  vi.mock("../shared/utils/FormatFileSize", () => ({
    formatFileSize: vi.fn((size: number) => `${size} bytes`),
  }));

  vi.mock("../shared/utils/ClampText", () => ({
    clampText: vi.fn((text: string) => text),
  }));

  vi.mock("file-saver", () => ({
    saveAs: vi.fn(),
  }));
});

describe("Attachments Component", () => {
  const mockAttachments: AttachmentResult = {
    attachments: [
      {
        filename: "test.pdf",
        size: 1024,
        contentType: "application/pdf",
        content: new Uint8Array(8),
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders without erroring", () => {
    render(<Attachments attachments={mockAttachments} />);
    expect(screen.getByText("Attachments - (1)")).toBeInTheDocument();
  });

  it("renders attachment actions when Outlook info is web", () => {
    vi.spyOn(OutlookContext, "parseOutlookSource").mockReturnValue({
      source: "web",
      version: "web",
      language: "en-US",
    });
    render(<Attachments attachments={mockAttachments} />);
    expect(screen.getByText("Download Icon")).toBeInTheDocument();
    expect(screen.getByText("Preview Icon")).toBeInTheDocument();
  });

  it("renders attachment actions when Outlook info is blank", () => {
    vi.spyOn(OutlookContext, "parseOutlookSource").mockReturnValue({
      source: "web",
      version: "",
      language: "en-US",
    });
    render(<Attachments attachments={mockAttachments} />);
    expect(screen.getByText("Download Icon")).toBeInTheDocument();
    expect(screen.getByText("Preview Icon")).toBeInTheDocument();
  });

  it("does not render attachment actions when Outlook info is not web", () => {
    vi.spyOn(OutlookContext, "parseOutlookSource").mockReturnValue({
      source: "windows",
      version: "16.0",
      language: "en-US",
    });
    render(<Attachments attachments={mockAttachments} />);
    expect(screen.queryByText("Download Icon")).not.toBeInTheDocument();
    expect(screen.queryByText("Preview Icon")).not.toBeInTheDocument();
  });

  it("handles empty attachments", () => {
    const emptyAttachments: AttachmentResult = { attachments: [] };
    const { container } = render(
      <Attachments attachments={emptyAttachments} />
    );
    expect(container.firstChild).toBeNull();
  });

  it("handles undefined attachments", () => {
    const { container } = render(<Attachments attachments={undefined} />);
    expect(container.firstChild).toBeNull();
  });

  it("calls handleAttachmentAction for download", () => {
    vi.spyOn(OutlookContext, "parseOutlookSource").mockReturnValue({
      source: "web",
      version: "web",
      language: "en-US",
    });

    render(<Attachments attachments={mockAttachments} />);

    const downloadButton = screen.getByText("Download Icon");
    fireEvent.click(downloadButton);

    expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), "test.pdf");
  });

  it("calls handleAttachmentAction for preview", () => {
    const windowOpenSpy = vi.spyOn(window, "open").mockReturnValue(null);

    vi.spyOn(OutlookContext, "parseOutlookSource").mockReturnValue({
      source: "web",
      version: "web",
      language: "en-US",
    });

    render(<Attachments attachments={mockAttachments} />);

    const previewButton = screen.getByText("Preview Icon");
    fireEvent.click(previewButton);

    expect(windowOpenSpy).toHaveBeenCalled();
  });

  it("calls formatFileSize and clampText with correct arguments", () => {
    render(<Attachments attachments={mockAttachments} />);
    expect(FormatFileSize.formatFileSize).toHaveBeenCalledWith(1024);
    expect(ClampText.clampText).toHaveBeenCalledWith("test.pdf", 20);
  });
});
