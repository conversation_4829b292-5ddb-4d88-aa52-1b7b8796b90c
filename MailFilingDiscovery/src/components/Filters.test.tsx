import { render, fireEvent, screen, waitFor } from "@testing-library/react";
import { expect, describe, it, vi } from "vitest";
import Filters from "./Filters";
import { Operation } from "../shared/types/Filter";
import { SortDirection } from "../shared/utils/FilterUtils";

describe("Filters Component", () => {
  const mockOnFilter = vi.fn();
  const mockOnClear = vi.fn();
  const mockOnSortChange = vi.fn();
  const mockSetIsConfidential = vi.fn();

  const defaultProps = {
    filters: [],
    onFilter: mockOnFilter,
    onClear: mockOnClear,
    disabledFilters: false,
    currentSortColumn: "subject",
    currentSortDirection: SortDirection.Ascending,
    onSortChange: mockOnSortChange,
    isConfidential: false,
    setIsConfidential: mockSetIsConfidential,
  };

  beforeEach(() => {
    mockOnFilter.mockClear();
    mockOnClear.mockClear();
    mockOnSortChange.mockClear();
    mockSetIsConfidential.mockClear();
  });

  it("renders the filter button", () => {
    render(<Filters {...defaultProps} />);
    expect(screen.getByRole("button")).toBeDefined();
  });

  it("disables the filter button when filters are disabled", () => {
    render(<Filters {...defaultProps} disabledFilters={true} />);
    const filterButton = screen.getByRole("button");
    expect(filterButton).toBeDisabled();
  });

  it("opens the popover when the filter button is clicked", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);
    await waitFor(() => {
      expect(screen.getByPlaceholderText("Subject")).toBeDefined();
    });
  });

  it("closes the popover when clicking outside", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);
    await waitFor(() => {
      expect(screen.getByPlaceholderText("Subject")).toBeInTheDocument();
      fireEvent.click(document.body);
    });
    await waitFor(() => {
      expect(screen.queryByPlaceholderText("Subject")).toBeNull();
    });
  });

  it("updates input values when typed", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const subjectInput = screen.getByPlaceholderText("Subject");
      fireEvent.change(subjectInput, { target: { value: "Test Subject" } });
      expect(subjectInput).toHaveValue("Test Subject");

      const fromInput = screen.getByPlaceholderText("Sent by");
      fireEvent.change(fromInput, { target: { value: "<EMAIL>" } });
      expect(fromInput).toHaveValue("<EMAIL>");

      const recipientsInput = screen.getByPlaceholderText("Recipients");
      fireEvent.change(recipientsInput, {
        target: { value: "<EMAIL>" },
      });
      expect(recipientsInput).toHaveValue("<EMAIL>");
    });
  });

  it("applies filters when Enter key is pressed", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const subjectInput = screen.getByPlaceholderText("Subject");
      const fromInput = screen.getByPlaceholderText("Sent by");
      const recipientsInput = screen.getByPlaceholderText("Recipients");

      fireEvent.change(subjectInput, { target: { value: "Test Subject" } });
      fireEvent.change(fromInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(recipientsInput, {
        target: { value: "<EMAIL>" },
      });
    });

    // Wait for state updates before simulating the "Enter" key press
    await waitFor(() => {
      const subjectInput = screen.getByPlaceholderText("Subject");
      fireEvent.keyDown(subjectInput, { key: "Enter", code: "Enter" });
    });

    // Expect the filters to have the correct values
    expect(mockOnFilter).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          field: "EmailSubject",
          values: ["Test Subject"],
          application: Operation.Contains,
        }),
        expect.objectContaining({
          field: "EmailFrom",
          values: ["<EMAIL>"],
          application: Operation.Contains,
        }),
        expect.objectContaining({
          field: "EmailTo",
          values: ["<EMAIL>"],
          application: Operation.Contains,
        }),
        expect.objectContaining({
          field: "EmailReceived",
          values: [""],
          application: Operation.GreaterThanDateTime,
        }),
        expect.objectContaining({
          field: "EmailReceived",
          values: [""],
          application: Operation.LessThanDateTime,
        }),
      ])
    );
  });

  it("calls onFilter with correct filters when Apply button is clicked", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const subjectInput = screen.getByPlaceholderText("Subject");
      const fromInput = screen.getByPlaceholderText("Sent by");
      const recipientsInput = screen.getByPlaceholderText("Recipients");
      fireEvent.change(subjectInput, { target: { value: "Test Subject" } });
      fireEvent.change(fromInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(recipientsInput, {
        target: { value: "<EMAIL>" },
      });
    });

    await waitFor(() => {
      const applyButton = screen.getByText("Apply");
      fireEvent.click(applyButton);
    });

    expect(mockOnFilter).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          field: "EmailSubject",
          values: ["Test Subject"],
          application: Operation.Contains,
        }),
        expect.objectContaining({
          field: "EmailFrom",
          values: ["<EMAIL>"],
          application: Operation.Contains,
        }),
        expect.objectContaining({
          field: "EmailTo",
          values: ["<EMAIL>"],
          application: Operation.Contains,
        }),
        expect.objectContaining({
          field: "EmailReceived",
          values: [""],
          application: Operation.GreaterThanDateTime,
        }),
        expect.objectContaining({
          field: "EmailReceived",
          values: [""],
          application: Operation.LessThanDateTime,
        }),
      ])
    );
  });

  it("applies 'Important Emails Only' filter when selected", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const importantCheckbox = screen.getByLabelText("Important Emails Only");
      fireEvent.click(importantCheckbox);
    });

    await waitFor(() => {
      const applyButton = screen.getByText("Apply");
      fireEvent.click(applyButton);
    });

    expect(mockOnFilter).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          field: "EmailImportant",
          values: ["Yes"],
          application: Operation.Equals,
        }),
      ])
    );
  });

  it("calls onClear when the Clear button is clicked", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const clearButton = screen.getByText("Clear");
      fireEvent.click(clearButton);
    });

    expect(mockOnClear).toHaveBeenCalled();
    expect(mockOnFilter).not.toHaveBeenCalled();
  });

  it("renders date pickers", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      expect(screen.getByText("Start Date")).toBeDefined();
      expect(screen.getByText("End Date")).toBeDefined();
    });
  });

  it("renders the 'Has attachments' checkbox", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      expect(screen.getByLabelText("Has attachments")).toBeDefined();
    });
  });

  it("renders the 'Important Emails Only' checkbox", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      expect(screen.getByLabelText("Important Emails Only")).toBeDefined();
    });
  });

  it("calls onFilter with 'AttachmentCount > 0' when 'Has attachments' is selected", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const hasAttachmentsCheckbox = screen.getByLabelText("Has attachments");
      fireEvent.click(hasAttachmentsCheckbox);
    });

    await waitFor(() => {
      const applyButton = screen.getByText("Apply");
      fireEvent.click(applyButton);
    });

    expect(mockOnFilter).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          field: "AttachmentCount",
          values: ["0"],
          application: Operation.GreaterThan, // Attachment filter
        }),
      ])
    );
  });

  it("calls onFilter with 'EmailImportant' when 'Important Emails Only' is selected", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const isImportantCheckbox = screen.getByLabelText(
        "Important Emails Only"
      );
      fireEvent.click(isImportantCheckbox);
    });

    await waitFor(() => {
      const applyButton = screen.getByText("Apply");
      fireEvent.click(applyButton);
    });

    expect(mockOnFilter).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          field: "EmailImportant",
          values: ["Yes"],
          application: Operation.Equals, // Important filter
        }),
      ])
    );
  });

  it("calls onSortChange when sort direction is changed", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const descendingRadio = screen.getByLabelText("Descending");
      fireEvent.click(descendingRadio);
    });

    expect(mockOnSortChange).toHaveBeenCalledWith(
      "subject",
      SortDirection.Descending
    );
  });

  it("resets the filter fields when filters are cleared", async () => {
    const updatedProps = {
      ...defaultProps,
      filters: [
        {
          field: "EmailSubject",
          values: ["Test Subject"],
          application: Operation.Contains,
        },
      ],
    };
    const { rerender } = render(<Filters {...updatedProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const subjectInput = screen.getByPlaceholderText("Subject");
      fireEvent.change(subjectInput, { target: { value: "Test Subject" } });
      expect(subjectInput).toHaveValue("Test Subject");
    });

    // Rerender with cleared filters
    rerender(<Filters {...defaultProps} filters={[]} />);

    await waitFor(() => {
      const subjectInput = screen.getByPlaceholderText("Subject");
      expect(subjectInput).toHaveValue("");
    });
  });

  it("resets 'Has attachments' filter when filters are cleared", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");

    // Open the popover
    fireEvent.click(filterButton);

    // Wait for the checkbox to be rendered
    await waitFor(() => {
      const hasAttachmentsCheckbox = screen.getByLabelText("Has attachments");
      fireEvent.click(hasAttachmentsCheckbox);
      expect(hasAttachmentsCheckbox).toBeChecked();
    });

    // Click the clear button
    await waitFor(() => {
      const clearButton = screen.getByText("Clear");
      fireEvent.click(clearButton);
    });

    // Reopen the popover to check the reset state
    fireEvent.click(filterButton);

    // Wait for the popover to open and check if the checkbox is not checked
    await waitFor(() => {
      const hasAttachmentsCheckbox = screen.getByLabelText("Has attachments");
      expect(hasAttachmentsCheckbox).not.toBeChecked();
    });
  });

  it("renders the Tags input field", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      expect(screen.getByPlaceholderText("Tags")).toBeDefined();
    });
  });

  it("updates Tags input value when typed", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const tagsInput = screen.getByPlaceholderText("Tags");
      fireEvent.change(tagsInput, { target: { value: "Important" } });
      expect(tagsInput).toHaveValue("Important");
    });
  });

  it("calls onFilter with correct Tags filter when Apply button is clicked", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const tagsInput = screen.getByPlaceholderText("Tags");
      fireEvent.change(tagsInput, { target: { value: "Important" } });
    });

    await waitFor(() => {
      const applyButton = screen.getByText("Apply");
      fireEvent.click(applyButton);
    });

    expect(mockOnFilter).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          field: "EmailTags",
          values: ["Important"],
          application: Operation.Contains,
        }),
      ])
    );
  });

  it("resets Tags input field when filters are cleared", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    // Update the Tags input field
    await waitFor(() => {
      const tagsInput = screen.getByPlaceholderText("Tags");
      fireEvent.change(tagsInput, { target: { value: "Important" } });
      expect(tagsInput).toHaveValue("Important");
    });

    // Click the clear button
    await waitFor(() => {
      const clearButton = screen.getByText("Clear");
      fireEvent.click(clearButton);
    });

    // Reopen the popover to check the reset state
    fireEvent.click(filterButton);

    // Check if the Tags input field is cleared
    await waitFor(() => {
      const tagsInput = screen.getByPlaceholderText("Tags");
      expect(tagsInput).toHaveValue("");
    });
  });

  it("resets 'Important Emails Only' checkbox when filters are cleared", async () => {
    render(<Filters {...defaultProps} />);
    const filterButton = screen.getByRole("button");
    fireEvent.click(filterButton);

    await waitFor(() => {
      const importantCheckbox = screen.getByLabelText("Important Emails Only");
      fireEvent.click(importantCheckbox);
      expect(importantCheckbox).toBeChecked();
    });

    await waitFor(() => {
      const clearButton = screen.getByText("Clear");
      fireEvent.click(clearButton);
    });

    fireEvent.click(filterButton);

    await waitFor(() => {
      const importantCheckbox = screen.getByLabelText("Important Emails Only");
      expect(importantCheckbox).not.toBeChecked();
    });
  });
});
