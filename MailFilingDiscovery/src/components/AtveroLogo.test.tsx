import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { AtveroLogo } from "./AtveroLogo";
import { Tenancy } from "../shared/types/Tenancy";

describe("AtveroLogo Component", () => {
  it("renders the Atvero logo image when tenancy is Atvero", () => {
    render(<AtveroLogo tenancy={Tenancy.Atvero} />);
    const logoImage = screen.getByAltText("Atvero Mail");
    expect(logoImage).toBeDefined();
    expect(logoImage).toHaveAttribute(
      "src",
      expect.stringContaining("Atvero_Mail_black_text.svg")
    );
  });

  it("renders the CMap logo image when tenancy is CMap", () => {
    render(<AtveroLogo tenancy={Tenancy.CMap} />);
    const logoImage = screen.getByAltText("CMap Mail");
    expect(logoImage).toBeDefined();
    expect(logoImage).toHaveAttribute(
      "src",
      expect.stringContaining("CMap_Mail_black_text_svg.svg")
    );
  });

  it("applies the correct styles to the Atvero logo image", () => {
    render(<AtveroLogo tenancy={Tenancy.Atvero} />);
    const logoImage = screen.getByAltText("Atvero Mail");
    expect(logoImage).toHaveStyle({
      width: "300px",
      paddingLeft: "6px",
      paddingRight: "6px",
    });
  });

  it("applies the correct styles to the CMap logo image", () => {
    render(<AtveroLogo tenancy={Tenancy.CMap} />);
    const logoImage = screen.getByAltText("CMap Mail");
    expect(logoImage).toHaveStyle({
      width: "300px",
      paddingLeft: "6px",
      paddingRight: "6px",
    });
  });
});
