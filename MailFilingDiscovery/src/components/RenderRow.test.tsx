import { render, screen, fireEvent } from "@testing-library/react";
import { describe, test, expect, vi } from "vitest";
import RenderRow, { ShimmerRow, generateShimmerRows } from "./RenderRow";
import { Email } from "../shared/types/Email";
import { Tag } from "../shared/types/Tags";
import { TestSharepointAdapter } from "../adapters/TestSharepointAdapter";

// Mock Email data
const mockEmail: Email = {
  projectCode: "P001",
  id: "1",
  subject: "Test Email",
  sentBy: "Test Sender",
  dateFiled: "2024-07-08T12:26:00",
  attachmentCount: "1",
  emailConfidential: false,
  emailImportant: "No",
};

const tags: Tag[] = [
  { Name: "No Tag", Colour: "", BackgroundColour: "" },
  { Name: "Client", Colour: "", BackgroundColour: "" },
  { Name: "Internal", Colour: "", BackgroundColour: "" },
];

const mockConfidentialEmail: Email = {
  ...mockEmail,
  id: "2",
  subject: "Confidential Email",
  emailConfidential: true, // Confidential email
};

const mockEmailWithoutAttachments: Email = {
  ...mockEmail,
  attachmentCount: "0",
};

let mockAdapter: TestSharepointAdapter;

mockAdapter = new TestSharepointAdapter();

vi.spyOn(navigator, "languages", "get").mockReturnValue([]);
vi.spyOn(navigator, "language", "get").mockImplementation(() => "en-GB");

describe("RenderRow Component", () => {
  test("renders a ShimmerRow when the item is not loaded", () => {
    render(<ShimmerRow style={{}} />);

    expect(screen.getByRole("row")).toBeInTheDocument();
    expect(screen.getAllByRole("cell")).toHaveLength(5); // 4 cells + 1 selection cell
  });

  test("renders centered message when no items are loaded and it's the first row", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(false);

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    expect(
      screen.getByText(
        "Please select a project or start searching for emails to display results."
      )
    ).toBeInTheDocument();
  });

  test("renders the row with email data", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[mockEmail]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    expect(screen.getByText("Test Email")).toBeInTheDocument();
    expect(screen.getByText("Test Sender")).toBeInTheDocument();
    expect(screen.getByText("08/07/2024, 12:26")).toBeInTheDocument(); // Adjust date formatting based on your locale
  });

  test("renders the row with confidential email and the lock icon", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[mockConfidentialEmail]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    expect(screen.getByText("Confidential Email")).toBeInTheDocument();
    expect(screen.getByTestId("confidential-icon")).toBeInTheDocument(); // Check for confidential icon
  });

  test("renders the row as selected when the email is selected", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[mockEmail]}
        selectedEmails={[mockEmail]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    expect(screen.getByRole("row")).toHaveAttribute("aria-selected", "true");
  });

  test("renders the row without an attachment icon when the email has no attachments", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[mockEmailWithoutAttachments]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    expect(screen.getByText("Test Email")).toBeInTheDocument();
    expect(screen.queryByTestId("attachment-icon")).not.toBeInTheDocument(); // Check for no attachment icon
  });

  test("renders the row without a project", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[mockEmailWithoutAttachments]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    expect(screen.queryAllByText("P001")).toHaveLength(0); // No project column
  });

  test("renders the row with a project when hideProjectColumn is false", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[mockEmailWithoutAttachments]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={false}
        tags={tags}
      />
    );

    expect(screen.getByText("P001")).toBeInTheDocument(); // Check if project code is shown
  });

  test("renders the attachment icon when the email has attachments", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[mockEmail]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    expect(screen.getByText("Test Email")).toBeInTheDocument();
    expect(screen.getByTestId("attachment-icon")).toBeInTheDocument(); // Check for the attachment icon
  });

  test("calls onSingleClick handler when a row is clicked", () => {
    const mockIsItemLoaded = vi.fn().mockReturnValue(true);
    const mockOnClick = vi.fn();
    const mockOnSingleClick = vi.fn();

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[mockEmail]}
        selectedEmails={[]}
        onClick={mockOnClick}
        onSingleClick={mockOnSingleClick}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    fireEvent.click(screen.getByRole("row"));

    expect(mockOnSingleClick).toHaveBeenCalledWith(mockEmail);
  });

  test("does not throw error when optional fields are missing", () => {
    const mockEmailWithMissingFields = {
      projectCode: "P001",
      id: "2",
      subject: "Test Email 2",
      sentBy: "Test Sender 2",
      dateFiled: "2024-07-08T12:26:00",
      // No attachmentCount
    } as Email; // Casting to Email since it's missing optional fields

    const mockIsItemLoaded = vi.fn().mockReturnValue(true);

    render(
      <RenderRow
        index={0}
        style={{}}
        items={[mockEmailWithMissingFields]}
        selectedEmails={[]}
        onClick={() => {}}
        onSingleClick={() => {}}
        adapter={mockAdapter}
        isItemLoaded={mockIsItemLoaded}
        hideProjectColumn={true}
        tags={tags}
      />
    );

    expect(screen.getByText("Test Email 2")).toBeInTheDocument();
    expect(screen.getByText("Test Sender 2")).toBeInTheDocument();
    expect(screen.getByText("08/07/2024, 12:26")).toBeInTheDocument();
  });
});

describe("ShimmerRow Component", () => {
  test("renders shimmer effect placeholders", () => {
    render(<ShimmerRow style={{}} />);

    expect(screen.getAllByRole("cell")).toHaveLength(5); // 4 cells + 1 selection cell
  });
});

describe("generateShimmerRows function", () => {
  test("generates the correct number of ShimmerRow components", () => {
    const rows = generateShimmerRows(5); // Generate 5 shimmer rows

    expect(rows).toHaveLength(5);
    rows.forEach((row) => {
      expect(row.type).toBe(ShimmerRow);
    });
  });

  test("generates ShimmerRow components with unique keys", () => {
    const rows = generateShimmerRows(5);

    const keys = rows.map((row) => row.key);
    const uniqueKeys = new Set(keys);

    expect(uniqueKeys.size).toBe(5); // All keys should be unique
  });
});
