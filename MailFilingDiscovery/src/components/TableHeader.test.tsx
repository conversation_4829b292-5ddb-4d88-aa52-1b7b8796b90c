import { render, fireEvent, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import TableHeaderComponent from "./TableHeader";
import { ListTitles } from "../shared/types/ListTitles";
import { Operation } from "../shared/types/Filter";
import { SortDirection } from "../shared/utils/FilterUtils";

const mockColumns = [
  { columnKey: "project", label: "Project", code: undefined },
  { columnKey: "subject", label: "Subject" },
  { columnKey: "sentBy", label: "Sent By" },
  { columnKey: "dateFiled", label: "Date Filed" },
];

const setup = (propsOverride = {}) => {
  const defaultProps = {
    columns: mockColumns,
    onSortChange: vi.fn(),
    currentSort: { columnKey: "subject", direction: SortDirection.Ascending },
    useStackedRows: false,
    setUseStackedRows: vi.fn(),
    filters: [],
    setFilters: vi.fn(),
    setAppendEmailData: vi.fn(),
    setPage: vi.fn(),
    isFiltersDisabledMemoized: vi.fn().mockReturnValue(false),
    isClearFiltersDisabledMemoized: vi.fn().mockReturnValue(false),
    hideProjectColumn: true,
    selectedEmailCount: 0, // Default no selected emails
    onClearSelectedEmails: vi.fn(), // Mock the clear selected emails function
    electedDriveFilter: ListTitles.NonConfidential,
    onToggleDriveFilter: vi.fn(),
    isConfidential: false,
    setIsConfidential: vi.fn(),
    confidentialEnabled: false,
    ...propsOverride,
  };

  return render(<TableHeaderComponent {...defaultProps} />);
};

describe("TableHeaderComponent", () => {
  it("renders the selected email count and the clear button when emails are selected", () => {
    setup({ selectedEmailCount: 3 });
    expect(screen.getByText("3 emails selected")).toBeInTheDocument();
    expect(screen.getByLabelText("Clear selected emails")).toBeInTheDocument();
  });

  it("renders singular email text when only one email is selected", () => {
    setup({ selectedEmailCount: 1 });
    expect(screen.getByText("1 email selected")).toBeInTheDocument();
    expect(screen.getByLabelText("Clear selected emails")).toBeInTheDocument();
  });

  it("calls onClearSelectedEmails when the clear selection button is clicked", () => {
    const onClearSelectedEmails = vi.fn();
    setup({ selectedEmailCount: 3, onClearSelectedEmails });

    const clearButton = screen.getByLabelText("Clear selected emails");
    fireEvent.click(clearButton);

    expect(onClearSelectedEmails).toHaveBeenCalled();
  });

  it("calls setFilters and pagination reset when clear filters button is clicked", () => {
    const setFilters = vi.fn();
    const setAppendEmailData = vi.fn();
    const setPage = vi.fn();
    setup({
      setFilters,
      setAppendEmailData,
      setPage,
    });

    // Click the clear filters button using its aria-label
    fireEvent.click(screen.getByLabelText("clear-filters-button"));

    // Ensure setFilters, setAppendEmailData, and setPage are called
    expect(setFilters).toHaveBeenCalledWith([]);
    expect(setAppendEmailData).toHaveBeenCalledWith(false);
    expect(setPage).toHaveBeenCalledWith(0);
  });

  it("calls setUseStackedRows when the switch is toggled", () => {
    const setUseStackedRows = vi.fn();
    setup({ setUseStackedRows });

    // Click the switch
    fireEvent.click(screen.getByRole("switch"));

    // Ensure the setUseStackedRows function is called
    expect(setUseStackedRows).toHaveBeenCalledWith(true);
  });

  it("calls onSortChange when a column header is clicked", () => {
    const onSortChange = vi.fn();
    setup({ onSortChange });

    // Click the 'Subject' column header
    fireEvent.click(screen.getByText("Subject"));

    // Ensure the onSortChange function is called with correct params
    expect(onSortChange).toHaveBeenCalledWith(
      "subject",
      SortDirection.Descending
    );
  });

  it("should show the project column", () => {
    setup({ hideProjectColumn: false });
    expect(screen.getByText("Project")).toBeInTheDocument();
  });

  describe("toggleDriveFilter", () => {
    it("toggles to confidential emails", () => {
      const onToggleDriveFilter = vi.fn();
      const setIsConfidential = vi.fn((updateFn) => {
        if (typeof updateFn === "function") {
          const newState = updateFn(false);
          return newState;
        }
      });
      const setFilters = vi.fn();
      const setAppendEmailData = vi.fn();
      const setPage = vi.fn();

      setup({
        isConfidential: false,
        setIsConfidential,
        onToggleDriveFilter,
        setFilters,
        setAppendEmailData,
        setPage,
        filters: [],
        confidentialEnabled: true,
      });

      // Find the toggle button
      const toggleButton = screen.getByText("Show Confidential Emails");
      fireEvent.click(toggleButton);

      expect(onToggleDriveFilter).toHaveBeenCalledWith(
        "Confidential Filed Email Content"
      );

      expect(setFilters).toHaveBeenCalledWith([
        {
          field: "EmailConfidential",
          values: ["true"],
          application: Operation.Equals,
        },
      ]);
      expect(setAppendEmailData).toHaveBeenCalledWith(false);
      expect(setPage).toHaveBeenCalledWith(0);
    });

    it("toggles to regular emails", () => {
      const onToggleDriveFilter = vi.fn();
      const setIsConfidential = vi.fn((updateFn) => updateFn(true));
      const setFilters = vi.fn();
      const setAppendEmailData = vi.fn();
      const setPage = vi.fn();

      setup({
        isConfidential: true,
        setIsConfidential,
        onToggleDriveFilter,
        setFilters,
        setAppendEmailData,
        setPage,
        filters: [
          {
            field: "EmailConfidential",
            values: ["true"],
            application: Operation.Equals,
          },
        ],
        confidentialEnabled: true,
      });

      // Find the toggle button
      const toggleButton = screen.getByText("Show Regular Emails");
      fireEvent.click(toggleButton);

      expect(onToggleDriveFilter).toHaveBeenCalledWith("Filed Email Content");
      expect(setFilters).toHaveBeenCalledWith([]);
      expect(setAppendEmailData).toHaveBeenCalledWith(false);
      expect(setPage).toHaveBeenCalledWith(0);
    });
  });

  describe("Confidential Email Toggle Button", () => {
    it("should be disabled when confidentialEnabled is false", () => {
      setup({ confidentialEnabled: false });
      const toggleButton = screen.getByText("Show Confidential Emails");
      expect(toggleButton).toBeDisabled();
    });

    it("should be enabled when confidentialEnabled is true", () => {
      setup({ confidentialEnabled: true });
      const toggleButton = screen.getByText("Show Confidential Emails");
      expect(toggleButton).not.toBeDisabled();
    });

    it("should show correct button text and state when confidential mode is active", () => {
      setup({
        confidentialEnabled: true,
        isConfidential: true,
      });
      const toggleButton = screen.getByText("Show Regular Emails");
      expect(toggleButton).not.toBeDisabled();
    });
  });
});
