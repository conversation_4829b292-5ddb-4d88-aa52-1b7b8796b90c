import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  TableRow,
  TableCell,
  TableHeaderCell,
  TableSelectionCell,
  Button,
  Switch,
  makeStyles,
  tokens,
  Tooltip,
} from "@fluentui/react-components";
import { Column } from "../shared/types/Column";
import {
  FilterDismiss24Regular,
  Dismiss24Regular,
  LockClosed20Filled,
  Mail20Filled,
} from "@fluentui/react-icons";
import Filters from "./Filters";
import { SortDirection } from "../shared/utils/FilterUtils";
import { Filter, Operation } from "../shared/types/Filter";
import { ListTitles } from "../shared/types/ListTitles";

interface TableHeaderComponentProps {
  columns: Column[];
  onSortChange: (columnKey: string, direction: SortDirection) => void;
  currentSort: { columnKey: string; direction: SortDirection };
  useStackedRows: boolean;
  setUseStackedRows: (value: boolean) => void; // To toggle stacked rows
  filters: Filter[];
  setFilters: (filters: Filter[]) => void; // Already exists to update filters
  setAppendEmailData: (value: boolean) => void; // To handle pagination resetting
  setPage: (value: number) => void; // To reset the page
  isFiltersDisabledMemoized: () => boolean; // Disabled filters condition
  isClearFiltersDisabledMemoized: () => boolean; // Disabled clear filters condition
  hideProjectColumn: boolean;
  selectedEmailCount: number;
  onClearSelectedEmails: () => void;
  onToggleDriveFilter: (newFilter: string) => void;
  isConfidential: boolean;
  setIsConfidential: React.Dispatch<React.SetStateAction<boolean>>;
  confidentialEnabled: boolean;
}

const TableHeaderComponent: React.FC<TableHeaderComponentProps> = ({
  columns,
  onSortChange,
  currentSort,
  useStackedRows,
  setUseStackedRows,
  filters,
  setFilters,
  setAppendEmailData,
  setPage,
  isFiltersDisabledMemoized,
  isClearFiltersDisabledMemoized,
  hideProjectColumn,
  selectedEmailCount,
  onClearSelectedEmails,
  onToggleDriveFilter,
  isConfidential,
  setIsConfidential,
  confidentialEnabled,
}) => {
  const classes = useStyles();

  const handleSort = (columnId: string) => {
    const newDirection =
      currentSort.columnKey === columnId &&
      currentSort.direction === SortDirection.Ascending
        ? SortDirection.Descending
        : SortDirection.Ascending;
    onSortChange(columnId, newDirection);
  };

  const toggleDriveFilter = () => {
    setIsConfidential((prevConfidential) => {
      const newConfidentialState = !prevConfidential;

      // Update the drive filter label
      onToggleDriveFilter(
        newConfidentialState
          ? ListTitles.Confidential
          : ListTitles.NonConfidential
      );

      // Update filters
      const updatedFilters = newConfidentialState
        ? [
            ...filters,
            {
              field: "EmailConfidential",
              values: ["true"],
              application: Operation.Equals,
            },
          ]
        : filters.filter((filter) => filter.field !== "EmailConfidential");

      setFilters(updatedFilters);
      setAppendEmailData(false);
      setPage(0);

      return newConfidentialState;
    });
  };

  return (
    <Table noNativeElements={true} aria-label="Custom table" sortable>
      <TableHeader>
        <TableRow>
          <TableCell colSpan={columns.length}>
            <div className={classes.headerContainer}>
              <div className={classes.headerTextContainer}>
                {selectedEmailCount > 0 && (
                  <div className={classes.selectedEmailContainer}>
                    {selectedEmailCount}{" "}
                    {selectedEmailCount === 1 ? "email" : "emails"} selected
                    <Tooltip
                      content="Clear selected emails"
                      relationship="label"
                    >
                      <Button
                        icon={<Dismiss24Regular />}
                        appearance="subtle"
                        onClick={onClearSelectedEmails}
                        className={classes.clearSelectionButton}
                      />
                    </Tooltip>
                  </div>
                )}
              </div>

              <div className={classes.filterContainer}>
                <Button
                  icon={
                    !isConfidential ? <LockClosed20Filled /> : <Mail20Filled />
                  }
                  onClick={toggleDriveFilter}
                  className={classes.actionButton}
                  disabled={!confidentialEnabled}
                >
                  {isConfidential
                    ? "Show Regular Emails"
                    : "Show Confidential Emails"}
                </Button>
                <Button
                  icon={<FilterDismiss24Regular />}
                  disabled={isClearFiltersDisabledMemoized()}
                  onClick={() => {
                    setFilters([]); // Clear filters from the App state
                    setAppendEmailData(false);
                    setPage(0);
                  }}
                  className={classes.actionButton}
                  aria-label="clear-filters-button"
                />
                <Filters
                  filters={filters}
                  onFilter={(newFilters) => {
                    setFilters(newFilters); // Set new filters
                    setAppendEmailData(false);
                    setPage(0);
                  }}
                  onClear={() => {
                    setFilters([]); // Clear filters through Filters component
                    setAppendEmailData(false);
                    setPage(0);
                  }}
                  disabledFilters={isFiltersDisabledMemoized()}
                  currentSortColumn={currentSort.columnKey}
                  currentSortDirection={currentSort.direction}
                  onSortChange={onSortChange}
                  isConfidential={isConfidential}
                  setIsConfidential={setIsConfidential}
                />
                <Switch
                  checked={useStackedRows}
                  onClick={() => setUseStackedRows(!useStackedRows)}
                  className={classes.switchHidden}
                  aria-label="Toggle table view"
                />
              </div>
            </div>
          </TableCell>
        </TableRow>

        {!useStackedRows && (
          <TableRow className={classes.tableRow}>
            <TableSelectionCell invisible={true} />

            {columns.map((column) => {
              const headerClass =
                column.label === "Project" && hideProjectColumn
                  ? classes.headerCellHidden
                  : "";
              return (
                <TableHeaderCell
                  className={headerClass}
                  key={column.columnKey}
                  onClick={() => handleSort(column.columnKey)}
                  sortDirection={
                    currentSort.columnKey === column.columnKey
                      ? currentSort.direction
                      : undefined
                  }
                >
                  {column.label}
                </TableHeaderCell>
              );
            })}
          </TableRow>
        )}
      </TableHeader>
    </Table>
  );
};

const useStyles = makeStyles({
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  headerTextContainer: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },
  headerCellHidden: {
    display: "none",
  },
  switchHidden: {
    "@media (max-width: 768px)": {
      display: "none",
    },
  },
  filterContainer: {
    display: "flex",
    alignItems: "center",
  },
  headerText: {
    color: "#888",
    fontSize: "16px",
  },
  tableRow: {
    "&:hover": {
      cursor: "pointer",
    },
  },
  actionButton: {
    height: "32px",
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    borderRadius: tokens.borderRadiusMedium,
    marginRight: "10px",
  },
  selectedEmailContainer: {
    display: "flex",
    alignItems: "center",
    color: "#888",
    fontSize: "16px",
    borderLeft: `2px solid ${tokens.colorNeutralStroke1}`,
    paddingLeft: tokens.spacingHorizontalS,
  },
  clearSelectionButton: {
    marginLeft: tokens.spacingHorizontalS,
    color: tokens.colorBrandForeground1,
  },
});

export default TableHeaderComponent;
