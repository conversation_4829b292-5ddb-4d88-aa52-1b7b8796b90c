import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import App from "./App";
import { createMockAdapter, hubsites } from "./shared/utils/TestHelpers";
import { vi } from "vitest";
import { Tenancy } from "./shared/types/Tenancy";

const mockAdapter = createMockAdapter();

beforeEach(() => {
  window.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
});

interface AutoSizerProps {
  children: (dimensions: { width: number; height: number }) => React.ReactNode;
}

interface SetupOptions {
  isAdmin?: boolean;
}

vi.mock("react-virtualized-auto-sizer", () => ({
  default: ({ children }: AutoSizerProps) =>
    children({ height: 500, width: 800 }),
}));

const setupWithSelectedEmail = async (options: SetupOptions = {}) => {
  const { isAdmin = false } = options;
  const mockAdapter = createMockAdapter();

  // Override admin access only if specified in options
  if (isAdmin) {
    mockAdapter.sharepointAdapter.getIsAdmin = vi.fn().mockResolvedValue(true);
  }

  const { container } = render(
    <App
      backendAdapter={mockAdapter.backendAdapter}
      sharepointAdapter={mockAdapter.sharepointAdapter}
      hubsites={[hubsites[0]]}
      selectedHubsite={hubsites[0]}
      hubsiteError={undefined}
      onHubsiteSelect={vi.fn()}
      tenancy={Tenancy.Atvero}
    />
  );

  const getProjectsSpy = vi.spyOn(mockAdapter.sharepointAdapter, "getProjects");

  // Select project
  const projectSelector = screen.getByPlaceholderText("Select a Project");
  fireEvent.input(projectSelector, { target: { value: "PRJ1" } });
  await new Promise((resolve) => setTimeout(resolve, 400));
  expect(getProjectsSpy).toHaveBeenCalledWith(hubsites[0], "PRJ1");

  fireEvent.click(projectSelector);
  await new Promise((resolve) => setTimeout(resolve, 400));

  const option = screen.getByTestId("project-item-PRJ1");
  fireEvent.click(option);
  await new Promise((resolve) => setTimeout(resolve, 400));

  // Select email
  const checkboxInput = container.querySelector(
    'input[aria-label="Select row"]'
  );
  fireEvent.click(checkboxInput!);

  return { mockAdapter, container };
};

describe("Hubsites Tests", () => {
  it("no hubsites renders warning", () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={[]}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    expect(
      screen.getByText(
        "No hubsites were found for your account. Please contact your administrator to get access to the required hubsites."
      )
    ).toBeInTheDocument();
  });

  it("error shows when no hubsites found", () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={undefined}
        selectedHubsite={undefined}
        hubsiteError={"Error fetching hubsites"}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    expect(
      screen.getByText(
        "No hubsites were found for your account. Please contact your administrator to get access to the required hubsites."
      )
    ).toBeInTheDocument();
  });

  it("hubsite dropdown renders", async () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={hubsites}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    await waitFor(() => {
      expect(screen.getByTestId("hubsite-combobox")).toBeInTheDocument();
    });
  });

  it("hubsite dropdown doesn't render when we only have 1 hubsite", async () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={[
          {
            displayName: "Hub 1",
            name: "Hub 1",
            url: "http://hub1",
            path: "/hub1",
          },
        ]}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );
    await waitFor(() => {
      expect(screen.queryByTestId("hubsite-combobox")).not.toBeInTheDocument();
    });
  });
});

describe("Atvero Icon", () => {
  it("renders", () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={hubsites}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    expect(screen.getByAltText("Atvero Mail Icon")).toBeInTheDocument();
  });
});

describe("Project Selector", () => {
  it("renders", () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={hubsites}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    expect(screen.getByPlaceholderText("Select a Project")).toBeInTheDocument();
  });

  it("disables project selector when no hubsite is selected", () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={hubsites}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    expect(screen.getByPlaceholderText("Select a Project")).toBeDisabled();
  });

  it("allows searching for projects", async () => {
    const mockAdapter = createMockAdapter();

    const getProjectsSpy = vi.spyOn(
      mockAdapter.sharepointAdapter,
      "getProjects"
    );

    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={[hubsites[0]]}
        selectedHubsite={hubsites[0]}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    // Verify the project selector is enabled
    const projectSelector = screen.getByPlaceholderText("Select a Project");
    expect(projectSelector).not.toBeDisabled();

    // Initial render calls getProjects with empty string
    expect(getProjectsSpy).toHaveBeenCalledWith(hubsites[0], "");

    // Reset our spy to only track new calls
    getProjectsSpy.mockClear();

    // Simulate user typing in the search box
    fireEvent.input(projectSelector, { target: { value: "test" } });

    // Wait for debounce timeout (300ms in the component)
    await new Promise((resolve) => setTimeout(resolve, 400));

    // Verify the search was performed with the correct term
    expect(getProjectsSpy).toHaveBeenCalledWith(hubsites[0], "test");
  });
});

describe("Action Buttons", () => {
  it("doesn't render Unfile button for non-admin users", () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={hubsites}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    expect(screen.queryByText("Unfile")).not.toBeInTheDocument();
  });

  it("renders Unfile button for admin users", () => {
    mockAdapter.sharepointAdapter.getIsAdmin = vi.fn().mockResolvedValue(true);

    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={hubsites}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    return waitFor(() => {
      expect(screen.getByText("Unfile")).toBeInTheDocument();
    });
  });

  it("renders Forward button as disabled when no email is selected", () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={hubsites}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    const forwardButton = screen.getByText("Forward").closest("button");
    expect(forwardButton).toBeDisabled();
  });

  it("renders Download button as disabled when no email is selected", () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={hubsites}
        selectedHubsite={undefined}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    const downloadButton = screen.getByText("Download").closest("button");
    expect(downloadButton).toBeDisabled();
  });

  it("enables Forward button when an email is selected", async () => {
    await setupWithSelectedEmail();

    const forwardButton = screen.getByText("Forward").closest("button");
    expect(forwardButton).not.toBeDisabled();
  });

  it("enables Download button when an email is selected", async () => {
    await setupWithSelectedEmail();

    const downloadButton = screen.getByText("Download").closest("button");
    expect(downloadButton).not.toBeDisabled();
  });

  it("enables Unfile button when an email is selected and user is admin", async () => {
    await setupWithSelectedEmail({ isAdmin: true });

    const unfileButton = screen.getByText("Unfile").closest("button");
    expect(unfileButton).not.toBeDisabled();
  });
  it("allows clicking Forward button and shows success notification", async () => {
    const { mockAdapter } = await setupWithSelectedEmail();
    mockAdapter.backendAdapter.forwardEmail = vi.fn().mockResolvedValue({});

    const forwardButton = screen.getByText("Forward").closest("button");
    expect(forwardButton).not.toBeDisabled();
    fireEvent.click(forwardButton!);

    await waitFor(() => {
      expect(screen.getByText("Email created to forward")).toBeInTheDocument();
    });

    expect(mockAdapter.backendAdapter.forwardEmail).toHaveBeenCalled();
  });

  it("shows spinner and downloading text when multiple emails are downloading", async () => {
    await setupWithSelectedEmail();

    const allCheckboxes = screen.getAllByRole("checkbox");

    const rowCheckboxes = allCheckboxes.filter((checkbox) => {
      const label = checkbox.getAttribute("aria-label");
      return label === "Select row" || label === "Deselect row";
    });

    expect(rowCheckboxes.length).toBeGreaterThanOrEqual(1);

    const uncheckedCheckbox = rowCheckboxes.find(
      (checkbox) => !(checkbox as HTMLInputElement).checked
    ) as HTMLInputElement | undefined;

    if (uncheckedCheckbox) {
      fireEvent.click(uncheckedCheckbox);
    } else {
      throw new Error("Could not find an unchecked email row to select");
    }

    await waitFor(() => {
      // Look for text like "2 emails selected"
      const selectionText = screen.getByText(/emails selected/);
      expect(selectionText.textContent).toMatch(/2 emails selected/);
    });

    const downloadButton = screen.getByText("Download").closest("button");
    expect(downloadButton).not.toBeDisabled();

    fireEvent.click(downloadButton!);

    await waitFor(() => {
      expect(screen.getByText("Downloading...")).toBeInTheDocument();
      const updatedDownloadButton = screen
        .getByText("Downloading...")
        .closest("button");
      expect(updatedDownloadButton).toBeDisabled();
    });
  });

  it("disables Forward button when a .msg email is selected", async () => {
    // Override the getListDataAsStream to return a .msg file
    mockAdapter.sharepointAdapter.getListDataAsStream = vi
      .fn()
      .mockImplementation(async () => {
        const mockEmailRows = [
          {
            ID: "1",
            Title: "MSG Email Test",
            EmailSubject: "MSG Email Test",
            EmailFrom: "<EMAIL>",
            EmailReceivedOn: "2023-01-01T12:00:00Z",
            FSObjType: "0",
            UniqueId: "email-unique-id-msg",
            FileRef: "/sites/PRJ1/FiledEmailContent/MSG Email Test.msg",
            FileLeafRef: "MSG Email Test.msg",
            EmailAttachmentCount: "0",
            EmailTags: "General",
            DriveItemID: "drive-item-id-msg",
          },
        ];

        return {
          CurrentFolderSpItemUrl: "",
          FilterLink: "",
          FirstRow: 0,
          FolderPermissions: "",
          ForceNoHierarchy: "",
          HierarchyHasIndention: "",
          LastRow: 0,
          NextHref: undefined,
          Row: mockEmailRows,
          RowLimit: 100,
        };
      });

    const { container } = render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={[hubsites[0]]}
        selectedHubsite={hubsites[0]}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    const projectSelector = screen.getByPlaceholderText("Select a Project");
    fireEvent.input(projectSelector, { target: { value: "PRJ1" } });
    await new Promise((resolve) => setTimeout(resolve, 400));

    fireEvent.click(projectSelector);
    await new Promise((resolve) => setTimeout(resolve, 400));

    const option = screen.getByTestId("project-item-PRJ1");
    fireEvent.click(option);
    await new Promise((resolve) => setTimeout(resolve, 400));

    const checkboxInput = container.querySelector(
      'input[aria-label="Select row"]'
    );
    fireEvent.click(checkboxInput!);
    await waitFor(() => {
      expect(screen.getByText("MSG Email Test")).toBeInTheDocument();
    });

    const forwardButton = screen.getByText("Forward").closest("button");
    expect(forwardButton).toBeDisabled();
  });

  it("shows tooltip when hovering over disabled Forward button for .msg email", async () => {
    // Override the getListDataAsStream to return a .msg file
    mockAdapter.sharepointAdapter.getListDataAsStream = vi
      .fn()
      .mockImplementation(async () => {
        const mockEmailRows = [
          {
            ID: "1",
            Title: "MSG Email Test",
            EmailSubject: "MSG Email Test",
            EmailFrom: "<EMAIL>",
            EmailReceivedOn: "2023-01-01T12:00:00Z",
            FSObjType: "0",
            UniqueId: "email-unique-id-msg",
            FileRef: "/sites/PRJ1/FiledEmailContent/MSG Email Test.msg",
            FileLeafRef: "MSG Email Test.msg",
            EmailAttachmentCount: "0",
            EmailTags: "General",
            DriveItemID: "drive-item-id-msg",
          },
        ];

        return {
          CurrentFolderSpItemUrl: "",
          FilterLink: "",
          FirstRow: 0,
          FolderPermissions: "",
          ForceNoHierarchy: "",
          HierarchyHasIndention: "",
          LastRow: 0,
          NextHref: undefined,
          Row: mockEmailRows,
          RowLimit: 100,
        };
      });

    const { container } = render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={[hubsites[0]]}
        selectedHubsite={hubsites[0]}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    const projectSelector = screen.getByPlaceholderText("Select a Project");
    fireEvent.input(projectSelector, { target: { value: "PRJ1" } });
    await new Promise((resolve) => setTimeout(resolve, 400));

    fireEvent.click(projectSelector);
    await new Promise((resolve) => setTimeout(resolve, 400));

    const option = screen.getByTestId("project-item-PRJ1");
    fireEvent.click(option);
    await new Promise((resolve) => setTimeout(resolve, 400));

    const checkboxInput = container.querySelector(
      'input[aria-label="Select row"]'
    );
    fireEvent.click(checkboxInput!);
    await waitFor(() => {
      expect(screen.getByText("MSG Email Test")).toBeInTheDocument();
    });

    const forwardButton = screen.getByText("Forward").closest("button");
    fireEvent.mouseOver(forwardButton!);

    await waitFor(() => {
      expect(
        screen.getByText(
          "MSG files cannot be forwarded directly. Please download the email instead."
        )
      ).toBeInTheDocument();
    });
  });
});

describe("Preview Component", () => {
  it("renders preview after email is selected", async () => {
    const { mockAdapter } = await setupWithSelectedEmail();

    mockAdapter.sharepointAdapter.getPreviewUrl = vi
      .fn()
      .mockResolvedValue("https://preview.example.com/email-unique-id-1");

    await waitFor(() => {
      expect(screen.queryByText("No email selected")).not.toBeInTheDocument();
      expect(screen.getByText("Test Email 1")).toBeInTheDocument();
    });
  });

  it("updates preview when a different email is selected", async () => {
    const { mockAdapter, container } = await setupWithSelectedEmail();

    mockAdapter.sharepointAdapter.getPreviewUrl = vi
      .fn()
      .mockResolvedValue("https://preview.example.com/email-unique-id-1");

    await waitFor(() => {
      expect(screen.getByText("Test Email 1")).toBeInTheDocument();
    });

    // Select the second email
    const firstEmailCheckbox = container.querySelector(
      'input[aria-label="Deselect row"]'
    );
    if (firstEmailCheckbox) {
      fireEvent.click(firstEmailCheckbox);
    }

    const secondEmailCheckbox = container.querySelector(
      'input[aria-label="Select row"]'
    );
    if (secondEmailCheckbox) {
      fireEvent.click(secondEmailCheckbox);
    }

    mockAdapter.sharepointAdapter.getPreviewUrl = vi
      .fn()
      .mockResolvedValue("https://preview.example.com/email-unique-id-2");

    await waitFor(() => {
      expect(screen.getByText("Test Email 2")).toBeInTheDocument();
    });
  });
});

describe("ScrollableTable Component", () => {
  it("renders correctly", async () => {
    render(
      <App
        backendAdapter={mockAdapter.backendAdapter}
        sharepointAdapter={mockAdapter.sharepointAdapter}
        hubsites={[hubsites[0]]}
        selectedHubsite={hubsites[0]}
        hubsiteError={undefined}
        onHubsiteSelect={vi.fn()}
        tenancy={Tenancy.Atvero}
      />
    );

    expect(
      screen.getByText(
        "Select a project or search a keyword to find your emails."
      )
    ).toBeInTheDocument();
  });
});
