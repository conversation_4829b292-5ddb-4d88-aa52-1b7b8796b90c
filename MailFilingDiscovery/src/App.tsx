import { useEffect, useState, useCallback } from "react";
import { AtveroIcon } from "./components/AtveroIcon";
import { UnfileDialog } from "./components/UnfileDialog";
import { Project } from "./shared/types/Project";
import { Tag, DEFAULT_TAGS } from "./shared/types/Tags";
import { Email } from "./shared/types/Email";
import {
  useId,
  makeStyles,
  shorthands,
  SearchBox,
  SearchBoxChangeEvent,
  InputOnChangeData,
  tokens,
  Button,
  Toaster,
  useToastController,
  Toast,
  ToastTitle,
  ToastBody,
  Dialog,
  useRestoreFocusTarget,
  ToastIntent,
  Spinner,
  Tooltip,
} from "@fluentui/react-components";
import { ProjectSelector } from "./components/ProjectSelector";
import TableHeaderComponent from "./components/TableHeader";
import { Filter } from "./shared/utils/CamlHelpers";
import {
  handleDownloadEmail,
  handleDownloadMultipleEmails,
} from "./shared/utils/DownloadEmails";
import { handleForwardEmail } from "./shared/utils/ForwardEmails";
import { handleUnfileEmail } from "./shared/utils/UnfileEmails";
import { ScrollableTable } from "./components/ScrollableTable";
import { Preview } from "./components/Preview";
import {
  ArrowDownload24Regular,
  ArrowForward24Regular,
  Delete24Regular,
} from "@fluentui/react-icons";
import { AttachmentResult } from "./shared/types/Attachments";
import {
  isFiltersDisabled,
  isClearFiltersDisabled,
} from "./shared/utils/FilterButtonHelpers";
import { SortDirection } from "./shared/utils/FilterUtils";
import {
  handleMobileEmailSelect,
  handleDesktopEmailSelect,
  handleSingleDesktopEmailSelect,
} from "./shared/utils/EmailSelection";
import { ListTitles } from "./shared/types/ListTitles";
import HubSiteSelector from "./components/HubSiteSelector";
import { Hubsite } from "./shared/types/Hubsite";
import HubsiteWarning from "./components/HubsiteWarning";
import { getQueryParamFromUrl } from "./shared/utils/BrowserHelper";
import { getIntegrationProject } from "./shared/utils/CmapProjectHelper";
import { fetchEmailsWithAdapter } from "./shared/utils/EmailHelpers";
import { filterGroups } from "./shared/utils/AdminChecker";
import { fetchPreview } from "./shared/utils/PreviewUtils";
import { Tenancy } from "./shared/types/Tenancy";
import { ISharepointAdapter } from "./adapters/ISharepointAdapter";
import { IBackendAdapter } from "./adapters/IBackendAdapter";

export interface AppProps {
  readonly sharepointAdapter: ISharepointAdapter;
  readonly backendAdapter: IBackendAdapter;
  readonly hubsites: Hubsite[] | undefined;
  readonly selectedHubsite: Hubsite | undefined;
  readonly hubsiteError: string | undefined;
  readonly onHubsiteSelect: (hubsite: Hubsite | undefined) => void;
  readonly tenancy: Tenancy;
}

function App({
  sharepointAdapter,
  backendAdapter,
  hubsites,
  selectedHubsite,
  hubsiteError,
  onHubsiteSelect,
  tenancy,
}: AppProps) {
  const pageSize = 100;
  const styles = useStyles();
  const [useStackedRows, setUseStackedRows] = useState<boolean>(true);
  const [emailData, setEmailData] = useState<Email[]>([]);
  const [selectedEmails, setSelectedEmails] = useState<Email[]>([]);
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [page, setPage] = useState(0);
  const [nextLink, setNextLink] = useState<string | undefined>(undefined);
  const [isNextPageLoading, setIsNextPageLoading] = useState(false);
  const [hasMoreItems, setHasMoreItems] = useState(true);
  const [shareRootUrl, setShareRootUrl] = useState<string>("");
  const [projects, setProjects] = useState<Project[]>([]);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [sortColumn, setSortColumn] = useState<string>("EmailReceivedOn");
  const [columnCode, setColumnCode] = useState<string | undefined>(
    "RefinableDate00"
  );
  const [sortDirection, setSortDirection] = useState<SortDirection>(
    SortDirection.Descending
  );
  const [appendEmailData, setAppendEmailData] = useState<boolean>(false);
  const [filters, setFilters] = useState<Filter[]>([]);
  const [emailsShown, setEmailsShown] = useState<number>(0);
  const [totalEmails, setTotalEmails] = useState<number>(0);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [isAttachmentLoading, setIsAttachmentLoading] = useState(false);
  const [inputValue, setInputValue] = useState<string>("");
  const [currentAttachments, setCurrentAttachments] = useState<
    AttachmentResult | undefined
  >(undefined);
  const [loading, setLoading] = useState<boolean>(false);
  const [isProjectsLoading, setIsProjectsLoading] = useState(false);
  const [isMobile, setIsMobile] = useState<boolean>(window.innerWidth < 768);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedDriveFilter, setSelectedDriveFilter] = useState(
    ListTitles.NonConfidential
  );
  const [isConfidential, setIsConfidential] = useState(false);
  const [projectParam, setProjectParam] = useState<string | undefined>(
    undefined
  );

  const [isForwarding, setIsForwarding] = useState<boolean>(false);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isUnfiling, setIsUnfiling] = useState<boolean>(false);
  const [openUnfileDialog, setOpenUnfileDialog] = useState<boolean>(false);
  const restoreFocusTargetAttribute = useRestoreFocusTarget();
  const toasterId = useId("toaster");
  const { dispatchToast } = useToastController(toasterId);
  const [forceRefresh, setForceRefresh] = useState<number>(0);
  const [confidentialEnabled, setConfidentialEnabled] =
    useState<boolean>(false);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isEmailsDownloading, setIsEmailsDownloading] =
    useState<boolean>(false);
  const [isSelectedEmailMSG, setIsSelectedEmailMSG] = useState<boolean>(false);

  const columns = [
    { columnKey: "project", label: "Project", code: undefined },
    { columnKey: "subject", label: "Subject", code: "RefinableString31" },
    { columnKey: "sentBy", label: "Sent By", code: "RefinableString35" },
    { columnKey: "dateFiled", label: "Received On", code: "RefinableDate00" },
    { columnKey: "emailTag", label: "Tag", code: "RefinableString51" },
  ];

  const notify = (title: string, body: string, intent: ToastIntent) =>
    dispatchToast(
      <Toast>
        <ToastTitle>{title}</ToastTitle>
        <ToastBody>{body}</ToastBody>
      </Toast>,
      { intent: intent }
    );

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    const checkForAdmin = async () => {
      const filteredGroups = filterGroups(
        await sharepointAdapter.getAtveroGroups(),
        await sharepointAdapter.getCmapGroups()
      );
      const isAdmin = await sharepointAdapter.getIsAdmin(filteredGroups);

      setIsAdmin(isAdmin);
    };
    checkForAdmin();
  }, [sharepointAdapter]);

  useEffect(() => {
    const initializeApp = async () => {
      // const sharePointUrl =
      //   await adapter.sharepointAdapter.getSharePointRootUrl();

      if (selectedHubsite === undefined) return;

      const sharePointUri = new URL(selectedHubsite?.url);
      const sharePointUrl = sharePointUri.hostname;

      console.log("SharePoint URL set to:", sharePointUrl);
      setShareRootUrl(sharePointUrl);
    };
    initializeApp();
  }, [selectedHubsite]);

  useEffect(() => {
    const projectParam = getQueryParamFromUrl(window.location.href, "project");

    // we exit this use effect if no project param is present
    if (!projectParam) return;

    const fetchProjectFromUrl = async () => {
      setProjectParam(projectParam);

      if (projectParam && hubsites && hubsites.length > 0) {
        try {
          const result = await getIntegrationProject(
            sharepointAdapter,
            hubsites,
            projectParam
          );

          if (result) {
            console.log("Auto-selecting hubsite and project from URL param");
            onHubsiteSelect(result.hubsite);
            setSelectedProjects([result.project.code]);
          } else {
            console.warn("Project not found for the provided projectParam.");
            setProjectParam(undefined);
          }
        } catch (error) {
          console.error("Failed to fetch project from URL param:", error);
          setProjectParam(undefined);
        }
      }
    };

    fetchProjectFromUrl();
  }, [sharepointAdapter, hubsites]);

  useEffect(() => {
    const getCustomTags = async (): Promise<void> => {
      const useCustomTags = await backendAdapter.getSetting("useCustomTags");

      if (useCustomTags) {
        try {
          const getTagsList = await backendAdapter.getTags();

          if (
            getTagsList.error === undefined &&
            getTagsList.tags !== undefined
          ) {
            setTags(getTagsList.tags);
          }
        } catch (error) {
          console.error("Error fetching custom tags:", error);
          // fall back to the standard set

          setTags(DEFAULT_TAGS);
        }
      } else {
        // fall back to the standard set

        setTags(DEFAULT_TAGS);
      }
    };

    getCustomTags();
  }, [sharepointAdapter]);

  async function fetchEmails() {
    if (!appendEmailData) {
      setLoading(true);
      setEmailData([]); // Clear the table before loading new data
    }
    setIsNextPageLoading(true); // Ensure loading state is set

    if (sharepointAdapter && selectedHubsite) {
      try {
        const response = await fetchEmailsWithAdapter(
          sharepointAdapter,
          selectedProjects,
          shareRootUrl,
          searchTerm,
          page,
          pageSize,
          selectedHubsite,
          nextLink,
          columnCode,
          sortColumn,
          sortDirection,
          filters,
          !appendEmailData, // When `appendEmailData` is false, replace the data
          selectedDriveFilter
        );

        if (response && response.emails.length > 0) {
          setEmailData((prevEmails) =>
            appendEmailData
              ? [...prevEmails, ...response.emails]
              : response.emails
          );
          setAppendEmailData(true);

          // Update nextLink from response
          setNextLink(response.nextLink);

          // Determine if there are more items to load based on nextLink and moreResultsAvailable
          setHasMoreItems(
            response.moreResultsAvailable || response.nextLink !== undefined
          );

          setTotalEmails(response.total ?? 0);
          setEmailsShown(pageSize * (page + 1));
        } else {
          if (!appendEmailData) {
            setEmailData([]); // Clear the table if no emails are returned and not appending
          }
          setHasMoreItems(false);
        }
      } catch (error) {
        console.error("Error fetching emails from App.tsx: ", error);
        if (!appendEmailData) {
          setEmailData([]);
        }
        setHasMoreItems(false);
      } finally {
        setIsNextPageLoading(false);
        setLoading(false); // Loading finished
      }
    }
  }

  useEffect(() => {
    if (searchTerm.length > 0 || selectedProjects.length > 0) {
      fetchEmails();
    }
  }, [
    page,
    searchTerm,
    filters,
    sortColumn,
    sortDirection,
    shareRootUrl,
    selectedProjects,
    selectedHubsite,
    forceRefresh,
  ]);

  const clearEmailsAndProjects = () => {
    setPage(0);
    setEmailData([]);
    setSelectedProjects([]);
    setNextLink(undefined);
    setHasMoreItems(false);
    setAppendEmailData(false);
  };

  const handleProjectSelect = (selected: string[]) => {
    setPage(0);
    setEmailData([]);
    setSelectedProjects(selected);
    setNextLink(undefined);
    setHasMoreItems(false);
    setAppendEmailData(false);
  };

  const loadMoreItems = useCallback(() => {
    if (hasMoreItems) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [hasMoreItems]);

  useEffect(() => {
    const loadPreview = async () => {
      if (sharepointAdapter && selectedEmails.length === 1 && shareRootUrl) {
        setIsPreviewLoading(true);
        setIsAttachmentLoading(true);
        const isSelectedEmailMSG = selectedEmails[0].fileRef
          ? selectedEmails[0].fileRef.toLowerCase().endsWith(".msg")
          : false;
        setIsSelectedEmailMSG(isSelectedEmailMSG);

        const url = await fetchPreview(sharepointAdapter, selectedEmails[0]);

        if (url) {
          setPreviewUrl(url);
          setIsPreviewLoading(false);
        } else {
          notify(
            "Failed to fetch preview",
            "The selected email failed to load. The URL for selected email was not found.",
            "error"
          );
        }
      } else {
        setPreviewUrl("");
        setIsPreviewLoading(false);
        setIsAttachmentLoading(false);
      }
    };
    loadPreview();
  }, [sharepointAdapter, selectedEmails, shareRootUrl]);

  const toggleDriveFilter = () => {
    setIsConfidential((prevConfidential) => {
      const newConfidential = !prevConfidential;
      // Set the drive filter label based on the new state
      setSelectedDriveFilter(
        newConfidential ? ListTitles.Confidential : ListTitles.NonConfidential
      );
      setAppendEmailData(false); // Ensure the data is reloaded
      setPage(0); // Reset the page to reload from the start
      return newConfidential; // Update the state
    });
  };

  const handleSortChange = (columnKey: string, direction: SortDirection) => {
    // Find the column with the given columnKey
    const column = columns.find((col) => col.columnKey === columnKey);
    if (column) setColumnCode(column.code);

    setSortColumn(columnKey);
    setAppendEmailData(false);
    setNextLink(undefined); // Reset nextLink on sort change
    setSortDirection(direction);
    setPage(0); // Reset the page on sort change
  };

  const handleLoadingComplete = () => {
    setIsAttachmentLoading(false);
  };

  const handleSearchChange = (
    e: SearchBoxChangeEvent,
    data: InputOnChangeData
  ) => {
    // Check if the event is from a mouse click on the close button or text input change
    if (e.type === "click") {
      // If the close button is clicked, or the input is cleared, reset search term and input value
      setSearchTerm("");
      setInputValue("");
      setAppendEmailData(false);
      setNextLink(undefined);
      setPage(0);
    } else {
      // Otherwise, update the input value normally
      setInputValue(data.value);
    }
  };

  const handleKeyDown = (e: any) => {
    if (e.key === "Enter") {
      setSearchTerm(inputValue); // Update the searchTerm only on Enter key press
      setAppendEmailData(false);
      setNextLink(undefined);
      setPage(0);
    }
  };

  // Disabled when:
  // There is email data, but no project selected, no search term available
  const isFiltersDisabledMemoized = useCallback(() => {
    return isFiltersDisabled(searchTerm, selectedProjects);
  }, [emailData.length, searchTerm, selectedProjects]);

  const isClearFiltersDisabledMemoized = useCallback(() => {
    return isClearFiltersDisabled(filters);
  }, [filters]);

  function handleDownload(): void {
    if (selectedEmails.length === 1) {
      // Download single email
      handleDownloadEmail(currentAttachments);
    } else if (selectedEmails.length > 1 && sharepointAdapter) {
      // Download multiple emails as zip
      setIsEmailsDownloading(true);
      handleDownloadMultipleEmails(sharepointAdapter, selectedEmails).finally(
        () => {
          setIsEmailsDownloading(false);
        }
      );
    } else {
      console.error("No emails selected for download");
    }
  }

  async function handleForward(): Promise<void> {
    if (selectedEmails.length === 1) {
      setIsForwarding(true);
      // forward selected email
      const forwarded = await handleForwardEmail(
        selectedEmails[0],
        backendAdapter,
        sharepointAdapter
      );

      if (forwarded) {
        notify(
          "Email created to forward",
          "The email has been added to your Drafts folder in Outlook",
          "success"
        );
      } else {
        notify(
          "Failed to forward",
          "Failed creating the forwarded message in drafts",
          "error"
        );
      }

      setIsForwarding(false);
    } else {
      console.error("No single email selected for forward");
    }
  }

  async function handleUnfile(): Promise<void> {
    if (selectedEmails.length === 1) {
      setIsUnfiling(true);

      // unfile selected email
      const unfiled = await handleUnfileEmail(
        selectedEmails[0],
        backendAdapter,
        sharepointAdapter
      );
      setIsUnfiling(false);
      setOpenUnfileDialog(false);
      if (unfiled) {
        notify(
          "Message Unfiled",
          "Message has been removed from project",
          "success"
        );

        // set up ready for a reload
        setPage(0);
        setEmailData([]);
        setNextLink(undefined);
        setHasMoreItems(false);
        setAppendEmailData(false);
        setSelectedEmails([]);
        setForceRefresh((old) => old + 1);
      } else
        notify(
          "Message Unfile Failed",
          "Failed to removed from project",
          "error"
        );
    } else {
      console.error("No single email selected for unfiling");
    }
  }

  const handleAttachmentsLoaded = (
    attachments: AttachmentResult | undefined
  ) => {
    setCurrentAttachments(attachments);
  };

  const searchProjects = useCallback(
    async (searchTerm: string) => {
      if (sharepointAdapter && selectedHubsite) {
        setIsProjectsLoading(true);
        try {
          const searchedProjects = await sharepointAdapter.getProjects(
            selectedHubsite,
            searchTerm
          );
          setProjects(searchedProjects);
        } finally {
          setIsProjectsLoading(false);
        }
      }
    },
    [selectedHubsite]
  );

  useEffect(() => {
    searchProjects("");
  }, [selectedHubsite, searchProjects]);

  const onEmailSelect = (email: Email) => {
    if (isMobile) {
      handleMobileEmailSelect(
        email,
        selectedEmails,
        setSelectedEmails,
        setIsModalOpen
      );
    } else {
      handleDesktopEmailSelect(email, setSelectedEmails);
    }
  };

  const onSingleEmailSelect = (email: Email) => {
    if (isMobile) {
      handleMobileEmailSelect(
        email,
        selectedEmails,
        setSelectedEmails,
        setIsModalOpen
      );
    } else {
      handleSingleDesktopEmailSelect(email, setSelectedEmails);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    const isEnabled =
      (searchTerm.length > 0 && selectedProjects.length === 0) ||
      selectedProjects.length === 1 ||
      (searchTerm.length > 0 && selectedProjects.length > 1);

    setConfidentialEnabled(isEnabled);
  }, [searchTerm.length, selectedProjects.length]);

  return (
    <div className={styles.app}>
      <div className={styles.panel}>
        {(hubsiteError || (hubsites && hubsites.length === 0)) && (
          <div className={styles.warningContainer}>
            <HubsiteWarning />
          </div>
        )}
        <div className={styles.topPanel}>
          <div className={styles.logoColumn}>
            <AtveroIcon />
          </div>
          <div className={styles.inputColumn}>
            {hubsites && hubsites.length > 1 && !projectParam ? (
              <HubSiteSelector
                hubsites={hubsites}
                selectedHubsite={selectedHubsite}
                onHubsiteSelect={(hubsite) => {
                  onHubsiteSelect(hubsite);
                  clearEmailsAndProjects();
                }}
              />
            ) : null}
            {!projectParam && (
              <ProjectSelector
                projects={projects}
                selectedProjects={selectedProjects}
                onProjectSelect={handleProjectSelect}
                onSearchProjects={searchProjects}
                isLoading={isProjectsLoading}
                disabled={!selectedHubsite}
              />
            )}

            <SearchBox
              value={inputValue}
              onChange={handleSearchChange}
              onKeyDown={handleKeyDown}
              placeholder="Search Emails"
              className={styles.input}
              disabled={!selectedHubsite}
            />
          </div>
          {isAdmin && (
            <div className={styles.topPanelButtons}>
              <Button
                {...restoreFocusTargetAttribute}
                icon={<Delete24Regular className={styles.actionIcon} />}
                appearance="secondary"
                onClick={() => {
                  // it is the user responsibility to open the dialog
                  setOpenUnfileDialog(true);
                }}
                className={styles.actionButton}
                disabled={selectedEmails.length !== 1 || isUnfiling}
              >
                Unfile
              </Button>
            </div>
          )}
          <div className={styles.topPanelButtons}>
            <div className={styles.topPanelButtons}>
              {isSelectedEmailMSG ? (
                <Tooltip
                  content="MSG files cannot be forwarded directly. Please download the email instead."
                  relationship="description"
                >
                  <Button
                    icon={
                      <ArrowForward24Regular className={styles.actionIcon} />
                    }
                    appearance="secondary"
                    onClick={handleForward}
                    className={styles.actionButton}
                    disabled={
                      selectedEmails.length !== 1 ||
                      isForwarding ||
                      isSelectedEmailMSG
                    }
                  >
                    Forward
                  </Button>
                </Tooltip>
              ) : (
                <Button
                  icon={<ArrowForward24Regular className={styles.actionIcon} />}
                  appearance="secondary"
                  onClick={handleForward}
                  className={styles.actionButton}
                  disabled={selectedEmails.length !== 1 || isForwarding}
                >
                  Forward
                </Button>
              )}
            </div>
          </div>
          <div className={styles.topPanelButtons}>
            <Button
              icon={
                isEmailsDownloading ? (
                  <Spinner size="tiny" />
                ) : (
                  <ArrowDownload24Regular className={styles.actionIcon} />
                )
              }
              appearance="secondary"
              onClick={handleDownload}
              className={styles.actionButton}
              disabled={selectedEmails.length === 0 || isEmailsDownloading}
            >
              {isEmailsDownloading ? "Downloading..." : "Download"}
            </Button>
          </div>
        </div>
      </div>
      <div className={styles.panelContainer}>
        <div className={`${styles.tablePanel} ${styles.scrollableTablePanel}`}>
          <TableHeaderComponent
            columns={columns}
            onSortChange={handleSortChange}
            currentSort={{
              columnKey: sortColumn,
              direction: sortDirection,
            }}
            useStackedRows={useStackedRows}
            setUseStackedRows={setUseStackedRows}
            filters={filters}
            setFilters={setFilters}
            setAppendEmailData={setAppendEmailData}
            setPage={setPage}
            isFiltersDisabledMemoized={isFiltersDisabledMemoized}
            isClearFiltersDisabledMemoized={isClearFiltersDisabledMemoized}
            hideProjectColumn={
              selectedProjects.length < 2 && searchTerm.length === 0
            }
            selectedEmailCount={selectedEmails.length}
            onClearSelectedEmails={() => setSelectedEmails([])}
            onToggleDriveFilter={toggleDriveFilter}
            isConfidential={isConfidential}
            setIsConfidential={setIsConfidential}
            confidentialEnabled={confidentialEnabled}
          />
          <ScrollableTable
            adapter={sharepointAdapter}
            columns={columns}
            items={emailData}
            onEmailSelect={onEmailSelect}
            onSingleEmailSelect={onSingleEmailSelect}
            loadMoreItems={loadMoreItems}
            hasMoreItems={hasMoreItems}
            onSortChange={handleSortChange}
            currentSort={{
              columnKey: sortColumn ?? "",
              direction: sortDirection,
            }}
            hasNextPage={hasMoreItems}
            loadNextPage={loadMoreItems}
            isNextPageLoading={isNextPageLoading}
            selectedEmails={selectedEmails}
            totalEmails={totalEmails}
            emailsShown={emailsShown}
            loading={loading}
            useStackedRows={useStackedRows}
            selectedProjectsCount={selectedProjects.length}
            hasSearchTerm={searchTerm.length > 0}
            hideProjectColumn={
              selectedProjects.length < 2 && searchTerm.length === 0
            }
            selectedHubsite={selectedHubsite}
            tags={tags}
          />
        </div>
        {/* MOBILE preview */}
        {isMobile && isModalOpen && (
          <Preview
            previewUrl={previewUrl}
            email={selectedEmails[0]}
            isPreviewLoading={isPreviewLoading || isAttachmentLoading}
            onLoadingComplete={handleLoadingComplete}
            onAttachmentsLoaded={handleAttachmentsLoaded}
            selectedEmailCount={selectedEmails.length}
            onClearSelectedEmails={() => setSelectedEmails([])}
            isMobile={isMobile}
            isModalOpen={isModalOpen}
            onCloseModal={handleCloseModal}
            shareRootUrl={shareRootUrl}
            sharepointAdapter={sharepointAdapter}
            tenancy={tenancy}
          />
        )}
        {/* DESKTOP preview */}
        {!isMobile && (
          <div className={`${styles.tablePanel} ${styles.previewPanel}`}>
            <Preview
              previewUrl={previewUrl}
              email={selectedEmails[0]}
              isPreviewLoading={isPreviewLoading || isAttachmentLoading}
              onLoadingComplete={handleLoadingComplete}
              onAttachmentsLoaded={handleAttachmentsLoaded}
              selectedEmailCount={selectedEmails.length}
              onClearSelectedEmails={() => setSelectedEmails([])}
              isMobile={isMobile}
              isModalOpen={isModalOpen}
              onCloseModal={handleCloseModal}
              shareRootUrl={shareRootUrl}
              sharepointAdapter={sharepointAdapter}
              tenancy={tenancy}
            />
          </div>
        )}
      </div>
      <Toaster toasterId={toasterId} />
      <Dialog
        // this controls the dialog open state
        open={openUnfileDialog}
        onOpenChange={(_event, data) => {
          // it is the users responsibility to react accordingly to the open state change
          setOpenUnfileDialog(data.open);
        }}
      >
        <UnfileDialog handleUnfile={handleUnfile} isUnfiling={isUnfiling} />
      </Dialog>
    </div>
  );
}

const useStyles = makeStyles({
  previewPanel: {
    marginLeft: "0px",
    minWidth: "800px",
    marginRight: tokens.spacingVerticalS,
  },
  scrollableTablePanel: {
    minWidth: "250px",
  },
  actionIcon: {
    color: "#0F6CBD",
  },
  actionButton: {
    marginRight: "16px",
    height: "32px",
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    borderRadius: tokens.borderRadiusMedium,
  },

  panelContainer: {
    display: "flex",
    flexDirection: "row",
    width: "100%",
    height: "100%",
    "@media (max-width: 768px)": {
      width: "calc(100vw - 10px)",
      flexDirection: "column",
      marginRight: tokens.spacingVerticalS,
    },
  },
  previewTemp: {
    minWidth: "800px",
    backgroundColor: "#FFFFFF",
  },
  loginContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
    textAlign: "center",
    gap: "20px",
  },
  app: {
    backgroundColor: "#F0F0F0",
    height: "100vh",
    width: "100vw",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "flex-start",
    boxSizing: "border-box",
  },
  panel: {
    width: "100%",
  },
  atveroLogoContainer: {
    marginLeft: tokens.spacingVerticalS,
  },
  tablePanel: {
    backgroundColor: "#FFFFFF",
    display: "flex",
    flexDirection: "column",
    flex: "1",
    marginLeft: tokens.spacingVerticalS,
    marginBottom: tokens.spacingVerticalS,
    ...shorthands.border("1px", "solid", "#E2E2E2"),
    borderRadius: "5px",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    overflow: "hidden",
  },
  topPanel: {
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    ...shorthands.border("1px", "solid", "#E2E2E2"),
    margin: tokens.spacingVerticalS,
    backgroundColor: "#FFFFFF",
    borderRadius: "5px",
    padding: "10px 5px",
    justifyContent: "space-between",
    display: "flex",
    alignItems: "center",
    boxSizing: "border-box",
  },
  logoColumn: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "auto",
    "@media (max-width: 768px)": {
      maxWidth: "60px",
    },
  },
  inputColumn: {
    display: "flex",
    flexDirection: "row",
    gap: "10px",
    flex: 1,
    "@media (max-width: 768px)": {
      padding: "0px",
      flexDirection: "column",
    },
  },
  previewContainer: {
    position: "absolute",
    top: 0,
    right: 0,
    width: "40%",
    height: "100%",
    backgroundColor: "#FFFFFF",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    borderBottomLeftRadius: "5px",
    borderTopLeftRadius: "5px",
    display: "flex",
    flexDirection: "column",
  },
  buttonContainer: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-around",
    padding: "10px",
    borderBottom: "1px solid #E2E2E2",
  },
  iframe: {
    flex: 1,
    border: "none",
    borderBottomLeftRadius: "5px",
  },
  logoContainer: {
    display: "flex",
    justifyContent: "space-around",
    alignItems: "center",
  },
  logo: {
    height: "40px",
  },
  topPanelButtons: {
    display: "flex",
    alignItems: "center",
    "@media (max-width: 768px)": {
      display: "none",
    },
  },
  input: {
    maxWidth: "300px",
    width: "100%",
  },
  logoProjects: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  warningContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    padding: tokens.spacingVerticalM,
    paddingBottom: "4px",
  },
});

export default App;
