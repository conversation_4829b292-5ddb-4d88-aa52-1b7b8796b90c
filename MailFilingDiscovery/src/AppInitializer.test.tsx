import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import AppInitializer from "./AppInitializer";
import { Hubsite } from "./shared/types/Hubsite";
import { TestSharepointAdapter } from "./adapters/TestSharepointAdapter";

import { AppProps } from "./App";

vi.mock("./App", () => ({
  default: ({ hubsites, selectedHubsite, hubsiteError }: AppProps) => (
    <div data-testid="mock-app">
      <div data-testid="hubsites">{JSON.stringify(hubsites)}</div>
      <div data-testid="selected-hubsite">
        {JSON.stringify(selectedHubsite)}
      </div>
      <div data-testid="hubsite-error">{hubsiteError}</div>
    </div>
  ),
}));

vi.mock("./components/AtveroIcon", () => ({
  AtveroIcon: () => <div data-testid="atvero-icon">Atvero Icon</div>,
}));

beforeEach(() => {
  window.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
});

describe("AppInitializer", () => {
  const mockHubsites: Hubsite[] = [
    {
      displayName: "Hub 1",
      name: "Hub 1",
      url: "http://hub1",
      path: "/hub1",
    },
    {
      displayName: "Hub 2",
      name: "Hub 2",
      url: "http://hub2",
      path: "/hub2",
    },
  ];

  const mockBackendAdapter = {
    getHubsites: vi.fn(),
    forwardEmail: vi.fn(),
    unfileEmail: vi.fn(),
    getTags: vi.fn(),
    getSetting: vi.fn(),
  };
  const mockSharepointAdapter = {} as TestSharepointAdapter;

  mockSharepointAdapter.getAtveroGroups = vi
    .fn()
    .mockResolvedValue(["Atvero Mail Admins"]);

  mockSharepointAdapter.getCmapGroups = vi
    .fn()
    .mockResolvedValue(["CMAP Mail Admins"]);

  mockSharepointAdapter.getIsAdmin = vi.fn().mockResolvedValue(false);

  let consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});

  beforeEach(() => {
    vi.clearAllMocks();
    consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    mockBackendAdapter.getHubsites.mockResolvedValue({
      hubsites: mockHubsites,
      error: undefined,
    });
  });

  afterEach(() => {
    consoleErrorSpy.mockRestore();
  });

  describe("Initial Loading States", () => {
    it("renders hubsite loading state after adapter initialization", async () => {
      render(
        <AppInitializer
          selectedHubsite={undefined}
          setSelectedHubsite={vi.fn()}
          sharepointAdapter={mockSharepointAdapter}
          backendAdapter={mockBackendAdapter}
        />
      );

      expect(screen.getByText("Fetching your hubsites...")).toBeInTheDocument();
    });
  });

  describe("Hubsite Handling", () => {
    it("automatically selects hubsite when only one is available", async () => {
      const singleHubsite: Hubsite[] = [mockHubsites[0]];
      mockBackendAdapter.getHubsites.mockResolvedValue({
        hubsites: singleHubsite,
        error: undefined,
      });

      const mockSelectProject = vi.fn();

      render(
        <AppInitializer
          selectedHubsite={undefined}
          setSelectedHubsite={mockSelectProject}
          sharepointAdapter={mockSharepointAdapter}
          backendAdapter={mockBackendAdapter}
        />
      );

      // await waitFor(() => {
      //   const selectedHubsiteElement = screen.getByTestId("selected-hubsite");
      //   expect(selectedHubsiteElement).toHaveTextContent(
      //     JSON.stringify(singleHubsite[0])
      //   );
      // });
      await waitFor(() => {
        expect(mockSelectProject).toHaveBeenCalled();
      });
    });

    it("doesn't automatically selects hubsite when more than one is available", async () => {
      const multipleHubsite: Hubsite[] = mockHubsites;
      mockBackendAdapter.getHubsites.mockResolvedValue({
        hubsites: multipleHubsite,
        error: undefined,
      });

      const mockSelectProject = vi.fn();

      render(
        <AppInitializer
          selectedHubsite={undefined}
          setSelectedHubsite={mockSelectProject}
          sharepointAdapter={mockSharepointAdapter}
          backendAdapter={mockBackendAdapter}
        />
      );

      await waitFor(() => {
        expect(mockSelectProject).not.toHaveBeenCalled();
      });
    });

    it("handles hubsite fetch error", async () => {
      const errorMessage = "Failed to fetch hubsites";
      mockBackendAdapter.getHubsites.mockResolvedValue({
        hubsites: undefined,
        error: errorMessage,
      });

      render(
        <AppInitializer
          selectedHubsite={undefined}
          setSelectedHubsite={vi.fn()}
          sharepointAdapter={mockSharepointAdapter}
          backendAdapter={mockBackendAdapter}
        />
      );

      await waitFor(() => {
        const errorElement = screen.getByTestId("hubsite-error");
        expect(errorElement).toHaveTextContent(errorMessage);
      });
    });

    it("handles empty hubsites response", async () => {
      mockBackendAdapter.getHubsites.mockResolvedValue({
        hubsites: [],
        error: undefined,
      });

      render(
        <AppInitializer
          selectedHubsite={undefined}
          setSelectedHubsite={vi.fn()}
          sharepointAdapter={mockSharepointAdapter}
          backendAdapter={mockBackendAdapter}
        />
      );

      await waitFor(() => {
        const errorElement = screen.getByTestId("hubsite-error");
        expect(errorElement).toHaveTextContent("No hubsites available");
      });
    });

    it("handles hubsite fetch exception", async () => {
      mockBackendAdapter.getHubsites.mockRejectedValue(
        new Error("Network error")
      );

      render(
        <AppInitializer
          selectedHubsite={undefined}
          setSelectedHubsite={vi.fn()}
          sharepointAdapter={mockSharepointAdapter}
          backendAdapter={mockBackendAdapter}
        />
      );

      await waitFor(() => {
        const errorElement = screen.getByTestId("hubsite-error");
        expect(errorElement).toHaveTextContent("Failed to fetch hubsites");
      });
    });
  });
});
