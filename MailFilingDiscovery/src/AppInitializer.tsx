import { useState, useEffect } from "react";
import { makeStyles, Spinner, tokens } from "@fluentui/react-components";
import App from "./App";
import { AtveroIcon } from "./components/AtveroIcon";
import { Hubsite } from "./shared/types/Hubsite";
import { detectTenancy } from "./shared/utils/TenancyService";
import { Tenancy } from "./shared/types/Tenancy";
import { IBackendAdapter } from "./adapters/IBackendAdapter";
import { ISharepointAdapter } from "./adapters/ISharepointAdapter";

const useStyles = makeStyles({
  loaderContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: "100vh",
    flexDirection: "column",
    width: "100vw",
    backgroundColor: tokens.colorNeutralBackground2,
  },
  spinner: {
    marginTop: "10px",
  },
});

const AuthenticatedContent = ({
  selectedHubsite,
  setSelectedHubsite,
  sharepointAdapter,
  backendAdapter,
}: {
  selectedHubsite: Hubsite | undefined;
  setSelectedHubsite: (hubsite: Hubsite | undefined) => void;
  sharepointAdapter: ISharepointAdapter;
  backendAdapter: IBackendAdapter;
}) => {
  const styles = useStyles();
  const [hubsites, setHubsites] = useState<Hubsite[] | undefined>();
  const [hubsiteError, setHubsiteError] = useState<string | undefined>();
  const [isHubsiteLoading, setIsHubsiteLoading] = useState(true);
  const [tenancy, _setTenancy] = useState<Tenancy>(() => detectTenancy());

  const switchHubsite = (selectedHubsite: Hubsite | undefined) => {
    console.log("Selecting hubsite", selectedHubsite?.url);
    setSelectedHubsite(selectedHubsite);
  };

  useEffect(() => {
    const fetchHubsites = async () => {
      try {
        if (!sharepointAdapter) return;
        const hubsiteResult = await backendAdapter.getHubsites();

        if (hubsiteResult.error) {
          setHubsiteError(hubsiteResult.error);
          setHubsites(undefined);
          setSelectedHubsite(undefined);
        } else if (
          !hubsiteResult.hubsites ||
          hubsiteResult.hubsites.length === 0
        ) {
          setHubsiteError("No hubsites available");
          setHubsites(undefined);
          setSelectedHubsite(undefined);
        } else {
          setHubsiteError(undefined);
          setHubsites(hubsiteResult.hubsites);

          if (hubsiteResult.hubsites.length === 1) {
            setSelectedHubsite(hubsiteResult.hubsites[0]);
          }
        }
      } catch (error) {
        setHubsiteError("Failed to fetch hubsites");
      } finally {
        setIsHubsiteLoading(false);
      }
    };

    fetchHubsites();
  }, [backendAdapter, sharepointAdapter]);

  if (isHubsiteLoading || !sharepointAdapter) {
    return (
      <div className={styles.loaderContainer}>
        <AtveroIcon />
        <Spinner
          className={styles.spinner}
          size="tiny"
          label="Fetching your hubsites..."
        />
      </div>
    );
  }

  return (
    <App
      sharepointAdapter={sharepointAdapter}
      backendAdapter={backendAdapter}
      hubsites={hubsites}
      selectedHubsite={selectedHubsite}
      hubsiteError={hubsiteError}
      onHubsiteSelect={switchHubsite}
      tenancy={tenancy}
    />
  );
};

const AppInitializer: React.FC<{
  selectedHubsite: Hubsite | undefined;
  setSelectedHubsite: (hubsite: Hubsite | undefined) => void;
  sharepointAdapter: ISharepointAdapter | undefined;
  backendAdapter: IBackendAdapter;
}> = ({
  selectedHubsite,
  setSelectedHubsite,
  sharepointAdapter,
  backendAdapter,
}) => {
  if (sharepointAdapter === undefined) {
    return null;
  }

  return (
    <AuthenticatedContent
      selectedHubsite={selectedHubsite}
      setSelectedHubsite={setSelectedHubsite}
      sharepointAdapter={sharepointAdapter}
      backendAdapter={backendAdapter}
    />
  );
};

export default AppInitializer;
