import { SortDirection } from "../utils/FilterUtils";

export type EmailResponse = {
  emails: Email[];
  nextLink: string | undefined;
  page: number;
  pageSize: number;
  sortField: string | undefined;
  sortDirection: SortDirection;
  moreResultsAvailable?: boolean | undefined;
  total?: number;
};

export type Email = {
  projectCode: string;
  id: string;
  subject: string;
  sentBy: string;
  dateFiled: string;
  tag?: string;
  driveItemID?: string | undefined;
  driveID?: string;
  attachmentCount?: string;
  fileRef?: string;
  emailTextSummary?: string;
  emailImportant?: string;
  emailConfidential?: boolean;
  messageId?: string;
  conversationId?: string;
};
