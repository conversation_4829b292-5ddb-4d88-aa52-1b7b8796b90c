import { describe, it, expect, vi } from "vitest";
import { buildProjectSearchQuery } from "./ProjectSearchHelper";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";

describe("ProjectSearchHelper", () => {
  let testAdapter: ISharepointAdapter;

  beforeEach(() => {
    testAdapter = new TestSharepointAdapter();
  });

  describe("buildProjectSearchQuery", () => {
    it("should build a query with empty where clause when no search string is provided", () => {
      const { viewXml, renderListDataParams } = buildProjectSearchQuery("");

      expect(viewXml).toContain("<Where></Where>");
      expect(viewXml).toContain('<RowLimit Paged="TRUE">200</RowLimit>');
      expect(viewXml).toContain(
        '<FieldRef Name="ProjectCode" Ascending="True"/>'
      );
      expect(viewXml).toContain('<FieldRef Name="ProjectCode"/>');
      expect(viewXml).toContain('<FieldRef Name="Description"/>');
      expect(viewXml).toContain('<FieldRef Name="ATVImportedSourceID"/>');
      expect(renderListDataParams.ViewXml).toBe(viewXml);
      expect(renderListDataParams.Paging).toBe("Paged=TRUE");
    });

    it("should build a query with search conditions when a search string is provided", () => {
      const searchTerm = "Test Project";
      const { viewXml, renderListDataParams } =
        buildProjectSearchQuery(searchTerm);

      expect(viewXml).toContain(`<Value Type='Text'>${searchTerm}</Value>`);
      expect(viewXml).toContain("<FieldRef Name='ProjectCode'/>");
      expect(viewXml).toContain("<FieldRef Name='Description'/>");
      expect(viewXml).toContain("<Or>");
      expect(viewXml).toContain("<Contains>");
      expect(renderListDataParams.ViewXml).toBe(viewXml);
      expect(renderListDataParams.Paging).toBe("Paged=TRUE");
    });

    it("should handle special characters in the search string", () => {
      const searchTerm = "Project & <Special> 'Characters'";
      const { viewXml } = buildProjectSearchQuery(searchTerm);

      expect(viewXml).toContain(`<Value Type='Text'>${searchTerm}</Value>`);
    });

    it("should limit the results to 200 items", () => {
      const { viewXml } = buildProjectSearchQuery("test");
      expect(viewXml).toContain('<RowLimit Paged="TRUE">200</RowLimit>');
    });
  });

  describe("integration with adapter", () => {
    it("should create params that can be used with the adapter's getListDataAsStream method", async () => {
      const mockResponse = {
        Row: [
          { ProjectCode: "PRJ001", Description: "Project 1", ID: "1" },
          { ProjectCode: "PRJ002", Description: "Project 2", ID: "2" },
        ],
      };

      testAdapter.getListDataAsStream = vi.fn().mockResolvedValue(mockResponse);

      const { renderListDataParams } = buildProjectSearchQuery("test");

      const mockWeb = { lists: { getByTitle: vi.fn() } };
      mockWeb.lists.getByTitle = vi.fn().mockReturnValue({
        renderListDataAsStream: vi.fn().mockResolvedValue(mockResponse),
      });

      const result = await testAdapter.getListDataAsStream(
        mockWeb as any,
        "Projects",
        renderListDataParams
      );

      expect(result).toBe(mockResponse);
      expect(testAdapter.getListDataAsStream).toHaveBeenCalledWith(
        mockWeb,
        "Projects",
        renderListDataParams
      );
    });

    it("should work with the adapter's getProjects method", async () => {
      const mockResponse = {
        Row: [
          { ProjectCode: "PRJ001", Description: "Project 1", ID: "1" },
          { ProjectCode: "PRJ002", Description: "Project 2", ID: "2" },
        ],
      };

      testAdapter.getProjects = vi.fn().mockImplementation(async () => {
        const projects = mockResponse.Row.map((project) => ({
          code: project.ProjectCode,
          title: project.Description,
          id: project.ID,
          favorite: false,
        }));

        return projects;
      });

      const result = await testAdapter.getProjects(
        { url: "/sites/hub" } as any,
        "test"
      );

      expect(result).toHaveLength(2);
      expect(result[0].code).toBe("PRJ001");
      expect(result[1].title).toBe("Project 2");
      expect(testAdapter.getProjects).toHaveBeenCalledWith(
        { url: "/sites/hub" } as any,
        "test"
      );
    });
  });
});
