import { describe, it, expect, vi } from "vitest";
import {
  handleMobileEmailSelect,
  handleDesktopEmailSelect,
  handleSingleDesktopEmailSelect,
} from "./EmailSelection";
import { Email } from "../types/Email";

describe("EmailSelection", () => {
  const mockSetSelectedEmails = vi.fn();
  const mockSetIsModalOpen = vi.fn();

  const sampleEmail: Email = {
    id: "1",
    subject: "Test Email",
    sentBy: "<EMAIL>",
    dateFiled: "2023-10-01",
    projectCode: "Test 1",
  };

  const anotherEmail: Email = {
    id: "2",
    subject: "Another Test Email",
    sentBy: "<EMAIL>",
    dateFiled: "2023-10-02",
    projectCode: "Test 2",
  };

  afterEach(() => {
    vi.clearAllMocks(); // Reset mocks after each test
  });

  // Tests for Mobile Email Selection
  describe("handleMobileEmailSelect", () => {
    it("should select the email and open the modal if no email is selected in mobile view", () => {
      const selectedEmails: Email[] = [];

      handleMobileEmailSelect(
        sampleEmail,
        selectedEmails,
        mockSetSelectedEmails,
        mockSetIsModalOpen
      );

      expect(mockSetSelectedEmails).toHaveBeenCalledWith([sampleEmail]);
      expect(mockSetIsModalOpen).toHaveBeenCalledWith(true);
    });

    it("should deselect the email and close the modal if the email is already selected in mobile view", () => {
      const selectedEmails = [sampleEmail];

      handleMobileEmailSelect(
        sampleEmail,
        selectedEmails,
        mockSetSelectedEmails,
        mockSetIsModalOpen
      );

      expect(mockSetSelectedEmails).toHaveBeenCalledWith([]);
      expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
    });
  });

  // Tests for Desktop Email Selection
  describe("handleDesktopEmailSelect", () => {
    it("should select the email in desktop view if it's not already selected", () => {
      handleDesktopEmailSelect(sampleEmail, mockSetSelectedEmails);

      // Handle the callback form by calling the function that was passed
      const setSelectedEmailsCallback = mockSetSelectedEmails.mock.calls[0][0];
      expect(setSelectedEmailsCallback([])).toEqual([sampleEmail]);

      expect(mockSetIsModalOpen).not.toHaveBeenCalled(); // Modal should not open in desktop
    });

    it("should deselect the email in desktop view if it's already selected", () => {
      handleDesktopEmailSelect(sampleEmail, mockSetSelectedEmails);

      // Handle the callback form by calling the function that was passed
      const setSelectedEmailsCallback = mockSetSelectedEmails.mock.calls[0][0];
      expect(setSelectedEmailsCallback([sampleEmail])).toEqual([]);

      expect(mockSetIsModalOpen).not.toHaveBeenCalled(); // Modal should not open in desktop
    });

    it("should select a new email in desktop view and allow multiple selections", () => {
      handleDesktopEmailSelect(anotherEmail, mockSetSelectedEmails);

      // Handle the callback form by calling the function that was passed
      const setSelectedEmailsCallback = mockSetSelectedEmails.mock.calls[0][0];
      expect(setSelectedEmailsCallback([sampleEmail])).toEqual([
        sampleEmail,
        anotherEmail,
      ]);

      expect(mockSetIsModalOpen).not.toHaveBeenCalled(); // Modal should not open in desktop
    });

    it("should remove only the deselected email in desktop view when multiple are selected", () => {
      handleDesktopEmailSelect(anotherEmail, mockSetSelectedEmails);

      // Handle the callback form by calling the function that was passed
      const setSelectedEmailsCallback = mockSetSelectedEmails.mock.calls[0][0];
      expect(setSelectedEmailsCallback([sampleEmail, anotherEmail])).toEqual([
        sampleEmail,
      ]);

      expect(mockSetIsModalOpen).not.toHaveBeenCalled(); // Modal should not open in desktop
    });
  });

  // Tests for Desktop Email Selection
  describe("handleSingleDesktopEmailSelect", () => {
    it("should select the email in desktop view if it's not already selected", () => {
      handleSingleDesktopEmailSelect(sampleEmail, mockSetSelectedEmails);

      // Handle the callback form by calling the function that was passed
      const setSelectedEmailsCallback = mockSetSelectedEmails.mock.calls[0][0];
      expect(setSelectedEmailsCallback([])).toEqual([sampleEmail]);

      expect(mockSetIsModalOpen).not.toHaveBeenCalled(); // Modal should not open in desktop
    });

    it("should deselect the email in desktop view if it's already selected", () => {
      handleSingleDesktopEmailSelect(sampleEmail, mockSetSelectedEmails);

      // Handle the callback form by calling the function that was passed
      const setSelectedEmailsCallback = mockSetSelectedEmails.mock.calls[0][0];
      expect(setSelectedEmailsCallback([sampleEmail])).toEqual([]);

      expect(mockSetIsModalOpen).not.toHaveBeenCalled(); // Modal should not open in desktop
    });

    it("should select a new email in desktop view and not allow multiple selections", () => {
      handleSingleDesktopEmailSelect(anotherEmail, mockSetSelectedEmails);

      // Handle the callback form by calling the function that was passed
      const setSelectedEmailsCallback = mockSetSelectedEmails.mock.calls[0][0];
      expect(setSelectedEmailsCallback([sampleEmail])).toEqual([anotherEmail]);

      expect(mockSetIsModalOpen).not.toHaveBeenCalled(); // Modal should not open in desktop
    });

    it("should remove only the deselected email in desktop view when multiple are selected", () => {
      handleDesktopEmailSelect(anotherEmail, mockSetSelectedEmails);

      // Handle the callback form by calling the function that was passed
      const setSelectedEmailsCallback = mockSetSelectedEmails.mock.calls[0][0];
      expect(setSelectedEmailsCallback([sampleEmail, anotherEmail])).toEqual([
        sampleEmail,
      ]);

      expect(mockSetIsModalOpen).not.toHaveBeenCalled(); // Modal should already be open in desktop
    });
  });
});
