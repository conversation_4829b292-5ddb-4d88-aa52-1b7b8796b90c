import { useState, useEffect, useMemo } from "react";
import addressParser, { ParsedMailbox } from "email-addresses";
import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";
import "@pnp/graph/users";
import "@pnp/graph/photos";

// Cache object URLs instead of promises
const photoCache = new Map<string, string>();

export const getNameAndEmail = (recipient: string): [string, string] => {
  const parts: ParsedMailbox | addressParser.ParsedGroup | null =
    addressParser.parseOneAddress(recipient) as ParsedMailbox;
  let name = recipient;
  let email = "";

  if (parts?.name && parts?.address) {
    name = parts.name;
    email = parts.address;
  } else if (parts?.name) {
    name = parts.name;
  } else if (parts?.address) {
    email = parts.address;
  }
  return [name, email];
};

export const useRecipient = (
  recipient: string,
  adapter: ISharepointAdapter
) => {
  const [img, setImg] = useState<string | null>(() => {
    const [, email] = getNameAndEmail(recipient);
    return photoCache.get(email) || null;
  });

  const [name, email] = useMemo(() => getNameAndEmail(recipient), [recipient]);

  useEffect(() => {
    let isSubscribed = true;

    const fetchPhoto = async () => {
      if (!email) return;

      // Return cached image if available
      const cachedUrl = photoCache.get(email);
      if (cachedUrl) {
        if (isSubscribed) {
          setImg(cachedUrl);
        }
        return;
      }

      try {
        const blob = await adapter.getPhotoBlob(email);
        const url = URL.createObjectURL(blob);

        // Cache the URL
        photoCache.set(email, url);

        if (isSubscribed) {
          setImg(url);
        }
      } catch (e) {
        if (isSubscribed) {
          setImg(null);
        }
      }
    };

    fetchPhoto();

    return () => {
      isSubscribed = false;
    };
  }, [email]);

  return { name, email, img };
};
