import { describe, it, expect } from "vitest";
import { getFileIconClass } from "./FileIconClass";

describe("getFileIconClass", () => {
  it("should return the correct icon class for PDF files", () => {
    expect(getFileIconClass("application/pdf")).toBe("ms-Icon--PDF");
  });

  it("should return the correct icon class for Word documents", () => {
    expect(getFileIconClass("application/msword")).toBe(
      "ms-Icon--WordDocument"
    );
    expect(
      getFileIconClass(
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      )
    ).toBe("ms-Icon--WordDocument");
  });

  it("should return the default icon class for unknown file types", () => {
    expect(getFileIconClass("application/unknown")).toBe("ms-Icon--Document");
  });
});
