import { parseFileRef } from "./ParseFileRef";
import { vi } from "vitest";

describe("parseFileRef", () => {
  it("should extract the relevant portion after 'Filed Email Content/'", () => {
    const fileRef =
      "https://example.com/sites/Mail2/Filed Email Content/Mail2/2024-09/06-10-Test.eml";
    const result = parseFileRef(fileRef);
    expect(result).toBe("Mail2/2024-09/06-10-Test.eml");
  });

  it("should extract the relevant portion after 'Confidential Email Content/'", () => {
    const fileRef =
      "https://example.com/sites/Mail2/Confidential Email Content/Mail2/2024-09/06-10-Confidential-Test.eml";
    const result = parseFileRef(fileRef);
    expect(result).toBe("Mail2/2024-09/06-10-Confidential-Test.eml");
  });

  it("should return 'unknown-file.eml' if the fileRef doesn't contain 'Filed Email Content' or 'Confidential Email Content'", () => {
    const fileRef =
      "https://example.com/sites/Mail2/InvalidFormat/06-10-Test.eml";
    const result = parseFileRef(fileRef);
    expect(result).toBe("unknown-file.eml");
  });

  it("should return 'unknown-file.eml' if the fileRef contains 'Filed Email Content' but no path", () => {
    const fileRef = "https://example.com/sites/Mail2/Filed Email Content";
    const result = parseFileRef(fileRef);
    expect(result).toBe("unknown-file.eml");
  });

  it("should return 'unknown-file.eml' for an invalid URL", () => {
    const invalidUrl = "invalid-url";
    const result = parseFileRef(invalidUrl);
    expect(result).toBe("unknown-file.eml");
  });

  it("should log an error if an invalid URL is provided", () => {
    const consoleErrorSpy = vi.spyOn(console, "error");
    const invalidUrl = "invalid-url";
    parseFileRef(invalidUrl);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      "Error parsing fileRef:",
      expect.any(Error)
    );
  });

  it("should log an error if neither 'Filed Email Content' nor 'Confidential Email Content' is found in the fileRef", () => {
    const consoleErrorSpy = vi.spyOn(console, "error");
    const fileRef =
      "https://example.com/sites/Mail2/InvalidFormat/06-10-Test.eml";
    parseFileRef(fileRef);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      "No valid 'Filed Email Content' or 'Confidential Filed Email Content' found in fileRef."
    );
  });
});
