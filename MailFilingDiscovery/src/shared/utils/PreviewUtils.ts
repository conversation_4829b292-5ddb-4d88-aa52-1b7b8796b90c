import { Email } from "../types/Email";
import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";
import { ListTitles } from "../types/ListTitles";
import {
  FieldValueSet,
  NullableOption,
} from "@microsoft/microsoft-graph-types";

interface CustomFieldValueSet extends FieldValueSet {
  EmailSubject?: string;
  EmailFrom?: string;
  EmailReceived?: string;
  ATVMessageId?: string;
  ATVConversationId?: string;
}

/**
 * Fetches the preview URL for a selected email
 *
 * @param adapter SharePoint adapter instance
 * @param selectedEmail The selected email to preview
 * @returns Preview URL string
 */
export async function fetchPreview(
  adapter: ISharepointAdapter,
  selectedEmail: Email | null
): Promise<NullableOption<string> | undefined> {
  if (!adapter || !selectedEmail) {
    return "";
  }

  try {
    if (!selectedEmail.driveItemID) {
      const newSelectedEmail = await getSelectedEmailWithDriveItemID(
        adapter,
        selectedEmail
      );
      return (await adapter.getPreviewUrl(newSelectedEmail)) ?? "";
    }
    return (await adapter.getPreviewUrl(selectedEmail)) ?? "";
  } catch (error) {
    console.error("Error fetching selected emails preview: ", error);
    return "";
  }
}

/**
 * Gets the selected email with the drive item ID
 *
 * @param adapter SharePoint adapter instance
 * @param selectedEmail The selected email
 * @returns Email with drive item ID
 */
async function getSelectedEmailWithDriveItemID(
  adapter: ISharepointAdapter,
  selectedEmail: Email
): Promise<Email> {
  try {
    // Determine the list type based on the emailConfidential property
    const listType = selectedEmail.emailConfidential
      ? ListTitles.Confidential
      : ListTitles.NonConfidential;

    // Get the site and list details
    const site = await adapter.getSiteFromSelectedEmail(selectedEmail);
    const lists = await adapter.getListsFromSite(site.id as string);

    const filteredLists = lists.filter((list) => list.name === listType);

    if (filteredLists.length === 0) {
      throw new Error(`List of type '${listType}' not found`);
    }

    const selectedEmailWithDriveItems = await adapter.getEmailWithDriveItem(
      site.id as string,
      filteredLists[0].id as string,
      selectedEmail?.id ?? ""
    );

    const fields = selectedEmailWithDriveItems.fields as CustomFieldValueSet;
    return {
      ...selectedEmail,
      id: fields?.id ?? "",
      subject: fields?.EmailSubject ?? "",
      sentBy: fields?.EmailFrom ?? "",
      dateFiled: fields?.EmailReceived ?? "",
      driveItemID: selectedEmailWithDriveItems.driveItem?.id,
      driveID: selectedEmail.driveID,
      projectCode: selectedEmail.projectCode,
      messageId: fields?.ATVMessageId ?? "",
      conversationId: fields?.ATVConversationId ?? "",
    };
  } catch (error) {
    console.error("Error getting email with drive item ID:", error);
    throw error;
  }
}
