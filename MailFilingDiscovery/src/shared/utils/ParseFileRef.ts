export const parseFileRef = (fileRef: string): string => {
  try {
    // Ensure URL is valid
    const url = new URL(fileRef); // This will throw an error if the URL is invalid
    const decodedUrl = decodeURIComponent(url.href);

    // Patterns to match "Filed Email Content" and "Confidential Filed Email Content"
    const filedEmailContent = "Filed Email Content/";
    const confidentialEmailContent = "Confidential Email Content/";

    // Find the position where the relevant content starts
    const filedIndex = decodedUrl.indexOf(filedEmailContent);
    const confidentialIndex = decodedUrl.indexOf(confidentialEmailContent);

    // Determine which phrase is present and extract everything after it
    if (filedIndex !== -1) {
      return decodedUrl.substring(filedIndex + filedEmailContent.length);
    } else if (confidentialIndex !== -1) {
      return decodedUrl.substring(
        confidentialIndex + confidentialEmailContent.length
      );
    } else {
      console.error(
        "No valid 'Filed Email Content' or 'Confidential Filed Email Content' found in fileRef."
      );
      return "unknown-file.eml"; // Fallback if no match is found
    }
  } catch (error) {
    console.error("Error parsing fileRef:", error);
    return "unknown-file.eml"; // Fallback in case of error
  }
};
