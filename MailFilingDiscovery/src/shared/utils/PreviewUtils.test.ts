import { describe, it, expect, vi, beforeEach } from "vitest";
import { fetchPreview } from "./PreviewUtils";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { Email } from "../types/Email";
import { ListTitles } from "../types/ListTitles";

describe("PreviewUtils", () => {
  let testAdapter: TestSharepointAdapter;
  let mockEmail: Email;

  beforeEach(() => {
    testAdapter = new TestSharepointAdapter();

    mockEmail = {
      id: "email123",
      subject: "Test Email",
      sentBy: "<EMAIL>",
      dateFiled: "2023-01-01T12:00:00Z",
      projectCode: "PRJ001",
      driveID: "drive123",
      driveItemID: "item123",
      emailConfidential: false,
      messageId: "msg123",
      conversationId: "conv123",
    };
  });

  describe("fetchPreview", () => {
    it("should return empty string when adapter is null", async () => {
      const result = await fetchPreview(null as any, mockEmail);
      expect(result).toBe("");
    });

    it("should return empty string when selectedEmail is null", async () => {
      const result = await fetchPreview(testAdapter, null);
      expect(result).toBe("");
    });

    it("should fetch preview URL directly when driveItemID is available", async () => {
      const mockPreviewUrl = "https://example.com/preview";
      testAdapter.getPreviewUrl = vi.fn().mockResolvedValue(mockPreviewUrl);

      const result = await fetchPreview(testAdapter, mockEmail);

      expect(result).toBe(mockPreviewUrl);
      expect(testAdapter.getPreviewUrl).toHaveBeenCalledWith(mockEmail);
    });

    it("should fetch driveItemID first when it's not available", async () => {
      const emailWithoutDriveItemID = { ...mockEmail, driveItemID: undefined };

      const mockSite = { id: "site123" };
      testAdapter.getSiteFromSelectedEmail = vi
        .fn()
        .mockResolvedValue(mockSite);

      const mockLists = [
        { id: "list123", name: ListTitles.NonConfidential },
        { id: "list456", name: ListTitles.Confidential },
      ];
      testAdapter.getListsFromSite = vi.fn().mockResolvedValue(mockLists);

      const mockDriveItem = { id: "newItem123" };
      const mockEmailWithDriveItem = {
        driveItem: mockDriveItem,
        fields: {
          id: "email123",
          EmailSubject: "Updated Subject",
          EmailFrom: "<EMAIL>",
          EmailReceived: "2023-01-02T12:00:00Z",
          ATVMessageId: "updatedMsg123",
          ATVConversationId: "updatedConv123",
        },
      };
      testAdapter.getEmailWithDriveItem = vi
        .fn()
        .mockResolvedValue(mockEmailWithDriveItem);

      const mockPreviewUrl = "https://example.com/preview";
      testAdapter.getPreviewUrl = vi.fn().mockResolvedValue(mockPreviewUrl);

      const result = await fetchPreview(testAdapter, emailWithoutDriveItemID);

      expect(result).toBe(mockPreviewUrl);

      expect(testAdapter.getSiteFromSelectedEmail).toHaveBeenCalledWith(
        emailWithoutDriveItemID
      );
      expect(testAdapter.getListsFromSite).toHaveBeenCalledWith(mockSite.id);
      expect(testAdapter.getEmailWithDriveItem).toHaveBeenCalledWith(
        mockSite.id,
        mockLists[0].id,
        emailWithoutDriveItemID.id
      );

      expect(testAdapter.getPreviewUrl).toHaveBeenCalledWith(
        expect.objectContaining({
          driveItemID: mockDriveItem.id,
          subject: mockEmailWithDriveItem.fields.EmailSubject,
          sentBy: mockEmailWithDriveItem.fields.EmailFrom,
        })
      );
    });

    it("should handle confidential emails correctly", async () => {
      const confidentialEmail = {
        ...mockEmail,
        driveItemID: undefined,
        emailConfidential: true,
      };

      const mockSite = { id: "site123" };
      testAdapter.getSiteFromSelectedEmail = vi
        .fn()
        .mockResolvedValue(mockSite);

      const mockLists = [
        { id: "list123", name: ListTitles.NonConfidential },
        { id: "list456", name: ListTitles.Confidential },
      ];
      testAdapter.getListsFromSite = vi.fn().mockResolvedValue(mockLists);

      const mockDriveItem = { id: "newItem123" };
      const mockEmailWithDriveItem = {
        driveItem: mockDriveItem,
        fields: {
          id: "email123",
          EmailSubject: "Confidential Subject",
          EmailFrom: "<EMAIL>",
          EmailReceived: "2023-01-02T12:00:00Z",
          ATVMessageId: "confMsg123",
          ATVConversationId: "confConv123",
        },
      };
      testAdapter.getEmailWithDriveItem = vi
        .fn()
        .mockResolvedValue(mockEmailWithDriveItem);

      const mockPreviewUrl = "https://example.com/confidential-preview";
      testAdapter.getPreviewUrl = vi.fn().mockResolvedValue(mockPreviewUrl);

      const result = await fetchPreview(testAdapter, confidentialEmail);

      expect(result).toBe(mockPreviewUrl);

      expect(testAdapter.getEmailWithDriveItem).toHaveBeenCalledWith(
        mockSite.id,
        mockLists[1].id, // This should be the confidential list
        confidentialEmail.id
      );
    });

    it("should throw an error when list is not found", async () => {
      const emailWithoutDriveItemID = { ...mockEmail, driveItemID: undefined };

      const mockSite = { id: "site123" };
      testAdapter.getSiteFromSelectedEmail = vi
        .fn()
        .mockResolvedValue(mockSite);

      testAdapter.getListsFromSite = vi.fn().mockResolvedValue([]);

      const consoleErrorSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      const result = await fetchPreview(testAdapter, emailWithoutDriveItemID);
      expect(result).toBe("");

      expect(consoleErrorSpy).toHaveBeenCalled();

      consoleErrorSpy.mockRestore();
    });

    it("should handle errors in getPreviewUrl gracefully", async () => {
      testAdapter.getPreviewUrl = vi
        .fn()
        .mockRejectedValue(new Error("Preview URL error"));

      const consoleErrorSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      const result = await fetchPreview(testAdapter, mockEmail);

      expect(result).toBe("");

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Error fetching selected emails preview: ",
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });
  });
});
