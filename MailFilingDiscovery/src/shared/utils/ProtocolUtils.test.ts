import { describe, it, expect } from "vitest";
import { ensureProtocol } from "./ProtocolUtils";

describe("ensureProtocol", () => {
  it("should add https:// to a URL without protocol", () => {
    const url = "example.com";
    const result = ensureProtocol(url);
    expect(result).toBe("https://example.com");
  });

  it("should not modify a URL with https:// protocol", () => {
    const url = "https://example.com";
    const result = ensureProtocol(url);
    expect(result).toBe("https://example.com");
  });

  it("should not modify a URL with http:// protocol", () => {
    const url = "http://example.com";
    const result = ensureProtocol(url);
    expect(result).toBe("http://example.com");
  });

  it("should handle URLs with subdomains", () => {
    const url = "subdomain.example.com";
    const result = ensureProtocol(url);
    expect(result).toBe("https://subdomain.example.com");
  });

  it("should handle URLs with paths", () => {
    const url = "example.com/path/to/resource";
    const result = ensureProtocol(url);
    expect(result).toBe("https://example.com/path/to/resource");
  });

  it("should handle URLs with query parameters", () => {
    const url = "example.com/search?q=test&page=1";
    const result = ensureProtocol(url);
    expect(result).toBe("https://example.com/search?q=test&page=1");
  });

  it("should handle URLs with hash fragments", () => {
    const url = "example.com/page#section";
    const result = ensureProtocol(url);
    expect(result).toBe("https://example.com/page#section");
  });

  it("should handle empty strings", () => {
    const url = "";
    const result = ensureProtocol(url);
    expect(result).toBe("https://");
  });
});
