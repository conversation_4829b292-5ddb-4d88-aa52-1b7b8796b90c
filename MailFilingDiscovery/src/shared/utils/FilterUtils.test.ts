import { describe, it, expect, vi, beforeEach } from "vitest";
import { handleFilter, SortDirection } from "./FilterUtils";
import { Filter, Operation } from "./CamlHelpers";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { ListTitles } from "../types/ListTitles";

describe("handleFilter", () => {
  let mockAdapter: TestSharepointAdapter;

  beforeEach(() => {
    mockAdapter = new TestSharepointAdapter();

    mockAdapter.getListDataAsStream = vi.fn();
  });

  const baseFilters: Filter[] = [
    {
      field: "EmailSubject",
      values: ["test"],
      application: Operation.Contains,
    },
  ];

  it("should call getListDataAsStream with correct parameters for ascending sorting", async () => {
    const mockResponse = {
      Row: [
        {
          ID: "1",
          EmailSubject: "Test Email",
          EmailFrom: "<EMAIL>",
          EmailReceivedOn: "2023-07-01",
          EmailTextSummary: "",
          EmailTags: "Client",
          EmailImportant: "Yes",
          FileRef: "/sites/Filed Email Content/TestEmail.eml",
        },
      ],
    };

    vi.mocked(mockAdapter.getListDataAsStream).mockResolvedValue(mockResponse);

    const result = await handleFilter(
      baseFilters,
      "subject",
      SortDirection.Ascending,
      "KD001",
      undefined,
      1,
      100,
      true,
      "example.com",
      false,
      mockAdapter
    );

    expect(mockAdapter.getListDataAsStream).toHaveBeenCalledWith(
      "KD001",
      ListTitles.NonConfidential,
      expect.objectContaining({
        ViewXml: expect.stringContaining(
          '<FieldRef Name="EmailSubject" Ascending="True"/>'
        ),
      })
    );

    expect(result).toEqual({
      emails: [
        {
          id: "1",
          subject: "Test Email",
          sentBy: "<EMAIL>",
          dateFiled: "2023-07-01",
          projectCode: "KD001",
          attachmentCount: undefined,
          fileRef:
            "https://example.com/sites/Filed Email Content/TestEmail.eml",
          emailTextSummary: "",
          emailImportant: "Yes",
          tag: "Client",
          emailConfidential: false,
        },
      ],
      nextLink: undefined,
      page: 1,
      pageSize: 100,
      sortDirection: "ascending",
      sortField: "subject",
    });
  });

  it("should handle descending sorting correctly", async () => {
    const mockResponse = {
      Row: [
        {
          ID: "2",
          EmailSubject: "Confidential Email",
          EmailFrom: "<EMAIL>",
          EmailReceivedOn: "2023-06-01",
          EmailTextSummary: "Summary",
          EmailTags: "Internal",
          EmailImportant: "No",
          FileRef:
            "/sites/Confidential Filed Email Content/ConfidentialEmail.eml",
        },
      ],
    };

    vi.mocked(mockAdapter.getListDataAsStream).mockResolvedValue(mockResponse);

    const result = await handleFilter(
      baseFilters,
      "dateFiled",
      SortDirection.Descending,
      "KD001",
      undefined,
      1,
      100,
      true,
      "example.com",
      true,
      mockAdapter
    );

    expect(mockAdapter.getListDataAsStream).toHaveBeenCalledWith(
      "KD001",
      ListTitles.Confidential,
      expect.objectContaining({
        ViewXml: expect.stringContaining(
          '<FieldRef Name="EmailReceivedOn" Ascending="False"/>'
        ),
      })
    );

    expect(result).toEqual({
      emails: [
        {
          id: "2",
          subject: "Confidential Email",
          sentBy: "<EMAIL>",
          dateFiled: "2023-06-01",
          projectCode: "KD001",
          attachmentCount: undefined,
          fileRef:
            "https://example.com/sites/Confidential Filed Email Content/ConfidentialEmail.eml",
          emailTextSummary: "Summary",
          emailImportant: "No",
          tag: "Internal",
          emailConfidential: true,
        },
      ],
      nextLink: undefined,
      page: 1,
      pageSize: 100,
      sortDirection: "descending",
      sortField: "dateFiled",
    });
  });

  it("should handle no sorting and filters", async () => {
    const mockResponse = {
      Row: [
        {
          ID: "3",
          EmailSubject: "Another Test Email",
          EmailFrom: "<EMAIL>",
          EmailReceivedOn: "2023-07-02",
          EmailTextSummary: "Summary",
          EmailTags: "Project",
          EmailImportant: "Yes",
          FileRef: "/sites/Filed Email Content/AnotherTestEmail.eml",
        },
      ],
    };

    vi.mocked(mockAdapter.getListDataAsStream).mockResolvedValue(mockResponse);

    const result = await handleFilter(
      [],
      undefined,
      SortDirection.Ascending,
      "KD001",
      undefined,
      1,
      50,
      true,
      "example.com",
      false,
      mockAdapter
    );

    expect(mockAdapter.getListDataAsStream).toHaveBeenCalledWith(
      "KD001",
      ListTitles.NonConfidential,
      expect.objectContaining({
        ViewXml: expect.stringContaining('<FieldRef Name="FSObjType" />'),
      })
    );

    expect(result).toEqual({
      emails: [
        {
          id: "3",
          subject: "Another Test Email",
          sentBy: "<EMAIL>",
          dateFiled: "2023-07-02",
          projectCode: "KD001",
          attachmentCount: undefined,
          fileRef:
            "https://example.com/sites/Filed Email Content/AnotherTestEmail.eml",
          emailTextSummary: "Summary",
          emailImportant: "Yes",
          tag: "Project",
          emailConfidential: false,
        },
      ],
      nextLink: undefined,
      page: 1,
      pageSize: 50,
      sortDirection: "ascending",
      sortField: undefined,
    });
  });

  it("should handle error gracefully and return empty results", async () => {
    vi.mocked(mockAdapter.getListDataAsStream).mockRejectedValue(
      new Error("Failed to fetch")
    );

    const result = await handleFilter(
      baseFilters,
      "subject",
      SortDirection.Ascending,
      "KD001",
      undefined,
      1,
      100,
      true,
      "example.com",
      false,
      mockAdapter
    );

    expect(result).toEqual({
      page: 0,
      pageSize: 0,
      nextLink: undefined,
      emails: [],
      sortField: "subject",
      sortDirection: "ascending",
    });
  });
});
