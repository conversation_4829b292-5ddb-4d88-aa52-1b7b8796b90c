import { describe, test, expect } from "vitest";
import { filterGroups } from "./AdminChecker";
import { Group } from "@microsoft/microsoft-graph-types";

describe("filterGroups", () => {
  test("should combine and filter groups correctly", () => {
    const atveroGroups: Group[] = [
      { id: "group1", displayName: "Atvero Mail Admins 1" },
      { id: "group2", displayName: "Atvero Mail Admins 2" },
    ];

    const cmapGroups: Group[] = [
      { id: "group3", displayName: "CMAP Mail Admins 1" },
      { id: "group4", displayName: "CMAP Mail Admins 2" },
    ];

    const result = filterGroups(atveroGroups, cmapGroups);

    expect(result).toEqual(["group1", "group2", "group3", "group4"]);
  });

  test("should handle undefined group IDs", () => {
    const atveroGroups: Group[] = [
      { id: "group1", displayName: "Atvero Mail Admins 1" },
      { displayName: "Atvero Mail Admins 2" } as Group,
    ];

    const cmapGroups: Group[] = [
      { id: "group3", displayName: "CMAP Mail Admins 1" },
      { displayName: "CMAP Mail Admins 2" } as Group,
    ];

    const result = filterGroups(atveroGroups, cmapGroups);

    expect(result).toEqual(["group1", "group3"]);
  });

  test("should handle empty arrays", () => {
    const atveroGroups: Group[] = [];
    const cmapGroups: Group[] = [];

    const result = filterGroups(atveroGroups, cmapGroups);

    expect(result).toEqual([]);
  });

  test("should handle one empty array", () => {
    const atveroGroups: Group[] = [
      { id: "group1", displayName: "Atvero Mail Admins 1" },
    ];
    const cmapGroups: Group[] = [];

    const result = filterGroups(atveroGroups, cmapGroups);

    expect(result).toEqual(["group1"]);
  });
});
