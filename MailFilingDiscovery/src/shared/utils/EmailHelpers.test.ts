import { describe, test, expect, vi, beforeEach } from "vitest";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { SortDirection } from "../utils/FilterUtils";
import { Filter, Operation } from "../utils/CamlHelpers";
import { Hubsite } from "../types/Hubsite";
import { ListTitles } from "../types/ListTitles";
import * as FilterUtils from "../utils/FilterUtils";
import * as GetEmailsFromSearchHelpers from "./GetEmailsFromSearchHelpers";
import * as SiteHelpers from "../utils/SiteHelpers";

// We'll directly test the actual implementation
import {
  getEmails,
  getEmailsFromSearch,
  getEmailsByProject,
  fetchEmailsWithAdapter,
} from "./EmailHelpers";

// Mock dependencies
vi.mock("../utils/FilterUtils", () => ({
  SortDirection: { Ascending: "ascending", Descending: "descending" },
  handleFilter: vi.fn().mockResolvedValue({
    page: 0,
    pageSize: 10,
    nextLink: "next-link-token",
    emails: [
      {
        id: "email-1",
        driveItemID: "driveItem1",
        subject: "Test Subject 1",
        sentBy: "<EMAIL>",
        dateFiled: "2023-01-15T10:30:00Z",
        driveID: "drive1",
        projectCode: "PROJECT1",
        tag: "Important",
        emailImportant: "true",
        attachmentCount: "2",
        emailConfidential: false,
      },
    ],
    sortField: "EmailReceivedOn",
    sortDirection: "descending",
  }),
}));

vi.mock("./GetEmailsFromSearchHelpers", () => ({
  buildEmailPaths: vi.fn().mockReturnValue(["path1", "path2"]),
  extractEmailsFromSearchResults: vi.fn().mockReturnValue([
    {
      id: "email-1",
      driveItemID: "driveItem1",
      subject: "Test Subject 1",
      sentBy: "<EMAIL>",
      dateFiled: "2023-01-15T10:30:00Z",
      driveID: "drive1",
      projectCode: "PROJECT1",
      tag: "Important",
      emailImportant: "true",
      attachmentCount: "2",
      emailConfidential: false,
    },
    {
      id: "email-2",
      driveItemID: "driveItem2",
      subject: "Test Subject 2",
      sentBy: "<EMAIL>",
      dateFiled: "2023-01-16T11:30:00Z",
      driveID: "drive2",
      projectCode: "PROJECT2",
      tag: "Urgent",
      emailImportant: "false",
      attachmentCount: "0",
      emailConfidential: false,
    },
  ]),
  setFilterQuery: vi.fn().mockReturnValue("filterQuery"),
}));

vi.mock("../utils/SiteHelpers", () => ({
  extractSiteId: vi.fn().mockReturnValue("123"),
}));

describe("EmailHelpers", () => {
  let adapter: TestSharepointAdapter;

  beforeEach(() => {
    vi.clearAllMocks();

    // Spy on console methods
    vi.spyOn(console, "error").mockImplementation(() => {});
    vi.spyOn(console, "warn").mockImplementation(() => {});

    adapter = new TestSharepointAdapter();

    // Mock adapter methods
    adapter.getSiteById = vi.fn().mockResolvedValue({ id: "site-1" });
    adapter.getSiteDrives = vi.fn().mockResolvedValue([
      { id: "drive-1", name: ListTitles.NonConfidential },
      { id: "drive-2", name: ListTitles.Confidential },
    ]);
    adapter.getSite = vi.fn().mockResolvedValue({ id: "hubsite-1" });
    adapter.getSearchResults = vi.fn().mockResolvedValue([
      {
        hitsContainers: [
          {
            hits: [
              {
                hitId: "email1",
                resource: {
                  id: "driveItem1",
                  listItem: {
                    fields: {
                      listitemid: "1",
                      refinableString31: "Test Subject 1",
                      refinableString35: "<EMAIL>",
                      refinableDate00: "2023-01-15T10:30:00Z",
                      driveId: "drive1",
                      sitename: "sites/PROJECT1",
                      refinableString51: "Important",
                      refinableString52: "true",
                      refinableDecimal09: "2",
                    },
                  },
                },
              },
            ],
            moreResultsAvailable: false,
            total: 2,
          },
        ],
      },
    ]);
  });

  // Simplified tests focusing on inputs and outputs
  describe("getEmails", () => {
    test("calls getEmailsFromSearch with correct parameters when search term is provided", async () => {
      // Step 1: Create the spy
      const getEmailsFromSearchSpy = vi.spyOn(
        GetEmailsFromSearchHelpers,
        "extractEmailsFromSearchResults"
      );

      // Step 2: Call the function
      await getEmails(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm", // WITH search term
        0,
        10,
        "https://example.sharepoint.com/sites/hubsite",
        undefined,
        "RefinableDate00",
        "EmailReceivedOn",
        SortDirection.Descending,
        [],
        true,
        ListTitles.NonConfidential
      );

      // Step 3: Verify expected behavior
      expect(adapter.getSite).toHaveBeenCalled();
      expect(getEmailsFromSearchSpy).toHaveBeenCalled();
    });

    test("calls handleFilter when single project is selected and no search term", async () => {
      // Step 1: Create the spy
      const handleFilterSpy = vi.spyOn(FilterUtils, "handleFilter");

      // Step 2: Call the function
      await getEmails(
        adapter,
        ["PROJECT1"], // Single project
        "example.sharepoint.com",
        "", // NO search term
        0,
        10,
        "https://example.sharepoint.com/sites/hubsite",
        undefined,
        "RefinableDate00",
        "EmailReceivedOn",
        SortDirection.Descending,
        [],
        true,
        ListTitles.NonConfidential
      );

      // Step 3: Verify expected behavior
      expect(adapter.getSiteById).toHaveBeenCalled();
      expect(handleFilterSpy).toHaveBeenCalled();
    });

    test("returns undefined if conditions are not met", async () => {
      const result = await getEmails(
        adapter,
        ["PROJECT1", "PROJECT2"], // Multiple projects
        "example.sharepoint.com",
        "", // NO search term
        0,
        10,
        "https://example.sharepoint.com/sites/hubsite",
        undefined,
        "RefinableDate00",
        "EmailReceivedOn",
        SortDirection.Descending,
        [],
        true,
        ListTitles.NonConfidential
      );

      expect(result).toBeUndefined();
    });

    test("handles errors and returns undefined", async () => {
      // Force an error
      adapter.getSite = vi.fn().mockRejectedValueOnce(new Error("Test error"));

      const result = await getEmails(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        "https://example.sharepoint.com/sites/hubsite",
        undefined,
        "RefinableDate00",
        "EmailReceivedOn",
        SortDirection.Descending,
        [],
        true,
        ListTitles.NonConfidential
      );

      expect(result).toBeUndefined();
      expect(console.error).toHaveBeenCalledWith(
        "Error fetching emails from getEmails: ",
        expect.any(Error)
      );
    });
  });

  describe("getEmailsFromSearch", () => {
    test("calls getRootSite when hubsite URL path is '/'", async () => {
      adapter.getRootSite = vi.fn().mockResolvedValue({
        id: "root-site-1",
        name: "root",
        displayName: "Root Site",
        url: "https://example.sharepoint.com",
        path: "/",
      });

      await getEmailsFromSearch(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        "https://example.sharepoint.com/",
        "RefinableDate00",
        SortDirection.Descending,
        []
      );

      expect(adapter.getRootSite).toHaveBeenCalled();
      expect(adapter.getSite).not.toHaveBeenCalled();

      expect(SiteHelpers.extractSiteId).toHaveBeenCalledWith("root-site-1");

      expect(adapter.getSearchResults).toHaveBeenCalled();
      expect(
        GetEmailsFromSearchHelpers.extractEmailsFromSearchResults
      ).toHaveBeenCalled();
    });

    test("correctly calls adapter methods", async () => {
      const result = await getEmailsFromSearch(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        "https://example.sharepoint.com/sites/hubsite",
        "RefinableDate00",
        SortDirection.Descending,
        []
      );

      expect(adapter.getSite).toHaveBeenCalledWith(
        "example.sharepoint.com",
        "/sites/hubsite"
      );
      expect(GetEmailsFromSearchHelpers.buildEmailPaths).toHaveBeenCalled();
      expect(GetEmailsFromSearchHelpers.setFilterQuery).toHaveBeenCalled();
      expect(SiteHelpers.extractSiteId).toHaveBeenCalledWith("hubsite-1");
      expect(adapter.getSearchResults).toHaveBeenCalled();
      expect(
        GetEmailsFromSearchHelpers.extractEmailsFromSearchResults
      ).toHaveBeenCalled();

      expect(result).toBeDefined();
      expect(result.emails).toHaveLength(2);
    });

    test("handles confidential filter correctly", async () => {
      const filters: Filter[] = [
        {
          field: "EmailConfidential",
          values: ["true"],
          application: Operation.Equals,
        },
      ];

      await getEmailsFromSearch(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        "https://example.sharepoint.com/sites/hubsite",
        "RefinableDate00",
        SortDirection.Descending,
        filters
      );

      expect(GetEmailsFromSearchHelpers.buildEmailPaths).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Array),
        true // isConfidentialFilterApplied should be true
      );
    });

    test("uses correct sort properties", async () => {
      await getEmailsFromSearch(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        "https://example.sharepoint.com/sites/hubsite",
        "EmailSubject", // Using a specific sort column
        SortDirection.Ascending, // Using ascending sort
        []
      );

      expect(adapter.getSearchResults).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Array),
        expect.any(Array),
        expect.any(Number),
        expect.any(Number),
        [
          {
            name: "EmailSubject",
            isDescending: false, // Should be false for ascending
          },
        ]
      );
    });

    test("handles search with no sort column", async () => {
      await getEmailsFromSearch(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        "https://example.sharepoint.com/sites/hubsite",
        undefined, // No sort column
        SortDirection.Descending,
        []
      );

      expect(adapter.getSearchResults).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Array),
        expect.any(Array),
        expect.any(Number),
        expect.any(Number),
        [] // Empty sort properties
      );
    });

    test("determines total results correctly", async () => {
      adapter.getSearchResults = vi.fn().mockResolvedValue([
        {
          hitsContainers: [
            {
              hits: [{ hitId: "email1" }],
              moreResultsAvailable: true,
              total: 50, // Specific total
            },
          ],
        },
      ]);

      const result = await getEmailsFromSearch(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        "https://example.sharepoint.com/sites/hubsite",
        undefined,
        SortDirection.Descending,
        []
      );

      expect(result.total).toBe(50);
      expect(result.moreResultsAvailable).toBe(true);
    });

    test("handles protocol prefix in URLs", async () => {
      await getEmailsFromSearch(
        adapter,
        ["PROJECT1"],
        "http://example.sharepoint.com", // With protocol
        "searchTerm",
        0,
        10,
        "http://example.sharepoint.com/sites/hubsite", // With protocol
        undefined,
        SortDirection.Descending,
        []
      );

      expect(adapter.getSite).toHaveBeenCalledWith(
        "example.sharepoint.com", // Protocol should be removed
        "/sites/hubsite"
      );
    });
  });

  describe("getEmailsByProject", () => {
    test("fetches site and drives correctly", async () => {
      await getEmailsByProject(
        adapter,
        "PROJECT1",
        "example.sharepoint.com",
        [],
        "EmailReceivedOn",
        SortDirection.Descending,
        undefined,
        0,
        10,
        true,
        ListTitles.NonConfidential
      );

      expect(adapter.getSiteById).toHaveBeenCalledWith(
        "example.sharepoint.com",
        "/sites/PROJECT1"
      );
      expect(adapter.getSiteDrives).toHaveBeenCalledWith("site-1");
      expect(FilterUtils.handleFilter).toHaveBeenCalled();
    });

    test("handles confidential filter correctly", async () => {
      const filters: Filter[] = [
        {
          field: "EmailConfidential",
          values: ["true"],
          application: Operation.Equals,
        },
      ];

      await getEmailsByProject(
        adapter,
        "PROJECT1",
        "example.sharepoint.com",
        filters,
        "EmailReceivedOn",
        SortDirection.Descending,
        undefined,
        0,
        10,
        true,
        ListTitles.Confidential
      );

      // Use a looser expectation pattern that just verifies the function was called
      // with the right parameters in the right positions, without being strict about array structure
      expect(FilterUtils.handleFilter).toHaveBeenCalledWith(
        expect.anything(), // filters
        expect.anything(), // sortColumn
        expect.anything(), // sortDirection
        "PROJECT1", // projectCode
        undefined, // nextLink
        0, // page
        10, // pageSize
        true, // resetPaging
        "example.sharepoint.com", // shareRootUrl
        true, // isConfidentialFilterApplied
        adapter // adapter
      );
    });

    test("handles missing drive correctly", async () => {
      adapter.getSiteDrives = vi.fn().mockResolvedValue([
        // No matching drive
        { id: "drive-3", name: "Other Drive" },
      ]);

      const result = await getEmailsByProject(
        adapter,
        "PROJECT1",
        "example.sharepoint.com",
        [],
        "EmailReceivedOn",
        SortDirection.Descending,
        undefined,
        0,
        10,
        true,
        ListTitles.NonConfidential
      );

      expect(console.warn).toHaveBeenCalledWith(
        "Drive with name Filed Email Content not found."
      );
      expect(result.emails).toHaveLength(0);
    });

    test("handles errors correctly", async () => {
      adapter.getSiteById = vi
        .fn()
        .mockRejectedValue(new Error("Site not found"));

      const result = await getEmailsByProject(
        adapter,
        "PROJECT1",
        "example.sharepoint.com",
        [],
        "EmailReceivedOn",
        SortDirection.Descending,
        undefined,
        0,
        10,
        true,
        ListTitles.NonConfidential
      );

      expect(console.error).toHaveBeenCalledWith(
        "Error fetching emails by project: ",
        expect.any(Error)
      );
      expect(result.emails).toHaveLength(0);
    });

    test("updates response with drive information", async () => {
      // Force empty email response to verify transformation
      vi.mocked(FilterUtils.handleFilter).mockResolvedValueOnce({
        page: 0,
        pageSize: 10,
        nextLink: "next-link-token",
        emails: [
          {
            projectCode: "",
            id: "email-1",
            subject: "Test Subject 1",
            sentBy: "<EMAIL>",
            dateFiled: "2023-01-15T10:30:00Z",
          },
        ],
        sortField: "EmailReceivedOn",
        sortDirection: SortDirection.Descending,
      });

      const result = await getEmailsByProject(
        adapter,
        "PROJECT1",
        "example.sharepoint.com",
        [],
        "EmailReceivedOn",
        SortDirection.Descending,
        undefined,
        0,
        10,
        true,
        ListTitles.NonConfidential
      );

      expect(result.emails[0].driveID).toBe("drive-1");
      expect(result.emails[0].projectCode).toBe("PROJECT1");
    });
  });

  describe("fetchEmailsWithAdapter", () => {
    test("calls getEmails with correct parameters", async () => {
      const hubsite: Hubsite = {
        id: "",
        name: "hubsite-1",
        displayName: "Test Hubsite",
        url: "/sites/hubsite",
        path: "hubsite",
      };

      // We'll monitor what getSite and other adapter methods are called
      await fetchEmailsWithAdapter(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        hubsite,
        undefined,
        "RefinableDate00",
        "EmailReceivedOn",
        SortDirection.Descending,
        [],
        true,
        ListTitles.NonConfidential
      );

      // We expect getSite to be called, which happens inside getEmails
      expect(adapter.getSite).toHaveBeenCalled();
    });

    test("handles errors correctly", async () => {
      // Make getSite throw an error, which will happen inside getEmails
      adapter.getSite = vi.fn().mockRejectedValue(new Error("Test error"));

      const hubsite: Hubsite = {
        id: "",
        name: "hubsite-1",
        displayName: "Test Hubsite",
        url: "/sites/hubsite",
        path: "hubsite",
      };

      const result = await fetchEmailsWithAdapter(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        hubsite,
        undefined,
        "RefinableDate00",
        "EmailReceivedOn",
        SortDirection.Descending,
        [],
        true,
        ListTitles.NonConfidential
      );

      expect(console.error).toHaveBeenCalledWith(
        "Error fetching emails from getEmails: ",
        expect.any(Error)
      );
      expect(result).toBeUndefined();
    });

    test("returns null if adapter is missing", async () => {
      const hubsite: Hubsite = {
        id: "",
        name: "hubsite-1",
        displayName: "Test Hubsite",
        url: "/sites/hubsite",
        path: "hubsite",
      };

      const result = await fetchEmailsWithAdapter(
        null as any,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        hubsite,
        undefined,
        "RefinableDate00",
        "EmailReceivedOn",
        SortDirection.Descending,
        [],
        true,
        ListTitles.NonConfidential
      );

      expect(result).toBeNull();
    });

    test("returns null if hubsite is missing", async () => {
      const result = await fetchEmailsWithAdapter(
        adapter,
        ["PROJECT1"],
        "example.sharepoint.com",
        "searchTerm",
        0,
        10,
        null as any,
        undefined,
        "RefinableDate00",
        "EmailReceivedOn",
        SortDirection.Descending,
        [],
        true,
        ListTitles.NonConfidential
      );

      expect(result).toBeNull();
    });
  });
});
