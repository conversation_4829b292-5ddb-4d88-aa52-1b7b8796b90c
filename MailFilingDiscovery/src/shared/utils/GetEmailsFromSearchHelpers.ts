import "@pnp/graph/search";
import {
  SearchResponse,
  SearchHitsContainer,
  SearchHit,
  Entity,
} from "@microsoft/microsoft-graph-types";
import { Email } from "../types/Email";
import { format } from "date-fns";
import { cs } from "date-fns/locale";
import { Operation, Filter } from "../types/Filter";
import { ListTitles } from "../types/ListTitles";

interface ExtendedSearchHitResource extends Entity {
  listItem: {
    fields: {
      listitemid: string;
      refinableString31: string;
      refinableString35: string;
      refinableDate00: string;
      driveId: string;
      sitename: string;
      refinableString51: string;
      refinableString52: string;
      refinableDecimal09: string;
    };
  };
  webUrl: string;
  id: string;
}

export const extractProjectCode = (siteName: string): string => {
  return siteName.split("/").pop() ?? "";
};

export const formatDateForQuery = (date: string): string => {
  return format(new Date(date), "yyyy-MM-dd'T'HH:mm:ss'Z'", { locale: cs });
};

export const mapFieldToRefinableString = (field: string): string => {
  switch (field) {
    case "EmailReceived":
      return "RefinableDate00";
    case "EmailSubject":
      return "RefinableString31";
    case "EmailTo":
      return "RefinableString32";
    case "EmailCC":
      return "RefinableString33";
    case "EmailFrom":
      return "RefinableString35";
    case "AttachmentCount":
      return "RefinableDecimal09";
    case "EmailTags":
      return "RefinableString51";
    case "EmailImportant":
      return "RefinableString52";
    default:
      return field;
  }
};

export const setFilterQuery = (filters: Filter[]): string => {
  return filters
    .filter((filter) => filter.values.some((value) => value !== ""))
    .map((filter) => {
      const field = mapFieldToRefinableString(filter.field);
      const filterValues = filter.values
        .filter((value) => value !== "")
        .map((value) => {
          switch (filter.application) {
            case Operation.Equals:
              return `${field}:${value}`;
            case Operation.Contains:
              return `${field}:${value}`;
            case Operation.Boolean:
              return `${field} eq ${value}`;
            case Operation.NotBoolean:
              return `${field} neq ${value}`;
            case Operation.NotBooleanOrNull:
              return `(${field} neq ${value} or ${field} eq null)`;
            case Operation.IsNull:
              return `${field} eq null`;
            case Operation.GreaterThanDateTime:
              return `${field}>=${formatDateForQuery(value)}`;
            case Operation.LessThanDateTime:
              return `${field}<=${formatDateForQuery(value)}`;
            case Operation.GreaterThan:
              return `${field}>0`;
            default:
              return "";
          }
        })
        .filter((query) => query !== "");

      return filterValues.length > 1
        ? `(${filterValues.join(" OR ")})`
        : filterValues[0];
    })
    .filter((query) => query !== "")
    .join(" AND ");
};

export const buildEmailPaths = (
  tenantUrl: string,
  sites: string[],
  isConfidentialFilterApplied: boolean | undefined
): string[] => {
  // Ensure the tenant URL has a protocol
  const ensureTenantProtocol = (url: string): string => {
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      return `https://${url}`;
    }
    return url;
  };

  const formattedTenantUrl = ensureTenantProtocol(tenantUrl);

  const buildPath = (site: string = "*") => {
    const baseFolder = isConfidentialFilterApplied
      ? "Confidential%20Filed%20Email%20Content"
      : "Filed%20Email%20Content";
    return `${formattedTenantUrl}/sites/${site}/${baseFolder}`;
  };

  return !sites.length ? [buildPath()] : sites.map((site) => buildPath(site));
};

export const extractEmailsFromSearchResults = (
  results: SearchResponse[]
): Email[] => {
  return results.reduce((acc: Array<Email>, result: SearchResponse) => {
    result.hitsContainers?.forEach((container: SearchHitsContainer) => {
      container.hits?.forEach((hit: SearchHit) => {
        const extendedHit = hit.resource as ExtendedSearchHitResource;
        acc.push({
          id: hit.hitId ?? "",
          subject: extendedHit.listItem.fields.refinableString31 ?? "",
          sentBy: extendedHit.listItem.fields.refinableString35 ?? "",
          dateFiled: extendedHit.listItem.fields.refinableDate00 ?? "",
          tag: extendedHit.listItem.fields.refinableString51 ?? "",
          attachmentCount:
            extendedHit.listItem.fields.refinableDecimal09 ?? "0",
          driveItemID: hit.resource?.id ?? "",
          driveID: extendedHit.listItem.fields.driveId ?? "",
          projectCode: extractProjectCode(extendedHit.listItem.fields.sitename),
          fileRef: extendedHit.webUrl,
          emailTextSummary: hit.summary ?? "",
          emailImportant: extendedHit.listItem.fields.refinableString52 ?? "",
          emailConfidential: extendedHit.webUrl.includes(
            ListTitles.Confidential
          ),
        });
      });
    });
    return acc;
  }, []);
};
