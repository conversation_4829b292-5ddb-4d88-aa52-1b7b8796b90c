export function getFileIconClass(contentType: string): string {
  switch (contentType.toLowerCase()) {
    case "application/pdf":
      return "ms-Icon--PDF";
    case "application/msword":
    case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      return "ms-Icon--WordDocument";
    case "application/vnd.ms-excel":
    case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      return "ms-Icon--ExcelDocument";
    case "application/vnd.ms-powerpoint":
    case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
      return "ms-Icon--PowerPointDocument";
    case "image/jpeg":
    case "image/png":
    case "image/gif":
      return "ms-Icon--FileImage";
    case "application/zip":
    case "application/x-zip-compressed":
      return "ms-Icon--ZipFolder";
    default:
      return "ms-Icon--Document";
  }
}
