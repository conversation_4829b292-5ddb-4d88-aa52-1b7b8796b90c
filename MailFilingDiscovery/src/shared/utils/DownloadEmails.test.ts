import {
  handleDownloadEmail,
  handleDownloadMultipleEmails,
} from "./DownloadEmails";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import J<PERSON>Zip from "jszip";
import { saveAs } from "file-saver";
import { Email } from "../types/Email";
import { parseFileRef } from "./ParseFileRef";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { ensureDriveItemID } from "./AttachmentHelpers";

vi.mock("file-saver", () => ({
  saveAs: vi.fn(),
}));

vi.mock("./ParseFileRef", () => ({
  parseFileRef: vi.fn((_fileRef) => "parsed-filename.eml"),
}));

vi.mock("./AttachmentHelpers", () => ({
  ensureDriveItemID: vi.fn().mockImplementation((_adapter, email) => {
    return Promise.resolve({
      ...email,
      driveItemID: "new-drive-item-id",
    });
  }),
}));

describe("handleDownloadEmail", () => {
  beforeEach(() => {
    global.open = vi.fn();
    URL.createObjectURL = vi.fn(() => "blob:http://example.com/blob");
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should open a new tab with the file reference if the fileRef exists", () => {
    const attachmentResult = {
      fileRef: "http://example.com/file.eml",
      attachments: [],
    };

    handleDownloadEmail(attachmentResult);

    expect(window.open).toHaveBeenCalledWith(
      "http://example.com/file.eml",
      "_blank"
    );
  });

  it("should log an error if no fileRef is available", () => {
    console.error = vi.fn();
    const attachmentResult = { attachments: [] };

    handleDownloadEmail(attachmentResult);

    expect(console.error).toHaveBeenCalledWith(
      "No attachments available for download"
    );
  });

  it("should log an error if fileRef is undefined", () => {
    console.error = vi.fn();
    const attachmentResult = { fileRef: undefined, attachments: [] };

    handleDownloadEmail(attachmentResult);

    expect(console.error).toHaveBeenCalledWith(
      "No attachments available for download"
    );
    expect(window.open).not.toHaveBeenCalled();
  });

  it("should log an error if fileRef is null (cast as any)", () => {
    console.error = vi.fn();
    const attachmentResult = { fileRef: null as any, attachments: [] };

    handleDownloadEmail(attachmentResult);

    expect(console.error).toHaveBeenCalledWith(
      "No attachments available for download"
    );
    expect(window.open).not.toHaveBeenCalled();
  });
});

describe("handleDownloadMultipleEmails", () => {
  let mockAdapter: TestSharepointAdapter;
  let emails: Email[];

  beforeEach(() => {
    mockAdapter = new TestSharepointAdapter();
    mockAdapter.getFileContents = vi
      .fn()
      .mockResolvedValue(new Blob(["Test content"], { type: "text/plain" }));

    emails = [
      {
        id: "email-1",
        subject: "Email 1",
        sentBy: "<EMAIL>",
        dateFiled: "2024-09-06T11:16:32.0000000+00:00",
        projectCode: "Mail",
        fileRef:
          "https://example.sharepoint.com/sites/Mail/2024-09/06-10-Email-1.eml",
        driveID: "test-drive-id",
        driveItemID: "test-drive-item-id",
      },
      {
        id: "email-2",
        subject: "Email 2",
        sentBy: "<EMAIL>",
        dateFiled: "2024-09-06T10:59:07.0000000+00:00",
        projectCode: "Mail",
        fileRef:
          "https://example.sharepoint.com/sites/Mail/2024-09/06-10-Email-2.eml",
        driveID: "test-drive-id",
        driveItemID: "test-drive-item-id",
      },
    ];

    console.error = vi.fn();
    vi.clearAllMocks();
  });

  it("should log an error if no emails are provided", async () => {
    await handleDownloadMultipleEmails(mockAdapter, []);

    expect(console.error).toHaveBeenCalledWith(
      "No emails available for download"
    );
    expect(saveAs).not.toHaveBeenCalled();
  });

  it("should log an error if zip generation fails", async () => {
    vi.spyOn(JSZip.prototype, "generateAsync").mockRejectedValue(
      new Error("Zip generation failed")
    );

    await handleDownloadMultipleEmails(mockAdapter, emails);

    expect(console.error).toHaveBeenCalledWith(
      "Error generating zip file:",
      expect.any(Error)
    );
  });

  it("should fetch the content for each email and add to the zip", async () => {
    vi.spyOn(JSZip.prototype, "generateAsync").mockResolvedValue(
      new Blob(["zip content"], { type: "application/zip" })
    );

    await handleDownloadMultipleEmails(mockAdapter, emails);

    expect(mockAdapter.getFileContents).toHaveBeenCalledTimes(2);
    expect(saveAs).toHaveBeenCalled();
  });

  it("should call parseFileRef to generate filenames", async () => {
    vi.spyOn(JSZip.prototype, "generateAsync").mockResolvedValue(
      new Blob(["zip content"], { type: "application/zip" })
    );

    await handleDownloadMultipleEmails(mockAdapter, emails);

    emails.forEach((email) => {
      expect(parseFileRef).toHaveBeenCalledWith(email.fileRef);
    });

    expect(saveAs).toHaveBeenCalled();
  });

  it("should log an error if no fileRef is found for an email", async () => {
    const faultyEmail = { ...emails[0], fileRef: undefined };

    await handleDownloadMultipleEmails(mockAdapter, [faultyEmail]);

    expect(console.error).toHaveBeenCalledWith(
      `No file reference found for email: ${faultyEmail.id}`
    );
  });

  it("should handle errors when fetching email content", async () => {
    // Simulate the first call failing by returning undefined
    let callCount = 0;
    mockAdapter.getFileContents = vi.fn().mockImplementation((email) => {
      callCount++;
      if (callCount === 1) {
        console.error(`Error fetching email file: ${email.fileRef}`);
        return Promise.resolve(undefined);
      }
      return Promise.resolve(
        new Blob(["Test content"], { type: "text/plain" })
      );
    });

    await handleDownloadMultipleEmails(mockAdapter, emails);

    expect(mockAdapter.getFileContents).toHaveBeenCalledWith(emails[0]);
    expect(console.error).toHaveBeenCalledWith(
      `Error fetching email file: ${emails[0].fileRef}`
    );
  });

  it("should call ensureDriveItemID if email is missing driveItemID", async () => {
    // Create an email without driveItemID
    const emailWithoutDriveItemID = {
      ...emails[0],
      driveItemID: undefined,
    };

    vi.spyOn(JSZip.prototype, "generateAsync").mockResolvedValue(
      new Blob(["zip content"], { type: "application/zip" })
    );

    await handleDownloadMultipleEmails(mockAdapter, [emailWithoutDriveItemID]);

    // Verify ensureDriveItemID was called with correct parameters
    expect(ensureDriveItemID).toHaveBeenCalledWith(
      mockAdapter,
      emailWithoutDriveItemID
    );

    // Verify getFileContents was called with the updated email containing the new driveItemID
    expect(mockAdapter.getFileContents).toHaveBeenCalledWith(
      expect.objectContaining({
        ...emailWithoutDriveItemID,
        driveItemID: "new-drive-item-id",
      })
    );

    // Verify the zip was generated and saved
    expect(saveAs).toHaveBeenCalled();
  });

  it("should log an error if email does not have driveID", async () => {
    const faultyEmail = { ...emails[0], driveID: undefined };

    await handleDownloadMultipleEmails(mockAdapter, [faultyEmail]);

    expect(console.error).toHaveBeenCalledWith(
      `No drive ID found for email: ${faultyEmail.id}`
    );
    expect(mockAdapter.getFileContents).not.toHaveBeenCalledWith(faultyEmail);
  });

  it("should handle undefined file contents", async () => {
    mockAdapter.getFileContents = vi.fn().mockResolvedValueOnce(undefined);

    await handleDownloadMultipleEmails(mockAdapter, emails);

    expect(console.error).toHaveBeenCalledWith(
      `Error fetching email file: ${emails[0].fileRef}`
    );
  });

  it("should use custom filename when provided", async () => {
    const customFilename = "custom-emails.zip";
    vi.spyOn(JSZip.prototype, "generateAsync").mockResolvedValue(
      new Blob(["zip content"], { type: "application/zip" })
    );

    await handleDownloadMultipleEmails(mockAdapter, emails, customFilename);

    expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), customFilename);
  });

  // Add these to your existing "handleDownloadMultipleEmails" describe block

  it("should continue processing emails if one email fails with an exception", async () => {
    // First call throws an error, second call succeeds
    mockAdapter.getFileContents = vi
      .fn()
      .mockImplementationOnce(() => {
        throw new Error("Network error when fetching email");
      })
      .mockResolvedValueOnce(
        new Blob(["Test content"], { type: "text/plain" })
      );

    vi.spyOn(JSZip.prototype, "generateAsync").mockResolvedValue(
      new Blob(["zip content"], { type: "application/zip" })
    );

    await handleDownloadMultipleEmails(mockAdapter, emails);

    // Should have attempted to get file contents for both emails
    expect(mockAdapter.getFileContents).toHaveBeenCalledTimes(2);

    // Should have logged the error for the first email
    expect(console.error).toHaveBeenCalledWith(
      `Error processing email ${emails[0].id}:`,
      expect.any(Error)
    );

    // Should still have created and downloaded the zip
    expect(saveAs).toHaveBeenCalled();
  });

  it("should not attempt to download a zip if all emails fail to process", async () => {
    mockAdapter.getFileContents = vi.fn().mockImplementation(() => {
      throw new Error("Network error when fetching email");
    });

    await handleDownloadMultipleEmails(mockAdapter, emails);

    expect(mockAdapter.getFileContents).toHaveBeenCalledTimes(2);

    expect(console.error).toHaveBeenCalledWith(
      `Error processing email ${emails[0].id}:`,
      expect.any(Error)
    );
    expect(console.error).toHaveBeenCalledWith(
      `Error processing email ${emails[1].id}:`,
      expect.any(Error)
    );

    expect(console.error).toHaveBeenCalledWith(
      "No emails were successfully processed for download"
    );

    expect(saveAs).not.toHaveBeenCalled();
  });

  it("should log the count of successful and failed emails", async () => {
    mockAdapter.getFileContents = vi
      .fn()
      .mockImplementationOnce(() => {
        throw new Error("Network error when fetching email");
      })
      .mockResolvedValueOnce(
        new Blob(["Test content"], { type: "text/plain" })
      );

    console.log = vi.fn();

    vi.spyOn(JSZip.prototype, "generateAsync").mockResolvedValue(
      new Blob(["zip content"], { type: "application/zip" })
    );

    await handleDownloadMultipleEmails(mockAdapter, emails);

    expect(console.log).toHaveBeenCalledWith(
      expect.stringContaining("Zip generated with 1 emails (1 failed)")
    );

    expect(saveAs).toHaveBeenCalled();
  });
});
