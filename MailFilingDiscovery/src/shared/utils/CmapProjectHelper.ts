import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";
import { Hubsite } from "../types/Hubsite";
import { Project } from "../types/Project";

export interface ProjectWithHubsite {
  project: Project;
  hubsite: Hubsite;
}

export const getIntegrationProject = async (
  sharepointAdapter: ISharepointAdapter,
  hubsites: Hubsite[],
  importId: string
): Promise<ProjectWithHubsite | undefined> => {
  if (hubsites.length === 0) {
    console.warn("No hubsites provided.");
    return undefined;
  }

  if (hubsites.length === 1) {
    const project = await sharepointAdapter.getProject(
      "ATVImportedSourceID",
      importId,
      hubsites[0].url
    );
    if (project) {
      return { project, hubsite: hubsites[0] };
    }
    return undefined;
  }

  for (const hubsite of hubsites) {
    try {
      const project = await sharepointAdapter.getProject(
        "ATVImportedSourceID",
        importId,
        hubsite.url
      );
      if (project) {
        return { project, hubsite };
      }
    } catch (error) {
      console.error(
        `Failed to get project from hubsite ${hubsite.url}:`,
        error
      );
    }
  }

  return undefined;
};
