import { describe, it, expect } from "vitest";
import { clampText } from "./ClampText";

describe("clampText", () => {
  it("should return the same text if the length is less than or equal to maxLength", () => {
    const text = "Short text";
    const maxLength = 20;
    const result = clampText(text, maxLength);
    expect(result).toBe(text);
  });

  it("should return clamped text with ellipsis if the length exceeds maxLength", () => {
    const text = "This is a very long text that needs to be clamped.";
    const maxLength = 20;
    const expected = "This is a very long ...";
    const result = clampText(text, maxLength);
    expect(result).toBe(expected);
  });

  it("should handle empty text", () => {
    const text = "";
    const maxLength = 10;
    const result = clampText(text, maxLength);
    expect(result).toBe(text);
  });

  it("should handle maxLength of zero", () => {
    const text = "Some text";
    const maxLength = 0;
    const expected = "...";
    const result = clampText(text, maxLength);
    expect(result).toBe(expected);
  });

  it("should handle maxLength less than zero", () => {
    const text = "Negative length";
    const maxLength = -5;
    const expected = "...";
    const result = clampText(text, maxLength);
    expect(result).toBe(expected);
  });

  it("should not crash if text is undefined", () => {
    const maxLength = 20;
    // @ts-ignore
    const result = clampText(undefined, maxLength);
    expect(result).toBe("");
  });
});
