import { describe, it, expect, vi } from "vitest";
import {
  base64ToArrayBuffer,
  handleAttachmentAction,
} from "./AttachmentPreviewHelpers";
import { saveAs } from "file-saver";

describe("base64ToArrayBuffer", () => {
  it("converts a base64 string to a Uint8Array", () => {
    const base64String = "SGVsbG8gd29ybGQ="; // Base64 for 'Hello world'

    const expectedArray = new Uint8Array([
      72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100,
    ]);

    const result = base64ToArrayBuffer(base64String);

    expect(result).toEqual(expectedArray);
  });

  it("returns an empty Uint8Array for an empty base64 string", () => {
    const result = base64ToArrayBuffer("");
    expect(result).toEqual(new Uint8Array());
  });

  it("handles incorrect base64 strings gracefully", () => {
    const invalidBase64 = "!!invalidBase64!!";
    expect(() => base64ToArrayBuffer(invalidBase64)).toThrowError();
  });
});

vi.mock("file-saver", () => ({
  saveAs: vi.fn(),
}));

describe("handleAttachmentAction", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Mock the global URL methods
    global.URL.createObjectURL = vi.fn(() => "blob:http://localhost");
    global.URL.revokeObjectURL = vi.fn();

    // Mock setTimeout behavior
    vi.useFakeTimers();

    vi.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  const mockAttachmentUint8Array = {
    filename: "test.pdf",
    size: 1024,
    contentType: "application/pdf",
    content: new Uint8Array(8),
  };

  const mockAttachmentBase64 = {
    filename: "image.png",
    size: 2048,
    contentType: "image/png",
    content: "iVBORw0KGgoAAAANSUhEUgAAAAUA",
  };

  it('downloads attachment when action is "download" and content is Uint8Array', () => {
    handleAttachmentAction(mockAttachmentUint8Array, "download");

    expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), "test.pdf");
    expect(URL.createObjectURL).toHaveBeenCalled();

    // Force timers to complete for URL.revokeObjectURL
    vi.runAllTimers();
    expect(URL.revokeObjectURL).toHaveBeenCalled();
  });

  it("downloads attachment with a default filename if filename is missing", () => {
    const mockAttachment = { ...mockAttachmentUint8Array, filename: undefined };
    handleAttachmentAction(mockAttachment, "download");

    expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), "unnamed-attachment");
  });

  it('previews attachment when action is "preview" and content is Uint8Array', () => {
    const mockWindow = {
      document: {
        write: vi.fn(),
        close: vi.fn(),
      },
    };

    // Properly mock window.open
    vi.spyOn(window, "open").mockReturnValue(mockWindow as unknown as Window);

    handleAttachmentAction(mockAttachmentUint8Array, "preview");

    expect(window.open).toHaveBeenCalled();
    expect(mockWindow.document.write).toHaveBeenCalledWith(
      expect.stringContaining("iframe")
    );
    expect(URL.createObjectURL).toHaveBeenCalled();

    // Force timers to complete for URL.revokeObjectURL
    vi.runAllTimers();
    expect(URL.revokeObjectURL).toHaveBeenCalled();
  });

  it('downloads attachment when action is "download" and content is base64', () => {
    // @ts-ignore
    handleAttachmentAction(mockAttachmentBase64, "download");

    expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), "image.png");
    expect(URL.createObjectURL).toHaveBeenCalled();

    // Force timers to complete for URL.revokeObjectURL
    vi.runAllTimers();
    expect(URL.revokeObjectURL).toHaveBeenCalled();
  });

  it('previews attachment when action is "preview" and content is base64', () => {
    const mockWindow = {
      document: {
        write: vi.fn(),
        close: vi.fn(),
      },
    };
    vi.spyOn(window, "open").mockReturnValue(mockWindow as unknown as Window);
    // @ts-ignore
    handleAttachmentAction(mockAttachmentBase64, "preview");

    expect(window.open).toHaveBeenCalled();
    expect(mockWindow.document.write).toHaveBeenCalledWith(
      expect.stringContaining("iframe")
    );
    expect(URL.createObjectURL).toHaveBeenCalled();

    // Force timers to complete for URL.revokeObjectURL
    vi.runAllTimers();
    expect(URL.revokeObjectURL).toHaveBeenCalled();
  });

  it("logs an error when content is undefined", () => {
    const mockAttachment = { ...mockAttachmentUint8Array, content: undefined };
    // @ts-ignore
    handleAttachmentAction(mockAttachment, "download");

    expect(console.error).toHaveBeenCalledWith(
      "Attachment content is undefined"
    );
    expect(saveAs).not.toHaveBeenCalled();
  });

  it("logs an error when content format is unsupported", () => {
    const mockAttachment = { ...mockAttachmentUint8Array, content: {} };
    // @ts-ignore
    handleAttachmentAction(mockAttachment, "download");

    expect(console.error).toHaveBeenCalledWith(
      "Unsupported attachment content format"
    );
    expect(saveAs).not.toHaveBeenCalled();
  });
});
