import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  extractProjectCode,
  formatDateForQuery,
  mapFieldToRefinableString,
  setFilterQuery,
  buildEmailPaths,
  extractEmailsFromSearchResults,
} from "./GetEmailsFromSearchHelpers";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { Operation, Filter } from "../types/Filter";
import { Entity, SearchResponse } from "@microsoft/microsoft-graph-types";
import { EntityType } from "@microsoft/microsoft-graph-types";

interface ExtendedSearchHitResource extends Entity {
  listItem: {
    fields: {
      listitemid: string;
      refinableString31: string;
      refinableString35: string;
      refinableDate00: string;
      driveId: string;
      sitename: string;
      refinableString51: string;
      refinableString52: string;
      refinableDecimal09: string;
    };
  };
  webUrl: string;
  id: string;
}

describe("GetEmailsFromSearchHelpers", () => {
  let testAdapter: TestSharepointAdapter;
  let defaultMockSearchResults: SearchResponse[];

  beforeEach(() => {
    // Create the adapter
    testAdapter = new TestSharepointAdapter();

    // Define default mock search results here in the test file
    defaultMockSearchResults = [
      {
        hitsContainers: [
          {
            hits: [
              {
                hitId: "email1",
                summary: "Test email summary",
                resource: {
                  id: "driveItem1",
                  webUrl:
                    "https://example.sharepoint.com/sites/PROJECT1/Filed%20Email%20Content/email1.msg",
                  listItem: {
                    fields: {
                      listitemid: "1",
                      refinableString31: "Test Subject 1",
                      refinableString35: "<EMAIL>",
                      refinableDate00: "2023-01-15T10:30:00Z",
                      driveId: "drive1",
                      sitename: "sites/PROJECT1",
                      refinableString51: "Important",
                      refinableString52: "true",
                      refinableDecimal09: "2",
                    },
                  },
                } as ExtendedSearchHitResource,
              },
              {
                hitId: "email2",
                summary: "Another test email",
                resource: {
                  id: "driveItem2",
                  webUrl:
                    "https://example.sharepoint.com/sites/PROJECT2/Confidential%20Filed%20Email%20Content/email2.msg",
                  listItem: {
                    fields: {
                      listitemid: "2",
                      refinableString31: "Test Subject 2",
                      refinableString35: "<EMAIL>",
                      refinableDate00: "2023-01-16T11:30:00Z",
                      driveId: "drive2",
                      sitename: "sites/PROJECT2",
                      refinableString51: "Urgent",
                      refinableString52: "false",
                      refinableDecimal09: "0",
                    },
                  },
                } as ExtendedSearchHitResource,
              },
            ],
            moreResultsAvailable: false,
            total: 2,
          },
        ],
      },
    ];
  });

  describe("extractProjectCode", () => {
    it("should extract project code from site name", () => {
      expect(extractProjectCode("https://example.com/sites/PROJECT123")).toBe(
        "PROJECT123"
      );
      expect(extractProjectCode("sites/PROJECT456")).toBe("PROJECT456");
      expect(extractProjectCode("PROJECT789")).toBe("PROJECT789");
    });

    it("should return empty string if no project code can be extracted", () => {
      expect(extractProjectCode("")).toBe("");
    });
  });

  describe("formatDateForQuery", () => {
    it("should format date correctly for query", () => {
      const date = "2023-01-15T10:30:00";
      const formattedDate = formatDateForQuery(date);

      // Format should be YYYY-MM-DDThh:mm:ssZ
      expect(formattedDate).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/);

      // The date parts should match our input
      expect(formattedDate).toContain("2023-01-15");
    });
  });

  describe("mapFieldToRefinableString", () => {
    it("should map known fields to refinable string properties", () => {
      expect(mapFieldToRefinableString("EmailReceived")).toBe(
        "RefinableDate00"
      );
      expect(mapFieldToRefinableString("EmailSubject")).toBe(
        "RefinableString31"
      );
      expect(mapFieldToRefinableString("EmailTo")).toBe("RefinableString32");
      expect(mapFieldToRefinableString("EmailCC")).toBe("RefinableString33");
      expect(mapFieldToRefinableString("EmailFrom")).toBe("RefinableString35");
      expect(mapFieldToRefinableString("AttachmentCount")).toBe(
        "RefinableDecimal09"
      );
      expect(mapFieldToRefinableString("EmailTags")).toBe("RefinableString51");
      expect(mapFieldToRefinableString("EmailImportant")).toBe(
        "RefinableString52"
      );
    });

    it("should return the field unchanged if not in mapping", () => {
      expect(mapFieldToRefinableString("UnknownField")).toBe("UnknownField");
    });
  });

  describe("setFilterQuery", () => {
    it("should create filter query with equals operation", () => {
      const filters: Filter[] = [
        {
          field: "EmailSubject",
          values: ["Test Subject"],
          application: Operation.Equals,
        },
      ];

      const query = setFilterQuery(filters);
      expect(query).toBe("RefinableString31:Test Subject");
    });

    it("should create filter query with contains operation", () => {
      const filters: Filter[] = [
        {
          field: "EmailFrom",
          values: ["<EMAIL>"],
          application: Operation.Contains,
        },
      ];

      const query = setFilterQuery(filters);
      expect(query).toBe("RefinableString35:<EMAIL>");
    });

    it("should create filter query with boolean operation", () => {
      const filters: Filter[] = [
        {
          field: "EmailImportant",
          values: ["true"],
          application: Operation.Boolean,
        },
      ];

      const query = setFilterQuery(filters);
      expect(query).toBe("RefinableString52 eq true");
    });

    it("should create filter query with date range operations", () => {
      const dateFrom = "2023-01-01T00:00:00";
      const dateTo = "2023-01-31T23:59:59";

      const filters: Filter[] = [
        {
          field: "EmailReceived",
          values: [dateFrom],
          application: Operation.GreaterThanDateTime,
        },
        {
          field: "EmailReceived",
          values: [dateTo],
          application: Operation.LessThanDateTime,
        },
      ];

      const query = setFilterQuery(filters);

      // Check that both date conditions are included and joined with AND
      expect(query).toContain("RefinableDate00>=");
      expect(query).toContain("RefinableDate00<=");
      expect(query).toContain(" AND ");
    });

    it("should handle multiple values with OR", () => {
      const filters: Filter[] = [
        {
          field: "EmailSubject",
          values: ["Test", "Important"],
          application: Operation.Contains,
        },
      ];

      const query = setFilterQuery(filters);
      expect(query).toBe(
        "(RefinableString31:Test OR RefinableString31:Important)"
      );
    });

    it("should filter out empty values", () => {
      const filters: Filter[] = [
        {
          field: "EmailSubject",
          values: ["Test", "", "Important", ""],
          application: Operation.Contains,
        },
      ];

      const query = setFilterQuery(filters);
      expect(query).toBe(
        "(RefinableString31:Test OR RefinableString31:Important)"
      );
    });

    it("should combine multiple filters with AND", () => {
      const filters: Filter[] = [
        {
          field: "EmailSubject",
          values: ["Test"],
          application: Operation.Contains,
        },
        {
          field: "EmailFrom",
          values: ["<EMAIL>"],
          application: Operation.Contains,
        },
      ];

      const query = setFilterQuery(filters);
      expect(query).toBe(
        "RefinableString31:Test AND RefinableString35:<EMAIL>"
      );
    });

    it("should handle attachment count greater than zero", () => {
      const filters: Filter[] = [
        {
          field: "AttachmentCount",
          values: ["1"], // Value doesn't matter for GreaterThan
          application: Operation.GreaterThan,
        },
      ];

      const query = setFilterQuery(filters);
      expect(query).toBe("RefinableDecimal09>0");
    });
  });

  describe("buildEmailPaths", () => {
    it("should build paths for non-confidential emails", () => {
      const tenantUrl = "example.sharepoint.com";
      const sites = ["PROJECT1", "PROJECT2"];
      const isConfidential = false;

      const paths = buildEmailPaths(tenantUrl, sites, isConfidential);

      expect(paths).toHaveLength(2);
      expect(paths[0]).toBe(
        "https://example.sharepoint.com/sites/PROJECT1/Filed%20Email%20Content"
      );
      expect(paths[1]).toBe(
        "https://example.sharepoint.com/sites/PROJECT2/Filed%20Email%20Content"
      );
    });

    it("should build paths for confidential emails", () => {
      const tenantUrl = "example.sharepoint.com";
      const sites = ["PROJECT1", "PROJECT2"];
      const isConfidential = true;

      const paths = buildEmailPaths(tenantUrl, sites, isConfidential);

      expect(paths).toHaveLength(2);
      expect(paths[0]).toBe(
        "https://example.sharepoint.com/sites/PROJECT1/Confidential%20Filed%20Email%20Content"
      );
      expect(paths[1]).toBe(
        "https://example.sharepoint.com/sites/PROJECT2/Confidential%20Filed%20Email%20Content"
      );
    });

    it("should handle empty sites array by creating wildcard path", () => {
      const tenantUrl = "example.sharepoint.com";
      const sites: string[] = [];
      const isConfidential = false;

      const paths = buildEmailPaths(tenantUrl, sites, isConfidential);

      expect(paths).toHaveLength(1);
      expect(paths[0]).toBe(
        "https://example.sharepoint.com/sites/*/Filed%20Email%20Content"
      );
    });

    it("should add https:// protocol if missing", () => {
      const tenantUrl = "example.sharepoint.com";
      const sites = ["PROJECT1"];
      const isConfidential = false;

      const paths = buildEmailPaths(tenantUrl, sites, isConfidential);

      expect(paths[0]).toContain("https://");
    });

    it("should not modify protocol if already present", () => {
      const tenantUrl = "http://example.sharepoint.com";
      const sites = ["PROJECT1"];
      const isConfidential = false;

      const paths = buildEmailPaths(tenantUrl, sites, isConfidential);

      expect(paths[0]).toContain("http://");
      expect(paths[0]).not.toContain("https://");
    });
  });

  describe("extractEmailsFromSearchResults", () => {
    it("should extract emails from search results", () => {
      // Create mock search results
      const mockSearchResults: SearchResponse[] = [
        {
          hitsContainers: [
            {
              hits: [
                {
                  hitId: "email1",
                  summary: "Test email summary",
                  resource: {
                    id: "driveItem1",
                    webUrl:
                      "https://example.sharepoint.com/sites/PROJECT1/Filed%20Email%20Content/email1.msg",
                    listItem: {
                      fields: {
                        listitemid: "1",
                        refinableString31: "Test Subject 1",
                        refinableString35: "<EMAIL>",
                        refinableDate00: "2023-01-15T10:30:00Z",
                        driveId: "drive1",
                        sitename: "sites/PROJECT1",
                        refinableString51: "Important",
                        refinableString52: "true",
                        refinableDecimal09: "2",
                      },
                    },
                  } as any,
                },
                {
                  hitId: "email2",
                  summary: "Another test email",
                  resource: {
                    id: "driveItem2",
                    webUrl:
                      "https://example.sharepoint.com/sites/PROJECT2/Confidential%20Filed%20Email%20Content/email2.msg",
                    listItem: {
                      fields: {
                        listitemid: "2",
                        refinableString31: "Test Subject 2",
                        refinableString35: "<EMAIL>",
                        refinableDate00: "2023-01-16T11:30:00Z",
                        driveId: "drive2",
                        sitename: "sites/PROJECT2",
                        refinableString51: "Urgent",
                        refinableString52: "false",
                        refinableDecimal09: "0",
                      },
                    },
                  } as any,
                },
              ],
            },
          ],
        },
      ];

      const emails = extractEmailsFromSearchResults(mockSearchResults);

      expect(emails).toHaveLength(2);

      // Check first email
      expect(emails[0]).toMatchObject({
        id: "email1",
        subject: "Test Subject 1",
        sentBy: "<EMAIL>",
        dateFiled: "2023-01-15T10:30:00Z",
        projectCode: "PROJECT1",
        driveItemID: "driveItem1",
        driveID: "drive1",
        tag: "Important",
        emailImportant: "true",
        attachmentCount: "2",
        emailConfidential: false,
      });

      expect(emails[1]).toMatchObject({
        id: "email2",
        subject: "Test Subject 2",
        sentBy: "<EMAIL>",
        dateFiled: "2023-01-16T11:30:00Z",
        projectCode: "PROJECT2",
        driveItemID: "driveItem2",
        driveID: "drive2",
        tag: "Urgent",
        emailImportant: "false",
        attachmentCount: "0",
        emailConfidential: false,
      });
    });

    it("should handle empty search results", () => {
      const emptyResults: SearchResponse[] = [];
      const emails = extractEmailsFromSearchResults(emptyResults);

      expect(emails).toHaveLength(0);
    });

    it("should handle missing fields gracefully", () => {
      const incompleteResults: SearchResponse[] = [
        {
          hitsContainers: [
            {
              hits: [
                {
                  hitId: "email3",
                  resource: {
                    id: "driveItem3",
                    webUrl:
                      "https://example.sharepoint.com/sites/PROJECT3/Filed%20Email%20Content/email3.msg",
                    listItem: {
                      fields: {
                        sitename: "sites/PROJECT3",
                        // Missing other fields
                      },
                    },
                  },
                },
              ],
            } as any,
          ],
        },
      ];

      const emails = extractEmailsFromSearchResults(incompleteResults);

      expect(emails).toHaveLength(1);
      expect(emails[0]).toMatchObject({
        id: "email3",
        subject: "",
        sentBy: "",
        projectCode: "PROJECT3",
        driveItemID: "driveItem3",
        emailConfidential: false,
      });
    });
  });

  describe("Integration with TestSharepointAdapter", () => {
    it("should work with adapter's getSearchResults method", async () => {
      // Create test filters
      const filters: Filter[] = [
        {
          field: "EmailSubject",
          values: ["Test"],
          application: Operation.Contains,
        },
      ];

      // Build query
      const filterQuery = setFilterQuery(filters);

      // Setup query parameters
      const query = {
        queryString: filterQuery,
        queryTemplate: "{searchTerms}",
      };

      const entityTypes: EntityType[] = ["listItem"];
      const fields = ["*"];

      // Mock the getSearchResults method with default mock results
      vi.spyOn(testAdapter, "getSearchResults").mockResolvedValue(
        defaultMockSearchResults
      );

      // Call adapter's getSearchResults
      const results = await testAdapter.getSearchResults(
        query,
        entityTypes,
        fields,
        0,
        50,
        [{ name: "RefinableDate00", isDescending: true }]
      );

      // Extract emails from results
      const emails = extractEmailsFromSearchResults(results);

      // Verify extraction
      expect(emails).toHaveLength(2);
      expect(emails[0].subject).toBe("Test Subject 1");
      expect(emails[0].projectCode).toBe("PROJECT1");
      expect(emails[1].subject).toBe("Test Subject 2");
      expect(emails[1].projectCode).toBe("PROJECT2");
    });

    it("should handle custom search results", async () => {
      // Setup custom search results
      const customSearchResults: SearchResponse[] = [
        {
          hitsContainers: [
            {
              hits: [
                {
                  hitId: "customEmail",
                  resource: {
                    id: "customDriveItem",
                    webUrl:
                      "https://example.sharepoint.com/sites/CUSTOM/Filed%20Email%20Content/custom.msg",
                    listItem: {
                      fields: {
                        refinableString31: "Custom Subject",
                        refinableString35: "<EMAIL>",
                        refinableDate00: "2023-02-15T10:30:00Z",
                        sitename: "sites/CUSTOM",
                      },
                    },
                  } as any,
                },
              ],
            },
          ],
        },
      ];

      // Mock the getSearchResults method with custom results
      vi.spyOn(testAdapter, "getSearchResults").mockResolvedValue(
        customSearchResults
      );

      // Call the search results method
      const results = await testAdapter.getSearchResults(
        { queryString: "custom query", queryTemplate: "" },
        ["listItem"],
        ["*"],
        0,
        10,
        []
      );

      // Extract and verify
      const emails = extractEmailsFromSearchResults(results);
      expect(emails).toHaveLength(1);
      expect(emails[0].subject).toBe("Custom Subject");
      expect(emails[0].projectCode).toBe("CUSTOM");
    });
  });
});
