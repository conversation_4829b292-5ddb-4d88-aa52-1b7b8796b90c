/**
 * Ensures a URL has a protocol (https:// if none exists)
 * @param url - The URL to check and modify if needed
 * @returns The URL with a protocol
 */
export const ensureProtocol = (url: string | null | undefined): string => {
  // Return empty string if url is null or undefined
  if (url == null) {
    return "";
  }

  // Convert to string if not already a string
  const urlString = String(url);

  if (!urlString.startsWith("http://") && !urlString.startsWith("https://")) {
    return `https://${urlString}`;
  }
  return urlString;
};
