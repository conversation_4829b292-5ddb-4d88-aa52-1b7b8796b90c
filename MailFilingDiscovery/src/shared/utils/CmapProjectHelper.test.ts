import { describe, it, expect, vi } from "vitest";
import { getIntegrationProject } from "./CmapProjectHelper";
import { Hubsite } from "../types/Hubsite";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { Project } from "../types/Project";

describe("getIntegrationProject", () => {
  const importId = "12345";
  const mockProject: Project = {
    code: "TEST123",
    title: "Test Project",
    id: "123",
    favorite: false,
  };

  const hubsites: Hubsite[] = [
    {
      id: "",
      displayName: "Hubsite 1",
      name: "hubsite-1",
      url: "https://example.com/sites/hubsite-1",
      path: "/sites/hubsite-1",
    },
    {
      id: "",
      displayName: "Hubsite 2",
      name: "hubsite-2",
      url: "https://example.com/sites/hubsite-2",
      path: "/sites/hubsite-2",
    },
  ];

  let adapter: TestSharepointAdapter;

  beforeEach(() => {
    adapter = new TestSharepointAdapter();
  });

  it("returns undefined if no hubsites are provided", async () => {
    const result = await getIntegrationProject(adapter, [], importId);
    expect(result).toBeUndefined();
  });

  it("returns undefined if no project is found in a single hubsite", async () => {
    //@ts-ignore
    vi.spyOn(adapter, "getProject").mockResolvedValue(undefined);

    const result = await getIntegrationProject(
      adapter,
      [hubsites[0]],
      importId
    );

    expect(result).toBeUndefined();
    expect(adapter.getProject).toHaveBeenCalledWith(
      "ATVImportedSourceID",
      importId,
      hubsites[0].url
    );
  });

  it("returns the project with hubsite if found in a single hubsite", async () => {
    vi.spyOn(adapter, "getProject").mockResolvedValue(mockProject);

    const result = await getIntegrationProject(
      adapter,
      [hubsites[0]],
      importId
    );

    expect(result).toEqual({
      project: mockProject,
      hubsite: hubsites[0],
    });
    expect(adapter.getProject).toHaveBeenCalledWith(
      "ATVImportedSourceID",
      importId,
      hubsites[0].url
    );
  });

  it("returns the first valid project with hubsite from multiple hubsites", async () => {
    vi.spyOn(adapter, "getProject")
      //@ts-ignore
      .mockResolvedValueOnce(undefined) // First hubsite returns nothing
      .mockResolvedValueOnce(mockProject); // Second hubsite returns a project

    const result = await getIntegrationProject(adapter, hubsites, importId);

    expect(result).toEqual({
      project: mockProject,
      hubsite: hubsites[1],
    });

    expect(adapter.getProject).toHaveBeenCalledTimes(2);
    expect(adapter.getProject).toHaveBeenCalledWith(
      "ATVImportedSourceID",
      importId,
      hubsites[0].url
    );
    expect(adapter.getProject).toHaveBeenCalledWith(
      "ATVImportedSourceID",
      importId,
      hubsites[1].url
    );
  });

  it("returns undefined if no projects are found across multiple hubsites", async () => {
    //@ts-ignore
    vi.spyOn(adapter, "getProject").mockResolvedValue(undefined);

    const result = await getIntegrationProject(adapter, hubsites, importId);

    expect(result).toBeUndefined();
    expect(adapter.getProject).toHaveBeenCalledTimes(2);
  });

  it("continues checking other hubsites if one throws an error", async () => {
    vi.spyOn(adapter, "getProject")
      .mockRejectedValueOnce(new Error("Network Error")) // First hubsite throws an error
      .mockResolvedValueOnce(mockProject); // Second hubsite returns a project

    const result = await getIntegrationProject(adapter, hubsites, importId);

    expect(result).toEqual({
      project: mockProject,
      hubsite: hubsites[1],
    });

    expect(adapter.getProject).toHaveBeenCalledTimes(2);
    expect(adapter.getProject).toHaveBeenCalledWith(
      "ATVImportedSourceID",
      importId,
      hubsites[0].url
    );
    expect(adapter.getProject).toHaveBeenCalledWith(
      "ATVImportedSourceID",
      importId,
      hubsites[1].url
    );
  });
  it("throws an error if getIntegrationProject encounters an unexpected issue", async () => {
    const faultyGetIntegrationProject = async () => {
      throw new Error("Unexpected internal error");
    };

    await expect(faultyGetIntegrationProject()).rejects.toThrow(
      "Unexpected internal error"
    );
  });
});
