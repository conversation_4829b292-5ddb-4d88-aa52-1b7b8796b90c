import { IBackendAdapter } from "../../adapters/IBackendAdapter";
import { Email } from "../types/Email";
import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";
import { ensureDriveItemID } from "./AttachmentHelpers";

export const handleForwardEmail = async (
  email: Email,
  backendAdapter: IBackendAdapter,
  sharepointAdapter?: ISharepointAdapter
): Promise<boolean> => {
  try {
    let newSelectedEmail = email;

    // If we don't have driveItemID, try to get it using the SharePoint adapter
    if (!email.driveItemID && sharepointAdapter) {
      newSelectedEmail = await ensureDriveItemID(sharepointAdapter, email);
    } else if (!email.driveItemID) {
      console.error(
        "Email missing driveItemID and no SharePoint adapter provided"
      );
      return false;
    }

    if (newSelectedEmail.driveID && newSelectedEmail.driveItemID) {
      const res = await backendAdapter.forwardEmail(
        newSelectedEmail.driveID,
        newSelectedEmail.driveItemID
      );

      if (res.error) {
        return false;
      } else {
        return true;
      }
    } else {
      console.error("Item is missing drive item details");
      return false;
    }
  } catch (error) {
    console.error("Forward failed with error", error);
    return false;
  }
};
