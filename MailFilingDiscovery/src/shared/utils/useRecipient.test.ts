import { describe, it, expect, vi, beforeEach } from "vitest";
import { getNameAndEmail, useRecipient } from "./useRecipient";
import { renderHook, act } from "@testing-library/react-hooks";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";

// Access the internal photoCache to clear it between tests
const photoCache = new Map<string, string>();

describe("getNameAndEmail", () => {
  it("should return both name and email when both are present", () => {
    const [name, email] = getNameAndEmail("<PERSON> <<EMAIL>>");
    expect(name).toBe("<PERSON> Doe");
    expect(email).toBe("<EMAIL>");
  });

  it("should return only name when email is not present", () => {
    const [name, email] = getNameAndEmail("John Doe");
    expect(name).toBe("John Doe");
    expect(email).toBe("");
  });

  it("should return only email when name is not present", () => {
    const [name, email] = getNameAndEmail("<EMAIL>");
    expect(name).toBe("<EMAIL>");
    expect(email).toBe("<EMAIL>");
  });

  it("should return the recipient as name and empty email when input is invalid", () => {
    const [name, email] = getNameAndEmail("invalid input");
    expect(name).toBe("invalid input");
    expect(email).toBe("");
  });
});

describe("useRecipient", () => {
  let testAdapter: TestSharepointAdapter;
  let mockBlob: Blob;

  beforeEach(() => {
    // Clear the photo cache before each test
    photoCache.clear();

    // Reset all mocks
    vi.clearAllMocks();

    mockBlob = new Blob([""], { type: "image/jpeg" });

    testAdapter = new TestSharepointAdapter();

    // Mock URL.createObjectURL
    global.URL.createObjectURL = vi.fn().mockReturnValue("mock-url");
    global.URL.revokeObjectURL = vi.fn();
  });

  it("should return initial state without adapter", () => {
    const { result } = renderHook(() =>
      useRecipient(
        "John Doe <<EMAIL>>",
        null as unknown as TestSharepointAdapter
      )
    );

    expect(result.current).toEqual({
      name: "John Doe",
      email: "<EMAIL>",
      img: null,
    });
  });

  it("should fetch and cache photo when adapter is available", async () => {
    testAdapter.getPhotoBlob = vi.fn().mockResolvedValue(mockBlob);

    const { result } = renderHook(() =>
      useRecipient("John Doe <<EMAIL>>", testAdapter)
    );

    await act(async () => {
      await vi.waitFor(() => {
        expect(result.current.img).toBe("mock-url");
      });
    });

    expect(testAdapter.getPhotoBlob).toHaveBeenCalledWith(
      "<EMAIL>"
    );
  });

  it("should use cached photo URL when available", async () => {
    testAdapter.getPhotoBlob = vi.fn().mockResolvedValue(mockBlob);

    // First render to cache the URL
    const { result: result1 } = renderHook(() =>
      useRecipient("John Doe <<EMAIL>>", testAdapter)
    );

    await act(async () => {
      await vi.waitFor(() => {
        expect(result1.current.img).toBe("mock-url");
      });
    });

    vi.clearAllMocks();

    // Second render should use cached URL
    const { result: result2 } = renderHook(() =>
      useRecipient("John Doe <<EMAIL>>", testAdapter)
    );

    expect(result2.current.img).toBe("mock-url");
    expect(testAdapter.getPhotoBlob).not.toHaveBeenCalled();
  });

  it("should handle photo fetch errors", async () => {
    const errorEmail = "<EMAIL>";
    const errorRecipient = `Error Case <${errorEmail}>`;

    testAdapter.getPhotoBlob = vi.fn().mockImplementation((email: string) => {
      if (email === errorEmail) {
        return Promise.reject(new Error("Failed to fetch"));
      }
      return Promise.resolve(mockBlob);
    });

    const { result } = renderHook(() =>
      useRecipient(errorRecipient, testAdapter)
    );

    // Initially the img should be null
    expect(result.current.img).toBe(null);

    // Verify that getPhotoBlob is called with the correct email
    expect(testAdapter.getPhotoBlob).toHaveBeenCalledWith(errorEmail);

    // Wait for the async operation to complete
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
    });

    // After error handling, img should still be null
    expect(result.current.img).toBe(null);
  });
});
