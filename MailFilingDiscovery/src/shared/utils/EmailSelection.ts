import { Email } from "../types/Email";

/**
 * Handles email selection logic for mobile view.
 * @param email - The email object being selected.
 * @param selectedEmails - The current list of selected emails.
 * @param setSelectedEmails - The state setter function to update selected emails.
 * @param setIsModalOpen - The state setter function to open/close the modal in mobile view.
 */
export const handleMobileEmailSelect = (
  email: Email,
  selectedEmails: Email[],
  setSelectedEmails: React.Dispatch<React.SetStateAction<Email[]>>,
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>
) => {
  const isAlreadySelected = selectedEmails.some((e) => e.id === email.id);
  if (isAlreadySelected) {
    setSelectedEmails([]);
    setIsModalOpen(false);
  } else {
    setSelectedEmails([email]);
    setIsModalOpen(true);
  }
};

/**
 * Handles email selection logic for desktop view.
 * @param email - The email object being selected.
 * @param selectedEmails - The current list of selected emails.
 * @param setSelectedEmails - The state setter function to update selected emails.
 */
export const handleDesktopEmailSelect = (
  email: Email,
  setSelectedEmails: React.Dispatch<React.SetStateAction<Email[]>>
) => {
  setSelectedEmails((prevSelectedEmails) => {
    const isAlreadySelected = prevSelectedEmails.some((e) => e.id === email.id);
    if (isAlreadySelected) {
      return prevSelectedEmails.filter((e) => e.id !== email.id);
    } else {
      return [...prevSelectedEmails, email];
    }
  });
};

export const handleSingleDesktopEmailSelect = (
  email: Email,
  setSelectedEmails: React.Dispatch<React.SetStateAction<Email[]>>
) => {
  setSelectedEmails((prevSelectedEmails) => {
    const isAlreadySelected = prevSelectedEmails.some((e) => e.id === email.id);
    if (isAlreadySelected) {
      return [];
    } else {
      return [email];
    }
  });
};
