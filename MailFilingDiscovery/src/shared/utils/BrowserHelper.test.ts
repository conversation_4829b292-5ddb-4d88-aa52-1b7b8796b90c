import { describe, it, expect } from "vitest";
import { getQueryParamFromUrl } from "./BrowserHelper";

describe("getQueryParamFromUrl", () => {
  it("returns a param", () => {
    // Arrange
    const url = "https://localhost:3001/?project=1336393";
    const param = "project";
    const expectedProject = "1336393";

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedProject);
  });

  it("handles no parameter present in url", () => {
    // Arrange
    const url = "https://localhost:3001/";
    const param = "project";
    const expectedProject = undefined;

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedProject);
  });

  it("handles undefined parameter", () => {
    // Arrange
    const url = "https://localhost:3001/?project=123123";
    const param = undefined;
    const expectedProject = undefined;

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedProject);
  });

  it("handles empty parameter value", () => {
    // Arrange
    const url = "https://localhost:3001/?project=";
    const param = "project";
    const expectedProject = undefined;

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedProject);
  });

  it("handles multiple parameters and returns correct value", () => {
    // Arrange
    const url = "https://localhost:3001/?project=1336393&user=JohnDoe";
    const param = "user";
    const expectedUser = "JohnDoe";

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedUser);
  });

  it("handles parameters with special characters", () => {
    // Arrange
    const url = "https://localhost:3001/?query=hello%20world";
    const param = "query";
    const expectedQuery = "hello world";

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedQuery);
  });

  it("handles repeated parameters and returns the first value", () => {
    // Arrange
    const url = "https://localhost:3001/?project=123&project=456";
    const param = "project";
    const expectedProject = "123";

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedProject);
  });

  it("handles url without query string", () => {
    // Arrange
    const url = "https://localhost:3001/some/path";
    const param = "project";
    const expectedProject = undefined;

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedProject);
  });

  it("handles url with hash fragment", () => {
    // Arrange
    const url = "https://localhost:3001/?project=123#section";
    const param = "project";
    const expectedProject = "123";

    const result = getQueryParamFromUrl(url, param);

    expect(result).toEqual(expectedProject);
  });

  it("handles parameter with number as value", () => {
    const url = "https://localhost:3001/?id=42";
    const param = "id";
    const expectedId = "42";

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedId);
  });

  it("handles case sensitivity (should not be case-sensitive)", () => {
    // Arrange
    const url = "https://localhost:3001/?Project=12345";
    const param = "project";
    const expectedProject = undefined;

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedProject);
  });

  it("handles null URL", () => {
    // Arrange
    const url = null as unknown as string;
    const param = "project";

    expect(() => getQueryParamFromUrl(url, param)).toThrowError();
  });

  it("handles empty string as URL", () => {
    // Arrange
    const url = "";
    const param = "project";

    // Assert
    expect(() => getQueryParamFromUrl(url, param)).toThrowError();
  });

  it("handles parameter value with `=` in it", () => {
    // Arrange
    const url = "https://localhost:3001/?token=abc=123";
    const param = "token";
    const expectedToken = "abc=123";

    // Act
    const result = getQueryParamFromUrl(url, param);

    // Assert
    expect(result).toEqual(expectedToken);
  });
});
