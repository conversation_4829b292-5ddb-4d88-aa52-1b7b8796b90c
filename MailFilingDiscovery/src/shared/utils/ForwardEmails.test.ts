import { handleForwardEmail } from "./ForwardEmails";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import { Email } from "../types/Email";
import { IBackendAdapter } from "../../adapters/IBackendAdapter";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { Site, ListItem } from "@microsoft/microsoft-graph-types";

describe("handleForwardEmail", () => {
  let mockSharepointAdapter: TestSharepointAdapter;
  let mockBackendAdapter: IBackendAdapter;

  beforeEach(() => {
    mockSharepointAdapter = new TestSharepointAdapter();
    mockSharepointAdapter.getSiteFromSelectedEmail = vi
      .fn()
      .mockResolvedValue({ id: "site123" } as Site);
    mockSharepointAdapter.getListsFromSite = vi
      .fn()
      .mockResolvedValue([
        { id: "list123", name: "Filed Email Content" } as ListItem,
      ]);
    mockSharepointAdapter.getEmailWithDriveItem = vi.fn().mockResolvedValue({
      fields: {
        id: "1",
        EmailSubject: "Test Subject",
        EmailFrom: "<EMAIL>",
        EmailReceived: "2023-11-12",
        ATVMessageId: "msg123",
        ATVConversationId: "conv123",
      },
      driveItem: {
        id: "item456",
      },
    } as any);

    mockBackendAdapter = {
      getHubsites: vi.fn(),
      forwardEmail: vi.fn().mockReturnValue({}),
      unfileEmail: vi.fn(),
      getSetting: vi.fn(),
      getTags: vi.fn(),
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const email: Email = {
    projectCode: "TEST",
    id: "123",
    subject: "Test Email",
    sentBy: "<EMAIL>",
    dateFiled: "2023-11-15",
  };

  it("should forward email with driveItemID already present", async () => {
    const emailWithDriveItemID = {
      ...email,
      driveItemID: "driveItemId",
      driveID: "driveId",
    };

    const result = await handleForwardEmail(
      emailWithDriveItemID,
      mockBackendAdapter
    );

    expect(result).toBe(true);
    expect(mockBackendAdapter.forwardEmail).toHaveBeenCalledWith(
      "driveId",
      "driveItemId"
    );
  });

  it("should fetch driveItemID using SharePoint adapter if not present", async () => {
    const emailWithoutDriveItemID = {
      ...email,
      driveItemID: undefined,
      driveID: "driveId",
    };

    const result = await handleForwardEmail(
      emailWithoutDriveItemID,
      mockBackendAdapter,
      mockSharepointAdapter
    );

    expect(mockSharepointAdapter.getSiteFromSelectedEmail).toHaveBeenCalledWith(
      emailWithoutDriveItemID
    );
    expect(mockSharepointAdapter.getListsFromSite).toHaveBeenCalledWith(
      "site123"
    );
    expect(mockSharepointAdapter.getEmailWithDriveItem).toHaveBeenCalledWith(
      "site123",
      "list123",
      "123"
    );
    expect(mockBackendAdapter.forwardEmail).toHaveBeenCalledWith(
      "driveId",
      "item456"
    );
    expect(result).toBe(true);
  });

  it("should return false when driveItemID is missing and no SharePoint adapter provided", async () => {
    const emailWithoutDriveItemID = {
      ...email,
      driveItemID: undefined,
    };

    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    const result = await handleForwardEmail(
      emailWithoutDriveItemID,
      mockBackendAdapter
    );

    expect(consoleSpy).toHaveBeenCalledWith(
      "Email missing driveItemID and no SharePoint adapter provided"
    );
    expect(result).toBe(false);
    expect(mockBackendAdapter.forwardEmail).not.toHaveBeenCalled();

    consoleSpy.mockRestore();
  });

  it("should handle errors during forward process", async () => {
    const emailWithDriveItemID = {
      ...email,
      driveItemID: "driveItemId",
      driveID: "driveId",
    };

    mockBackendAdapter.forwardEmail = vi
      .fn()
      .mockReturnValue({ error: "Failed to forward" });
    const result = await handleForwardEmail(
      emailWithDriveItemID,
      mockBackendAdapter
    );

    expect(result).toBe(false);
  });

  it("should handle exceptions during the forward process", async () => {
    const emailWithDriveItemID = {
      ...email,
      driveItemID: "driveItemId",
      driveID: "driveId",
    };

    mockBackendAdapter.forwardEmail = vi
      .fn()
      .mockRejectedValue(new Error("API error"));
    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    const result = await handleForwardEmail(
      emailWithDriveItemID,
      mockBackendAdapter
    );

    expect(consoleSpy).toHaveBeenCalledWith(
      "Forward failed with error",
      expect.any(Error)
    );
    expect(result).toBe(false);

    consoleSpy.mockRestore();
  });
});
