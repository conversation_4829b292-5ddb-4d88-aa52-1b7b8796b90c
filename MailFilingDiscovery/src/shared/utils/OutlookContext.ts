type OutlookSource = "mac" | "web" | "windows" | "unknown"; // What does our Linux userbase look like?

export interface OutlookInfo {
  source: OutlookSource;
  version: string;
  language: string;
}

export function parseOutlookSource(url: string): OutlookInfo {
  const lowerUrl = url.toLowerCase();
  const hostInfo = new URLSearchParams(lowerUrl.split("?")[1]).get(
    "_host_info"
  );

  if (!hostInfo) {
    return { source: "web", version: "", language: "" };
  }

  const [source, version, language] = hostInfo.split("$");

  let parsedSource: OutlookSource = "web";

  if (source === "mac") {
    parsedSource = "mac";
  } else if (source === "win32") {
    parsedSource = "windows";
  }

  return {
    source: parsedSource,
    version,
    language,
  };
}
