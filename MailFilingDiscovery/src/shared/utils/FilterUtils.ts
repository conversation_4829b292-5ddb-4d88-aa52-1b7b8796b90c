import { Filter, createCAMLFilters } from "./CamlHelpers";
import { IRenderListDataParameters } from "@pnp/sp/lists";
import { EmailResponse } from "../types/Email";
import { ListTitles } from "../types/ListTitles";
import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";
import { ensureProtocol } from "./ProtocolUtils";

export enum SortDirection {
  Ascending = "ascending",
  Descending = "descending",
}

export const handleFilter = async (
  filters: Filter[],
  sortColumn: string | undefined,
  sortDirection: SortDirection,
  projectCode: string,
  nextLink: string | undefined,
  page: number,
  pageSize: number,
  resetPaging: boolean,
  shareRootUrl: string,
  isConfidential: boolean,
  adapter: ISharepointAdapter
): Promise<EmailResponse> => {
  // Make sure shareRootUrl has protocol
  const safeShareRootUrl = ensureProtocol(shareRootUrl);

  // Exclude "EmailConfidential" filter from the filters array for ViewXml
  const sanitizedFilters = filters.filter(
    (filter) => filter.field !== "EmailConfidential"
  );

  let whereClause = createCAMLFilters(sanitizedFilters);
  let orderByClause = "";

  if (sortColumn) {
    const ascending = sortDirection === "ascending" ? "True" : "False";
    let fieldRef = sortColumn;

    if (sortColumn === "subject") fieldRef = "EmailSubject";
    else if (sortColumn === "sentBy") fieldRef = "EmailFrom";
    else if (sortColumn === "dateFiled") fieldRef = "EmailReceivedOn";

    orderByClause = `<OrderBy><FieldRef Name="${fieldRef}" Ascending="${ascending}"/></OrderBy>`;
  }

  const excludeFoldersCondition =
    '<Eq><FieldRef Name="FSObjType" /><Value Type="Integer">0</Value></Eq>';

  const combinedWhereClause = whereClause
    ? `<And>${excludeFoldersCondition}${whereClause}</And>`
    : excludeFoldersCondition;

  const viewXml = `<View Scope="RecursiveAll">
    <Query>
      <Where>${combinedWhereClause}</Where>
      ${orderByClause}
    </Query>
    <ViewFields>
      <FieldRef Name="EmailSubject"/>
      <FieldRef Name="EmailFrom"/>
      <FieldRef Name="EmailTo"/>
      <FieldRef Name="EmailCC"/>
      <FieldRef Name="EmailReceivedOn"/>
      <FieldRef Name="EmailTextSummary"/>
      <FieldRef Name="AttachmentCount"/>
      <FieldRef Name="EmailTextSummary"/>
      <FieldRef Name="EmailTags"/>
      <FieldRef Name="EmailImportant"/>
      <FieldRef Name="FileRef"/>
    </ViewFields>
    <RowLimit Paged="TRUE">${pageSize}</RowLimit>
  </View>`;

  const renderListDataParams: IRenderListDataParameters = {
    ViewXml: viewXml,
    Paging: nextLink && !resetPaging ? nextLink : "Paged=TRUE",
  };

  try {
    const listTitle = isConfidential
      ? ListTitles.Confidential
      : ListTitles.NonConfidential;

    const r = await adapter.getListDataAsStream(
      projectCode,
      listTitle,
      renderListDataParams
    );

    return {
      page: page,
      pageSize: pageSize,
      nextLink: r.NextHref ? r.NextHref.slice(1) : undefined,
      sortField: sortColumn,
      sortDirection: sortDirection,
      emails: r.Row.map((item: any) => ({
        id: item.ID,
        subject: item.EmailSubject,
        sentBy: item.EmailFrom,
        dateFiled: item.EmailReceivedOn,
        projectCode: projectCode,
        attachmentCount: item.AttachmentCount,
        fileRef: `${safeShareRootUrl}${item.FileRef}`,
        emailTextSummary: item.EmailTextSummary,
        emailImportant: item.EmailImportant,
        tag: item.EmailTags,
        emailConfidential: item.FileRef.includes(
          "Confidential Filed Email Content"
        ),
      })),
    };
  } catch (error) {
    console.error("Error fetching filtered and sorted data:", error);
  }

  return {
    page: 0,
    pageSize: 0,
    nextLink: undefined,
    emails: [],
    sortField: sortColumn,
    sortDirection: sortDirection,
  };
};
