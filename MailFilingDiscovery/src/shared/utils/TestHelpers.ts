import { vi } from "vitest";

import { IRenderListDataAsStreamResult } from "@pnp/sp/lists";
import { Project } from "../types/Project";
import { Hubsite } from "../types/Hubsite";
import { Tag } from "../types/Tags";
import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { Email } from "../types/Email";
import { ListTitles } from "../types/ListTitles";
import { IBackendAdapter } from "../../adapters/IBackendAdapter";

// Sample data that can be exported and used in tests
export const projects: Project[] = [
  { code: "PRJ1", title: "Project 1", id: "1", favorite: false },
  { code: "PRJ2", title: "Project 2", id: "2", favorite: false },
];

export const hubsites: Hubsite[] = [
  {
    displayName: "Hubsite 1",
    name: "hub1",
    url: "https://demo.sharepoint.com/sites/hub1",
    path: "/sites/hub1",
  },
  {
    displayName: "Hubsite 2",
    name: "hub2",
    url: "https://demo.sharepoint.com//sites/hub2",
    path: "/sites/hub2",
  },
];

export const tags: Tag[] = [
  { Colour: "gray", Name: "General", BackgroundColour: "#000001" },
  { Colour: "red", Name: "Important", BackgroundColour: "#FFFFFF" },
];

// Adapter factory with all mocks
export function createMockAdapter(): {
  sharepointAdapter: ISharepointAdapter;
  backendAdapter: IBackendAdapter;
} {
  const sharepointAdapter: ISharepointAdapter = new TestSharepointAdapter();

  sharepointAdapter.getProjects = vi.fn().mockResolvedValue(projects);

  sharepointAdapter.getAtveroGroups = vi
    .fn()
    .mockResolvedValue(["Atvero Mail Admins"]);

  sharepointAdapter.getCmapGroups = vi
    .fn()
    .mockResolvedValue(["CMAP Mail Admins"]);

  sharepointAdapter.getIsAdmin = vi.fn().mockResolvedValue(false);

  sharepointAdapter.getPreviewUrl = vi
    .fn()
    .mockImplementation(async (email: Email) => {
      return `https://preview.example.com/${email.id}`;
    });

  sharepointAdapter.getSiteById = vi.fn().mockImplementation(async () => {
    return {
      id: "site-id-123",
      name: "Test Site",
      displayName: "Test Site Display Name",
      webUrl: "https://example.sharepoint.com/sites/PRJ1",
    };
  });

  sharepointAdapter.getSiteDrives = vi.fn().mockImplementation(async () => {
    return [
      {
        id: "drive-id-123",
        name: ListTitles.NonConfidential,
        driveType: "documentLibrary",
        webUrl: "https://example.sharepoint.com/sites/PRJ1/FiledEmailContent",
      },
      {
        id: "drive-id-456",
        name: ListTitles.Confidential,
        driveType: "documentLibrary",
        webUrl:
          "https://example.sharepoint.com/sites/PRJ1/ConfidentialFiledEmailContent",
      },
    ];
  });

  sharepointAdapter.getListDataAsStream = vi
    .fn()
    .mockImplementation(async () => {
      const mockEmailRows = [
        {
          ID: "1",
          Title: "Test Email 1",
          EmailSubject: "Test Email 1",
          EmailFrom: "<EMAIL>",
          EmailReceivedOn: "2023-01-01T12:00:00Z",
          FSObjType: "0",
          UniqueId: "email-unique-id-1",
          FileRef: "/sites/PRJ1/FiledEmailContent/Test Email 1.eml",
          FileLeafRef: "Test Email 1.eml",
          EmailAttachmentCount: "2",
          EmailTags: "General",
          DriveItemID: "drive-item-id-1",
        },
        {
          ID: "2",
          Title: "Test Email 2",
          EmailSubject: "Test Email 2",
          EmailFrom: "<EMAIL>",
          EmailReceivedOn: "2023-01-02T12:00:00Z",
          FSObjType: "0",
          UniqueId: "email-unique-id-2",
          FileRef: "/sites/PRJ1/FiledEmailContent/Test Email 2.eml",
          FileLeafRef: "Test Email 2.eml",
          EmailAttachmentCount: "0",
          EmailTags: "Important",
          DriveItemID: "drive-item-id-2",
        },
      ];

      return {
        CurrentFolderSpItemUrl: "",
        FilterLink: "",
        FirstRow: 0,
        FolderPermissions: "",
        ForceNoHierarchy: "",
        HierarchyHasIndention: "",
        LastRow: 1,
        NextHref: undefined,
        Row: mockEmailRows,
        RowLimit: 100,
      } as IRenderListDataAsStreamResult;
    });

  // Required for handling attachments and other email operations
  sharepointAdapter.getSiteFromSelectedEmail = vi.fn().mockResolvedValue({
    id: "site-id-123",
    name: "Test Site",
    displayName: "Test Site Display Name",
    webUrl: "https://example.sharepoint.com/sites/PRJ1",
  });

  sharepointAdapter.getListsFromSite = vi.fn().mockResolvedValue([
    {
      id: "list-id-123",
      name: ListTitles.NonConfidential,
    },
  ]);

  sharepointAdapter.getEmailWithDriveItem = vi.fn().mockResolvedValue({
    id: "email-unique-id-1",
    driveItem: {
      id: "drive-item-id-1",
    },
    fields: {
      id: "email-unique-id-1",
      EmailSubject: "Test Email 1",
      EmailFrom: "<EMAIL>",
      EmailReceived: "2023-01-01T12:00:00Z",
      ATVMessageId: "message-id-123",
      ATVConversationId: "conversation-id-123",
    },
  });

  // Backend adapter mocks
  const backendAdapter: IBackendAdapter = {
    getSetting: vi.fn().mockImplementation(async (setting: string) => {
      if (setting === "useCustomTags") {
        return "false";
      }
      return undefined;
    }),

    getTags: vi.fn().mockResolvedValue({
      tags: tags,
      error: undefined,
    }),

    getHubsites: vi.fn().mockResolvedValue({
      hubsites: hubsites,
    }),

    forwardEmail: vi.fn().mockResolvedValue({}),

    unfileEmail: vi.fn().mockResolvedValue({}),
  };

  return {
    sharepointAdapter,
    backendAdapter,
  };
}
