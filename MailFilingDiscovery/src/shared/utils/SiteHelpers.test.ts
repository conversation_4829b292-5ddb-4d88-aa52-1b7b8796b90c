import { describe, it, expect } from "vitest";
import { extractSiteId, getSitePath } from "./SiteHelpers";

describe("extractSiteId", () => {
  it("should extract the site ID from a valid SharePoint site ID string", () => {
    const input =
      "atverodevs.sharepoint.com,521540af-aecb-4883-9551-9ae1bbdbac98,688d8e59-d011-4b45-a1a6-5dbc51e25dad";
    const expected = "521540af-aecb-4883-9551-9ae1bbdbac98";

    expect(extractSiteId(input)).toBe(expected);
  });

  it("should return undefined when input is undefined", () => {
    expect(extractSiteId(undefined)).toBeUndefined();
  });

  it("should return undefined when input is empty string", () => {
    expect(extractSiteId("")).toBeUndefined();
  });

  it("should return undefined when input has no commas", () => {
    expect(extractSiteId("invalid-input")).toBeUndefined();
  });

  it("should return undefined when input has only one part", () => {
    expect(extractSiteId("domain.sharepoint.com")).toBeUndefined();
  });

  it("should extract the second part even if third part is missing", () => {
    const input = "domain.sharepoint.com,521540af-aecb-4883-9551-9ae1bbdbac98";
    const expected = "521540af-aecb-4883-9551-9ae1bbdbac98";

    expect(extractSiteId(input)).toBe(expected);
  });

  it("should handle whitespace in the input", () => {
    const input =
      " domain.sharepoint.com , 521540af-aecb-4883-9551-9ae1bbdbac98 , 688d8e59-d011-4b45-a1a6-5dbc51e25dad ";
    const expected = " 521540af-aecb-4883-9551-9ae1bbdbac98 ";

    expect(extractSiteId(input)).toBe(expected);
  });
});

describe("getSitePath", () => {
  it("should get the site from a sharepoint url", () => {
    const input = "https://atverodevs.sharepoint.com/sites/MySite";
    const expected = "/sites/MySite";

    expect(getSitePath(input)).toBe(expected);
  });

  it("should get the site from a root sharepoint url", () => {
    const input = "https://atverodevs.sharepoint.com";
    const expected = "/";

    expect(getSitePath(input)).toBe(expected);
  });

  it("should get the site from a root sharepoint url with a trailing slash", () => {
    const input = "https://atverodevs.sharepoint.com/";
    const expected = "/";

    expect(getSitePath(input)).toBe(expected);
  });

  it("should return a sensible value for an invlid url", () => {
    const input = "odevs.sharepoi";
    const expected = undefined;

    expect(getSitePath(input)).toBe(expected);
  });

  it("should return a sensible value for the empty string", () => {
    const input = "";
    const expected = undefined;

    expect(getSitePath(input)).toBe(expected);
  });

  it("should return a sensible value for undefined", () => {
    const input = undefined;
    const expected = undefined;

    expect(getSitePath(input)).toBe(expected);
  });
});
