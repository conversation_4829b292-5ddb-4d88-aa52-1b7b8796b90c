import { describe, it, expect, vi, beforeEach } from "vitest";
import { Email } from "../types/Email";
import {
  ensureDriveItemID,
  handleAttachments,
  determineFileType,
  readFileContent,
  readFileAsync,
  parseEmlFileAttachments,
  parseMsgFileAttachments,
} from "./AttachmentHelpers";
import MsgReader from "@kenjiuno/msgreader";
import { simpleParser } from "mailparser";
import { TestSharepointAdapter } from "../../adapters/TestSharepointAdapter";
import { ListItem, Site } from "@microsoft/microsoft-graph-types";

// Mocking external dependencies
vi.mock("mailparser", () => ({
  simpleParser: vi.fn(),
}));

vi.mock("@kenjiuno/msgreader", () => ({
  default: vi.fn(),
}));

describe("AttachmentHelpers", () => {
  let mockAdapter: TestSharepointAdapter;
  let mockEmail: Email;

  beforeEach(() => {
    vi.resetAllMocks();

    mockAdapter = new TestSharepointAdapter();

    // Mock adapter methods
    mockAdapter.getFileContents = vi
      .fn()
      .mockResolvedValue(new Blob(["test content"]));
    mockAdapter.getSiteFromSelectedEmail = vi
      .fn()
      .mockResolvedValue({ id: "site123" } as Site);
    mockAdapter.getListsFromSite = vi
      .fn()
      .mockResolvedValue([
        { id: "list123", name: "Filed Email Content" } as ListItem,
      ]);
    mockAdapter.getEmailWithDriveItem = vi.fn().mockResolvedValue({
      fields: {
        id: "1",
        EmailSubject: "Test Subject",
        EmailFrom: "<EMAIL>",
        EmailReceived: "2023-11-12",
        ATVMessageId: "msg123",
        ATVConversationId: "conv123",
      },
      driveItem: {
        id: "item456",
      },
    } as any);

    mockEmail = {
      id: "1",
      projectCode: "testProject",
      subject: "Test Subject",
      sentBy: "<EMAIL>",
      dateFiled: "2023-11-12",
      driveID: "drive123",
      driveItemID: "item123",
      fileRef: "test.eml",
      emailConfidential: false,
    };
  });

  describe("ensureDriveItemID", () => {
    it("returns the email if driveItemID is already present", async () => {
      const emailWithDriveItemID = { ...mockEmail, driveItemID: "item123" };
      const result = await ensureDriveItemID(mockAdapter, emailWithDriveItemID);
      expect(result).toEqual(emailWithDriveItemID);
      expect(mockAdapter.getSiteFromSelectedEmail).not.toHaveBeenCalled();
    });

    it("fetches driveItemID if not present", async () => {
      const emailWithoutDriveItemID = { ...mockEmail, driveItemID: undefined };

      const result = await ensureDriveItemID(
        mockAdapter,
        emailWithoutDriveItemID
      );

      expect(mockAdapter.getSiteFromSelectedEmail).toHaveBeenCalledWith(
        emailWithoutDriveItemID
      );
      expect(mockAdapter.getListsFromSite).toHaveBeenCalledWith("site123");
      expect(mockAdapter.getEmailWithDriveItem).toHaveBeenCalledWith(
        "site123",
        "list123",
        "1"
      );
      expect(result.driveItemID).toBe("item456");
      expect(result.subject).toBe("Test Subject");
      expect(result.messageId).toBe("msg123");
      expect(result.conversationId).toBe("conv123");
    });

    it("throws error if list is not found", async () => {
      const emailWithoutDriveItemID = { ...mockEmail, driveItemID: undefined };
      mockAdapter.getListsFromSite = vi.fn().mockResolvedValue([]);

      await expect(
        ensureDriveItemID(mockAdapter, emailWithoutDriveItemID)
      ).rejects.toThrow("List of type 'Filed Email Content' not found");
    });

    it("handles confidential emails correctly", async () => {
      const confidentialEmail = {
        ...mockEmail,
        driveItemID: undefined,
        emailConfidential: true,
      };

      mockAdapter.getListsFromSite = vi.fn().mockResolvedValue([
        {
          id: "list456",
          name: "Confidential Filed Email Content",
        } as ListItem,
      ]);

      await ensureDriveItemID(mockAdapter, confidentialEmail);

      // Check that the correct list was selected
      expect(mockAdapter.getEmailWithDriveItem).toHaveBeenCalledWith(
        "site123",
        "list456",
        "1"
      );
    });
  });

  describe("parseMsgFileAttachments", () => {
    beforeEach(() => {
      vi.resetAllMocks();
    });

    it("skips inline attachments (embedded messages and cid images)", async () => {
      const mockInlineAttachment = {
        fileName: "image1.png",
        contentLength: 1024,
        attachMimeTag: "image/png",
        pidContentId: "abc123",
        innerMsgContent: false,
      };

      const mockMsgReader = {
        getFileData: vi.fn().mockReturnValue({
          attachments: [mockInlineAttachment],
        }),
        getAttachment: vi.fn().mockReturnValue({
          content: Buffer.from("inline content"),
        }),
      };

      vi.mocked(MsgReader).mockImplementation(() => mockMsgReader as any);

      const result = await parseMsgFileAttachments(new ArrayBuffer(8));
      expect(result).toEqual({ attachments: [] });
    });

    it("correctly parses MSG file attachments", async () => {
      const mockAttachment = {
        fileName: "test.txt",
        contentLength: 1024,
        attachMimeTag: "text/plain",
      };

      const mockMsgReader = {
        getFileData: vi.fn().mockReturnValue({
          attachments: [mockAttachment],
        }),
        getAttachment: vi.fn().mockReturnValue({
          content: Buffer.from("test content"),
        }),
      };

      vi.mocked(MsgReader).mockImplementation(() => mockMsgReader as any);

      const result = await parseMsgFileAttachments(new ArrayBuffer(8));

      expect(result).toEqual({
        attachments: [
          {
            filename: "test.txt",
            size: 1024,
            contentType: "text/plain",
            content: Buffer.from("test content"),
          },
        ],
      });
    });

    it("handles MSG files with no attachments", async () => {
      const mockMsgReader = {
        getFileData: vi.fn().mockReturnValue({
          attachments: [],
        }),
        getAttachment: vi.fn(),
      };

      vi.mocked(MsgReader).mockImplementation(() => mockMsgReader as any);

      const result = await parseMsgFileAttachments(new ArrayBuffer(8));

      expect(result).toEqual({
        attachments: [],
      });
    });

    it("throws an error when MSG file parsing fails", async () => {
      const mockMsgReader = {
        getFileData: vi.fn().mockImplementation(() => {
          throw new Error("MSG parsing error");
        }),
      };

      vi.mocked(MsgReader).mockImplementation(() => mockMsgReader as any);

      await expect(parseMsgFileAttachments(new ArrayBuffer(8))).rejects.toThrow(
        "MSG parsing error"
      );
    });
  });

  describe("parseEmlFileAttachments", () => {
    beforeEach(() => {
      vi.resetAllMocks();
    });

    it("filters out inline EML attachments with cid", async () => {
      vi.mocked(simpleParser).mockResolvedValue({
        attachments: [
          {
            filename: "logo.png",
            size: 1234,
            contentType: "image/png",
            contentDisposition: "inline",
            cid: "logo123",
            content: Buffer.from("inline content"),
          },
        ],
      } as any);

      const result = await parseEmlFileAttachments(new ArrayBuffer(8));

      expect(result.attachments).toEqual([]);
    });

    it("correctly parses EML file attachments", async () => {
      const mockAttachment = {
        filename: "test.txt",
        size: 1024,
        contentType: "text/plain",
        content: Buffer.from("test content"),
      };

      vi.mocked(simpleParser).mockResolvedValue({
        attachments: [mockAttachment],
      } as any);

      const result = await parseEmlFileAttachments(new ArrayBuffer(8));

      expect(result).toEqual({
        attachments: [
          {
            filename: "test.txt",
            size: 1024,
            contentType: "text/plain",
            content: Buffer.from("test content"),
          },
        ],
      });
    });

    it("handles EML files with no attachments", async () => {
      vi.mocked(simpleParser).mockResolvedValue({
        attachments: [],
      } as any);

      const result = await parseEmlFileAttachments(new ArrayBuffer(8));

      expect(result).toEqual({
        attachments: [],
      });
    });

    it("throws an error when EML file parsing fails", async () => {
      vi.mocked(simpleParser).mockRejectedValue(new Error("Parsing error"));

      await expect(parseEmlFileAttachments(new ArrayBuffer(8))).rejects.toThrow(
        "Parsing error"
      );
    });
  });

  describe("handleAttachments", () => {
    it("returns empty attachments if fileRef is missing", async () => {
      const emailWithoutFileRef = { ...mockEmail, fileRef: undefined };
      const result = await handleAttachments(emailWithoutFileRef, mockAdapter);
      expect(result).toEqual({ attachments: [] });
    });

    it("handles EML file attachments", async () => {
      vi.mocked(simpleParser).mockResolvedValue({
        attachments: [
          {
            filename: "file1.txt",
            size: 100,
            contentType: "text/plain",
            content: Buffer.from("file content"),
          },
        ],
      } as any);

      const result = await handleAttachments(mockEmail, mockAdapter);
      expect(result.attachments).toHaveLength(1);
      expect(result.attachments[0].filename).toBe("file1.txt");
      expect(mockAdapter.getFileContents).toHaveBeenCalledWith(mockEmail);
    });

    it("handles MSG file attachments", async () => {
      const mockMsgReader = {
        getFileData: vi.fn().mockReturnValue({
          attachments: [
            {
              fileName: "test.msg",
              contentLength: 1024,
              attachMimeTag: "application/octet-stream",
            },
          ],
        }),
        getAttachment: vi
          .fn()
          .mockReturnValue({ content: Buffer.from("attachment content") }),
      };

      vi.mocked(MsgReader).mockImplementation(() => mockMsgReader as any);

      const emailWithMsgFile = { ...mockEmail, fileRef: "test.msg" };
      const result = await handleAttachments(emailWithMsgFile, mockAdapter);

      expect(result.attachments).toHaveLength(1);
      expect(result.attachments[0].filename).toBe("test.msg");
    });

    it("logs an error for unsupported file types", async () => {
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});
      const emailWithUnknownFile = { ...mockEmail, fileRef: "unsupported.xyz" };

      const result = await handleAttachments(emailWithUnknownFile, mockAdapter);

      expect(consoleSpy).toHaveBeenCalledWith("Unsupported file type");
      expect(result).toEqual({
        attachments: [],
        fileRef: emailWithUnknownFile.fileRef,
      });
      consoleSpy.mockRestore();
    });

    it("handles missing driveItemID by fetching it through adapter", async () => {
      vi.mocked(simpleParser).mockResolvedValue({
        attachments: [
          {
            filename: "file1.txt",
            size: 100,
            contentType: "text/plain",
            content: Buffer.from("file content"),
          },
        ],
      } as any);

      const emailWithoutDriveItemID = { ...mockEmail, driveItemID: undefined };

      const result = await handleAttachments(
        emailWithoutDriveItemID,
        mockAdapter
      );

      expect(mockAdapter.getSiteFromSelectedEmail).toHaveBeenCalled();
      expect(result.attachments).toHaveLength(1);
    });

    it("throws error when no adapter is provided with missing driveItemID", async () => {
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      const emailWithoutDriveItemID = { ...mockEmail, driveItemID: undefined };

      const result = await handleAttachments(
        emailWithoutDriveItemID,
        undefined as any
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        "Email missing driveItemID and no adapter provided"
      );
      expect(result).toEqual({
        attachments: [],
        fileRef: emailWithoutDriveItemID.fileRef,
      });

      consoleSpy.mockRestore();
    });
  });

  describe("determineFileType", () => {
    it("identifies EML files correctly", () => {
      expect(determineFileType("file.eml")).toBe("eml");
    });

    it("identifies MSG files correctly", () => {
      expect(determineFileType("file.msg")).toBe("msg");
    });

    it("returns unknown for unsupported file types", () => {
      expect(determineFileType("file.txt")).toBe("unknown");
    });

    it("handles case insensitivity", () => {
      expect(determineFileType("FILE.EML")).toBe("eml");
      expect(determineFileType("file.MSG")).toBe("msg");
    });
  });

  describe("readFileContent", () => {
    it("reads file content as ArrayBuffer", async () => {
      const result = await readFileContent(mockAdapter, mockEmail);

      expect(result).toBeInstanceOf(ArrayBuffer);
      expect(mockAdapter.getFileContents).toHaveBeenCalledWith(mockEmail);
    });

    it("throws an error if reading content fails", async () => {
      mockAdapter.getFileContents = vi
        .fn()
        .mockRejectedValue(new Error("Failed"));

      await expect(readFileContent(mockAdapter, mockEmail)).rejects.toThrow(
        "Failed"
      );
    });
  });

  describe("readFileAsync", () => {
    it("reads a Blob as ArrayBuffer", async () => {
      const blob = new Blob(["test content"]);
      const result = await readFileAsync(blob);
      expect(result).toBeInstanceOf(ArrayBuffer);
    });

    it("throws an error if FileReader fails", async () => {
      const mockFileReader = {
        readAsArrayBuffer: vi.fn(() => {
          throw new Error("FileReader error");
        }),
      };
      global.FileReader = vi.fn(() => mockFileReader) as any;

      await expect(readFileAsync(new Blob([]))).rejects.toThrow(
        "FileReader error"
      );
    });
  });
});
