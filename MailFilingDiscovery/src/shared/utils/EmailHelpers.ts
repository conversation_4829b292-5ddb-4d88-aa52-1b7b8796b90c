import { Filter } from "../utils/CamlHelpers";
import { SortDirection, handleFilter } from "../utils/FilterUtils";
import { EmailResponse } from "../types/Email";
import { extractSiteId } from "../utils/SiteHelpers";
import { Operation } from "../types/Filter";
import { SearchResponse } from "@microsoft/microsoft-graph-types";
import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";
import { Hubsite } from "../types/Hubsite";
import {
  buildEmailPaths,
  extractEmailsFromSearchResults,
  setFilterQuery,
} from "./GetEmailsFromSearchHelpers";
import { ensureProtocol } from "./ProtocolUtils";

export const getEmails = async (
  adapter: ISharepointAdapter,
  selectedProjects: string[],
  shareRootUrl: string,
  searchTerm: string,
  page: number,
  pageSize: number,
  hubsiteUrl: string,
  nextLink: string | undefined,
  columnCode: string | undefined,
  sortColumn: string | undefined,
  sortDirection: SortDirection,
  filters: Filter[],
  resetPaging: boolean,
  selectedDriveFilter: string
): Promise<EmailResponse | undefined> => {
  try {
    // Case 1: When there is a search term, use search API
    if (searchTerm) {
      return await getEmailsFromSearch(
        adapter,
        selectedProjects,
        shareRootUrl,
        searchTerm,
        page,
        pageSize,
        hubsiteUrl,
        columnCode,
        sortDirection,
        filters
      );
    }
    // Case 2: When a single project is selected, use list API
    else if (selectedProjects && selectedProjects.length === 1) {
      return await getEmailsByProject(
        adapter,
        selectedProjects[0],
        shareRootUrl,
        filters,
        sortColumn,
        sortDirection,
        nextLink,
        page,
        pageSize,
        resetPaging,
        selectedDriveFilter
      );
    }

    // Return undefined if none of the conditions are met
    return undefined;
  } catch (error) {
    console.error("Error fetching emails from getEmails: ", error);
    return undefined;
  }
};

export const getEmailsFromSearch = async (
  adapter: ISharepointAdapter,
  sites: string[],
  tenantUrl: string,
  searchTerm: string,
  page: number,
  pageSize: number,
  hubsiteURL: string,
  orderColumn?: string,
  orderDirection?: SortDirection,
  filters?: Filter[]
): Promise<EmailResponse> => {
  let hubsite;

  const formattedTenantUrl = new URL(ensureProtocol(tenantUrl)).hostname;
  const formattedHubsiteUrl = new URL(ensureProtocol(hubsiteURL)).pathname;

  // Check if user is searching on the root hubsite
  if (formattedHubsiteUrl === "/") {
    hubsite = await adapter.getRootSite();
  } else {
    hubsite = await adapter.getSite(formattedTenantUrl, formattedHubsiteUrl);
  }

  const sortProperties =
    orderColumn && orderDirection
      ? [
          {
            name: orderColumn,
            isDescending: orderDirection === SortDirection.Descending,
          },
        ]
      : [];

  const firstFilters = [
    {
      field: "ContentType",
      values: ["Document"],
      application: Operation.Equals,
    },
  ];

  // Determine whether the confidential filter is applied
  const isConfidentialFilterApplied = filters?.some(
    (filter) =>
      filter.field === "EmailConfidential" && filter.values.includes("true")
  );

  // Remove "EmailConfidential" filter from the filters array
  const filtersWithoutConfidential = filters?.filter(
    (filter) => filter.field !== "EmailConfidential"
  );

  // Process the filters without EmailConfidential
  const intermedFilters = filtersWithoutConfidential ?? [];

  // Ensure tenantUrl has a protocol
  const safetenantUrl = ensureProtocol(tenantUrl);

  // Modify finalFilters based on the confidential filter selection
  const finalFilters = [
    {
      field: "Path",
      values: buildEmailPaths(
        safetenantUrl,
        sites,
        isConfidentialFilterApplied
      ),
      application: Operation.Equals,
    },
  ];

  // This query only works if content type is the first filter and path is the last filter
  const combinedFilters: Filter[] = [
    ...firstFilters,
    ...intermedFilters,
    ...finalFilters,
  ];

  const filterQuery = setFilterQuery(combinedFilters);

  // If a hubsiteId is provided, add it to the query string
  let hubsiteFilter = "";
  hubsiteFilter = ` AND DepartmentId:${extractSiteId(hubsite.id)}`; // Add the filter for DepartmentId

  // Build the final query template with hubsiteFilter
  const query = {
    queryString: searchTerm || "*", // Default to wildcard search if no search term
    queryTemplate: `({searchTerms}) ${filterQuery}${hubsiteFilter}`, // Combine filter query and hubsite filter
  };

  const results: SearchResponse[] = await adapter.getSearchResults(
    query,
    ["driveItem"],
    [
      "id",
      "Title",
      "listitemid",
      "RefinableDate00", // EmailReceivedOn
      "RefinableString31", // EmailSubject
      "RefinableString32", // EmailTo
      "RefinableString33", // EmailCC
      "RefinableString35", // EmailFrom
      "driveId",
      "sitename",
      "webUrl",
      "RefinableString51", // EmailTags
      "RefinableString52", // EmailImportant
      "RefinableDecimal09", // AttachmentCount
    ],
    page * pageSize,
    pageSize,
    sortProperties
  );

  const emails = extractEmailsFromSearchResults(results);

  // Determine if more results are available
  const moreResultsAvailable = results.some((result) =>
    result.hitsContainers?.some((container) => container.moreResultsAvailable)
  );
  const total =
    results
      .map(
        (result) =>
          result?.hitsContainers?.find(
            (container) => container?.total !== undefined
          )?.total
      )
      .find((total) => total !== null && total !== undefined) ?? 0; // Default to 0 if no valid total is found

  return {
    emails,
    nextLink: undefined, // or the next link if available
    page,
    pageSize,
    sortField: orderColumn,
    sortDirection: orderDirection ?? SortDirection.Descending,
    moreResultsAvailable,
    total,
  };
};

export const getEmailsByProject = async (
  adapter: ISharepointAdapter,
  projectCode: string,
  shareRootUrl: string,
  filters: Filter[],
  sortColumn: string | undefined,
  sortDirection: SortDirection,
  nextLink: string | undefined,
  page: number,
  pageSize: number,
  resetPaging: boolean,
  selectedDriveFilter: string
): Promise<EmailResponse> => {
  try {
    // Check if the confidential filter is applied
    const isConfidentialFilterApplied = filters?.some(
      (filter) =>
        filter.field === "EmailConfidential" && filter.values.includes("true")
    );

    // Get the site information to find the drive ID
    const projectPath = `/sites/${projectCode}`;

    const site = await adapter.getSiteById(shareRootUrl, projectPath);

    // Fetch the drives from the site and filter to get the appropriate drive
    const allSiteDrives = await adapter.getSiteDrives(site.id as string);

    const filteredDrives = allSiteDrives.filter(
      (drive) => drive.name === selectedDriveFilter
    );

    // Ensure we have a valid drive based on the filter
    if (filteredDrives.length === 0) {
      console.warn(`Drive with name ${selectedDriveFilter} not found.`);
      return {
        page: page,
        pageSize: pageSize,
        nextLink: undefined,
        emails: [],
        sortField: sortColumn,
        sortDirection: sortDirection,
      };
    }

    const emailResponse = await handleFilter(
      filters,
      sortColumn,
      sortDirection,
      projectCode,
      nextLink,
      page,
      pageSize,
      resetPaging,
      shareRootUrl,
      isConfidentialFilterApplied,
      adapter
    );

    // Update the response with drive information
    const updatedResponse = {
      ...emailResponse,
      emails: emailResponse.emails.map((item) => ({
        ...item,
        driveID: filteredDrives[0].id,
        projectCode: projectCode,
        emailConfidential: isConfidentialFilterApplied,
      })),
    };

    return updatedResponse;
  } catch (error) {
    console.error("Error fetching emails by project: ", error);
    // Return an empty response if there's an error
    return {
      page: page,
      pageSize: pageSize,
      nextLink: undefined,
      emails: [],
      sortField: sortColumn,
      sortDirection: sortDirection,
    };
  }
};

export async function fetchEmailsWithAdapter(
  adapter: ISharepointAdapter,
  selectedProjects: string[],
  shareRootUrl: string,
  searchTerm: string,
  page: number,
  pageSize: number,
  selectedHubsite: Hubsite,
  nextLink: string | undefined,
  columnCode: string | undefined,
  sortColumn: string | undefined,
  sortDirection: SortDirection,
  filters: Filter[],
  resetPaging: boolean,
  selectedDriveFilter: string
) {
  if (adapter && selectedHubsite) {
    try {
      const response = await getEmails(
        adapter,
        selectedProjects,
        shareRootUrl,
        searchTerm,
        page,
        pageSize,
        selectedHubsite.url,
        nextLink,
        columnCode,
        sortColumn,
        sortDirection,
        filters,
        resetPaging,
        selectedDriveFilter
      );

      return response;
    } catch (error) {
      console.error("Error in fetchEmailsWithAdapter:", error);
      return {
        page: page,
        pageSize: pageSize,
        nextLink: undefined,
        emails: [],
        sortField: sortColumn,
        sortDirection: sortDirection,
      };
    }
  }

  return null;
}
