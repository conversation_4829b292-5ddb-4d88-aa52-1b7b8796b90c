import { Email } from "../types/Email";
import "@pnp/graph/users";
import "@pnp/graph/files";
import { simpleParser, ParsedMail } from "mailparser";
import MsgReader from "@kenjiuno/msgreader";
import { Attachment, AttachmentResult } from "../types/Attachments";
import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";

interface CustomFieldValueSet extends FieldValueSet {
  EmailSubject?: string;
  EmailFrom?: string;
  EmailReceived?: string;
  ATVMessageId?: string;
  ATVConversationId?: string;
}

// Honestly im not sure why we need this exactly.
// one of mailparser's dependencies uses this package
// and its not defined by default but this fixes it.
import "setimmediate";
import { FieldValueSet } from "@microsoft/microsoft-graph-types";
import { ListTitles } from "../types/ListTitles";

export const ensureDriveItemID = async (
  adapter: ISharepointAdapter,
  email: Email
): Promise<Email> => {
  // If driveItem<PERSON> is already present, return the email as-is
  if (email.driveItemID) {
    return email;
  }

  try {
    // Get site details from the selected email
    const site = await adapter.getSiteFromSelectedEmail(email);

    // Determine the list type based on the emailConfidential property
    const listType = email.emailConfidential
      ? ListTitles.Confidential
      : ListTitles.NonConfidential;

    // Get lists from the site
    const lists = await adapter.getListsFromSite(site.id as string);

    // Filter lists by name
    const filteredLists = lists.filter((list) => list.name === listType);

    if (filteredLists.length === 0) {
      throw new Error(`List of type '${listType}' not found`);
    }

    // Get the email with drive item
    const selectedEmailWithDriveItems = await adapter.getEmailWithDriveItem(
      site.id as string,
      filteredLists[0].id as string,
      email?.id ?? ""
    );

    const fields = selectedEmailWithDriveItems.fields as CustomFieldValueSet;
    return {
      ...email,
      id: fields?.id ?? "",
      subject: fields?.EmailSubject ?? "",
      sentBy: fields?.EmailFrom ?? "",
      dateFiled: fields?.EmailReceived ?? "",
      driveItemID: selectedEmailWithDriveItems.driveItem?.id,
      driveID: email.driveID,
      projectCode: email.projectCode,
      messageId: fields?.ATVMessageId ?? "",
      conversationId: fields?.ATVConversationId ?? "",
    };
  } catch (error) {
    console.error("Error ensuring drive item ID:", error);
    throw error;
  }
};

export const handleAttachments = async (
  email: Email,
  adapter: ISharepointAdapter
): Promise<AttachmentResult> => {
  if (email.fileRef) {
    const fileType = determineFileType(email.fileRef);

    let newSelectedEmail = email;

    // If we have an adapter, use it to ensure we have the driveItemID
    if (!email.driveItemID && adapter) {
      newSelectedEmail = await ensureDriveItemID(adapter, email);
    } else if (!email.driveItemID) {
      // We need driveItemID to proceed, but we don't have an adapter to get it
      console.error("Email missing driveItemID and no adapter provided");
      return { attachments: [], fileRef: email.fileRef };
    }

    const fileContent = await readFileContent(adapter, newSelectedEmail);

    if (fileType === "eml") {
      const emlContent = await parseEmlFileAttachments(fileContent);
      return { ...emlContent, fileRef: email.fileRef };
    } else if (fileType === "msg") {
      const msgContent = await parseMsgFileAttachments(fileContent);
      return { ...msgContent, fileRef: email.fileRef };
    } else {
      console.error("Unsupported file type");
      return { attachments: [], fileRef: email.fileRef };
    }
  }

  return { attachments: [] };
};

export function determineFileType(fileRef: string): "eml" | "msg" | "unknown" {
  const lowercaseFileRef = fileRef.toLowerCase();
  if (lowercaseFileRef.endsWith(".eml")) {
    return "eml";
  } else if (lowercaseFileRef.endsWith(".msg")) {
    return "msg";
  } else {
    return "unknown";
  }
}

export async function readFileContent(
  adapter: ISharepointAdapter,
  email: Email
): Promise<ArrayBuffer> {
  const fileContents = await adapter.getFileContents(email);
  const content: ArrayBuffer = await readFileAsync(fileContents);
  return content;
}

export function readFileAsync(file: Blob): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result as ArrayBuffer);
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
}

export async function parseEmlFileAttachments(
  fileContent: ArrayBuffer
): Promise<AttachmentResult> {
  try {
    const decoder = new TextDecoder("utf-8");
    const parsed: ParsedMail = await simpleParser(decoder.decode(fileContent));

    const attachments = parsed.attachments.filter(
      (attachment) =>
        attachment.contentDisposition !== "inline" || !attachment.cid
    );

    return {
      attachments: attachments.map((attachment) => ({
        filename: attachment.filename,
        size: attachment.size,
        contentType: attachment.contentType,
        content: attachment.content,
      })),
    };
  } catch (error) {
    console.error("Error parsing EML file:", error);
    throw error;
  }
}

export async function parseMsgFileAttachments(
  fileContent: ArrayBuffer
): Promise<AttachmentResult> {
  try {
    const reader = new MsgReader(fileContent);
    const fileData = reader.getFileData();

    const attachments: Attachment[] = [];

    for (const rawAttachment of fileData.attachments || []) {
      const isInline =
        rawAttachment.innerMsgContent || !!rawAttachment.pidContentId;

      if (isInline) continue;

      const contentBuffer = reader.getAttachment(rawAttachment).content;
      const fileName =
        rawAttachment.fileName ?? rawAttachment.name ?? "unknown";
      const mimeType =
        rawAttachment.attachMimeTag ?? "application/octet-stream";
      const size = rawAttachment.contentLength ?? contentBuffer.byteLength;

      attachments.push({
        filename: fileName,
        size,
        contentType: mimeType,
        content: contentBuffer,
      });
    }

    return { attachments };
  } catch (error) {
    console.error("Error parsing MSG file:", error);
    throw error;
  }
}
