export const getQueryParamFromUrl = (
  url: string,
  param: string | undefined
): string | undefined => {
  if (!url) {
    throw new Error("URL must be a non-empty string.");
  }

  if (!param) {
    return undefined;
  }

  try {
    const urlObject = new URL(url);
    const value = urlObject.searchParams.get(param);

    // Return undefined if the value is empty string
    return value === "" ? undefined : (value ?? undefined);
  } catch (error) {
    throw new Error(`Invalid URL: ${url}`);
  }
};
