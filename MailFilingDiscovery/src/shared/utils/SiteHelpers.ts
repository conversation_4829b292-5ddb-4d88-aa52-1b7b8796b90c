export const extractSiteId = (
  fullSiteId: string | undefined
): string | undefined => {
  if (!fullSiteId) return undefined;

  const parts = fullSiteId.split(",");
  return parts.length >= 2 ? parts[1] : undefined;
};

export const getSitePath = (
  siteUrl: string | undefined
): string | undefined => {
  if (!siteUrl) {
    return undefined;
  }

  try {
    const url = new URL(siteUrl);
    return url.pathname;
  } catch (error) {
    return undefined;
  }
};
