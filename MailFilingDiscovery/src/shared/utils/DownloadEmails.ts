import { AttachmentResult } from "../types/Attachments";
import J<PERSON><PERSON><PERSON> from "jszip";
import { saveAs } from "file-saver";
import { Email } from "../types/Email";
import { parseFileRef } from "./ParseFileRef";

import "@pnp/graph/users";
import "@pnp/graph/files";
import { ISharepointAdapter } from "../../adapters/ISharepointAdapter";
import { ensureDriveItemID } from "./AttachmentHelpers";

/**
 * Downloads the 1 email.
 * @param {AttachmentResult | undefined} attachment - The attachment object containing file references.
 */
export const handleDownloadEmail = (
  attachment: AttachmentResult | undefined
): void => {
  const fileRef = attachment?.fileRef;

  if (fileRef) {
    // Opens the file reference in a new tab to trigger the download
    window.open(fileRef, "_blank");
  } else {
    console.error("No attachments available for download");
  }
};

/**
 * Downloads a zip containing multiple email (.eml) files.
 * @param adapter - The SharePoint adapter used to fetch email contents.
 * @param emails - An array of Email objects containing file references.
 * @param zipFilename - The name of the zip file to be downloaded.
 * @returns A Promise that resolves when the download is complete.
 */
export const handleDownloadMultipleEmails = async (
  adapter: ISharepointAdapter,
  emails: Email[],
  zipFilename: string = "emails-archive.zip"
): Promise<void> => {
  if (!emails || emails.length === 0) {
    console.error("No emails available for download");
    return;
  }

  const zip = new JSZip();
  let successfulEmails: number = 0;
  let failedEmails: number = 0;

  try {
    // Process each email sequentially to avoid throttling
    for (const email of emails) {
      try {
        const fileRef = email.fileRef;

        if (!fileRef) {
          console.error(`No file reference found for email: ${email.id}`);
          failedEmails++;
          continue;
        }

        if (!email.driveID) {
          console.error(`No drive ID found for email: ${email.id}`);
          failedEmails++;
          continue;
        }

        let newSelectedEmail = email;

        if (!email.driveItemID && adapter) {
          newSelectedEmail = await ensureDriveItemID(adapter, email);
        } else if (!email.driveItemID) {
          console.error("Email missing driveItemID and no adapter provided");
          failedEmails++;
          continue;
        }

        const fileContents = await adapter.getFileContents(newSelectedEmail);

        if (!fileContents) {
          console.error(`Error fetching email file: ${fileRef}`);
          failedEmails++;
          continue;
        }

        // Create a filename for each email, or use the subject as a default filename
        const filename = parseFileRef(fileRef);

        // Add the .eml file to the zip
        zip.file(filename, fileContents);
        successfulEmails++;
      } catch (emailError) {
        // Log the error but continue processing other emails
        console.error(`Error processing email ${email.id}:`, emailError);
        failedEmails++;
      }
    }

    // Check if we have any emails to include in the zip
    if (zip.files && Object.keys(zip.files).length === 0) {
      console.error("No emails were successfully processed for download");
      return;
    }

    // Generate the zip file after all files have been added
    const content = await zip.generateAsync({ type: "blob" });

    console.log(
      `Zip generated with ${successfulEmails} emails (${failedEmails} failed), size: ${content.size}`
    );

    // Trigger the download of the zip file
    saveAs(content, zipFilename);
  } catch (error) {
    console.error("Error generating zip file:", error);
    // Don't throw the error - we want the loading state to be reset in the UI
  }
};
