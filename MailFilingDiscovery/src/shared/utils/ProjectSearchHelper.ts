import "@pnp/graph/search";
import { IRenderListDataParameters } from "@pnp/sp/lists";

/**
 * Builds the search query for projects based on the search string
 * @param searchString The string to search for in projects
 * @returns Object containing viewXml and renderListDataParams
 */
export const buildProjectSearchQuery = (searchString: string) => {
  let whereClause = "";
  if (searchString) {
    whereClause = `
      <Or>
        <Contains>
          <FieldRef Name='ProjectCode'/>
            <Value Type='Text'>${searchString}</Value>
        </Contains>
        <Contains>
          <FieldRef Name='Description'/><Value Type='Text'>${searchString}</Value>
        </Contains>
      </Or>
    `;
  }

  const viewXml = `<View Scope="RecursiveAll">
    <Query>
      <Where>${whereClause}</Where>
      <OrderBy><FieldRef Name="ProjectCode" Ascending="True"/></OrderBy>
    </Query>
     <ViewFields>
      <FieldRef Name="ProjectCode"/>
      <FieldRef Name="Description"/>
      <FieldRef Name="ATVImportedSourceID"/>
    </ViewFields>
    <RowLimit Paged="TRUE">${200}</RowLimit>
  </View>`;

  const renderListDataParams: IRenderListDataParameters = {
    ViewXml: viewXml,
    Paging: "Paged=TRUE",
  };

  return { viewXml, renderListDataParams };
};
