{"name": "shared", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"@pnp/graph": "^4.2.0", "@pnp/msaljsclient": "^4.2.0"}}, "node_modules/@azure/msal-browser": {"version": "3.17.0", "resolved": "https://registry.npmjs.org/@azure/msal-browser/-/msal-browser-3.17.0.tgz", "integrity": "sha512-csccKXmW2z7EkZ0I3yAoW/offQt+JECdTIV/KrnRoZyM7wCSsQWODpwod8ZhYy7iOyamcHApR9uCh0oD1M+0/A==", "dependencies": {"@azure/msal-common": "14.12.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-common": {"version": "14.12.0", "resolved": "https://registry.npmjs.org/@azure/msal-common/-/msal-common-14.12.0.tgz", "integrity": "sha512-IDDXmzfdwmDkv4SSmMEyAniJf6fDu3FJ7ncOjlxkDuT85uSnLEhZi3fGZpoR7T4XZpOMx9teM9GXBgrfJgyeBw==", "engines": {"node": ">=0.8.0"}}, "node_modules/@microsoft/microsoft-graph-types": {"version": "2.40.0", "resolved": "https://registry.npmjs.org/@microsoft/microsoft-graph-types/-/microsoft-graph-types-2.40.0.tgz", "integrity": "sha512-1fcPVrB/NkbNcGNfCy+Cgnvwxt6/sbIEEFgZHFBJ670zYLegENYJF8qMo7x3LqBjWX2/Eneq5BVVRCLTmlJN+g=="}, "node_modules/@pnp/core": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/@pnp/core/-/core-4.2.0.tgz", "integrity": "sha512-6dewgrUxl2wavAWZAWDQzmEmvztI6dXoMJOfcgdxX+MUhF83YYtjLCJaRfxMJsrnHmMJbTiIhnIAsZbaO5IczQ==", "dependencies": {"tslib": "2.6.3"}, "engines": {"node": ">=18.12.0"}, "funding": {"type": "individual", "url": "https://github.com/sponsors/patrick-rodgers/"}}, "node_modules/@pnp/graph": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/@pnp/graph/-/graph-4.2.0.tgz", "integrity": "sha512-Kz3HwpdFQfY/3QOVR04DgujgWtEW0vjbIBYFXFQiV6C6+JyU953LMMm19MOz+k5ufgs93OfrYAMgxZSJgPjbnQ==", "dependencies": {"@microsoft/microsoft-graph-types": "2.40.0", "@pnp/core": "4.2.0", "@pnp/queryable": "4.2.0", "tslib": "2.6.3"}, "engines": {"node": ">=18.12.0"}, "funding": {"type": "individual", "url": "https://github.com/sponsors/patrick-rodgers/"}}, "node_modules/@pnp/msaljsclient": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/@pnp/msaljsclient/-/msaljsclient-4.2.0.tgz", "integrity": "sha512-eDM6st7P0OqLOL9VWZpyDZ3iKSTYosXUOcg7aC6xlhtQKb71DUj0DYbOxzmLrArmX3yj32HO2N1Snwuk0AsgUw==", "dependencies": {"@azure/msal-browser": "3.17.0", "@pnp/queryable": "4.2.0", "tslib": "2.6.3"}, "engines": {"node": ">=18.12.0"}, "funding": {"type": "individual", "url": "https://github.com/sponsors/patrick-rodgers/"}}, "node_modules/@pnp/queryable": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/@pnp/queryable/-/queryable-4.2.0.tgz", "integrity": "sha512-DtRlsY++rQCzAyYQIF0fm+lhLOkx2fo0NSb++k/CCTPb8eLiR39PicOq7h7Zy1zWKG5D2+g90mDNa5THEmxd3Q==", "dependencies": {"@pnp/core": "4.2.0", "tslib": "2.6.3"}, "engines": {"node": ">=18.12.0"}, "funding": {"type": "individual", "url": "https://github.com/sponsors/patrick-rodgers/"}}, "node_modules/tslib": {"version": "2.6.3", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.3.tgz", "integrity": "sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ=="}}}