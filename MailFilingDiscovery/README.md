/* v8 ignore next */
# Mail Filing Discovery

## Setup
Run the following:
`npm install`

## Running Tests's
To run the test suit run the following:
`npm test`

Running individual tests:
`npm test {test-file-name}.test.tsx`

## Development
To your dev enviroment run the following:
`npm run dev`

Sometimes after pulling a devs changes they may have installed or changed some of our dependencies. To deal with this, run the following commands:

`rm -rf node_modules`
`rm package-lock.json`
`npm cache clean --force`
`npm install`

This sequence of commands will ensure a fresh build.