{"compilerOptions": {"strict": true, "allowUnusedLabels": false, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "module": "CommonJS", "moduleResolution": "node", "noImplicitReturns": true, "noImplicitAny": true, "noUnusedParameters": true, "outDir": "dist", "removeComments": false, "sourceMap": true, "target": "es6", "resolveJsonModule": true, "lib": ["es7", "dom"], "pretty": true, "typeRoots": ["node_modules/@types"]}, "exclude": ["node_modules"], "compileOnSave": false, "buildOnSave": false, "ts-node": {"files": true}}