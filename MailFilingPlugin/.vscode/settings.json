{"eslint.validate": ["javascript", "javascriptreact", "typescript"], "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/bower_components": true, "**/coverage": true, "**/jest-output": true, "**/lib-amd": true, "dist/**/*": true, "coverage/**/*": true, "src/**/*.scss.ts": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.configPath": ".prettierrc.json"}