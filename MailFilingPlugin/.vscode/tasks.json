{"version": "2.0.0", "tasks": [{"label": "Build (Development)", "type": "npm", "script": "build:dev", "group": {"kind": "build", "isDefault": true}, "presentation": {"clear": true, "panel": "shared", "showReuseMessage": false}}, {"label": "Build (Production)", "type": "npm", "script": "build", "group": "build", "presentation": {"clear": true, "panel": "shared", "showReuseMessage": false}}, {"label": "Debug: Excel Desktop", "type": "shell", "command": "npm", "args": ["run", "start:desktop", "--", "--app", "excel"], "presentation": {"clear": true, "panel": "dedicated"}, "problemMatcher": []}, {"label": "Debug: Outlook Desktop", "type": "shell", "command": "npm", "args": ["run", "start:desktop", "--", "--app", "outlook"], "presentation": {"clear": true, "panel": "dedicated"}, "problemMatcher": []}, {"label": "Debug: PowerPoint Desktop", "type": "shell", "command": "npm", "args": ["run", "start:desktop", "--", "--app", "powerpoint"], "presentation": {"clear": true, "panel": "dedicated"}, "problemMatcher": []}, {"label": "Debug: Word Desktop", "type": "shell", "command": "npm", "args": ["run", "start:desktop", "--", "--app", "word"], "presentation": {"clear": true, "panel": "dedicated"}, "problemMatcher": []}, {"label": "Dev Server", "type": "npm", "script": "dev-server", "presentation": {"clear": true, "panel": "dedicated"}, "problemMatcher": []}, {"label": "Install", "type": "npm", "script": "install", "presentation": {"clear": true, "panel": "shared", "showReuseMessage": false}, "problemMatcher": []}, {"label": "Lin<PERSON>: Check for problems", "type": "npm", "script": "lint", "problemMatcher": ["$eslint-stylish"]}, {"label": "Lint: Fix all auto-fixable problems", "type": "npm", "script": "lint:fix", "problemMatcher": ["$eslint-stylish"]}, {"label": "Stop Debug", "type": "npm", "script": "stop", "presentation": {"clear": true, "panel": "shared", "showReuseMessage": false}, "problemMatcher": []}, {"label": "Watch", "type": "npm", "script": "watch", "presentation": {"clear": true, "panel": "dedicated"}, "problemMatcher": []}]}