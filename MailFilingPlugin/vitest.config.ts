import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: "./src/setup.tests.ts",
    coverage: {
      reporter: ["text", "json", "html", "lcov"],

      include: ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx"],
      exclude: [
        "src/taskpane/index.tsx", // entry point
        "**/*.d.ts",
        "*.config.js",
        "**/*.d.ts",
        "src/**/*.test.ts",
        "src/**/*.test.tsx",
        "src/**/*.test.js",
        "src/**/*.test.jsx",
      ],
    },
  },
});
