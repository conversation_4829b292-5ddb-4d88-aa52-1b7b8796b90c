{"name": "@atvero/mail-filing-plugin", "version": "0.0.1", "repository": {"type": "git", "url": "https://github.com/OfficeDev/Office-Addin-TaskPane-React.git"}, "license": "MIT", "config": {"app_to_debug": "outlook", "app_type_to_debug": "desktop", "dev_server_port": 3000}, "scripts": {"build": "webpack --mode production", "build:prep": "npm run atv-dev-manifest && npm run cmap-dev-manifest && npm run cmap-dev-us1-manifest && npm run dev-config", "build:dev": "webpack --mode development", "dev-manifest": "npm run atv-dev-manifest && npm run cmap-dev-manifest", "atv-dev-manifest": "npx ejs-cli -f manifest/manifest.xml.ejs  -O manifest/atvero-dev.json > manifest.xml && npx ejs-cli -f manifest/manifest-with-file-on-send.xml.ejs  -O manifest/atvero-dev.json > manifest-file-on-send.xml ", "cmap-dev-manifest": "npx ejs-cli -f manifest/manifest.xml.ejs  -O manifest/cmap-dev.json > cmapmail.xml && npx ejs-cli -f manifest/manifest-with-file-on-send.xml.ejs  -O manifest/cmap-dev.json > cmapmail-file-on-send.xml ", "cmap-dev-us1-manifest": "npx ejs-cli -f manifest/manifest-us1.xml.ejs  -O manifest/cmap-us1-dev.json > cmapmail-us1.xml && npx ejs-cli -f manifest/manifest-us1-file-on-send.xml.ejs  -O manifest/cmap-us1-dev.json > cmapmail-us1-file-on-send.xml ", "dev-config": "cp config/dev.json config.json", "test-config": "cp config/test.json config.json", "preprod-config": "cp config/stage.json config.json && npx ejs-cli -f manifest/manifest.xml.ejs  -O manifest/stage.json > cmapmail.xml && npx ejs-cli -f manifest/manifest-with-file-on-send.xml.ejs  -O manifest/stage.json > cmapmail-file-on-send.xml", "dev": "npm run dev-server", "dev-server": "npm run dev-config && npm run dev-manifest && webpack serve --mode development", "lint": "office-addin-lint check", "lint:fix": "office-addin-lint fix", "prettier": "office-addin-lint prettier", "signin": "office-addin-dev-settings m365-account login", "signout": "office-addin-dev-settings m365-account logout", "start": "npm run dev-config && npm run dev-manifest && office-addin-debugging start manifest.xml", "start:desktop": "npm run dev-config && npm run dev-manifest && office-addin-debugging start manifest.xml desktop", "start:web": "npm run dev-config && npm run dev-manifest && office-addin-debugging start manifest.xml web", "stop": "office-addin-debugging stop manifest.xml", "validate": "npm run dev-manifest && office-addin-manifest validate manifest.xml", "watch": "npm run dev-config && npm run dev-manifest && webpack --mode development --watch", "test": "npm run test-config  && vitest", "coverage": "npm run test-config  && vitest run --coverage", "test:watch": "npm run test-config && vitest --watch", "check:formatting": "npx prettier 'src/**/*.{js,ts,tsx,jsx}' --check "}, "dependencies": {"@azure/msal-browser": "^3.27.0", "@fluentui/react-components": "^9.54.2", "@fluentui/react-icons": "^2.0.245", "@sentry/react": "^8.43.0", "core-js": "^3.36.0", "es6-promise": "^4.2.8", "eslint-plugin-react-hooks": "^4.6.2", "jwt-decode": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "regenerator-runtime": "^0.14.1"}, "devDependencies": {"@azure/static-web-apps-cli": "^2.0.1", "@babel/core": "^7.24.0", "@babel/preset-typescript": "^7.24.7", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.1", "@types/jest": "^29.5.12", "@types/office-js": "^1.0.377", "@types/office-runtime": "^1.0.35", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/webpack": "^5.28.5", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.0.5", "acorn": "^8.11.3", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^12.0.2", "ejs-cli": "^2.2.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-office-addins": "^2.1.8", "eslint-plugin-react": "^7.28.0", "file-loader": "^6.2.0", "html-loader": "^5.0.0", "html-webpack-plugin": "^5.6.0", "jsdom": "^24.1.0", "less": "^4.2.0", "less-loader": "^12.2.0", "office-addin-cli": "^1.5.9", "office-addin-debugging": "^5.0.17", "office-addin-dev-certs": "^1.12.2", "office-addin-lint": "^2.2.9", "office-addin-manifest": "^1.12.11", "office-addin-mock": "^3.0.3", "office-addin-prettier-config": "^1.2.0", "os-browserify": "^0.3.0", "prettier": "3.5.3", "process": "^0.11.10", "source-map-loader": "^5.0.0", "ts-loader": "^9.5.1", "typescript": "^5.4.2", "vitest": "^2.0.5", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "webpack-dev-server": "5.0.3"}, "prettier": "office-addin-prettier-config", "browserslist": ["last 2 versions", "ie 11"]}