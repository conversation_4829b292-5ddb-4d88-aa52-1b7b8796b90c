<!DOCTYPE html>
<html lang="en-GB">
  <head>
    <title>Install the CMap Mail Plugin US1</title>
    <link
      rel="stylesheet"
      href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css"
    />
  </head>
  <body>
    <div class="ms-Grid" dir="ltr">
      <div class="ms-Grid-row">
        <div class="ms-Grid-col ms-sm12 ms-md12 ms-lg12">
          <h1 class="ms-font-xxl">Install the CMap Mail Plugin US1</h1>
          <p class="ms-font-m">
            Follow the steps below to install the Office.js plugin using the integrated app mechanism in the Office 365
            admin:
          </p>
          <ol class="ms-List">

            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText">Sign in to your Office 365 admin account.</span>
            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText">Go to the Admin center.</span>
            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText">Navigate to the <strong>Settings</strong> section.</span>
            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                >Click on <strong>Integrated apps</strong> depending on your Office 365 version.</span
              >
            </li>

            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                >Click on "Upload custom apps"</span
              >
            </li>  
                   <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                >Select the <strong>Office Add-in</strong> type</span
              >
            </li>
                 </li>  
                   <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                ><strong>Provide a link to manifest</strong> 
                
                and copy and paste <a href="https://filing.cmapmail.com/cmapmail-us1.xml">https://filing.cmapmail.com/cmapmail-us1.xml</a>
                
                
                in the field
          
                </span

              >
            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                >Follow the on-screen instructions to complete the installation.</span
              >
            </li>
          </ol>

        
          <p class="ms-font-l">
            If you are using the CMap Mail plugin with File On Send install <a href="./cmapmail-us1-file-on-send.html">here</a>
        </div>
      </div>
    </div>
  </body>
</html>
