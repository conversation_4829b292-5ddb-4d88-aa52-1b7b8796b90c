/* eslint-disable no-undef */

const devCerts = require("office-addin-dev-certs");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const webpack = require("webpack");

const urlDev = "localhost:3000/";
const urlProd = "filing.atveromail.com/";

async function getHttpsOptions() {
  const httpsOptions = await devCerts.getHttpsServerOptions();
  return { ca: httpsOptions.ca, key: httpsOptions.key, cert: httpsOptions.cert };
}

module.exports = async (env, options) => {
  const dev = options.mode === "development";
  const config = {
    devtool: "source-map",
    entry: {
      polyfill: ["core-js/stable", "regenerator-runtime/runtime"],
      vendor: ["react", "react-dom", "core-js", "@fluentui/react-components", "@fluentui/react-icons"],
      taskpane: ["./src/taskpane/index.tsx", "./src/taskpane/taskpane.html"],
      taskpane_us1: ["./src/taskpane/index.tsx", "./src/taskpane/taskpane_us1.html"],
      commands: "./src/commands/commands.ts",
      commands_us1: "./src/commands/commands_us1.ts",
      launcheventhtml: "./src/launchevent/launchevent.js",
    },
    output: {
      clean: true,
    },
    resolve: {
      extensions: [".ts", ".tsx", ".html", ".js"],
    },
    module: {
      rules: [
        {
          test: /\.ts$/,
          exclude: /node_modules/,
          use: {
            loader: "babel-loader",
            options: {
              presets: ["@babel/preset-typescript"],
            },
          },
        },
        {
          test: /\.tsx?$/,
          exclude: /node_modules/,
          use: ["ts-loader"],
        },
        {
          test: /\.html$/,
          exclude: /node_modules/,
          use: "html-loader",
        },
        {
          test: /\.(png|jpg|jpeg|ttf|woff|woff2|gif|ico)$/,
          type: "asset/resource",
          generator: {
            filename: "assets/[name][ext][query]",
          },
        },
      ],
    },
    plugins: [
      new CopyWebpackPlugin({
        patterns: [
          {
            from: "assets/*",
            to: "assets/[name][ext][query]",
          },

          {
            from: "cmapmail.xml",
            to: "[name]" + "[ext]",
          },
          {
            from: "cmapmail-file-on-send.xml",
            to: "[name]" + "[ext]",
          },

          {
            from: "cmapmail-us1.xml",
            to: "cmapmail-us1.xml",
          },

          {
            from: "cmapmail-us1-file-on-send.xml",
            to: "cmapmail-us1-file-on-send.xml",
          },
          {
            from: "index.html",
            to: "index.html",
          },
          {
            from: "cmapmail.html",
            to: "cmapmail.html",
          },
          {
            from: "cmapmail-file-on-send.html",
            to: "cmapmail-file-on-send.html",
          },
          {
            from: "cmapmail-us1.html",
            to: "cmapmail-us1.html",
          },
          {
            from: "cmapmail-us1-file-on-send.html",
            to: "cmapmail-us1-file-on-send.html",
          },
          {
            from: "./src/launchevent/launchevent.js",
            to: "launchevent.js",
          },
        ],
      }),
      new HtmlWebpackPlugin({
        filename: "taskpane.html",
        template: "./src/taskpane/taskpane.html",
        chunks: ["polyfill", "vendor", "taskpane"],
      }),

      new HtmlWebpackPlugin({
        filename: "commands.html",
        template: "./src/commands/commands.html",
        chunks: ["commands", "launcheventhtml"],
      }),

      new HtmlWebpackPlugin({
        filename: "taskpane_us1.html",
        template: "./src/taskpane/taskpane_us1.html",
        chunks: ["polyfill", "vendor", "taskpane_us1"],
      }),

      new HtmlWebpackPlugin({
        filename: "commands_us1.html",
        template: "./src/commands/commands_us1.html",
        chunks: ["commands_us1", "launcheventhtml"],
      }),
      new webpack.ProvidePlugin({
        Promise: ["es6-promise", "Promise"],
      }),
    ],
    devServer: {
      hot: true,
      headers: {
        "Access-Control-Allow-Origin": "*",
      },
      server: {
        type: "https",
        options: env.WEBPACK_BUILD || options.https !== undefined ? options.https : await getHttpsOptions(),
      },
      port: process.env.npm_package_config_dev_server_port || 3000,
    },
  };

  return config;
};
