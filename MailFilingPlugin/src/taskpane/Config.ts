import config from "../../config.json";
import { detectTenancy } from "./services/TenancyService";
import { Tenancy } from "./types/types";
export const Version = (): string => {
  return config.version;
};

export const ClientId = (): string => {
  switch (detectTenancy()) {
    case Tenancy.CMap:
      return config.cmap.appId;
    case Tenancy.CMap_US1:
      return config.cmap_us1.appId;
    case Tenancy.Atvero:
      return config.atvero.appId;
    default:
      return config.cmap.appId;
  }
};
export const DiscoveryUrl = (): string => {
  switch (detectTenancy()) {
    case Tenancy.CMap:
      return config.cmap.discovery;
    case Tenancy.CMap_US1:
      return config.cmap_us1.discovery;
    case Tenancy.Atvero:
      return config.atvero.discovery;
    default:
      return config.cmap.discovery;
  }
};

export const BackendScope = () => {
  switch (detectTenancy()) {
    case Tenancy.CMap:
      return `api://${config.cmap.backendScope}/${config.cmap.appId}/access_as_user`;
    case Tenancy.Atvero:
      return `api://${config.atvero.backendScope}/${config.atvero.appId}/access_as_user`;
    case Tenancy.CMap_US1:
      return `api://${config.cmap_us1.backendScope}/${config.cmap_us1.appId}/access_as_user`;
    default:
      return `api://${config.cmap.backendScope}/${config.cmap.appId}/access_as_user`;
  }
};
export const BackendUrl = () => {
  switch (detectTenancy()) {
    case Tenancy.CMap:
      return config.cmap.backendUrl;
    case Tenancy.Atvero:
      return config.atvero.backendUrl;
    case Tenancy.CMap_US1:
      return config.cmap_us1.backendUrl;
    default:
      return config.cmap.backendUrl;
  }
};

export const Scopes = () => {
  switch (detectTenancy()) {
    case Tenancy.Atvero: {
      return [
        "User.Read",
        "Sites.Read.All",
        "Contacts.Read",
        "Files.ReadWrite",
        "Group.Read.All",
        "Mail.ReadWrite",
        "ProfilePhoto.Read.All",
      ];
    }

    default: {
      return ["User.Read", "Sites.Read.All", "Group.ReadWrite.All", "Sites.FullControl.All"];
    }
  }
};
