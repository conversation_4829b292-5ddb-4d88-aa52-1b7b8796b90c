import { describe, it, expect } from "vitest";
import { BackendScope, ClientId, DiscoveryUrl, Scopes } from "./Config";
import config from "../../config.json";
describe("Scopes", () => {
  it("should return an array of scopes", () => {
    const expectedScopes = [
      "User.Read",
      "Sites.Read.All",
      "Contacts.Read",
      "Files.ReadWrite",
      "Group.Read.All",
      "Mail.ReadWrite",
      "ProfilePhoto.Read.All",
    ];

    const result = Scopes();
    expect(result).toEqual(expectedScopes);
  });

  it("should return an array with the correct length", () => {
    const result = Scopes();
    expect(result).toHaveLength(7);
  });

  it("should return an array of scopes", () => {
    const expectedScopes = [
      "User.Read",
      "Sites.Read.All",
      "Contacts.Read",
      "Files.ReadWrite",
      "Group.Read.All",
      "Mail.ReadWrite",
      "ProfilePhoto.Read.All",
    ];

    const result = Scopes();
    expect(result).toEqual(expectedScopes);
  });

  it("should return an array with the correct length", () => {
    const result = Scopes();
    expect(result).toHaveLength(7);
  });

  it("should contain specific scopes", () => {
    const result = Scopes();
    expect(result).toContain("User.Read");
  });

  it("should return an array of strings", () => {
    const result = Scopes();
    result.forEach((scope) => {
      expect(typeof scope).toBe("string");
    });
  });
});

describe("ClientId", () => {
  const originalLocation = window.location;

  afterEach(() => {
    Object.defineProperty(window, "location", {
      value: originalLocation,
      writable: true,
      configurable: true,
    });
  });

  it("returns cmap backendUrl when tenancy is cmap", () => {
    Object.defineProperty(window, "location", {
      value: { hostname: "discovery.cmapmail.com" },
      writable: true,
      configurable: true,
    });

    const result = ClientId();

    expect(result).toBe(config.cmap.appId);
  });

  it("returns cmap_us1 backendUrl when tenancy is cmap_us1", () => {
    Object.defineProperty(window, "location", {
      value: { pathname: "taskpanel_us1.html" },
      writable: true,
      configurable: true,
    });

    const result = ClientId();

    expect(result).toBe(config.cmap_us1.appId);
  });

  it("returns atvero backendUrl when tenancy is atvero", () => {
    Object.defineProperty(window, "location", {
      value: { hostname: "discovery.atveromail.com" },
      writable: true,
      configurable: true,
    });

    const result = ClientId();

    expect(result).toBe(config.atvero.appId);
  });

  it("returns cmap backendUrl by default when tenancy is unknown", () => {
    Object.defineProperty(window, "location", {
      value: { hostname: "discovery.testmail.com" },
      writable: true,
      configurable: true,
    });
    const result = ClientId();

    expect(result).toBe(config.atvero.appId);
  });
});

describe("DiscoveryUrl", () => {
  const originalLocation = window.location;

  afterEach(() => {
    Object.defineProperty(window, "location", {
      value: originalLocation,
      writable: true,
      configurable: true,
    });
  });

  it("returns cmap backendUrl when tenancy is cmap", () => {
    Object.defineProperty(window, "location", {
      value: { hostname: "discovery.cmapmail.com" },
      writable: true,
      configurable: true,
    });

    const result = DiscoveryUrl();

    expect(result).toBe(config.cmap.discovery);
  });

  it("returns cmap_us1 backendUrl when tenancy is cmap_us1", () => {
    Object.defineProperty(window, "location", {
      value: { pathname: "taskpanel_us1.html" },
      writable: true,
      configurable: true,
    });

    const result = DiscoveryUrl();

    expect(result).toBe(config.cmap_us1.discovery);
  });

  it("returns atvero backendUrl when tenancy is atvero", () => {
    Object.defineProperty(window, "location", {
      value: { hostname: "discovery.atveromail.com" },
      writable: true,
      configurable: true,
    });

    const result = DiscoveryUrl();

    expect(result).toBe(config.atvero.discovery);
  });

  it("returns cmap backendUrl by default when tenancy is unknown", () => {
    Object.defineProperty(window, "location", {
      value: { hostname: "discovery.testmail.com" },
      writable: true,
      configurable: true,
    });
    const result = DiscoveryUrl();

    expect(result).toBe(config.atvero.discovery);
  });
});

describe("BackendScope", () => {
  const originalLocation = window.location;

  afterEach(() => {
    Object.defineProperty(window, "location", {
      value: originalLocation,
      writable: true,
      configurable: true,
    });
  });

  it("returns cmap backendUrl when tenancy is cmap", () => {
    Object.defineProperty(window, "location", {
      value: { hostname: "discovery.cmapmail.com" },
      writable: true,
      configurable: true,
    });

    const result = BackendScope();

    expect(result).toBe("api://test.cmapmail.com/test-cmap-test/access_as_user");
  });

  it("returns cmap_us1 backendUrl when tenancy is cmap_us1", () => {
    Object.defineProperty(window, "location", {
      value: { pathname: "taskpanel_us1.html" },
      writable: true,
      configurable: true,
    });
  });

  it("returns atvero backendUrl when tenancy is atvero", () => {
    Object.defineProperty(window, "location", {
      value: { hostname: "discovery.atveromail.com" },
      writable: true,
      configurable: true,
    });

    const result = BackendScope();

    expect(result).toBe("api://test.atveromail.com/test-atvero-test/access_as_user");
  });

  it("returns cmap backendUrl by default when tenancy is unknown", () => {
    Object.defineProperty(window, "location", {
      value: { hostname: "discovery.testmail.com" },
      writable: true,
      configurable: true,
    });
    const result = BackendScope();

    expect(result).toBe("api://test.atveromail.com/test-atvero-test/access_as_user");
  });
});
