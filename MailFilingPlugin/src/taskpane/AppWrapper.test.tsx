import { render, screen, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import AppWrapper from "./AppWrapper";
import { IApiServices } from "./adapters/IApiServices";

vi.mock("./components/App", () => ({
  default: ({ mode }: { mode: string }) => <div data-testid="mock-app">Mode: {mode}</div>,
}));

const mockGetInitializationContext = vi.fn();
const mockOffice = {
  context: {
    mailbox: {
      item: {
        getInitializationContextAsync: mockGetInitializationContext,
      },
    },
  },
};

global.Office = mockOffice as any;

describe("AppWrapper", () => {
  const mockApiServices: IApiServices = {
    login: vi.fn(),
    getProjects: vi.fn(),
    getHubsites: vi.fn(),
    fileEmails: vi.fn(),
    syncEmails: vi.fn(),
    getFavouriteProjects: vi.fn(),
    setFavouriteProjects: vi.fn(),
    getFiledEmailInfo: vi.fn(),
    deleteFavouriteProject: vi.fn(),
    getUserPermissions: vi.fn(),
    getSetting: vi.fn(),
    setSubject: vi.fn(),
    setFilingProperty: vi.fn(),
    setFilingMessage: vi.fn(),
    getTags: vi.fn(),
    getActiveSubscriptions: vi.fn(),
    clearActiveSubscriptions: vi.fn(),
    getSharedInfo: vi.fn(),
    isConversationFiled: vi.fn(),
  };

  beforeEach(() => {
    mockGetInitializationContext.mockReset();
  });

  it("renders with read mode by default", () => {
    mockGetInitializationContext.mockImplementation((callback) => callback({ value: undefined }));

    render(<AppWrapper apiServices={mockApiServices} />);
    expect(screen.getByText("Mode: read")).toBeInTheDocument();
  });

  it('switches to onsend mode when initialization context is "onsend"', async () => {
    mockGetInitializationContext.mockImplementation((callback) => callback({ value: "onsend" }));

    render(<AppWrapper apiServices={mockApiServices} />);

    await waitFor(() => {
      expect(screen.getByText("Mode: onsend")).toBeInTheDocument();
    });
  });

  it('maintains read mode when initialization context is not "onsend"', async () => {
    mockGetInitializationContext.mockImplementation((callback) => callback({ value: "other" }));

    render(<AppWrapper apiServices={mockApiServices} />);

    await waitFor(() => {
      expect(screen.getByText("Mode: read")).toBeInTheDocument();
    });
  });

  it("handles error in initialization context", async () => {
    mockGetInitializationContext.mockImplementation((callback) => callback({ error: new Error("Test error") }));

    render(<AppWrapper apiServices={mockApiServices} />);

    await waitFor(() => {
      expect(screen.getByText("Mode: read")).toBeInTheDocument();
    });
  });

  it("sets mode to read when initialization value is undefined", async () => {
    mockGetInitializationContext.mockImplementation((callback) => callback({ value: undefined }));

    let renderedMode = "";

    render(<AppWrapper apiServices={mockApiServices} />);

    await waitFor(() => {
      const app = screen.getByTestId("mock-app");
      renderedMode = app.textContent?.split(": ")[1] || "";
      expect(renderedMode).toBe("read");
    });
  });
});
