import { createRoot } from "react-dom/client";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import * as Sentry from "@sentry/react";
import { createNestablePublicClientApplication, IPublicClientApplication } from "@azure/msal-browser";

import { ClientId } from "../taskpane/Config";
import { ApiServices } from "./adapters/ApiServices";
import { IAuthService } from "./adapters/IAuthService";
import { AuthService } from "./adapters/AuthService";
import { IApiServices } from "./adapters/IApiServices";

import { LogLevel } from "@azure/msal-browser";
import AppWrapper from "./AppWrapper";
import EmptyStateMessage, { ErrorState } from "./components/EmptyStateMessage/EmptyStateMessage";

let pca: IPublicClientApplication | undefined = undefined;
let mode = "";

/* global document, Office, module, require */

// set up Sentry error tracking
Sentry.setTag("project", "atveromail-filing");

const rootElement: HTMLElement | null = document.getElementById("container");
const root = rootElement ? createRoot(rootElement) : undefined;

/* Render application after Office initializes */
Office.onReady(async (info) => {
  if (info.host) {
    if (Office.context.requirements.isSetSupported("NestedAppAuth", "1.1")) {
      console.log("NAA supported");
      // Initialize the public client application
      pca = await createNestablePublicClientApplication({
        auth: {
          clientId: ClientId(),
          authority: "https://login.microsoftonline.com/common",
        },
        system: {
          loggerOptions: {
            logLevel: LogLevel.Verbose,
            loggerCallback: (level, message, _containsPii) => {
              switch (level) {
                case LogLevel.Error:
                  console.log(message);
                  return;
                case LogLevel.Info:
                  console.log(message);
                  return;
                case LogLevel.Verbose:
                  console.log(message);
                  return;
                case LogLevel.Warning:
                  console.log(message);
                  return;
              }
            },
          },
        },
      });
    } else {
      console.log("NAA NOT supported");
    }
  }

  const authServices: IAuthService = new AuthService(pca);
  const apiServices: IApiServices = new ApiServices(authServices);

  console.log(Office.context ? "Office context found" : "Office context not found", Office.context);
  console.log(Office.context.mailbox ? "Office mailbox found" : "Office mailbox not found", Office.context.mailbox);
  console.log(Office.context.mailbox.item ? "Office mailbox item found" : "Office mailbox item not found");

  let errorState = null as ErrorState;

  if (Office.context === undefined) {
    errorState = "NO_CONTEXT" as ErrorState;
  } else if (Office.context.mailbox === undefined) {
    errorState = "NO_MAILBOX" as ErrorState;
  }
  if (errorState) {
    root?.render(
      <FluentProvider theme={webLightTheme}>
        <EmptyStateMessage errorState={errorState} />
      </FluentProvider>
    );
  } else {
    Office.context.mailbox.addHandlerAsync(
      Office.EventType.ItemChanged,
      root?.render(<AppWrapper apiServices={apiServices} />)
    );

    root?.render(<AppWrapper apiServices={apiServices} />);
  }
});

if ((module as any).hot) {
  (module as any).hot.accept("./components/App", () => {
    const NextApp = require("./components/App").default;
    root?.render(NextApp);
  });
}
