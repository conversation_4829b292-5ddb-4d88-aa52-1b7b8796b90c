export type SelectedItems = {
  itemId: string;
  itemMode?: string;
  itemType?: string;
  subject: string;
  conversationId?: string;
  internetMessageId?: string;
  item: Office.Item;
};

export type Project = {
  SitePath: string;
  ProjectCode: string;
  ProjectTitle: string;
  Id: string;
  Rank: number;
};

export type Hubsite = {
  Name: string;
  Title: string;
  ID: string;
  WebUrl: string;
  CustomTags: boolean;
};

export type Tag = {
  Name: string;
  Colour: string;
  BackgroundColour: string;
  Order: number;
};

export type UserPermissions = {
  CanFileConfidential: boolean;
  Licensed: boolean;
  CanFile: boolean;
};

export enum ProjectCategory {
  Favourite = "Favourite",
  Standard = "Standard",
}

export type ProjectFilingMeta = {
  isConfidential: boolean;
  isImportant: boolean;
  selectedItems: SelectedItems[] | undefined;
  projectCode: string;
  selectedTag: string;
  sitePath: string;
  siteId: string;
};

export type FiledEmail = {
  ProjectCode: string;
  InternetMessageId: string;
  Tag: string;
  Confidential: boolean;
  Important: boolean;
  SharedMailbox: string | null;
  FiledBy: string;
  Timestamp: string;
};

export type FiledEmailResponse = {
  IsSuccess: boolean;
  Message: string;
  Data: FiledEmail[];
};

export type WebHookSubscription = {
  subscription: string;
};

export enum Tenancy {
  Atvero = "atvero",
  CMap = "cmap",
  CMap_US1 = "cmap_us1",
}

export interface AttachmentWithMeta extends Office.AttachmentDetails {
  recordTitle?: string;
  revision?: string;
}
