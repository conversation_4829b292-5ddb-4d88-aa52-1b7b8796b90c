//Prevents ESLint from reporting an error about Office not being defined
/* global  Office */

import { SelectedItems } from "../types/types";

export const FILED_BY_OTHERS_COLOUR = "Preset4";
export const AUTO_FILED_COLOUR = "Preset7";
export const FILED_IN_TENANCY_COLOUR = "Preset19";

export const removeSelectedItemsChangedHander = (
  updateSelectedCount: (asyncResult: Office.AsyncResult<void>) => void
): void => {
  if (isMobile()) {
    // Not yet supported
    return;
  }

  Office.context.mailbox.removeHandlerAsync(Office.EventType.SelectedItemsChanged, updateSelectedCount);
};

// Be very careful when using this function, you'll need to consider a fallback for
// mobile
export const addSelectedItemsChangedHander = (handler: (asyncResult: Office.AsyncResult<void>) => void): void => {
  // Mobile only supports Mailbox API 1.5 so we can only handle selections of 1
  if (isMobile()) {
    // Not supported
  } else {
    console.log("Desktop: Adding SelectedItemsChanged handler");
    // Register the event handler for selected items change
    Office.context.mailbox.addHandlerAsync(Office.EventType.SelectedItemsChanged, handler, (asyncResult) => {
      if (asyncResult.status !== Office.AsyncResultStatus.Succeeded) {
        console.error("Failed to register SelectedItemsChanged handler:", asyncResult.error.message);
      }
    });
  }
};
export const removeItemChangedHander = (itemChanged: (asyncResult: Office.AsyncResult<void>) => void): void => {
  Office.context.mailbox.removeHandlerAsync(Office.EventType.ItemChanged, itemChanged);
};

// Be very careful when using this function, you'll need to consider a fallback for
// mobile
export const addItemChangedHander = (handler: (asyncResult: Office.AsyncResult<void>) => void): void => {
  // // Mobile only supports Mailbox API 1.5, but still throws errors when we set it

  console.log("Desktop: Adding SelectedItemsChanged handler");
  // Register the event handler for selected items change
  Office.context.mailbox.addHandlerAsync(Office.EventType.ItemChanged, handler, (asyncResult) => {
    if (asyncResult.status !== Office.AsyncResultStatus.Succeeded) {
      console.error("Failed to register SelectedItemsChanged handler:", asyncResult.error.message);
    }
  });
};
export const getSelectedEmails = (): Promise<SelectedItems[]> => {
  const mailbox = Office.context.mailbox;
  const options: Office.AsyncContextOptions = { asyncContext: { currentItem: mailbox } };
  if (isMobile()) {
    console.log("Mobile: returning item");
    // Mobile only supports Mailbox API 1.5 so we can only handle selections of 1
    return new Promise((resolve, _reject) => {
      if (!Office.context.mailbox.item) {
        resolve([]);
        return;
      }
      resolve([
        {
          itemId: Office.context.mailbox.item.itemId,
          itemMode: "Read",
          itemType: Office.context.mailbox.item.itemType,
          subject: Office.context.mailbox.item.subject,
          item: Office.context.mailbox.item,
        },
      ]);
    });
  } else {
    console.log("Desktop: Getting selected items");
    return new Promise((resolve, reject) => {
      const callback = (asyncResult: Office.AsyncResult<Office.SelectedItemDetails[]>) => {
        if (asyncResult.status === Office.AsyncResultStatus.Succeeded) {
          const selectedItems: Office.SelectedItemDetails[] = asyncResult.value;

          const convertedItems: SelectedItems[] = selectedItems.map((item) => ({
            itemId: item.itemId,
            itemMode: item.itemMode,
            itemType: item.itemType,
            subject: item.subject,
            item: item,
          }));

          resolve(convertedItems);
        } else if (Office.context.mailbox.item?.itemId) {
          const itemMode = "to" in Office.context.mailbox.item ? "Compose" : "Read";

          resolve([
            {
              itemId: Office.context.mailbox.item.itemId,
              itemType: Office.context.mailbox.item.itemType,
              subject: Office.context.mailbox.item.subject,
              item: Office.context.mailbox.item,
              itemMode: itemMode,
            },
          ]);
        } else {
          console.error("Error fetching selected items:", asyncResult.error);
          reject(asyncResult.error as Error);
        }
      };

      mailbox.getSelectedItemsAsync(options, callback);
    });
  }
};

export const getAttachmentsForSelectedEmail = async (): Promise<Office.AttachmentDetails[]> => {
  return new Promise((resolve, reject) => {
    const item = Office.context.mailbox?.item;

    if (!item) {
      return reject(new Error("No email item is currently loaded."));
    }

    // Works in Read mode only
    if (item.attachments && Array.isArray(item.attachments)) {
      resolve(item.attachments);
    } else {
      reject(new Error("Attachments are not available. This item may not be in read mode."));
    }
  });
};

export const getSelectedEmailWithIDs = (): Promise<SelectedItems> => {
  const mailbox = Office.context.mailbox;
  const options: Office.AsyncContextOptions = { asyncContext: { currentItem: mailbox } };
  return new Promise((resolve, reject) => {
    const callback = (asyncResult: Office.AsyncResult<Office.SelectedItemDetails[]>) => {
      if (asyncResult.status === Office.AsyncResultStatus.Succeeded) {
        const currentItem = asyncResult.asyncContext.currentItem.item;
        if (currentItem) {
          resolve({
            itemId: currentItem.itemId,
            itemType: currentItem.itemType,
            itemMode: currentItem.itemMode,
            subject: currentItem.subject,
            conversationId: currentItem.conversationId,
            internetMessageId: currentItem.internetMessageId,
            item: currentItem,
          });
        } else {
          reject(new Error("No item found in asyncContext"));
        }
      } else if (Office.context.mailbox.item?.itemId) {
        const itemMode = "to" in Office.context.mailbox.item ? "Compose" : "Read";
        resolve({
          itemId: Office.context.mailbox.item.itemId,
          itemType: Office.context.mailbox.item.itemType,
          itemMode: itemMode,
          subject: Office.context.mailbox.item.subject,
          conversationId: Office.context.mailbox.item.conversationId,
          internetMessageId: Office.context.mailbox.item.internetMessageId,
          item: Office.context.mailbox.item,
        });
      } else {
        console.error("Error fetching selected items:", asyncResult.error);
        reject(asyncResult.error as Error);
      }
    };
    mailbox.getSelectedItemsAsync(options, callback);
  });
};

export const setAtveroCategories = () => {
  const tenancyName = "CMap Mail";

  const CATEGORIES = [
    {
      displayName: `Filed in ${tenancyName}`,
      color: FILED_IN_TENANCY_COLOUR,
    },
    {
      displayName: "Filed by others",
      color: FILED_BY_OTHERS_COLOUR,
    },
    {
      displayName: `Auto Filed by ${tenancyName}`,
      color: AUTO_FILED_COLOUR,
    },
  ];

  Office.context.mailbox.masterCategories.getAsync(function (asyncResult) {
    if (asyncResult.status === Office.AsyncResultStatus.Failed) {
      console.log("Action failed with error: " + asyncResult.error.message);
    } else {
      let masterCategories = asyncResult.value;
      console.log("masterCategories", masterCategories);
      let categoriesToAdd = CATEGORIES.filter(
        (newCat) => !masterCategories.some((existingCat) => existingCat.displayName === newCat.displayName)
      );
      console.log("categoriesToAdd", categoriesToAdd);
      if (categoriesToAdd.length > 0) {
        Office.context.mailbox.masterCategories.addAsync(categoriesToAdd, function (asyncResult) {
          if (asyncResult.status === Office.AsyncResultStatus.Succeeded) {
            console.log("Successfully added categories to master list");
          } else {
            console.log("masterCategories.addAsync call failed with error: " + asyncResult.error.message);
          }
        });
      }
    }
  });
};

export const getEmailCustomProperties = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    // Check if Office.context.mailbox.item is defined
    if (Office.context?.mailbox?.item) {
      // Load the custom properties of the current item (selected email)
      Office.context.mailbox.item.loadCustomPropertiesAsync((asyncResult) => {
        if (asyncResult.status === Office.AsyncResultStatus.Succeeded) {
          const customProps = asyncResult.value.getAll(); // Get all properties as an object
          resolve(customProps);
        } else {
          // Reject with an Error object
          reject(new Error(asyncResult.error.message));
        }
      });
    } else {
      // Reject with an Error object when no mail item is available
      reject(new Error("No mail item is available."));
    }
  });
};

export const isMobile = (): boolean => {
  return (
    Office.context.diagnostics.platform === Office.PlatformType.Android ||
    Office.context.diagnostics.platform === Office.PlatformType.iOS
  );
};
