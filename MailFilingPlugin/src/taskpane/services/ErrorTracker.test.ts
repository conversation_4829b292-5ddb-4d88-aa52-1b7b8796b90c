import { describe, it, expect, vi, beforeEach } from "vitest";
import * as Sen<PERSON> from "@sentry/react";
import { setBreadcrumb, trackError } from "./ErrorTracker";

vi.mock("@sentry/react");

describe("ErrorTracker", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("setBreadcrumb", () => {
    it("should log breadcrumb and add it to Sentry", () => {
      const crumb = "Test breadcrumb";
      const consoleInfoSpy = vi.spyOn(console, "info").mockImplementation(() => {});

      setBreadcrumb(crumb);

      expect(consoleInfoSpy).toHaveBeenCalledWith(crumb);
      expect(Sentry.addBreadcrumb).toHaveBeenCalledWith({
        category: "filing",
        message: crumb,
        level: "info",
      });

      consoleInfoSpy.mockRestore();
    });
  });

  describe("trackError", () => {
    it("should log error message and capture exception if error is an instance of Error", () => {
      const message = "Test error message";
      const error = new Error("Test error");
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      trackError(message, error);

      expect(consoleErrorSpy).toHaveBeenCalledWith(message, error);
      expect(Sentry.captureException).toHaveBeenCalledWith(error);
      expect(Sentry.addBreadcrumb).toHaveBeenCalledWith({
        category: "filing",
        message: `${message}: ${error.message}`,
        level: "info",
      });

      consoleErrorSpy.mockRestore();
    });

    it("should log error message and capture message if error is not an instance of Error", () => {
      const message = "Test error message";
      const error = "Test error";
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      trackError(message, error);

      expect(consoleErrorSpy).toHaveBeenCalledWith(message, error);
      expect(Sentry.captureMessage).toHaveBeenCalledWith(message);
      expect(Sentry.addBreadcrumb).toHaveBeenCalledWith({
        category: "filing",
        message: error,
        level: "info",
      });

      consoleErrorSpy.mockRestore();
    });

    it("should log error message and capture message if no error is provided", () => {
      const message = "Test error message";
      const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      trackError(message);

      expect(consoleErrorSpy).toHaveBeenCalledWith(message, undefined);
      expect(Sentry.captureMessage).toHaveBeenCalledWith(message);

      consoleErrorSpy.mockRestore();
    });
  });
});
