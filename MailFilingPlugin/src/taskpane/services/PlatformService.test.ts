import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { detectPlatform, Platform } from "./PlatformService";

// Define mocked PlatformType enum with proper type casting
const MockedPlatformType = {
  PC: "PC" as unknown as Office.PlatformType,
  iOS: "iOS" as unknown as Office.PlatformType,
  Mac: "Mac" as unknown as Office.PlatformType,
  OfficeOnline: "OfficeOnline" as unknown as Office.PlatformType,
  Android: "Android" as unknown as Office.PlatformType,
};

describe("PlatformService", () => {
  const originalOffice = global.Office;

  beforeEach(() => {
    vi.clearAllMocks();

    // Create a mock Office object with PlatformType
    global.Office = {
      context: {
        diagnostics: {
          platform: undefined,
        },
      },
      PlatformType: MockedPlatformType,
    } as unknown as typeof Office;
  });

  afterEach(() => {
    global.Office = originalOffice;
  });

  it("should detect Windows platform correctly", () => {
    global.Office.context.diagnostics.platform = MockedPlatformType.PC;

    const platform = detectPlatform();

    expect(platform.platform).toBe(MockedPlatformType.PC);
    expect(platform.isWindows).toBe(true);
    expect(platform.isIOS).toBe(false);
    expect(platform.isMac).toBe(false);
    expect(platform.isWeb).toBe(false);
    expect(platform.isMobile).toBe(false);
  });

  it("should detect iOS platform correctly", () => {
    global.Office.context.diagnostics.platform = MockedPlatformType.iOS;

    const platform = detectPlatform();

    expect(platform.platform).toBe(MockedPlatformType.iOS);
    expect(platform.isWindows).toBe(false);
    expect(platform.isIOS).toBe(true);
    expect(platform.isMac).toBe(false);
    expect(platform.isWeb).toBe(false);
    expect(platform.isMobile).toBe(true);
  });

  it("should detect Mac platform correctly", () => {
    global.Office.context.diagnostics.platform = MockedPlatformType.Mac;

    const platform = detectPlatform();

    expect(platform.platform).toBe(MockedPlatformType.Mac);
    expect(platform.isWindows).toBe(false);
    expect(platform.isIOS).toBe(false);
    expect(platform.isMac).toBe(true);
    expect(platform.isWeb).toBe(false);
    expect(platform.isMobile).toBe(false);
  });

  it("should detect Web platform correctly", () => {
    global.Office.context.diagnostics.platform = MockedPlatformType.OfficeOnline;

    const platform = detectPlatform();

    expect(platform.platform).toBe(MockedPlatformType.OfficeOnline);
    expect(platform.isWindows).toBe(false);
    expect(platform.isIOS).toBe(false);
    expect(platform.isMac).toBe(false);
    expect(platform.isWeb).toBe(true);
    expect(platform.isMobile).toBe(false);
  });

  it("should detect Android platform correctly", () => {
    global.Office.context.diagnostics.platform = MockedPlatformType.Android;

    const platform = detectPlatform();

    expect(platform.platform).toBe(MockedPlatformType.Android);
    expect(platform.isWindows).toBe(false);
    expect(platform.isIOS).toBe(false);
    expect(platform.isMac).toBe(false);
    expect(platform.isWeb).toBe(false);
    expect(platform.isMobile).toBe(true);
  });

  it("should handle unknown platform gracefully", () => {
    (global.Office.context.diagnostics as any).platform = "UnknownPlatform";

    const platform = detectPlatform();

    expect(platform.platform).toBe("UnknownPlatform");
    expect(platform.isWindows).toBe(false);
    expect(platform.isIOS).toBe(false);
    expect(platform.isMac).toBe(false);
    expect(platform.isWeb).toBe(false);
    expect(platform.isMobile).toBe(false);
  });

  it("should handle undefined platform gracefully", () => {
    // Platform is already undefined by default
    const platform = detectPlatform();

    expect(platform.platform).toBe("Unknown");
    expect(platform.isWindows).toBe(false);
    expect(platform.isIOS).toBe(false);
    expect(platform.isMac).toBe(false);
    expect(platform.isWeb).toBe(false);
    expect(platform.isMobile).toBe(false);
  });

  it("should handle errors gracefully when Office object is not available", () => {
    // Set up spy to verify proper error handling
    const consoleErrorSpy = vi.spyOn(console, "error");

    // Save original Office.PlatformType before modifying global
    const originalPlatformType = global.Office?.PlatformType;

    // Set Office to undefined but keep the PlatformType to avoid errors
    const mockOffice = global.Office;
    global.Office = undefined as unknown as typeof Office;

    // We need to spy on the detectPlatform implementation
    const platform = detectPlatform();

    // Restore original Office object and PlatformType
    global.Office = mockOffice;

    // Assertion would happen if error handling works properly
    expect(platform.platform).toBe("Unknown");
    expect(platform.isWindows).toBe(false);
    expect(platform.isIOS).toBe(false);
    expect(platform.isMac).toBe(false);
    expect(platform.isWeb).toBe(false);
    expect(platform.isMobile).toBe(false);
  });

  it("should log errors when exceptions occur", () => {
    const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});
    const errorObj = new Error("Test error");

    // Create Office object that throws when accessing platform
    global.Office = {
      context: {
        diagnostics: {
          get platform() {
            throw errorObj;
          },
        },
      },
      PlatformType: MockedPlatformType,
    } as unknown as typeof Office;

    const platform = detectPlatform();

    expect(consoleErrorSpy).toHaveBeenCalledWith("Error detecting platform:", errorObj);
    expect(platform.platform).toBe("Unknown");
  });
});
