export function getFileIconClassFromName(filename: string): string {
  const extension = filename.split(".").pop()?.toLowerCase();

  if (!extension) {
    return "ms-Icon--Document";
  }

  switch (extension) {
    case "pdf":
      return "ms-Icon--PDF";
    case "doc":
    case "docx":
      return "ms-Icon--WordDocument";
    case "xls":
    case "xlsx":
      return "ms-Icon--ExcelDocument";
    case "ppt":
    case "pptx":
      return "ms-Icon--PowerPointDocument";
    case "jpg":
    case "jpeg":
    case "png":
    case "gif":
    case "bmp":
    case "webp":
      return "ms-Icon--FileImage";
    case "zip":
    case "rar":
    case "7z":
      return "ms-Icon--ZipFolder";
    default:
      return "ms-Icon--Document";
  }
}
