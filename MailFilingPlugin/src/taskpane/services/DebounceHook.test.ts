import { renderHook, act } from "@testing-library/react";
import { useDebounce } from "./DebounceHook";

describe("useDebounce", () => {
  it("should return the initial value immediately", () => {
    const { result } = renderHook(() => useDebounce("initial", 500));
    expect(result.current).toBe("initial");
  });

  it("should update the debounced value after the delay", async () => {
    const { result, rerender } = renderHook(({ value }) => useDebounce(value, 500), {
      initialProps: { value: "initial" },
    });

    // Update the value
    rerender({ value: "updated" });

    // Before the delay, the value should still be 'initial'
    expect(result.current).toBe("initial");

    // Fast-forward time by 500ms
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 500));
    });

    // Now the value should be updated
    expect(result.current).toBe("updated");
  });

  it("should cancel previous timeout when value changes quickly", async () => {
    const { result, rerender } = renderHook(({ value }) => useDebounce(value, 500), {
      initialProps: { value: "first" },
    });

    // Update the value before the debounce delay
    rerender({ value: "second" });

    // Fast-forward time by 300ms, less than the debounce delay
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 300));
    });

    // The value should still be 'first' because the debounce timer hasn't completed
    expect(result.current).toBe("first");

    // Fast-forward time by another 300ms (total 600ms > 500ms delay)
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 300));
    });

    // Now the value should be 'second' as the debounce delay has completed
    expect(result.current).toBe("second");
  });
});
