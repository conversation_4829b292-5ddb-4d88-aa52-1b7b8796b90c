import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { formatDate } from "./FormatDate";
import { setDefaultOptions } from "date-fns";
import { enGB } from "date-fns/locale";

describe("formatDate", () => {
  const mockDate = new Date(2024, 3, 10, 12, 0); // April 10, 2024, 12:00 PM

  beforeEach(() => {
    // Mock the current date
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);

    // Set the locale to ensure consistent day names
    setDefaultOptions({ locale: enGB });
    vi.spyOn(navigator, "languages", "get").mockReturnValue([]);
    vi.spyOn(navigator, "language", "get").mockImplementation(() => "en-GB");
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it("formats ISO date string correctly", () => {
    expect(formatDate("2024-03-01T11:43:00Z")).toBe("01/03/2024, 11:43");
  });

  it("handles Date object input", () => {
    const date = new Date(2024, 2, 1, 11, 43);
    expect(formatDate(date)).toBe("01/03/2024, 11:43");
  });

  it("formats already formatted date string correctly", () => {
    expect(formatDate("3/1/2024 11:43 AM")).toBe("01/03/2024, 11:43");
  });

  it('returns "Today" for today\'s date', () => {
    expect(formatDate(new Date(2024, 3, 10, 14, 30))).toBe("Today 14:30");
  });

  it('returns "Yesterday" for yesterday\'s date', () => {
    expect(formatDate(new Date(2024, 3, 9, 14, 30))).toBe("Yesterday 14:30");
  });

  it("returns day name for dates within this week", () => {
    expect(formatDate(new Date(2024, 3, 8, 14, 30))).toBe("Monday 14:30");
  });

  it("returns full date for dates outside this week", () => {
    expect(formatDate(new Date(2024, 3, 1, 14, 30))).toBe("01/04/2024, 14:30");
  });

  it("handles invalid date input", () => {
    expect(formatDate("invalid date")).toBe("Invalid Date");
  });
  it("handles no input", () => {
    //@ts-ignore
    expect(formatDate(null)).toBe("");
    //@ts-ignore
    expect(formatDate(undefined)).toBe("");
  });
});
