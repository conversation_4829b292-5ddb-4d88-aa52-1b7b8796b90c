export const isValidRecordTitle = (text: string): boolean => {
  return !!text.trim() && !/^[\p{Emoji_Presentation}\p{Emoji}\p{Extended_Pictographic}]+$/u.test(text);
};

export const isValidRevision = (text: string): boolean => {
  return !!text.trim() && /^[a-zA-Z0-9.\-]+$/.test(text); // e.g., "A1", "Rev-2"
};

export const isValidFileName = (text: string): boolean => {
  return !!text.trim() && text.includes(".");
};
