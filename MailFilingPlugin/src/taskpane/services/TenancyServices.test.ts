import { describe, it, expect, beforeEach, vi, afterEach } from "vitest";
import { detectTenancy } from "./TenancyService";
import { Tenancy } from "../types/types";

describe("TenancyService", () => {
  const localStorageMock = (() => {
    let store: Record<string, string> = {};
    return {
      getItem: vi.fn((key: string) => store[key] || null),
      setItem: vi.fn((key: string, value: string) => {
        store[key] = value.toString();
      }),
      clear: vi.fn(() => {
        store = {};
      }),
      removeItem: vi.fn((key: string) => {
        delete store[key];
      }),
    };
  })();

  const originalLocation = window.location;

  beforeEach(() => {
    Object.defineProperty(window, "location", {
      value: { hostname: "", pathname: "" },
      writable: true,
      configurable: true,
    });

    Object.defineProperty(window, "localStorage", {
      value: localStorageMock,
      writable: true,
    });

    localStorageMock.clear();
  });

  afterEach(() => {
    Object.defineProperty(window, "location", {
      value: originalLocation,
      writable: true,
      configurable: true,
    });
  });

  it("should return 'cmap' for cmapmail.com domain", () => {
    window.location.hostname = "portal.cmapmail.com";
    expect(detectTenancy()).toBe(Tenancy.CMap);
  });

  it("should return 'cmap_us1' for cmapmail.com domain with us1 path", () => {
    window.location.hostname = "portal.cmapmail.com";
    window.location.pathname = "taskpanel_us1.html";
    expect(detectTenancy()).toBe(Tenancy.CMap_US1);
  });

  it("should return 'atvero' for atveromail.com domain", () => {
    window.location.hostname = "app.atveromail.com";
    expect(detectTenancy()).toBe(Tenancy.Atvero);
  });

  it("should return 'atvero' as default for unknown domains", () => {
    window.location.hostname = "example.com";
    expect(detectTenancy()).toBe(Tenancy.Atvero);
  });

  it("should return forced tenancy from localStorage when available", () => {
    window.location.hostname = "app.cmap.io";

    localStorageMock.setItem("forcedTenancy", Tenancy.Atvero);
    expect(detectTenancy()).toBe(Tenancy.Atvero);
  });

  it("should handle invalid forced tenancy values", () => {
    window.location.hostname = "app.cmap.io";

    localStorageMock.setItem("forcedTenancy", "invalid");

    const result = detectTenancy();
    expect(
      result === Tenancy.CMap || result === Tenancy.Atvero || result === Tenancy.CMap_US1 || result === "invalid"
    ).toBeTruthy();
  });

  it("should prioritize forcedTenancy over hostname detection", () => {
    window.location.hostname = "app.atveromail.com";

    localStorageMock.setItem("forcedTenancy", Tenancy.CMap);
    expect(detectTenancy()).toBe(Tenancy.CMap);
  });
});
