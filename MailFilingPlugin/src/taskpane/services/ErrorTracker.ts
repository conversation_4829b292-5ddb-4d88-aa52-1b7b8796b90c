import * as Sentry from "@sentry/react";

export const setBreadcrumb = (crumb: string) => {
  console.info(crumb);
  Sentry.addBreadcrumb({
    category: "filing",
    message: crumb,
    level: "info",
  });
};

export const trackError = (message: string, error?: any) => {
  console.error(message, error);
  if (error) {
    if (error instanceof Error) {
      message = `${message}: ${error.message}`;
      setBreadcrumb(message);
      Sentry.captureException(error);
    } else {
      setBreadcrumb(error);
      Sentry.captureMessage(message);
    }
  } else {
    Sentry.captureMessage(message);
  }
};
