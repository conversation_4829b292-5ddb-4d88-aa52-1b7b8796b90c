import { describe, it, expect } from "vitest";
import { getSitePath } from "./SharepointHelpers";
describe("getSitePath", () => {
  it("should get the site from a sharepoint url", () => {
    const input = "https://atverodevs.sharepoint.com/sites/MySite";
    const expected = "/sites/MySite";

    expect(getSitePath(input)).toBe(expected);
  });

  it("should get the site from a root sharepoint url", () => {
    const input = "https://atverodevs.sharepoint.com";
    const expected = "/";

    expect(getSitePath(input)).toBe(expected);
  });

  it("should get the site from a root sharepoint url with a trailing slash", () => {
    const input = "https://atverodevs.sharepoint.com/";
    const expected = "/";

    expect(getSitePath(input)).toBe(expected);
  });

  it("should return a sensible value for an invlid url", () => {
    const input = "odevs.sharepoi";
    const expected = undefined;

    expect(getSitePath(input)).toBe(expected);
  });

  it("should return a sensible value for the empty string", () => {
    const input = "";
    const expected = undefined;

    expect(getSitePath(input)).toBe(expected);
  });

  it("should return a sensible value for undefined", () => {
    const input = undefined;
    const expected = undefined;

    expect(getSitePath(input)).toBe(expected);
  });
});
