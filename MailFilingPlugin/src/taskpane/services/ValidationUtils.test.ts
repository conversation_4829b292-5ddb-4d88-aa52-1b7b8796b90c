import { describe, it, expect } from "vitest";
import { isValidRecordTitle, isValidRevision, isValidFileName } from "./ValidationUtils";

describe("Validation Utils", () => {
  describe("isValidRecordTitle", () => {
    it("returns false for empty string or whitespace only", () => {
      expect(isValidRecordTitle("")).toBe(false);
      expect(isValidRecordTitle("   ")).toBe(false);
    });

    it("returns false for emojis only", () => {
      expect(isValidRecordTitle("😂😂")).toBe(false);
      expect(isValidRecordTitle("👍")).toBe(false);
    });

    it("returns true for valid titles", () => {
      expect(isValidRecordTitle("Project Title")).toBe(true);
      expect(isValidRecordTitle("123")).toBe(false);
      expect(isValidRecordTitle("Title with emojis 😊 is invalid")).toBe(true); // contains text + emoji, valid
    });
  });

  describe("isValidRevision", () => {
    it("returns false for empty or whitespace only", () => {
      expect(isValidRevision("")).toBe(false);
      expect(isValidRevision("  ")).toBe(false);
    });

    it("returns false for invalid characters", () => {
      expect(isValidRevision("!!")).toBe(false);
      expect(isValidRevision("Rev!1")).toBe(false);
      expect(isValidRevision("Rev_1")).toBe(false); // underscore not allowed
    });

    it("returns true for valid revisions", () => {
      expect(isValidRevision("A1")).toBe(true);
      expect(isValidRevision("Rev-2")).toBe(true);
      expect(isValidRevision("1.0")).toBe(true);
      expect(isValidRevision("abc123")).toBe(true);
    });
  });

  describe("isValidFileName", () => {
    it("returns false for empty or whitespace only", () => {
      expect(isValidFileName("")).toBe(false);
      expect(isValidFileName("   ")).toBe(false);
    });

    it("returns false if missing extension", () => {
      expect(isValidFileName("filename")).toBe(false);
      expect(isValidFileName("filename.")) // questionable but probably false because no extension after dot
        .toBe(true); // if you want to allow trailing dot, adjust here
    });

    it("returns true for valid file names", () => {
      expect(isValidFileName("file.txt")).toBe(true);
      expect(isValidFileName("document.pdf")).toBe(true);
      expect(isValidFileName("archive.tar.gz")).toBe(true);
      expect(isValidFileName("name.with.many.dots.ext")).toBe(true);
    });
  });
});
