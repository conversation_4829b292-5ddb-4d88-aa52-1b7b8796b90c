import { describe, it, expect } from "vitest";
import { AttachmentWithMeta } from "../types/types";
import { handleAttachmentSaveLogic } from "./ProjectUtils";

describe("handleAttachmentSaveLogic", () => {
  it("updates attachment and selected list when name changes", () => {
    const attachments = [{ name: "old.pdf", recordTitle: "Title", revision: "1" }] as AttachmentWithMeta[];
    const selected = ["old.pdf"];
    const editing = attachments[0];

    const {
      updated,
      selected: updatedSelected,
      editing: updatedEditing,
    } = handleAttachmentSaveLogic(attachments, selected, editing, "new.pdf", "New Title", "2");

    expect(updated[0].name).toBe("new.pdf");
    expect(updated[0].recordTitle).toBe("New Title");
    expect(updated[0].revision).toBe("2");
    expect(updatedSelected).toEqual(["new.pdf"]);
    expect(updatedEditing).toBeNull();
  });

  it("updates attachment but keeps selected list when name is unchanged", () => {
    const attachments = [{ name: "same.pdf", recordTitle: "Title", revision: "1" }] as AttachmentWithMeta[];
    const selected = ["same.pdf"];
    const editing = attachments[0];

    const result = handleAttachmentSaveLogic(attachments, selected, editing, "same.pdf", "Updated Title", "3");

    expect(result.updated[0].name).toBe("same.pdf");
    expect(result.updated[0].recordTitle).toBe("Updated Title");
    expect(result.updated[0].revision).toBe("3");
    expect(result.selected).toEqual(["same.pdf"]); // unchanged
    expect(result.editing).toBeNull();
  });

  it("returns original attachments and selected when editing is null", () => {
    const attachments = [{ name: "file.pdf", recordTitle: "Title", revision: "1" }] as AttachmentWithMeta[];
    const selected = ["file.pdf"];
    const editing = null;

    const result = handleAttachmentSaveLogic(attachments, selected, editing, "new.pdf", "New Title", "2");

    expect(result.updated).toBe(attachments);
    expect(result.selected).toBe(selected);
    expect(result.editing).toBeNull();
  });

  it("returns early if editing is null", () => {
    const attachments = [{ name: "doc.pdf", recordTitle: "Old", revision: "1" }] as AttachmentWithMeta[];
    const selected = ["doc.pdf"];

    const result = handleAttachmentSaveLogic(attachments, selected, null, "new.pdf", "New Title", "2");

    expect(result).toEqual({
      updated: attachments,
      selected,
      editing: null,
    });
  });

  it("returns same selected list if newName equals editing.name", () => {
    const attachments = [{ name: "doc.pdf", recordTitle: "Old", revision: "1" }] as AttachmentWithMeta[];
    const selected = ["doc.pdf"];
    const editing = attachments[0];

    const result = handleAttachmentSaveLogic(attachments, selected, editing, "doc.pdf", "Updated Title", "2");

    expect(result.updated[0].recordTitle).toBe("Updated Title");
    expect(result.selected).toBe(selected); // same reference
    expect(result.editing).toBeNull();
  });

  it("does not update selected list if newName is same as editing.name", () => {
    const attachments = [{ name: "same.pdf", recordTitle: "Old", revision: "1" }] as AttachmentWithMeta[];

    const selected = ["same.pdf"];
    const editing = attachments[0];

    const result = handleAttachmentSaveLogic(
      attachments,
      selected,
      editing,
      "same.pdf", // same name
      "Updated Title",
      "2"
    );

    expect(result.updated[0].recordTitle).toBe("Updated Title");
    expect(result.updated[0].revision).toBe("2");
    expect(result.selected).toEqual(["same.pdf"]); // should remain unchanged
    expect(result.editing).toBeNull();
  });

  it("leaves unmatched attachments unchanged", () => {
    const attachments = [
      { name: "doc1.pdf", recordTitle: "Title1", revision: "1" },
      { name: "doc2.pdf", recordTitle: "Title2", revision: "2" },
    ] as AttachmentWithMeta[];

    const selected = ["doc2.pdf"];
    const editing = attachments[1]; // editing doc2.pdf

    const result = handleAttachmentSaveLogic(attachments, selected, editing, "updated2.pdf", "Updated Title 2", "3");

    // doc1.pdf should be unchanged
    expect(result.updated[0]).toEqual(attachments[0]);

    // doc2.pdf should be updated
    expect(result.updated[1].name).toBe("updated2.pdf");
    expect(result.updated[1].recordTitle).toBe("Updated Title 2");
    expect(result.updated[1].revision).toBe("3");

    expect(result.selected).toEqual(["updated2.pdf"]);
    expect(result.editing).toBeNull();
  });

  it("does not replace unmatched names in selected list", () => {
    const attachments = [{ name: "doc2.pdf", recordTitle: "Old", revision: "1" }] as AttachmentWithMeta[];
    const selected = ["unrelated.pdf", "doc2.pdf"];
    const editing = attachments[0];

    const result = handleAttachmentSaveLogic(
      attachments,
      selected,
      editing,
      "updated2.pdf", // new name
      "Updated Title",
      "3"
    );

    expect(result.selected).toEqual(["unrelated.pdf", "updated2.pdf"]);
  });
});
