import { Tenancy } from "../types/types";

/**
 * Detects which tenancy the application is running in based on the hostname
 * @returns The detected tenancy: "cmap" or "atvero"
 */
export const detectTenancy = (): Tenancy => {
  // For local development or testing, we might want to allow forcing a tenancy
  const forcedTenancy = localStorage.getItem("forcedTenancy") as Tenancy | null;
  if (forcedTenancy) {
    return forcedTenancy;
  }

  const hostname = window.location.hostname;
  const pathname = window.location.pathname;

  if (pathname?.includes("us1")) {
    return Tenancy.CMap_US1;
  }

  if (hostname.includes("cmapmail.com")) {
    return Tenancy.CMap;
  }

  if (hostname.includes("atveromail.com")) {
    return Tenancy.Atvero;
  }

  return Tenancy.Atvero;
};
