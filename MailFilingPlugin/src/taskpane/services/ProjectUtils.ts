import { AttachmentWithMeta } from "../types/types";
export const handleAttachmentSaveLogic = (
  attachments: AttachmentWithMeta[],
  selected: string[],
  editing: AttachmentWithMeta | null,
  newName: string,
  title: string,
  revision: string
) => {
  if (!editing) return { updated: attachments, selected, editing: null };

  const updated = attachments.map((att) =>
    att.name === editing.name ? { ...att, name: newName, recordTitle: title, revision } : att
  );

  const updatedSelected =
    newName !== editing.name ? selected.map((name) => (name === editing.name ? newName : name)) : selected;

  return { updated, selected: updatedSelected, editing: null };
};
