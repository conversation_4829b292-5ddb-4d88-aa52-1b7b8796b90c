//Prevents ESLint from reporting an error about Office not being defined
/* global  Office */
import { describe, it, expect, vi, beforeEach } from "vitest";
import { SelectedItems } from "../types/types";
import {
  getSelectedEmails,
  setAtveroCategories,
  getEmailCustomProperties,
  getSelectedEmailWithIDs,
  addSelectedItemsChangedHander,
  removeSelectedItemsChangedHander,
  addItemChangedHander,
  removeItemChangedHander,
} from "./OfficeServices";

import OfficeAddinMock from "office-addin-mock";

// Mock data for the test
const mockData = {
  host: "outlook",
  PlatformType: {
    PC: "PC",
    iOS: "iOS",
    Mac: "Mac",
    OfficeOnline: "OfficeOnline",
    Android: "Android",
  },
  AsyncResultStatus: {
    Succeeded: "succeeded",
    Failed: "failed",
  },
  context: {
    diagnostics: {
      platform: "OfficeOnline",
    },
    mailbox: {
      item: {
        loadCustomPropertiesAsync: vi.fn(),
      },
      getSelectedItemsAsync: vi.fn().mockImplementation((_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "succeeded",
          value: [
            {
              itemId: "item1",
              itemMode: "read",
              itemType: "message",
              subject: "Test Email 1",
              conversationId: "conv1",
              internetMessageId: "conv1",
            },
          ],
        };
        callback(asyncResult);
      }),
      masterCategories: {
        getAsync: vi.fn().mockImplementation((callback: any) => {
          const asyncResult = {
            status: "succeeded",
            value: [
              { displayName: "Red category", color: "Preset0" },
              { displayName: "Orange category", color: "Preset1" },
              { displayName: "Yellow category", color: "Preset3" },
              { displayName: "Green category", color: "Preset4" },
              { displayName: "Blue category", color: "Preset7" },
              { displayName: "Purple category", color: "Preset8" },
              { displayName: "Sausages", color: "Preset19" },
            ],
          };
          callback(asyncResult);
        }),
        addAsync: vi.fn().mockImplementation((categories, callback) => {
          const asyncResult = {
            status: "succeeded",
            value: categories,
          };
          callback(asyncResult);
        }),
      },
    },
  },
};

const officeMock = new OfficeAddinMock.OfficeMockObject(mockData);
global.Office = officeMock as any;

Object.defineProperty(window, "location", {
  value: {
    hostname: "",
  },
  writable: true,
});

describe("getSelectedEmails", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should return the selected emails with correct properties", async () => {
    // Setup mock implementation for getSelectedItemsAsync
    officeMock.context.mailbox.getSelectedItemsAsync.mockImplementation(
      (_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "succeeded",
          value: [
            {
              itemId: "item1",
              itemMode: "read",
              itemType: "message",
              subject: "Test Email 1",
              conversationId: "conv1",
              internetMessageId: "msg1",
            },
          ],
        };
        callback(asyncResult);
      }
    );

    const expected: SelectedItems[] = [
      {
        itemId: "item1",
        itemType: "message",
        subject: "Test Email 1",
        itemMode: "read",

        item: {
          itemId: "item1",
          itemMode: "read",
          itemType: "message",
          subject: "Test Email 1",
          conversationId: "conv1",
          internetMessageId: "msg1",
        },
      },
    ];

    const result = await getSelectedEmails();
    expect(result).toEqual(expected);
  });

  it("should handle multiple selected emails", async () => {
    // Setup mock implementation for getSelectedItemsAsync with multiple items
    officeMock.context.mailbox.getSelectedItemsAsync.mockImplementation(
      (_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "succeeded",
          value: [
            {
              itemId: "item1",
              itemMode: "read",
              itemType: "message",
              subject: "Test Email 1",
              conversationId: "conv1",
              internetMessageId: "msg1",
            },
            {
              itemId: "item2",
              itemMode: "read",
              itemType: "message",
              subject: "Test Email 2",
              conversationId: "conv2",
              internetMessageId: "msg2",
            },
          ],
        };
        callback(asyncResult);
      }
    );

    const result = await getSelectedEmails();
    expect(result).toHaveLength(2);
    expect(result[0].itemId).toBe("item1");
    expect(result[1].itemId).toBe("item2");
  });

  it("should handle errors correctly", async () => {
    officeMock.context.mailbox.getSelectedItemsAsync.mockImplementation(
      (_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "failed",
          error: new Error("Test Error"),
        };
        callback(asyncResult);
      }
    );

    await expect(getSelectedEmails()).rejects.toThrow("Test Error");
  });

  it("should fallback to Office.context.mailbox.item when operation fails but item is available", async () => {
    officeMock.context.mailbox.getSelectedItemsAsync.mockImplementation(
      (_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "failed",
          error: { message: "Failed to get selected items, but we have a fallback" },
        };
        callback(asyncResult);
      }
    );

    const item = {
      itemId: "item2",
      itemType: "message",
      subject: "Fallback Email",
      conversationId: "conv2",
      internetMessageId: "msg2",
      loadCustomPropertiesAsync: vi.fn(),
    };

    officeMock.context.mailbox.item = item;

    const expected: SelectedItems[] = [
      {
        itemId: "item2",
        itemType: "message",
        itemMode: "Read",
        subject: "Fallback Email",
        item: item,
      },
    ];

    const result = await getSelectedEmails();
    expect(result).toEqual(expected);
  });

  it("should return an empty array when no items are selected and no fallback is available", async () => {
    officeMock.context.mailbox.getSelectedItemsAsync.mockImplementation(
      (_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "succeeded",
          value: [],
        };
        callback(asyncResult);
      }
    );

    // Remove the mailbox item to test the case where no fallback is available
    const originalItem = officeMock.context.mailbox.item;
    delete officeMock.context.mailbox.item;

    try {
      const result = await getSelectedEmails();
      expect(result).toEqual([]);
    } finally {
      // Restore the mailbox item
      officeMock.context.mailbox.item = originalItem;
    }
  });

  it("should handle mobile platform correctly", async () => {
    // Mock isMobile to return true
    officeMock.context.diagnostics.platform = "iOS";

    // Setup a mock item for mobile
    const mobileItem = {
      itemId: "mobileItem1",
      itemType: "message",
      subject: "Mobile Email",
      conversationId: "mobileConv1",
      internetMessageId: "mobileMsgId1",
    };

    officeMock.context.mailbox.item = mobileItem;

    const expected: SelectedItems[] = [
      {
        itemId: "mobileItem1",
        itemType: "message",
        itemMode: "Read",
        subject: "Mobile Email",
        item: mobileItem,
      },
    ];

    const result = await getSelectedEmails();

    expect(result).toEqual(expected);

    // Reset the mock
    vi.resetModules();
  });

  it("should return an empty array on mobile when no item is available", async () => {
    // Mock isMobile to return true
    officeMock.context.diagnostics.platform = "iOS";

    // Remove the mailbox item
    const originalItem = officeMock.context.mailbox.item;
    delete officeMock.context.mailbox.item;

    try {
      // We need to re-import getSelectedEmails to get the mocked version
      const { getSelectedEmails: mockedGetSelectedEmails } = await import("./OfficeServices");
      const result = await mockedGetSelectedEmails();

      expect(result).toEqual([]);
    } finally {
      // Restore the mailbox item
      officeMock.context.mailbox.item = originalItem;

      // Reset the mock
      vi.resetModules();
    }
  });
});

describe("getSelectedEmailWithIDs", () => {
  it("should return the selected email with all IDs when operation succeeds", async () => {
    const item = {
      itemId: "item1",
      itemType: "message",
      subject: "Test Email 1",
      conversationId: "conv1",
      internetMessageId: "msg1",
    };
    // Mock implementation for getSelectedItemsAsync
    officeMock.context.mailbox.getSelectedItemsAsync.mockImplementation(
      (_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "succeeded",
          asyncContext: {
            currentItem: {
              item: item,
            },
          },
        };
        callback(asyncResult);
      }
    );

    const expected: SelectedItems = {
      itemId: "item1",
      itemType: "message",
      subject: "Test Email 1",
      conversationId: "conv1",
      internetMessageId: "msg1",
      item: item,
    };

    const result = await getSelectedEmailWithIDs();
    expect(result).toEqual(expected);
  });

  it("should fallback to Office.context.mailbox.item when operation fails but item is available", async () => {
    officeMock.context.mailbox.getSelectedItemsAsync.mockImplementation(
      (_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "failed",
          error: { message: "Failed to get selected items, but we have a fallback" },
        };
        callback(asyncResult);
      }
    );

    const item = {
      itemId: "item2",
      itemType: "message",
      subject: "Fallback Email",
      conversationId: "conv2",
      internetMessageId: "msg2",
      loadCustomPropertiesAsync: vi.fn(),
    };

    officeMock.context.mailbox.item = item;

    const expected: SelectedItems = {
      itemId: "item2",
      itemType: "message",
      itemMode: "Read",
      subject: "Fallback Email",
      conversationId: "conv2",
      internetMessageId: "msg2",
      item: item,
    };

    const result = await getSelectedEmailWithIDs();
    expect(result).toEqual(expected);
  });

  it("should reject with error when operation fails", async () => {
    officeMock.context.mailbox.getSelectedItemsAsync.mockImplementation(
      (_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "failed",
          error: new Error("Failed to get selected emails"),
        };
        callback(asyncResult);
      }
    );

    delete officeMock.context.mailbox.item;

    await expect(getSelectedEmailWithIDs()).rejects.toThrow("Failed to get selected emails");
  });

  it("should reject with specific error when no item found in asyncContext", async () => {
    officeMock.context.mailbox.getSelectedItemsAsync.mockImplementation(
      (_options: Office.AsyncContextOptions, callback: any) => {
        const asyncResult = {
          status: "succeeded",
          asyncContext: {
            currentItem: {
              item: null,
            },
          },
        };
        callback(asyncResult);
      }
    );

    const originalItem = officeMock.context.mailbox.item;

    try {
      delete officeMock.context.mailbox.item;

      await expect(getSelectedEmailWithIDs()).rejects.toThrow("No item found in asyncContext");
    } finally {
      officeMock.context.mailbox.item = originalItem;
    }
  });
});

describe("setAtveroCategories", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
  });

  it("should set all categories for CMap tenancy if none exist", async () => {
    officeMock.context.mailbox.masterCategories.getAsync.mockImplementation((callback: any) => {
      const asyncResult = {
        status: "succeeded",
        value: [
          { displayName: "Red category", color: "Preset0" },
          { displayName: "Orange category", color: "Preset1" },
        ],
      };
      callback(asyncResult);
    });

    const test: any = vi.fn();
    officeMock.context.mailbox.masterCategories.addAsync.mockImplementation(test);

    setAtveroCategories();
    expect(test).toHaveBeenCalledWith(
      [
        {
          displayName: "Filed in CMap Mail",
          color: "Preset19",
        },
        {
          displayName: "Filed by others",
          color: "Preset4",
        },
        {
          displayName: "Auto Filed by CMap Mail",
          color: "Preset7",
        },
      ],
      expect.any(Function)
    );
  });

  it("should set all categories for CMap tenancy if none exist", async () => {
    // Set up hostname for CMap
    window.location.hostname = "app.cmapmail.com";

    officeMock.context.mailbox.masterCategories.getAsync.mockImplementation((callback: any) => {
      const asyncResult = {
        status: "succeeded",
        value: [
          { displayName: "Red category", color: "Preset0" },
          { displayName: "Orange category", color: "Preset1" },
        ],
      };
      callback(asyncResult);
    });

    const test: any = vi.fn();
    officeMock.context.mailbox.masterCategories.addAsync.mockImplementation(test);

    setAtveroCategories();
    expect(test).toHaveBeenCalledWith(
      [
        {
          displayName: "Filed in CMap Mail",
          color: "Preset19",
        },
        {
          displayName: "Filed by others",
          color: "Preset4",
        },
        {
          displayName: "Auto Filed by CMap Mail",
          color: "Preset7",
        },
      ],
      expect.any(Function)
    );
  });

  it("should only set missing categories for CMap tenancy", async () => {
    officeMock.context.mailbox.masterCategories.getAsync.mockImplementation((callback: any) => {
      const asyncResult = {
        status: "succeeded",
        value: [
          { displayName: "Filed in CMap Mail", color: "Preset19" },
          { displayName: "Filed by others", color: "Preset4" },
        ],
      };
      callback(asyncResult);
    });

    const test: any = vi.fn();
    officeMock.context.mailbox.masterCategories.addAsync.mockImplementation(test);

    setAtveroCategories();
    expect(test).toHaveBeenCalledWith(
      [
        {
          displayName: "Auto Filed by CMap Mail",
          color: "Preset7",
        },
      ],
      expect.any(Function)
    );
  });

  it("should set the Atvero Mail categories if the Atvero ones already exist for Atvero tenancy", async () => {
    // Set up hostname for Atvero
    window.location.hostname = "app.atveromail.com";

    officeMock.context.mailbox.masterCategories.getAsync.mockImplementation((callback: any) => {
      const asyncResult = {
        status: "succeeded",
        value: [
          { displayName: "Filed in Atvero", color: "Preset19" },
          { displayName: "Filed by others", color: "Preset4" },
          { displayName: "Auto Filed by Atvero", color: "Preset7" },
        ],
      };
      callback(asyncResult);
    });

    const test: any = vi.fn();
    officeMock.context.mailbox.masterCategories.addAsync.mockImplementation(test);
    setAtveroCategories();
    expect(test).toHaveBeenCalledTimes(1);
  });

  it("should not set any categories if all already exist for CMap tenancy", async () => {
    // Set up hostname for CMap
    window.location.hostname = "app.cmapmail.com";

    officeMock.context.mailbox.masterCategories.getAsync.mockImplementation((callback: any) => {
      const asyncResult = {
        status: "succeeded",
        value: [
          { displayName: "Filed in CMap Mail", color: "Preset19" },
          { displayName: "Filed by others", color: "Preset4" },
          { displayName: "Auto Filed by CMap Mail", color: "Preset7" },
        ],
      };
      callback(asyncResult);
    });

    const test: any = vi.fn();
    officeMock.context.mailbox.masterCategories.addAsync.mockImplementation(test);
    setAtveroCategories();
    expect(test).toHaveBeenCalledTimes(0);
  });

  it("should fail silently if it can't get the categories", async () => {
    officeMock.context.mailbox.masterCategories.getAsync.mockImplementation((callback: any) => {
      const asyncResult = {
        status: Office.AsyncResultStatus.Failed,
        error: new Error("Test Error"),
      };
      callback(asyncResult);
    });

    const test: any = vi.fn();
    officeMock.context.mailbox.masterCategories.addAsync.mockImplementation(test);
    setAtveroCategories();
    expect(test).toHaveBeenCalledTimes(0);
  });

  it("should fail silently if it can't set the categories", async () => {
    // Set up hostname for CMap
    window.location.hostname = "app.cmapmail.com";

    officeMock.context.mailbox.masterCategories.getAsync.mockImplementation((callback: any) => {
      const asyncResult = {
        status: "succeeded",
        value: [{ displayName: "Red category", color: "Preset0" }],
      };
      callback(asyncResult);
    });

    const test: any = vi.fn((_: any, callback: any) => {
      const asyncResult = {
        status: Office.AsyncResultStatus.Failed,
        error: new Error("Test Error"),
      };
      callback(asyncResult);
    });

    officeMock.context.mailbox.masterCategories.addAsync.mockImplementation(test);
    setAtveroCategories();
    expect(test).toHaveBeenCalled();
  });
});

describe("getEmailCustomProperties", () => {
  beforeEach(() => {
    if (!officeMock.context.mailbox.item) {
      officeMock.context.mailbox.item = {
        loadCustomPropertiesAsync: vi.fn(),
      };
    }
  });

  it("should resolve with custom properties when the operation succeeds", async () => {
    officeMock.context.mailbox.item.loadCustomPropertiesAsync.mockImplementation((callback: any) => {
      const asyncResult = {
        status: Office.AsyncResultStatus.Succeeded,
        value: {
          getAll: () => ({
            customProp1: "value1",
            customProp2: "value2",
          }),
        },
      };
      callback(asyncResult);
    });

    const result = await getEmailCustomProperties();
    expect(result).toEqual({
      customProp1: "value1",
      customProp2: "value2",
    });
  });

  it("should reject with an error when the operation fails", async () => {
    officeMock.context.mailbox.item.loadCustomPropertiesAsync.mockImplementation((callback: any) => {
      const asyncResult = {
        status: Office.AsyncResultStatus.Failed,
        error: { message: "Test error" },
      };
      callback(asyncResult);
    });

    await expect(getEmailCustomProperties()).rejects.toThrow("Test error");
  });

  it("should reject with an error if no mail item is available", async () => {
    delete officeMock.context.mailbox.item;
    await expect(getEmailCustomProperties()).rejects.toThrow("No mail item is available.");
  });
});

describe("Event handlers", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("addSelectedItemsChangedHander", () => {
    it("should register a handler for SelectedItemsChanged event on desktop", () => {
      // Mock that we're not on mobile
      const officeMock = new OfficeAddinMock.OfficeMockObject({
        host: "outlook",
        EventType: { SelectedItemsChanged: "selectedItemsChanged" },
        AsyncResultStatus: {
          Failed: "failed",
        },
        PlatformType: {
          PC: "PC",
          iOS: "iOS",
          Mac: "Mac",
          OfficeOnline: "OfficeOnline",
          Android: "Android",
        },
        context: {
          diagnostics: { platform: "PC" },
          mailbox: {
            removeHandlerAsync: vi.fn(),

            addHandlerAsync: vi.fn().mockImplementationOnce((_eventType, _handler, callback) => {
              callback({
                status: Office.AsyncResultStatus.Failed,
                error: { message: "Failed to register handler" },
              });
            }),
          },
        },
      });
      global.Office = officeMock as any;
      const mockHandler = vi.fn();
      addSelectedItemsChangedHander(mockHandler);

      expect(officeMock.context.mailbox.addHandlerAsync).toHaveBeenCalledWith(
        Office.EventType.SelectedItemsChanged,
        mockHandler,
        expect.any(Function)
      );
    });

    it("should not register a handler for SelectedItemsChanged event on mobile", () => {
      // Mock that we're on mobile
      const officeMock = new OfficeAddinMock.OfficeMockObject({
        host: "outlook",
        EventType: { SelectedItemsChanged: "selectedItemsChanged" },
        AsyncResultStatus: {
          Failed: "failed",
        },
        PlatformType: {
          PC: "PC",
          iOS: "iOS",
          Mac: "Mac",
          OfficeOnline: "OfficeOnline",
          Android: "Android",
        },
        context: {
          diagnostics: { platform: "iOS" },
          mailbox: {
            removeHandlerAsync: vi.fn(),

            addHandlerAsync: vi.fn().mockImplementationOnce((_eventType, _handler, callback) => {
              callback({
                status: Office.AsyncResultStatus.Failed,
                error: { message: "Failed to register handler" },
              });
            }),
          },
        },
      });
      global.Office = officeMock as any;

      const mockHandler = vi.fn();
      addSelectedItemsChangedHander(mockHandler);

      expect(officeMock.context.mailbox.addHandlerAsync).not.toHaveBeenCalled();
    });
  });

  describe("removeSelectedItemsChangedHander", () => {
    it("should not remove a handler for SelectedItemsChanged event on mobile", () => {
      const officeMock = new OfficeAddinMock.OfficeMockObject({
        host: "outlook",
        EventType: { SelectedItemsChanged: "selectedItemsChanged" },
        AsyncResultStatus: {
          Failed: "failed",
        },
        PlatformType: {
          PC: "PC",
          iOS: "iOS",
          Mac: "Mac",
          OfficeOnline: "OfficeOnline",
          Android: "Android",
        },
        context: {
          diagnostics: { platform: "iOS" },
          mailbox: {
            removeHandlerAsync: vi.fn(),

            addHandlerAsync: vi.fn().mockImplementationOnce((_eventType, _handler, callback) => {
              callback({
                status: Office.AsyncResultStatus.Failed,
                error: { message: "Failed to register handler" },
              });
            }),
          },
        },
      });
      global.Office = officeMock as any;
      const mockHandler = vi.fn();
      removeSelectedItemsChangedHander(mockHandler);

      expect(officeMock.context.mailbox.removeHandlerAsync).not.toHaveBeenCalled();
    });
  });

  describe("addItemChangedHander", () => {
    it("should register a handler for ItemChanged event", () => {
      const officeMock = new OfficeAddinMock.OfficeMockObject({
        host: "outlook",
        EventType: { ItemChanged: "itemChanged" },
        AsyncResultStatus: {
          Failed: "failed",
        },
        context: {
          mailbox: {
            removeHandlerAsync: vi.fn(),

            addHandlerAsync: vi.fn().mockImplementationOnce((_eventType, _handler, callback) => {
              callback({
                status: Office.AsyncResultStatus.Failed,
                error: { message: "Failed to register handler" },
              });
            }),
          },
        },
      });

      global.Office = officeMock as any;

      const mockHandler = vi.fn();
      addItemChangedHander(mockHandler);

      expect(officeMock.context.mailbox.addHandlerAsync).toHaveBeenCalledWith(
        Office.EventType.ItemChanged,
        mockHandler,
        expect.any(Function)
      );
    });

    it("should log an error when registration fails", () => {
      // Mock the addHandlerAsync to fail

      const officeMock = new OfficeAddinMock.OfficeMockObject({
        host: "outlook",
        EventType: { ItemChanged: "itemChanged" },
        AsyncResultStatus: {
          Failed: "failed",
        },
        context: {
          mailbox: {
            removeHandlerAsync: vi.fn(),

            addHandlerAsync: vi.fn().mockImplementationOnce((_eventType, _handler, callback) => {
              callback({
                status: Office.AsyncResultStatus.Failed,
                error: { message: "Failed to register handler" },
              });
            }),
          },
        },
      });

      global.Office = officeMock as any;

      const consoleSpy = vi.spyOn(console, "error");
      const mockHandler = vi.fn();

      addItemChangedHander(mockHandler);

      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to register SelectedItemsChanged handler:",
        "Failed to register handler"
      );
    });
  });

  describe("removeItemChangedHander", () => {
    it("should remove a handler for ItemChanged event", () => {
      const officeMock = new OfficeAddinMock.OfficeMockObject({
        host: "outlook",
        EventType: { ItemChanged: "itemChanged" },
        context: {
          mailbox: {
            removeHandlerAsync: vi.fn(),
          },
        },
      });
      global.Office = officeMock as any;

      const mockHandler = vi.fn();
      removeItemChangedHander(mockHandler);

      expect(officeMock.context.mailbox.removeHandlerAsync).toHaveBeenCalledWith(
        Office.EventType.ItemChanged,
        mockHandler
      );
    });
  });
});
