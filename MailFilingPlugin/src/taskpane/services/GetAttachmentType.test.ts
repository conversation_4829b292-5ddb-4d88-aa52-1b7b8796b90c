import { describe, it, expect } from "vitest";
import { getFileIconClassFromName } from "./GetAttachmentType";

describe("getFileIconClassFromName", () => {
  it("returns PDF icon for pdf files", () => {
    expect(getFileIconClassFromName("file.pdf")).toBe("ms-Icon--PDF");
  });

  it("returns WordDocument icon for doc and docx files", () => {
    expect(getFileIconClassFromName("file.doc")).toBe("ms-Icon--WordDocument");
    expect(getFileIconClassFromName("file.docx")).toBe("ms-Icon--WordDocument");
  });

  it("returns ExcelDocument icon for xls and xlsx files", () => {
    expect(getFileIconClassFromName("file.xls")).toBe("ms-Icon--ExcelDocument");
    expect(getFileIconClassFromName("file.xlsx")).toBe("ms-Icon--ExcelDocument");
  });

  it("returns PowerPointDocument icon for ppt and pptx files", () => {
    expect(getFileIconClassFromName("file.ppt")).toBe("ms-Icon--PowerPointDocument");
    expect(getFileIconClassFromName("file.pptx")).toBe("ms-Icon--PowerPointDocument");
  });

  it("returns FileImage icon for common image files", () => {
    expect(getFileIconClassFromName("photo.jpg")).toBe("ms-Icon--FileImage");
    expect(getFileIconClassFromName("photo.jpeg")).toBe("ms-Icon--FileImage");
    expect(getFileIconClassFromName("photo.png")).toBe("ms-Icon--FileImage");
    expect(getFileIconClassFromName("photo.gif")).toBe("ms-Icon--FileImage");
    expect(getFileIconClassFromName("photo.bmp")).toBe("ms-Icon--FileImage");
    expect(getFileIconClassFromName("photo.webp")).toBe("ms-Icon--FileImage");
  });

  it("returns ZipFolder icon for archive files", () => {
    expect(getFileIconClassFromName("archive.zip")).toBe("ms-Icon--ZipFolder");
    expect(getFileIconClassFromName("archive.rar")).toBe("ms-Icon--ZipFolder");
    expect(getFileIconClassFromName("archive.7z")).toBe("ms-Icon--ZipFolder");
  });

  it("returns Document icon for unknown or no extension files", () => {
    expect(getFileIconClassFromName("file.unknown")).toBe("ms-Icon--Document");
    expect(getFileIconClassFromName("file")).toBe("ms-Icon--Document");
    expect(getFileIconClassFromName("")).toBe("ms-Icon--Document");
  });
});
