export type Platform = {
  platform: Office.PlatformType | string;
  isIOS: boolean;
  isMac: boolean;
  isWindows: boolean;
  isWeb: boolean;
  isMobile: boolean;
};

/**
 * Detects the platform the user is accessing the application from using Office context
 * @returns Platform object with platform information
 */
export const detectPlatform = (): Platform => {
  let platform: Office.PlatformType | string = "Unknown";

  try {
    if (typeof Office !== "undefined" && Office?.context?.diagnostics?.platform) {
      platform = Office.context.diagnostics.platform;
    }
  } catch (error) {
    console.error("Error detecting platform:", error);
  }

  return {
    platform,
    isIOS: platform === "iOS",
    isMac: platform === "Mac",
    isWindows: platform === "PC",
    isWeb: platform === "OfficeOnline",
    isMobile: platform === "iOS" || platform === "Android",
  };
};
