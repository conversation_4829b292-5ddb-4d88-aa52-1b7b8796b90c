import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import React, { useEffect, useState } from "react";
import App from "./components/App";
import { IApiServices } from "./adapters/IApiServices";

interface AppWrapper {
  apiServices: IApiServices;
}

const AppWrapper: React.FC<AppWrapper> = ({ apiServices }) => {
  const [mode, setMode] = useState<string>("read");

  useEffect(() => {
    Office.context.mailbox.item?.getInitializationContextAsync((res) => {
      if (res.value === "onsend") {
        console.log("opened by compose command");
        setMode("onsend");
      } else setMode("read");
    });
  }, [Office.context.mailbox.item]);

  return (
    <FluentProvider theme={webLightTheme}>
      <App apiServices={apiServices} mode={mode} />
    </FluentProvider>
  );
};

export default AppWrapper;
