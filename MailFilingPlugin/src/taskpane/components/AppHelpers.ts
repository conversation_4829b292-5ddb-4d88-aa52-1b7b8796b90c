import { IApiServices } from "../adapters/IApiServices";
import { ProjectFilingMeta, SelectedItems } from "../types/types";
import * as Sentry from "@sentry/react";

export const prependSubject = (project: string, subject: string) => {
  if (subject.startsWith("[")) {
    const endPos = subject.indexOf("]");
    if (endPos > 0) {
      return "[" + project + "]" + subject.slice(endPos + 1);
    } else return "[" + project + "] " + subject;
  } else {
    return "[" + project + "] " + subject;
  }
};

const setComposeMetadata = async (apiServices: IApiServices, meta: ProjectFilingMeta, item: Office.MessageCompose) => {
  let conversationId = undefined;
  if (item.conversationId !== undefined && item.conversationId !== "") {
    console.log("We have an item with a conversation ID so this is a reply");
    console.log("ConversationID:", item.conversationId);
    conversationId = item.conversationId;
  }

  if (conversationId !== undefined && conversationId !== "") {
    // we need to capture the original conversation ID as setting metadata changes the conversation ID
    await apiServices.setFilingProperty("CmailMailOriginalConversationID", conversationId);
  }

  await apiServices.setFilingMessage(meta.projectCode);
  await apiServices.setFilingProperty("CmapMailFilingLocation", meta.projectCode);

  if (meta.selectedTag) await apiServices.setFilingProperty("CmapMailFilingTag", meta.selectedTag);
  if (meta.isImportant) await apiServices.setFilingProperty("CmapMailFilingImportant", "true");
  if (meta.isConfidential) await apiServices.setFilingProperty("CmapMailFilingConfidential", "true");

  return conversationId;
};

export const handleAction = async (
  meta: ProjectFilingMeta,
  mode: string,
  apiServices: IApiServices,
  setFiling: (flag: boolean) => void,
  showErrorToast: (msg: string) => void
) => {
  let pathToUse = !meta.sitePath ? "/sites/" + meta.projectCode : meta.sitePath;

  const fetchedPermissions = await apiServices.getUserPermissions(pathToUse);
  if (!fetchedPermissions.CanFile) {
    showErrorToast("You don't have permission to file into this project");
    return false;
  }

  if (mode === "onsend") {
    if (Office.context.mailbox.item) {
      const item = Office.context.mailbox.item;

      const conversationId = await setComposeMetadata(apiServices, meta, item);

      if (conversationId !== undefined && conversationId !== "") {
        console.log("We have an item with a conversation ID so this is a reply");
        Office.context.ui.closeContainer();
        return true;
      } else {
        const newSubject = await apiServices.setSubject(meta.projectCode);
        if (newSubject === undefined) {
          console.error("Error setting subject");
          showErrorToast("There was an error updating the subject. Please contact your administrator.");
        }
        Office.context.ui.closeContainer();
        return true;
      }
    } else return false;
  } else {
    // Validate that we have selected items before setting filing to true
    if (!meta.selectedItems || meta.selectedItems.length === 0) {
      showErrorToast("No emails selected for filing. Please select at least one email.");
      return false;
    }

    setFiling(true);

    // to support tagging emails in compose mode, we want to set the metadata as above

    const composeEntries = meta.selectedItems.filter((item: SelectedItems) => {
      return item.itemMode === "Compose";
    });

    for (var i = 0; i < composeEntries.length; i++) {
      if (composeEntries[i].item !== undefined) {
        const item = composeEntries[i].item as Office.MessageCompose;
        const conversationId = await setComposeMetadata(apiServices, meta, item);
        if (conversationId !== undefined && conversationId !== "") {
          console.log("We have an item with a conversation ID so this is a reply");
        } else {
          const newSubject = await apiServices.setSubject(meta.projectCode);
          if (newSubject === undefined) {
            console.error("Error setting subject");
            showErrorToast("There was an error updating the subject. Please contact your administrator.");
          }
        }
      } else {
        console.log("Skipping item", composeEntries[i]);
      }
    }

    const readEntries = meta.selectedItems.filter(
      (item: SelectedItems) => !item.itemMode || item.itemMode !== "Compose"
    );
    try {
      const result = await apiServices.fileEmails(
        readEntries,
        meta.projectCode,
        meta.sitePath,
        meta.selectedTag,
        meta.isConfidential,
        meta.isImportant
      );

      if (result !== true) {
        Sentry.captureException("Error filing emails in handleAction", {
          extra: {
            projectCode: meta.projectCode,
            tag: meta.selectedTag,
            isConfidential: meta.isConfidential,
            isImportant: meta.isImportant,
            selectedItemsCount: meta.selectedItems?.length,
            mode: mode,
            result: result,
          },
        });
        throw new Error("Filing failed: API returned " + (result === undefined ? "undefined" : "false"));
      }
      return result;
    } catch (error) {
      console.error("Error filing emails:", error);
      Sentry.captureException("Error filing emails in handleAction", {
        extra: {
          projectCode: meta.projectCode,
          tag: meta.selectedTag,
          isConfidential: meta.isConfidential,
          isImportant: meta.isImportant,
          selectedItemsCount: meta.selectedItems?.length,
          error: error instanceof Error ? { message: error.message, stack: error.stack } : String(error),
          mode: mode,
        },
      });
      showErrorToast("There was an error filing your emails. Please contact your administrator.");
      return false;
    } finally {
      setFiling(false);
    }
  }
};
