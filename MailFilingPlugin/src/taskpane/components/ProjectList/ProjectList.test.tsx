//Prevents ESLint from reporting an error about Office not being defined
/* global  Office */
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import "@testing-library/jest-dom";
import { describe, it, vi, beforeEach, expect } from "vitest";
import ProjectList from "./ProjectList";
import { IApiServices } from "../../adapters/IApiServices";
import { TestApiServices } from "../../adapters/TestApiServices";
import { AttachmentWithMeta, SelectedItems, Tag } from "../../types/types";
import { DEFAULT_TAGS } from "../DefaultTags";
import OfficeAddinMock from "office-addin-mock";

global.Office = new OfficeAddinMock.OfficeMockObject({
  host: "outlook",
  PlatformType: {
    PC: "PC",
    iOS: "iOS",
    Mac: "Mac",
    OfficeOnline: "OfficeOnline",
    Android: "Android",
  },
  context: {
    mailbox: {
      addHandlerAsync: vi.fn(),
      removeHandlerAsync: vi.fn(),
      getSelectedItemsAsync: vi.fn(),
    },
    diagnostics: { platform: "PC" },
  },
  AsyncResultStatus: {
    Succeeded: "succeeded",
    Failed: "failed",
  },
  EventType: {
    SelectedItemsChanged: "selectedItemsChanged",
  },
}) as unknown as typeof Office;

const tags: Tag[] = DEFAULT_TAGS;

const selectedEmails: SelectedItems[] = [
  {
    internetMessageId: "msg-123",
    subject: "Test subject",
    itemId: "item-123",
    item: {} as Office.MessageRead,
  },
];

const attachments: AttachmentWithMeta[] = [
  {
    name: "test.pdf",
    size: 12345,
    isInline: true,
    attachmentType: "file",
    contentType: "application/pdf",
    id: "att-001",
  },
];

describe("ProjectList Component", () => {
  const onFavouriteProject = vi.fn();
  let apiServices: IApiServices;

  beforeEach(() => {
    vi.clearAllMocks();
    apiServices = new TestApiServices();
  });

  const renderComponent = (props = {}) =>
    render(
      <ProjectList
        tags={tags}
        onFavouriteProject={onFavouriteProject}
        apiServices={apiServices}
        visible={true}
        handleAction={vi.fn()}
        compose={false}
        useTagOnSend={false}
        useAttachmentFiling={false}
        selectedEmails={selectedEmails}
        attachments={attachments}
        {...props}
      />
    );

  it("renders the hub site dropdown when multiple hub sites", async () => {
    apiServices.getHubsites = () =>
      Promise.resolve([
        { Name: "Hub1", Title: "Hub1", ID: "1", WebUrl: "url1", CustomTags: false },
        { Name: "Hub2", Title: "Hub2", ID: "2", WebUrl: "url2", CustomTags: false },
      ]);

    act(() => {
      renderComponent();
    });

    await waitFor(() => {
      const dropdown = screen.getByRole("combobox");
      expect(dropdown).toBeInTheDocument();
      fireEvent.click(dropdown);
      expect(screen.getByText("Hub1")).toBeInTheDocument();
      expect(screen.getByText("Hub2")).toBeInTheDocument();
    });
  });

  it("does not render the hub site dropdown when only one hub site", async () => {
    apiServices.getHubsites = () =>
      Promise.resolve([{ Name: "Hub", Title: "Hub", ID: "1", WebUrl: "url1", CustomTags: false }]);

    act(() => {
      renderComponent();
    });

    await waitFor(() => {
      expect(screen.queryByRole("combobox")).not.toBeInTheDocument();
    });
  });

  it("renders the search box", async () => {
    renderComponent();
    await waitFor(() => {
      expect(screen.getByPlaceholderText("Search Projects...")).toBeInTheDocument();
    });
  });

  it("renders loading spinner when projects are undefined", async () => {
    apiServices.getProjects = () => {
      throw new Error("Fail fetch");
    };

    renderComponent();

    await waitFor(() => {
      expect(screen.getByRole("progressbar")).toBeInTheDocument();
    });
  });

  it("renders no projects message when projects is empty", async () => {
    apiServices.getProjects = () => Promise.resolve([]);
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText("You have no projects to file to.")).toBeInTheDocument();
    });
  });

  it("does not render when not visible", () => {
    renderComponent({ visible: false });
    expect(screen.queryByTestId("project-list")).not.toBeInTheDocument();
  });

  it("renders project list with correct titles", async () => {
    await act(() => {
      renderComponent();
    });

    await waitFor(() => {
      expect(screen.getByText("Test Project 1")).toBeInTheDocument();
      expect(screen.getByText("Another Project")).toBeInTheDocument();
      expect(screen.getByText("Sample Project")).toBeInTheDocument();
    });
  });

  it("filters projects based on search term", async () => {
    await act(() => {
      renderComponent();
    });

    const searchInput = screen.getByPlaceholderText("Search Projects...");
    fireEvent.change(searchInput, { target: { value: "Test" } });

    await waitFor(() => {
      expect(screen.getByText("Test Project 1")).toBeInTheDocument();
      expect(screen.queryByText("Another Project")).not.toBeInTheDocument();
    });
  });

  it("applies mobile panel height for iOS", async () => {
    const platform = {
      isIOS: true,
      isMac: false,
      isWindows: false,
      isWeb: false,
      isMobile: true,
      platform: "iOS",
    };

    renderComponent({ platform });

    await waitFor(() => {
      expect(screen.getByText("Test Project 1")).toBeInTheDocument();
    });
  });
});
