import React, { useEffect, useState } from "react";

import {
  <PERSON><PERSON><PERSON>,
  ProjectCategory,
  Project as ProjectType,
  ProjectFilingMeta,
  Tag,
  SelectedItems,
  AttachmentWithMeta,
} from "../../types/types";
import {
  Field,
  InputOnChangeData,
  Label,
  makeStyles,
  SearchBox,
  SearchBoxChangeEvent,
  Spinner,
  Dropdown,
  Option,
  tokens,
} from "@fluentui/react-components";
import Project from "../Project/Project";
import { IApiServices } from "../../adapters/IApiServices";
import { useDebounce } from "../../services/DebounceHook";
import { useErrorToast } from "../ErrorToast/ErrorToast";
import { getSitePath } from "../../services/SharepointHelpers";
import { Platform } from "../../services/PlatformService";

interface ProjectListProps {
  tags: Tag[];
  favouriteProjects?: ProjectType[];
  onFavouriteProject: (project: ProjectType, category: ProjectCategory) => void;
  apiServices: IApiServices;
  visible: boolean;
  compose: boolean;
  handleAction: (meta: ProjectFilingMeta) => Promise<boolean>;
  platform?: Platform;
  useTagOnSend: boolean;
  useAttachmentFiling: boolean;
  selectedEmails: SelectedItems[];
  attachments: AttachmentWithMeta[];
}

const ProjectList: React.FC<ProjectListProps> = ({
  tags,
  favouriteProjects,
  onFavouriteProject,
  apiServices,
  visible,
  compose,
  handleAction,
  platform,
  useTagOnSend,
  useAttachmentFiling,
  selectedEmails,
  attachments,
}) => {
  const styles = useStyles();
  const [projects, setProjects] = useState<ProjectType[]>();
  const [searchTerm, setSearchTerm] = useState<string>("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [hubSites, setHubSites] = useState<Hubsite[] | undefined>(undefined);
  const [selectedHubSite, setSelectedHubSite] = useState<Hubsite | undefined>(undefined);
  const isApplePlatform = platform?.isIOS || platform?.isMac || false;

  const showErrorToast = useErrorToast();

  useEffect(() => {
    const getHubsites = async (): Promise<void> => {
      try {
        const hubsiteList = await apiServices.getHubsites();

        if (hubsiteList) {
          setHubSites(hubsiteList);
          if (hubsiteList.length === 1) {
            setSelectedHubSite(hubsiteList[0]);
          }
          if (hubsiteList.length === 0) {
            showErrorToast("You have no Mail hubsites available");
          }
        }
      } catch (error) {
        console.error("Error fetching hubsites:", error);
        showErrorToast("There was an error getting your hubsite list. Please contact your administrator.");
      }
    };

    getHubsites();
  }, []);

  useEffect(() => {
    const getProjectLists = async (term: string): Promise<void> => {
      if (selectedHubSite) {
        try {
          const hubsiteUrl = selectedHubSite.WebUrl;

          if (!hubsiteUrl) {
            showErrorToast(
              "There was an error fetching your projects, the hubsite path was incorrect. Please contact your administrator."
            );
            return;
          }
          const getProjectsList = await apiServices.getProjects(hubsiteUrl, term);

          setProjects(getProjectsList || []);
        } catch (error) {
          console.error("Error fetching projects:", error);
          showErrorToast("There was an error fetching your projects. Please contact your administrator.");
        }
      }
    };

    getProjectLists(debouncedSearchTerm);
  }, [debouncedSearchTerm, selectedHubSite]);

  const renderProject = (index: number) => {
    if (!projects || index >= projects.length) return null;

    const project = projects[index];
    const category = favouriteProjects?.some((favProject) => favProject.ProjectCode === project.ProjectCode)
      ? ProjectCategory.Favourite
      : ProjectCategory.Standard;

    return (
      <Project
        tags={tags}
        category={category}
        key={`${project.ProjectCode}-${project.Id}`}
        projectCode={project.ProjectCode}
        project={project}
        onFavouriteProject={onFavouriteProject}
        apiServices={apiServices}
        compose={compose}
        handleAction={handleAction}
        useTagOnSend={useTagOnSend}
        useAttachmentFiling={useAttachmentFiling}
        selectedEmails={selectedEmails}
        attachments={attachments}
      />
    );
  };

  const handleSearchChange = (_event: SearchBoxChangeEvent, data: InputOnChangeData) => {
    setSearchTerm(data.value);
  };

  const handleHubSiteChange = (_event: React.SyntheticEvent | null, data: { selectedOptions: string[] }) => {
    const hubsite = hubSites?.find((site) => site.WebUrl === data.selectedOptions[0]);
    setSelectedHubSite(hubsite);
  };

  const renderContent = () => {
    if (projects === undefined || projects === null) {
      return (
        <div className={styles.spinnerContainer}>
          <Spinner className={styles.spinner} />
        </div>
      );
    } else if (projects.length === 0) {
      return <Label className={styles.controlMargin}>You have no projects to file to.</Label>;
    } else {
      return (
        <div className={styles.scrollableContainer}>
          {projects.map((project) => (
            <Project
              key={`${project.Id}`}
              tags={tags}
              category={
                favouriteProjects?.some((favProject) => favProject.ProjectCode === project.ProjectCode)
                  ? ProjectCategory.Favourite
                  : ProjectCategory.Standard
              }
              projectCode={project.ProjectCode}
              project={project}
              onFavouriteProject={onFavouriteProject}
              apiServices={apiServices}
              compose={compose}
              handleAction={handleAction}
              useTagOnSend={useTagOnSend}
              useAttachmentFiling={useAttachmentFiling}
              selectedEmails={selectedEmails}
              attachments={attachments}
            />
          ))}
        </div>
      );
    }
  };

  if (!visible) return null;

  const panelContainerStyle = {
    height: isApplePlatform ? "calc(100vh - 180px)" : "calc(100vh - 230px)",
  };

  return (
    <div data-testid="project-list" className={styles.container}>
      {hubSites === undefined && <Spinner size="medium"></Spinner>}
      {hubSites !== undefined && hubSites.length > 1 ? (
        <div className={styles.itemContainer}>
          <Dropdown
            className={styles.controlMargin}
            placeholder="Select a Hub Site"
            selectedOptions={[selectedHubSite?.WebUrl || ""]}
            onOptionSelect={handleHubSiteChange}
          >
            {hubSites.map((site) => {
              return (
                <Option value={site.WebUrl} key={site.WebUrl}>
                  {site.Title}
                </Option>
              );
            })}
          </Dropdown>
        </div>
      ) : null}

      {selectedHubSite ? (
        <>
          <div className={styles.itemContainer}>
            <Field className={styles.searchInputContainer}>
              <SearchBox placeholder="Search Projects..." value={searchTerm} onChange={handleSearchChange} />
            </Field>
          </div>
          <div className={styles.panelContainer} style={panelContainerStyle}>
            {renderContent()}
          </div>
        </>
      ) : null}
    </div>
  );
};

const useStyles = makeStyles({
  scrollableContainer: {
    height: "100%",
    width: "100%",
    overflowY: "auto",
    WebkitOverflowScrolling: "touch",
  },
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    width: "100%",
  },
  spinnerContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    height: "100%",
  },
  spinner: {},
  atveroBlue: {
    color: "#0178DB",
  },
  searchInputContainer: {
    fontWeight: "600",
    padding: "0 15px",
    boxSizing: "border-box",
    width: "100%",
    marginBottom: tokens.spacingVerticalS,
  },
  projectTextContainer: {
    justifyContent: "space-between",
    display: "flex",
    flexDirection: "row",
    marginBottom: "5px",
  },
  itemContainer: {
    marginTop: "10px",
    display: "flex",
    flexDirection: "column",
  },
  panelContainer: {
    display: "flex",
    flexGrow: 1,
    width: "100%",
    padding: "0",
    margin: 0,
    boxSizing: "border-box",
    overflow: "hidden",
  },
  controlMargin: {
    margin: "0 15px",
  },
});

export default ProjectList;
