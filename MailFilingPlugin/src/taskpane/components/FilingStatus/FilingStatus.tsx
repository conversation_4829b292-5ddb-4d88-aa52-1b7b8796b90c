import React, { useEffect, useState } from "react";
import { makeStyles, ProgressBar, Field, Text } from "@fluentui/react-components";

interface FilingStatusProps {
  isFiling: boolean;
}

const FilingStatus: React.FC<FilingStatusProps> = ({ isFiling }) => {
  const styles = useStyles();
  const [progress, setProgress] = useState<number>(0);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    let hideTimeoutId: NodeJS.Timeout | null = null;

    if (isFiling) {
      setProgress(0);
      setShowSuccess(false);
      setVisible(true);

      let startTime = Date.now();
      intervalId = setInterval(() => {
        const elapsedTime = Date.now() - startTime;
        const newProgress = Math.min(0.9, elapsedTime / 3000); // Assume max 10 seconds for filing
        setProgress(newProgress);
      }, 100);
    } else if (visible) {
      // Filing has just completed
      setProgress(1);
      setShowSuccess(true);

      hideTimeoutId = setTimeout(() => {
        setShowSuccess(false);
        setVisible(false);
        setProgress(0);
      }, 2000);
    }

    // Cleanup function
    return () => {
      if (intervalId) clearInterval(intervalId);
      if (hideTimeoutId) clearTimeout(hideTimeoutId);
    };
  }, [isFiling, visible]);

  return (
    <div className={`${styles.filingStatus} ${visible ? styles.visible : ""}`}>
      <div className={styles.progressContainer}>
        {showSuccess ? (
          <Text>Filing requested</Text>
        ) : (
          <Field validationMessage="Filing in progress" validationState="none">
            <ProgressBar value={progress} />
          </Field>
        )}
      </div>
    </div>
  );
};

const useStyles = makeStyles({
  filingStatus: {
    position: "fixed",
    bottom: "-100px",
    left: "50%",
    transform: "translateX(-50%)",
    width: "80%",
    maxWidth: "500px",
    padding: "16px",
    backgroundColor: "#fff",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    borderRadius: "8px",
    transition: "bottom 0.5s ease-in-out",
    zIndex: 1000,
  },
  visible: {
    bottom: "20px",
  },
  progressContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  },
});

export default FilingStatus;
