import { render, screen, waitFor } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import FilingStatus from "./FilingStatus";

describe("FilingStatus component", () => {
  it("renders filing progress initially", () => {
    render(<FilingStatus isFiling={true} />);
    expect(screen.getByText(/filing in progress/i)).toBeInTheDocument();
  });

  it("shows progress bar with initial value", () => {
    render(<FilingStatus isFiling={true} />);
    const progressBar = screen.getByRole("progressbar");
    expect(progressBar).toBeInTheDocument();
    expect(progressBar).toHaveAttribute("aria-valuenow", "0");
  });
});
