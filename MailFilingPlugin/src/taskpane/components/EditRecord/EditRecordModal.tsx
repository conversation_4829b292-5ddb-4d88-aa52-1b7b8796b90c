import React, { useState } from "react";
import { Label, Input, Button, makeStyles, Text, Tooltip } from "@fluentui/react-components";
import { getFileIconClassFromName } from "../../services/GetAttachmentType";
import { isValidRecordTitle, isValidRevision, isValidFileName } from "../../services/ValidationUtils";

interface EditRecordFormProps {
  fileName: string;
  initialTitle?: string;
  initialRevision?: string;
  onSave: (fileName: string, recordTitle: string, revision: string) => void;
  onBack: () => void;
}

const EditRecordForm: React.FC<EditRecordFormProps> = ({
  fileName,
  initialTitle = "",
  initialRevision = "",
  onSave,
  onBack,
}) => {
  const styles = useStyles();
  const [editableFileName, setEditableFileName] = useState(fileName);
  const [recordTitle, setRecordTitle] = useState(initialTitle);
  const [revision, setRevision] = useState(initialRevision);
  const [hasAttemptedSave, setHasAttemptedSave] = useState(false);

  const handleSave = () => {
    setHasAttemptedSave(true);
    if (!editableFileName || !recordTitle || !revision) return;
    onSave(editableFileName, recordTitle, revision);
  };

  return (
    <div className={styles.container}>
      <Text className={styles.headerText}>Edit Record Details</Text>

      <div className={styles.fileRow}>
        <i className={`ms-Icon ${getFileIconClassFromName(editableFileName)}`} style={{ fontSize: "16px" }} />
        <Tooltip content={editableFileName} relationship="label">
          <Text className={styles.attachmentText}>{editableFileName}</Text>
        </Tooltip>
      </div>

      <div className={styles.fieldBlock}>
        <Label required htmlFor="file-name">
          File Name
        </Label>
        <Input
          id="file-name"
          data-testid="edit-file-name"
          value={editableFileName}
          onChange={(e) => setEditableFileName(e.target.value)}
        />
        {hasAttemptedSave && !isValidFileName(editableFileName) && (
          <div className={styles.error}>{editableFileName.trim() === "" ? "Entry required" : "Invalid file name"}</div>
        )}
      </div>

      <div className={styles.fieldBlock}>
        <Label required htmlFor="record-title">
          Record Title
        </Label>
        <Input
          id="record-title"
          data-testid="edit-record-title"
          value={recordTitle}
          onChange={(e) => setRecordTitle(e.target.value)}
        />
        {hasAttemptedSave && !isValidRecordTitle(recordTitle) && (
          <div className={styles.error}>{recordTitle.trim() === "" ? "Entry required" : "Invalid record title"}</div>
        )}
      </div>

      <div className={styles.fieldBlock}>
        <Label required htmlFor="revision">
          Revision Number
        </Label>
        <Input
          id="revision"
          data-testid="edit-revision"
          value={revision}
          onChange={(e) => setRevision(e.target.value)}
          className={styles.revisionInput}
        />
        {hasAttemptedSave && !isValidRevision(revision) && (
          <div className={styles.error}>{revision.trim() === "" ? "Entry required" : "Invalid revision"}</div>
        )}
      </div>

      <div style={{ marginTop: "16px", display: "flex", gap: "8px" }}>
        <Button appearance="secondary" onClick={onBack} data-testid="edit-back-button">
          Back
        </Button>
        <Button
          appearance="primary"
          onClick={handleSave}
          disabled={
            !isValidFileName(editableFileName) || !isValidRecordTitle(recordTitle) || !isValidRevision(revision)
          }
          data-testid="edit-save-button"
        >
          Save
        </Button>
      </div>
    </div>
  );
};

const useStyles = makeStyles({
  container: {
    padding: "10px",
  },
  revisionInput: {
    width: "99px",
  },
  headerText: {
    fontSize: "14px",
    fontWeight: 600,
    marginBottom: "12px",
  },
  modalHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "12px 16px 0 16px",
  },
  closeButton: {
    background: "none",
    border: "none",
    cursor: "pointer",
    color: "#666",
    padding: 0,
  },
  projectInfoContainer: {
    display: "flex",
    alignItems: "center",
    marginBottom: "16px",
  },
  personaContainer: {
    marginRight: "10px",
  },
  projectInfo: {
    display: "flex",
    flexDirection: "column",
  },
  projectTitle: {
    fontSize: "14px",
    fontWeight: 500,
  },
  projectCode: {
    fontSize: "12px",
    color: "#666",
  },
  fileRow: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    marginTop: "8px",
    marginBottom: "16px",
  },
  attachmentText: {
    fontSize: "12px",
    fontWeight: 400,
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  fieldBlock: {
    marginBottom: "12px",
    display: "flex",
    flexDirection: "column",
  },
  error: {
    color: "#C50F1F",
    fontSize: "12px",
    marginTop: "4px",
  },
});

export default EditRecordForm;
