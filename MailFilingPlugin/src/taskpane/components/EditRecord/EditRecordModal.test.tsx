import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import EditRecordForm from "./EditRecordModal";
import { vi } from "vitest";

describe("EditRecordForm", () => {
  const onSave = vi.fn();
  const onBack = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("disables Save for empty fields", () => {
    render(<EditRecordForm fileName="test.pdf" onSave={onSave} onBack={onBack} />);
    const saveButton = screen.getByRole("button", { name: /save/i });
    expect(saveButton).toBeDisabled();
  });

  it("disables Save if title is emojis only", () => {
    render(<EditRecordForm fileName="test.pdf" onSave={onSave} onBack={onBack} />);
    fireEvent.change(screen.getByLabelText(/record title/i), {
      target: { value: "😀😀😀" },
    });
    fireEvent.change(screen.getByLabelText(/revision number/i), {
      target: { value: "A1" },
    });
    const saveButton = screen.getByRole("button", { name: /save/i });
    expect(saveButton).toBeDisabled();
  });

  it("disables Save if revision is invalid", () => {
    render(<EditRecordForm fileName="test.pdf" onSave={onSave} onBack={onBack} />);
    fireEvent.change(screen.getByLabelText(/record title/i), {
      target: { value: "Title" },
    });
    fireEvent.change(screen.getByLabelText(/revision number/i), {
      target: { value: "***" },
    });
    const saveButton = screen.getByRole("button", { name: /save/i });
    expect(saveButton).toBeDisabled();
  });

  it("enables Save when inputs are valid", () => {
    render(<EditRecordForm fileName="test.pdf" onSave={onSave} onBack={onBack} />);
    fireEvent.change(screen.getByLabelText(/record title/i), {
      target: { value: "My Record" },
    });
    fireEvent.change(screen.getByLabelText(/revision number/i), {
      target: { value: "Rev-2" },
    });
    const saveButton = screen.getByRole("button", { name: /save/i });
    expect(saveButton).toBeEnabled();
  });

  it("calls onSave when valid Save clicked", () => {
    render(<EditRecordForm fileName="test.pdf" onSave={onSave} onBack={onBack} />);
    fireEvent.change(screen.getByLabelText(/record title/i), {
      target: { value: "My Title" },
    });
    fireEvent.change(screen.getByLabelText(/revision number/i), {
      target: { value: "A1" },
    });
    const saveButton = screen.getByRole("button", { name: /save/i });
    fireEvent.click(saveButton);
    expect(onSave).toHaveBeenCalledWith("test.pdf", "My Title", "A1");
  });

  it("calls onBack when Back button is clicked", () => {
    render(<EditRecordForm fileName="test.pdf" onSave={onSave} onBack={onBack} />);
    const backButton = screen.getByRole("button", { name: /back/i });
    fireEvent.click(backButton);
    expect(onBack).toHaveBeenCalled();
  });

  it("renders the file name input field", () => {
    render(<EditRecordForm fileName="test.pdf" onSave={vi.fn()} onBack={vi.fn()} />);
    const fileNameInput = screen.getByLabelText(/file name/i);
    expect(fileNameInput).toBeInTheDocument();
  });

  it("renders file icon class from file name", () => {
    render(<EditRecordForm fileName="test.docx" onSave={onSave} onBack={onBack} />);
    const icon = document.querySelector(".ms-Icon");
    expect(icon?.className).toMatch(/ms-Icon/);
  });

  it("renders tooltip with correct file name", () => {
    render(<EditRecordForm fileName="file123.pdf" onSave={onSave} onBack={onBack} />);
    expect(screen.getByText("file123.pdf")).toBeInTheDocument();
  });
});
