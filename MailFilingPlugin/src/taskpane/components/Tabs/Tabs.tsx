import React, { useState, useEffect } from "react";
import { makeStyles } from "@griffel/react";
import { Tab, TabList, SelectTabEvent, SelectTabData, TabValue } from "@fluentui/react-components";
import { Star24Regular, List24Regular, InfoRegular, SettingsRegular } from "@fluentui/react-icons";

interface TabsProps {
  selectedTab: TabValue;
  onTabChange: (value: TabValue) => void;
}

const Tabs: React.FC<TabsProps> = ({ selectedTab, onTabChange }) => {
  const styles = useStyles();
  const [showText, setShowText] = useState(true);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width: 480px)"); // maxed this out because there's not enough room for icons and words

    const handleMediaQueryChange = (e: MediaQueryListEvent) => {
      setShowText(!e.matches);
      setIsSmallScreen(e.matches);
    };

    setShowText(!mediaQuery.matches);
    setIsSmallScreen(mediaQuery.matches);
    mediaQuery.addListener(handleMediaQueryChange);

    return () => {
      mediaQuery.removeListener(handleMediaQueryChange);
    };
  }, []);

  return (
    <TabList
      selectedValue={selectedTab}
      onTabSelect={(_event: SelectTabEvent, data: SelectTabData) => onTabChange(data.value)}
      className={styles.tabList}
    >
      <Tab
        className={isSmallScreen ? styles.spacer : ""}
        value="favourites"
        icon={<Star24Regular data-testid="favourites-icon" />}
      >
        {showText ? <span>Favourites</span> : null}
      </Tab>
      <Tab
        className={isSmallScreen ? styles.spacer : ""}
        value="project-list"
        icon={<List24Regular data-testid="project-list-icon" />}
      >
        {showText ? <span>Projects</span> : null}
      </Tab>
      <Tab
        className={isSmallScreen ? styles.spacer : ""}
        value="filed-info"
        icon={<InfoRegular data-testid="filed-info-icon" />}
      >
        {showText ? <span>Filed Info</span> : null}
      </Tab>
      <Tab
        className={isSmallScreen ? styles.spacer : ""}
        value="settings"
        icon={<SettingsRegular data-testid="settings-icon" />}
      >
        {showText ? <span>Settings</span> : null}
      </Tab>
    </TabList>
  );
};

const useStyles = makeStyles({
  spacer: {
    marginLeft: "10px",
    marginRight: "10px",
  },
  tabList: {
    display: "flex",
    justifyContent: "center",
    padding: "0px 0px",
    marginTop: "12px",
    marginBottom: "12px",
    boxSizing: "border-box",
    width: "100%",
  },
});

export default Tabs;
