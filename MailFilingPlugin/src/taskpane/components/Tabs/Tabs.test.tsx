import { render, screen } from "@testing-library/react";
import { expect, describe, it, vi, beforeEach, afterEach } from "vitest";
import OfficeAddinMock from "office-addin-mock";
import Tabs from "./Tabs";

const mockData = {
  host: "outlook",
  AsyncResultStatus: { Succeeded: "succeeded", Failed: "failed" },
  context: { diagnostics: { platform: "Windows" } },
  PlatformType: { Windows: "Windows", Android: "Android", iOS: "iOS" },
};

const officeMock = new OfficeAddinMock.OfficeMockObject(mockData);
global.Office = officeMock as any;

describe("Tabs Component", () => {
  const mockOnTabChange = vi.fn();
  const matchMediaMock = vi.fn();

  beforeEach(() => {
    Object.defineProperty(window, "matchMedia", {
      writable: true,
      value: matchMediaMock,
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("renders all tabs correctly", () => {
    matchMediaMock.mockReturnValue({
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
    });

    render(<Tabs selectedTab="favourites" onTabChange={mockOnTabChange} />);
    const tabs = screen.getAllByRole("tab");
    expect(tabs).toHaveLength(4);

    const visibleContent = screen.getAllByText(/Projects|Favourites|Filed Info|Settings/, {
      selector: ".fui-Tab__content span",
    });
    expect(visibleContent).toHaveLength(4);
  });

  it("displays the correct icons for each tab", () => {
    matchMediaMock.mockReturnValue({
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
    });

    render(<Tabs selectedTab="favourites" onTabChange={mockOnTabChange} />);
    expect(screen.getByTestId("favourites-icon")).toBeInTheDocument();
    expect(screen.getByTestId("project-list-icon")).toBeInTheDocument();
    expect(screen.getByTestId("filed-info-icon")).toBeInTheDocument();
    expect(screen.getByTestId("settings-icon")).toBeInTheDocument();
  });

  it("highlights the selected tab", () => {
    matchMediaMock.mockReturnValue({
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
    });

    render(<Tabs selectedTab="filed-info" onTabChange={mockOnTabChange} />);
    const filedInfoTab = screen.getByRole("tab", { name: /filed info/i });
    expect(filedInfoTab).toHaveAttribute("aria-selected", "true");
  });

  it("calls onTabChange when a tab is clicked", () => {
    matchMediaMock.mockReturnValue({
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
    });

    render(<Tabs selectedTab="favourites" onTabChange={mockOnTabChange} />);
    screen.getByRole("tab", { name: /filed info/i }).click();
    expect(mockOnTabChange).toHaveBeenCalledWith("filed-info");
  });

  it("hides text for small screens", () => {
    matchMediaMock.mockReturnValue({
      matches: true,
      addListener: vi.fn(),
      removeListener: vi.fn(),
    });

    render(<Tabs selectedTab="favourites" onTabChange={mockOnTabChange} />);
    const tabContents = screen.queryAllByText(/Projects|Favourites|Filed Info|Settings/, {
      selector: ".fui-Tab__content span",
    });
    expect(tabContents).toHaveLength(0);
  });

  it("shows text and does not show get info on mobile", () => {
    matchMediaMock.mockReturnValue({
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
    });

    officeMock.context.diagnostics.platform = "Android";

    render(<Tabs selectedTab="favourites" onTabChange={mockOnTabChange} />);
    const tabs = screen.getAllByRole("tab");
    expect(tabs[0]).toHaveTextContent("Favourites");
    expect(tabs[1]).toHaveTextContent("Projects");
    expect(tabs[2]).toHaveTextContent("Filed Info");
    expect(tabs[3]).toHaveTextContent("Settings");
  });

  it("shows text and does not apply spacer for larger screens", () => {
    matchMediaMock.mockReturnValue({
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
    });

    officeMock.context.diagnostics.platform = "Windows";

    render(<Tabs selectedTab="favourites" onTabChange={mockOnTabChange} />);
    const tabs = screen.getAllByRole("tab");
    expect(tabs[0]).toHaveTextContent("Favourites");
    expect(tabs[1]).toHaveTextContent("Projects");
    expect(tabs[2]).toHaveTextContent("Filed Info");
    expect(tabs[3]).toHaveTextContent("Settings");
    tabs.forEach((tab) => {
      expect(tab).not.toHaveClass("spacer");
    });
  });
});
