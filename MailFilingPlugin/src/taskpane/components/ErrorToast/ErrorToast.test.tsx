import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { useErrorToast } from "./ErrorToast";
import {
  useToastController,
  Toast,
  ToastTitle,
  ToastBody,
  ToastFooter,
  But<PERSON>,
  makeStyles,
} from "@fluentui/react-components";
import React from "react";

// Mock the Fluent UI components and hook
vi.mock("@fluentui/react-components", async (importOriginal) => {
  const actual = await importOriginal<typeof import("@fluentui/react-components")>();

  return {
    ...actual, // Preserve the original exports except for the ones we mock
    useToastController: vi.fn(), // Mock useToastController
    Toast: ({ children }: any) => <div data-testid="mock-toast">{children}</div>,
    ToastTitle: ({ children }: any) => <div data-testid="mock-toast-title">{children}</div>,
    ToastBody: ({ children }: any) => <div data-testid="mock-toast-body">{children}</div>,
    ToastFooter: ({ children }: any) => <div data-testid="mock-toast-footer">{children}</div>,
    Button: ({ children, onClick, disabled }: any) => (
      <button onClick={onClick} disabled={disabled}>
        {children}
      </button>
    ),
    makeStyles: vi.fn(() => () => ({
      buttonContainer: "mock-button-container",
    })),
  };
});

describe("useErrorToast", () => {
  const mockToastController = {
    dispatchToast: vi.fn(),
    dismissToast: vi.fn(),
    dismissAllToasts: vi.fn(),
    updateToast: vi.fn(),
    pauseToast: vi.fn(),
    playToast: vi.fn(),
  };

  beforeEach(() => {
    vi.mocked(useToastController).mockReturnValue(mockToastController);
  });

  // Wrapper component to use the hook
  const TestComponent = ({ message }: { message: string }) => {
    const showErrorToast = useErrorToast();
    React.useEffect(() => {
      showErrorToast(message);
    }, [message]);
    return null;
  };

  it("should call dispatchToast when showErrorToast is called", () => {
    render(<TestComponent message="Test error" />);
    expect(mockToastController.dispatchToast).toHaveBeenCalledTimes(1);
  });

  it("should return a function", () => {
    render(<TestComponent message="Test error" />);
    expect(typeof useErrorToast).toBe("function");
  });

  it("should pass the error message to dispatchToast", () => {
    const errorMessage = "Test error message";
    render(<TestComponent message={errorMessage} />);

    expect(mockToastController.dispatchToast).toHaveBeenCalledWith(
      expect.objectContaining({
        type: Toast,
        props: expect.objectContaining({
          children: expect.arrayContaining([
            expect.objectContaining({ type: ToastTitle }),
            expect.objectContaining({
              type: ToastBody,
              props: expect.objectContaining({ children: errorMessage }),
            }),
            expect.objectContaining({ type: ToastFooter }),
          ]),
        }),
      }),
      expect.objectContaining({ intent: "error" })
    );
  });
});
