import { useState } from "react";
import {
  Button,
  useToastController,
  Toast,
  ToastTitle,
  ToastBody,
  ToastFooter,
  makeStyles,
} from "@fluentui/react-components";

const useStyles = makeStyles({
  buttonContainer: {
    display: "flex",
    gap: "10px",
    justifyContent: "flex-end",
    width: "100%",
  },
});

export const useErrorToast = () => {
  const { dispatchToast, dismissAllToasts } = useToastController();
  const styles = useStyles();

  const showErrorToast = (message: string) => {
    dispatchToast(
      <Toast>
        <ToastTitle>Error:</ToastTitle>
        <ToastBody>{message}</ToastBody>
        <ToastFooter>
          <div className={styles.buttonContainer}>
            <Button size="small" appearance="outline" onClick={dismissAllToasts}>
              Dismiss
            </Button>
          </div>
        </ToastFooter>
      </Toast>,
      { intent: "error" }
    );
  };

  return showErrorToast;
};
