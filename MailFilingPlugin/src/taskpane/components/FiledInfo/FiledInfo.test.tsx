import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import FiledInfo from "./FiledInfo";
import { TestApiServices } from "../../adapters/TestApiServices";
import { FiledEmail, Tag } from "../../types/types";
import * as OfficeServices from "../../services/OfficeServices";
import { DEFAULT_TAGS } from "../DefaultTags";

vi.mock("../../services/OfficeServices");

const tags: Tag[] = DEFAULT_TAGS;

const item = {
  itemId: "item1",
  itemType: "message",
  subject: "Test Email 1",
  conversationId: "conv1",
  internetMessageId: "msg1",
};

describe("FiledInfo", () => {
  const mockApiServices = new TestApiServices();
  const mockSelectedEmail = {
    itemId: "123",
    internetMessageId: "<<EMAIL>>",
    subject: "Test Subject",
    itemType: "message",
    conversationId: "conv123",
    item: item,
  };

  const mockFiledEmails: FiledEmail[] = [
    {
      ProjectCode: "ACM001",
      InternetMessageId: "<<EMAIL>>",
      Tag: "Client",
      Confidential: false,
      Important: true,
      SharedMailbox: null,
      FiledBy: "John Smith",
      Timestamp: "2025-01-30T15:23:23.0287112+00:00",
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    global.Office = {
      context: {
        mailbox: {
          addHandlerAsync: vi.fn((_eventType, _handler, callback) => callback({ status: "succeeded" })),
          removeHandlerAsync: vi.fn(),
          item: {
            itemId: "123",
            internetMessageId: mockSelectedEmail.internetMessageId,
            subject: "Test Subject",
            item: item,
          },
        },
      },
      EventType: { ItemChanged: "itemChanged" },
      AsyncResultStatus: { Succeeded: "succeeded" },
    } as any;

    vi.mocked(OfficeServices.getSelectedEmailWithIDs).mockResolvedValue(mockSelectedEmail);
  });

  it("should show loading state initially", async () => {
    vi.spyOn(mockApiServices, "getFiledEmailInfo").mockResolvedValue(mockFiledEmails);
    render(<FiledInfo apiServices={mockApiServices} visible={true} tags={tags} />);

    expect(screen.getByText("Loading email details...")).toBeInTheDocument();
  });

  it("should display filed email information when data is loaded", async () => {
    vi.spyOn(mockApiServices, "getFiledEmailInfo").mockResolvedValue(mockFiledEmails);
    render(<FiledInfo apiServices={mockApiServices} visible={true} tags={tags} />);

    await waitFor(
      () => {
        expect(screen.getByText("ACM001")).toBeInTheDocument();
      },
      { timeout: 3000 }
    );
  });

  it("should handle empty filed emails array", async () => {
    vi.spyOn(mockApiServices, "getFiledEmailInfo").mockResolvedValue([]);
    render(<FiledInfo apiServices={mockApiServices} visible={true} tags={tags} />);

    await waitFor(
      () => {
        expect(screen.getByText(/This email has not been filed yet/)).toBeInTheDocument();
      },
      { timeout: 3000 }
    );
  });

  it("should display badges for important items", async () => {
    vi.spyOn(mockApiServices, "getFiledEmailInfo").mockResolvedValue(mockFiledEmails);
    render(<FiledInfo apiServices={mockApiServices} visible={true} tags={tags} />);

    // Ensure loading is complete
    await waitFor(() => expect(screen.queryByText("Loading email details...")).not.toBeInTheDocument(), {
      timeout: 5000,
    });

    const importantIcon = screen.getByRole("img", { name: "Important" });
    expect(importantIcon).toBeInTheDocument();
  });

  it("should not render when visible is false", () => {
    render(<FiledInfo apiServices={mockApiServices} visible={false} tags={tags} />);
    expect(screen.queryByText(/Loading email details/)).not.toBeInTheDocument();
  });
});
