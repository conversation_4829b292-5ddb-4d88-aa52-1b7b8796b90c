//Prevents ESLint from reporting an error about Office not being defined
/* global  Office */
import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  makeStyles,
  tokens,
  Text,
  Card,
  Table,
  TableCell,
  TableRow,
  TableBody,
  Avatar,
  Label,
  Spinner,
  Tooltip,
} from "@fluentui/react-components";
import {
  DocumentRegular,
  ClockRegular,
  PersonRegular,
  TagRegular,
  Important20Regular,
  LockClosedRegular,
} from "@fluentui/react-icons";
import { FiledEmail, Tag } from "../../types/types";
import { IApiServices } from "../../adapters/IApiServices";
import { formatDate } from "../../services/FormatDate";
import {
  addItemChangedHander,
  addSelectedItemsChangedHander,
  getSelectedEmailWithIDs,
  removeItemChangedHander,
  removeSelectedItemsChangedHander,
} from "../../services/OfficeServices";

interface FiledInfoProps {
  apiServices: IApiServices;
  visible: boolean;
  tags: Tag[];
}

const InfoRow: React.FC<{
  icon: React.ReactNode;
  label: string;
  children: React.ReactNode;
  isLastRow?: boolean;
}> = ({ icon, children, isLastRow }) => {
  const styles = useStyles();
  return (
    <TableRow className={isLastRow ? styles.lastRow : undefined}>
      <TableCell className={styles.iconCell}>{icon}</TableCell>
      <TableCell>{children}</TableCell>
    </TableRow>
  );
};

const TagBadge: React.FC<{ tags: Tag[]; tag: string | undefined }> = ({ tags, tag }) => {
  const styles = useStyles();

  if (tag === undefined || tag === null || tag === "" || tag === "No Tag" || tag === "NoTag") return null;

  const tagInfo = tags.filter((t) => t.Name === tag);

  const defaultTag: Tag = {
    Name: tag,
    BackgroundColour: "Silver",
    Colour: "Black",
    Order: 1,
  };

  const displayTag = tagInfo.length > 0 ? tagInfo[0] : defaultTag;
  return (
    <Label className={styles.tag} style={{ backgroundColor: displayTag.BackgroundColour, color: displayTag.Colour }}>
      {displayTag.Name}
    </Label>
  );
};

const FiledInfo: React.FC<FiledInfoProps> = ({ apiServices, visible, tags }) => {
  const styles = useStyles();
  const [filedEmails, setFiledEmails] = useState<FiledEmail[]>([]);
  const [loading, setLoading] = useState(true);
  const [emailSubject, setEmailSubject] = useState<string>("");
  const [isLoadingEmail, setIsLoadingEmail] = useState(false);
  const [selectedItemCount, setSelectedItemCount] = useState<number>(0);
  const handlerRegistered = useRef(false);

  const fetchFiledEmails = useCallback(async () => {
    try {
      setLoading(true);
      setIsLoadingEmail(true);

      await new Promise((resolve) => setTimeout(resolve, 1000));

      const customProps = await getSelectedEmailWithIDs();

      if (customProps.internetMessageId) {
        setEmailSubject(customProps.subject);
        const filedEmailInfo = await apiServices.getFiledEmailInfo(customProps.internetMessageId);
        setFiledEmails(filedEmailInfo);
      } else {
        setEmailSubject("Failed to fetch your email data.");
      }
    } catch (error) {
      console.error("Error fetching filed emails:", error);
      setEmailSubject("Failed to fetch your email data.");
      setFiledEmails([]);
    } finally {
      setLoading(false);
      setIsLoadingEmail(false);
    }
  }, [apiServices]);

  useEffect(() => {
    const handleItemChanged = () => {
      const item = Office.context.mailbox.item;

      if (item) {
        setSelectedItemCount(1);
        fetchFiledEmails();
      } else {
        setSelectedItemCount(0);
      }
    };

    if (Office.context.mailbox && !handlerRegistered.current) {
      addItemChangedHander(handleItemChanged);
      // Initial call to handle the current item
      handleItemChanged();

      return () => {
        if (handlerRegistered.current) {
          removeItemChangedHander(handleItemChanged);
        }
      };
    }
    return undefined;
  }, []);

  if (!visible) return null;

  const renderContent = () => {
    if (isLoadingEmail) {
      return (
        <div className={styles.spinnerContainer}>
          <Spinner className={styles.spinner} aria-label="Loading email details" />
          <Text>Loading email details...</Text>
        </div>
      );
    }

    if (selectedItemCount === 0) {
      return (
        <div className={styles.messageContainer}>
          <Text weight="semibold" size={400}>
            Please select only one email to view filed locations
          </Text>
        </div>
      );
    }

    return (
      <>
        <Text size={200} className={styles.subheader}>
          Subject:
        </Text>
        <Text weight="semibold" size={400} className={styles.subject}>
          {emailSubject}
        </Text>
        {loading ? (
          <div className={styles.spinnerContainer}>
            <Spinner className={styles.spinner} aria-label="Loading filed locations" />
            <Text>Loading filed locations...</Text>
          </div>
        ) : filedEmails.length === 0 ? (
          <div className={styles.messageContainer}>
            <Text weight="medium" size={300}>
              This email has not been filed yet. Please navigate to your favourites or project list to file.
            </Text>
          </div>
        ) : (
          <div className={styles.scrollArea}>
            {filedEmails.map((email, index) => (
              <Card key={index} className={styles.card} appearance="subtle">
                <Table className={styles.table}>
                  <TableBody>
                    <InfoRow icon={<DocumentRegular />} label="Project">
                      <div className={styles.projectSection}>
                        <div>
                          <Avatar
                            name={email.ProjectCode}
                            size={32}
                            color="colorful"
                            className={styles.projectAvatar}
                            shape="square"
                          />
                          <Text>{email.ProjectCode}</Text>
                        </div>
                        <div className={styles.badges}>
                          {email.Important && (
                            <Tooltip content="Important" relationship="label">
                              <Important20Regular
                                data-testid="important-icon"
                                className={styles.statusIcon}
                                style={{ color: tokens.colorPaletteRedForeground1 }}
                              />
                            </Tooltip>
                          )}
                          {email.Confidential && (
                            <Tooltip content="Confidential" relationship="label">
                              <LockClosedRegular
                                className={styles.statusIcon}
                                style={{ color: tokens.colorPaletteGrapeForeground2 }}
                              />
                            </Tooltip>
                          )}
                        </div>
                      </div>
                    </InfoRow>

                    <InfoRow icon={<PersonRegular />} label="Filed by">
                      <div className={styles.avatarRow}>
                        <Avatar name={email.FiledBy} size={32} color="colorful" />
                        <Text>{email.FiledBy}</Text>
                      </div>
                    </InfoRow>

                    <InfoRow icon={<ClockRegular />} label="Filed on">
                      <div className={styles.timeSection}>
                        <Text>{formatDate(email.Timestamp)}</Text>
                      </div>
                    </InfoRow>

                    <InfoRow icon={<TagRegular />} label="Tag" isLastRow={true}>
                      <TagBadge tags={tags} tag={email.Tag} />
                    </InfoRow>
                  </TableBody>
                </Table>
              </Card>
            ))}
          </div>
        )}
      </>
    );
  };

  return <div className={styles.container}>{renderContent()}</div>;
};

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100vh",
    paddingTop: tokens.spacingVerticalM,
    backgroundColor: tokens.colorNeutralBackground1,
    padding: tokens.spacingHorizontalL,
  },
  lastRow: {
    border: "none",
  },
  messageContainer: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    textAlign: "center",
  },
  spinnerContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: tokens.spacingVerticalS,
    marginBottom: tokens.spacingVerticalM,
  },
  scrollArea: {
    maxHeight: "calc(100vh - 150px)",
    overflowY: "auto",
    overflowX: "hidden",
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalM,
  },
  card: {
    width: "100%",
    height: "100%",
    minHeight: "205px",
    boxShadow: "none",
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    borderRadius: tokens.borderRadiusMedium,
    padding: tokens.spacingVerticalM,
  },
  table: {
    width: "100%",
  },
  iconCell: {
    width: "14px",
  },
  labelCell: {
    width: "30%",
    verticalAlign: "top",
    paddingTop: tokens.spacingVerticalS,
  },
  projectSection: {
    justifyContent: "space-between",
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
    overflow: "hidden",
    flex: 1,
    "& span": {
      overflow: "hidden",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
    },
  },
  avatarRow: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
    overflow: "hidden",
    flex: 1,
    "& span": {
      overflow: "hidden",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
    },
  },
  subject: {
    marginBottom: tokens.spacingVerticalS,
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
  },
  subheader: {
    color: tokens.colorNeutralForeground3,
  },
  projectAvatar: {
    marginRight: "8px",
    flexShrink: 0,
  },
  timeSection: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
  },
  tag: {
    borderRadius: "4px",
    padding: "2px 8px",
    fontSize: tokens.fontSizeBase200,
  },
  badges: {
    display: "flex",
    gap: tokens.spacingHorizontalXS,
    marginLeft: tokens.spacingHorizontalM,
  },
  statusIcon: {
    fontSize: "16px",
  },
  spinner: {
    margin: "auto",
  },
});

export default FiledInfo;
