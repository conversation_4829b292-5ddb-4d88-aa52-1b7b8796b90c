import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import App from "./App";
import { expect, it, describe, vi } from "vitest";
import { IApiServices } from "../adapters/IApiServices";
import { TestApiServices } from "../adapters/TestApiServices";
import { act } from "react";

// Mock our Test API Services
let apiServices: IApiServices = new TestApiServices();

// Setup Office.js mocks
const mockAddHandlerAsync = vi.fn();
const mockRemoveHandlerAsync = vi.fn();
const mockGetSelectedItemsAsync = vi.fn();
const mockGetAsync = vi.fn().mockImplementation((callback) => {
  callback({
    status: "succeeded",
    value: [],
  });
});

global.Office = {
  context: {
    mailbox: {
      addHandlerAsync: mockAddHandlerAsync,
      removeHandlerAsync: mockRemoveHandlerAsync,
      getSelectedItemsAsync: mockGetSelectedItemsAsync,
      masterCategories: {
        getAsync: mockGetAsync,
      },
    },
    diagnostics: {
      platform: "PC",
    },
  },
  AsyncResultStatus: {
    Succeeded: "succeeded",
    Failed: "failed",
  },
  EventType: {
    SelectedItemsChanged: "selectedItemsChanged",
  },
  PlatformType: {
    PC: "PC",
    iOS: "iOS",
    Mac: "Mac",
    OfficeOnline: "OfficeOnline",
    Android: "Android",
  },
} as unknown as typeof Office;

// Mock the window used in Tabs component
window.matchMedia =
  window.matchMedia ||
  function () {
    return {
      matches: false,
      addListener: function () {},
      removeListener: function () {},
    };
  };

describe("App", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("renders without app crashing", async () => {
    await act(async () => {
      render(<App apiServices={apiServices} mode="read" />);
    });

    const app = screen.getByTestId("app-tsx");

    expect(app).toBeInTheDocument();
  });

  it("should render two pivots", async () => {
    await act(async () => {
      render(<App apiServices={apiServices} mode="read" />);
    });

    await waitFor(() => {
      expect(screen.getByTestId("project-list-icon")).toBeInTheDocument();
      expect(screen.getByTestId("favourites-icon")).toBeInTheDocument();
    });
  });

  it("should default to the favourites pivot", async () => {
    await act(async () => {
      render(<App apiServices={apiServices} mode="read" />);
    });

    await waitFor(() => {
      expect(screen.getByTestId("favourites-content")).toBeInTheDocument();
      expect(screen.queryByTestId("project-list")).not.toBeInTheDocument();
    });
  });

  it("should show the projects pivot when clicked", async () => {
    await act(async () => {
      render(<App apiServices={apiServices} mode="read" />);
    });

    await waitFor(() => {
      expect(screen.getByTestId("project-list-icon")).toBeInTheDocument();
      expect(screen.getByTestId("favourites-content")).toBeInTheDocument();
      expect(screen.queryByTestId("project-list")).not.toBeInTheDocument();
    });

    await act(async () => {
      const projectsButton = screen.getByTestId("project-list-icon");
      fireEvent.click(projectsButton);
    });

    await waitFor(() => {
      expect(screen.queryByTestId("favourites-content")).not.toBeInTheDocument();
      expect(screen.getByTestId("project-list")).toBeInTheDocument();
    });
  });
});
