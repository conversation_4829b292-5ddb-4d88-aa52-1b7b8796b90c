import { FC, useEffect, useState } from "react";
import {
  makeStyles,
  TabValue,
  To<PERSON>,
  Spinner,
} from "@fluentui/react-components";
import Tabs from "./Tabs/Tabs";
import Favourites from "./Favourites/Favourites";
import Settings from "./Settings/Settings";
import ProjectList from "./ProjectList/ProjectList";
import {
  Project,
  ProjectCategory,
  ProjectFilingMeta,
  Tag,
  Tenancy,
  AttachmentWithMeta,
  SelectedItems,
} from "../types/types";
import FilingStatus from "./FilingStatus/FilingStatus";
import DiscoveryDialog from "./DiscoveryDialog/DiscoveryDialog";
import { useErrorToast } from "./ErrorToast/ErrorToast";
import { IApiServices } from "../adapters/IApiServices";
import {
  getAttachmentsForSelectedEmail,
  getSelectedEmails,
  setAtveroCategories,
  addItemChangedHander,
  removeItemChangedHander,
} from "../services/OfficeServices";
import { handleAction } from "./AppHelpers";
import { DEFAULT_TAGS } from "./DefaultTags";
import FiledInfo from "./FiledInfo/FiledInfo";
import { detectPlatform, Platform } from "../services/PlatformService";
import { detectTenancy } from "../services/TenancyService";

const setBackgroundFilingTimeout = (
  apiServices: IApiServices,
  emailFilingTimeoutId: ReturnType<typeof setTimeout> | undefined
) => {
  if (emailFilingTimeoutId) {
    clearTimeout(emailFilingTimeoutId);
  }
  const timerId: ReturnType<typeof setTimeout> = setTimeout(
    async () => {
      await apiServices.syncEmails();
    },
    5 * 60 * 1000
  ); //5 minutes in milliseconds
  return timerId;
};

interface AppProps {
  apiServices: IApiServices;
  mode: string;
}

const App: FC<AppProps> = ({ apiServices, mode }) => {
  const styles = useStyles();
  const showErrorToast = useErrorToast();
  const [favouriteProjects, setFavouriteProjects] = useState<Project[]>([]);
  const [selectedTab, setSelectedTab] = useState<TabValue>("favourites");
  const [filing, setFiling] = useState<boolean>(false);
  const [favouritesIsLoading, setFavouritesIsLoading] = useState<boolean>(true);
  const [tags, setTags] = useState<Tag[]>([]);
  const [platform, setPlatform] = useState<Platform | null>(null);
  const [loggedIn, setLoggedIn] = useState<boolean>(false);
  const [tenancy, setTenancy] = useState<Tenancy>(Tenancy.Atvero);
  const [emailFilingTimeoutId, setEmailFilingTimeoutId] = useState<
    undefined | ReturnType<typeof setTimeout>
  >(undefined);
  const [useAttachmentFiling, setUseAttachmentFiling] =
    useState<boolean>(false);
  const [useTagOnSend, setUseTagOnSend] = useState<boolean>(false);
  const [selectedEmails, setSelectedEmails] = useState<SelectedItems[]>([]);
  const [attachments, setAttachments] = useState<AttachmentWithMeta[]>([]);

  useEffect(() => {
    setAtveroCategories();
    const newTimeout = setBackgroundFilingTimeout(
      apiServices,
      emailFilingTimeoutId
    );
    setEmailFilingTimeoutId(newTimeout);

    const currentTenancy = detectTenancy();
    setTenancy(currentTenancy);
  }, []);

  useEffect(() => {
    try {
      const detectedPlatform = detectPlatform();
      setPlatform(detectedPlatform);
    } catch (error) {
      console.error("Error detecting platform:", error);
    }
  }, []);

  useEffect(() => {
    const getTagOnSend = async (): Promise<void> => {
      const useTagOnSend = await apiServices.getSetting("useTagOnSend");
      if (useTagOnSend !== undefined && useTagOnSend == "enabled")
        setUseTagOnSend(true);
    };

    const fetchAttachmentFilingSetting = async (): Promise<void> => {
      const setting = await apiServices.getSetting("useAttachmentFiling");
      setUseAttachmentFiling(setting !== undefined);
    };

    fetchAttachmentFilingSetting();
    getTagOnSend();
  }, [loggedIn]);

  useEffect(() => {
    const login = async (): Promise<void> => {
      console.log("Logging in");
      const token = await apiServices.login();

      if (token === undefined) {
        showErrorToast("Unable to connect to your account");
      } else {
        setLoggedIn(true);
      }
    };

    login();
  }, []);

  useEffect(() => {
    const fetchAttachmentFilingSetting = async (): Promise<void> => {
      const setting = await apiServices.getSetting("useAttachmentFiling");
      setUseAttachmentFiling(setting !== undefined);
    };

    fetchAttachmentFilingSetting();
  }, []);

  useEffect(() => {
    const getFavourites = async (): Promise<void> => {
      try {
        const getFavouriteProjectsList =
          await apiServices.getFavouriteProjects();
        setFavouriteProjects(getFavouriteProjectsList);
      } catch (error) {
        console.error("Error fetching favorites", error);
        showErrorToast(
          "There was an error getting your favourite projects. Please contact your administrator."
        );
      } finally {
        setFavouritesIsLoading(false);
      }
    };
    if (loggedIn) {
      getFavourites();
    }
  }, [loggedIn]);

  useEffect(() => {
    const getCustomTags = async (): Promise<void> => {
      const useCustomTags = await apiServices.getSetting("useCustomTags");
      if (useCustomTags) {
        try {
          const getTagsList = await apiServices.getTags();
          const sortedTags = getTagsList.sort((a, b) =>
            a.Order < b.Order ? -1 : 1
          );
          setTags(sortedTags);
        } catch (error) {
          console.error("Error fetching custom tags:", error);

          setTags(DEFAULT_TAGS);

          // fall back to the standard set
        }
      } else {
        setTags(DEFAULT_TAGS);
      }
    };

    getCustomTags();
  }, [loggedIn]);

  useEffect(() => {
    const handleItemChanged = async () => {
      try {
        const selectedItems = await getSelectedEmails();

        setSelectedEmails(selectedItems);

        if (selectedItems.length === 1) {
          const allAttachments = await getAttachmentsForSelectedEmail();
          setAttachments(allAttachments.filter((att) => !att.isInline));
        } else {
          setAttachments([]);
        }
      } catch (err) {
        console.error("ItemChanged handler failed", err);
        setSelectedEmails([]);
        setAttachments([]);
      }
    };

    addItemChangedHander(handleItemChanged);

    handleItemChanged(); // initial load

    return () => {
      removeItemChangedHander(handleItemChanged);
    };
  }, []);

  const handleFavouriteProject = async (
    project: Project,
    category: ProjectCategory
  ) => {
    if (category === ProjectCategory.Favourite) {
      try {
        await apiServices.deleteFavouriteProject(project);
        const getFavouriteProjectsList =
          await apiServices.getFavouriteProjects();
        setFavouriteProjects(getFavouriteProjectsList);
      } catch (error) {
        showErrorToast(
          "Failed to remove your project from your favourites. Please contact your administrator."
        );
      }
    } else if (category === ProjectCategory.Standard) {
      try {
        await apiServices.setFavouriteProjects(project);
        const getFavouriteProjectsList =
          await apiServices.getFavouriteProjects();
        setFavouriteProjects(getFavouriteProjectsList);
      } catch (error) {
        showErrorToast(
          "Failed to add your project to your favourites. Please contact your administrator."
        );
      }
    }
  };

  const processHandleAction = async (meta: ProjectFilingMeta) => {
    return await handleAction(
      meta,
      mode,
      apiServices,
      setFiling,
      showErrorToast
    );
  };

  return (
    <div className={styles.root} data-testid="app-tsx">
      <Toaster />

      {!loggedIn && (
        <div className={styles.spinnerContainer}>
          <Spinner size="medium" aria-label="Authenticating" />
        </div>
      )}

      {loggedIn && (
        <>
          <Tabs selectedTab={selectedTab} onTabChange={setSelectedTab} />

          <Favourites
            tags={tags}
            favourites={favouriteProjects}
            onFavouriteProject={handleFavouriteProject}
            favouritesIsLoading={favouritesIsLoading}
            apiServices={apiServices}
            visible={selectedTab === "favourites"}
            compose={mode === "onsend"}
            handleAction={processHandleAction}
            platform={platform || undefined}
            useTagOnSend={useTagOnSend}
            useAttachmentFiling={useAttachmentFiling}
            selectedEmails={selectedEmails}
            attachments={attachments}
          />

          <ProjectList
            tags={tags}
            onFavouriteProject={handleFavouriteProject}
            favouriteProjects={favouriteProjects}
            apiServices={apiServices}
            visible={selectedTab === "project-list"}
            compose={mode === "onsend"}
            handleAction={processHandleAction}
            platform={platform || undefined}
            useTagOnSend={useTagOnSend}
            useAttachmentFiling={useAttachmentFiling}
            selectedEmails={selectedEmails}
            attachments={attachments}
          />

          <FiledInfo
            apiServices={apiServices}
            visible={selectedTab === "filed-info"}
            tags={tags}
          />

          <Settings
            apiServices={apiServices}
            visible={selectedTab === "settings"}
          />
        </>
      )}

      <FilingStatus isFiling={filing} />
      <DiscoveryDialog platform={platform || undefined} />
    </div>
  );
};

const useStyles = makeStyles({
  root: {
    minHeight: "100vh",
    overflow: "hidden",
  },
  logo: {
    textAlign: "center",
  },
  accessToken: {
    overflow: "scroll",
  },
  spinnerContainer: {
    height: "100vh",
    alignContent: "center",
  },
  addPadding: {
    padding: "10px",
  },
});

export default App;
