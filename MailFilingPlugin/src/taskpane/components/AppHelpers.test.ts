import { describe, it, expect, vi, beforeEach } from "vitest";
import { TestApiServices } from "../adapters/TestApiServices";
import { handleAction, prependSubject } from "./AppHelpers";
import { ProjectFilingMeta } from "../types/types";
import * as Sentry from "@sentry/react";

const setFiling = vi.fn();
const showErrorToast = vi.fn();

const mockGetInitializationContext = vi.fn();
const mockOffice = {
  context: {
    ui: {
      closeContainer: vi.fn(),
    },
    mailbox: {
      item: {
        getInitializationContextAsync: mockGetInitializationContext,
      },
    },
  },
};

const fakeitem = {
  itemId: "item1",
  itemType: "fkae message",
  itemMode: "Compose",
  subject: "Test Email 1",
  conversationId: "conv1",
  internetMessageId: "msg1",
};

vi.mock("@sentry/react", () => ({
  captureException: vi.fn(),
}));

describe("Subject handling", () => {
  it("should tag an untagged subject with the project code", () => {
    expect(prependSubject("AAAA", "A subject line")).toBe("[AAAA] A subject line");
  });
  it("should retag an tagged subject with the project code", () => {
    expect(prependSubject("BBBB", "[AAAA] A subject line")).toBe("[BBBB] A subject line");
  });
  it("should retag a already tagged subject with the project code", () => {
    expect(prependSubject("BBBB", "Re: [AAAA] A subject line")).toBe("[BBBB] Re: [AAAA] A subject line");
  });
  it("should tag a broken tagged subject with the project code", () => {
    expect(prependSubject("BBBB", "[A subject line")).toBe("[BBBB] [A subject line");
  });
});

describe("Project Action", () => {
  beforeEach(() => {
    vi.resetAllMocks();
    global.Office = mockOffice as any;
  });

  it("should set the subject in onsend mode", async () => {
    const apiServices = new TestApiServices();
    const setSubjectSpy = vi.spyOn(apiServices, "setSubject");
    const setFileSpy = vi.spyOn(apiServices, "fileEmails");
    const meta: ProjectFilingMeta = {
      isConfidential: true,
      isImportant: true,
      projectCode: "AAAA",
      siteId: "1",
      selectedItems: undefined,
      selectedTag: "TEST",
      sitePath: "https://test.sharepoint.com/sites/testhub/P123",
    };
    const res = await handleAction(meta, "onsend", apiServices, setFiling, showErrorToast);
    expect(setSubjectSpy).toHaveBeenCalledTimes(1);
    expect(setFileSpy).not.toBeCalled();
  });

  it("should not set the subject in onsend mode when replying", async () => {
    const mockOffice = {
      context: {
        ui: {
          closeContainer: vi.fn(),
        },
        mailbox: {
          item: {
            conversationId: "REPLY",
            getInitializationContextAsync: mockGetInitializationContext,
          },
        },
      },
    };
    global.Office = mockOffice as any;

    const apiServices = new TestApiServices();
    const setSubjectSpy = vi.spyOn(apiServices, "setSubject");
    const setFileSpy = vi.spyOn(apiServices, "fileEmails");
    const meta: ProjectFilingMeta = {
      isConfidential: true,
      isImportant: true,
      projectCode: "AAAA",
      siteId: "1",
      selectedItems: undefined,
      selectedTag: "TEST",
      sitePath: "https://test.sharepoint.com/sites/testhub/P123",
    };
    const res = await handleAction(meta, "onsend", apiServices, setFiling, showErrorToast);
    expect(setSubjectSpy).not.toBeCalled();
    expect(setFileSpy).not.toBeCalled();
  });

  it("should set the subject in compose mode", async () => {
    const apiServices = new TestApiServices();
    const setSubjectSpy = vi.spyOn(apiServices, "setSubject");
    const fakeitem = {
      itemId: "item1",
      itemType: "fkae message",
      itemMode: "Compose",
      subject: "Test Email 1",
      internetMessageId: "msg1",
    };
    const meta: ProjectFilingMeta = {
      isConfidential: true,
      isImportant: true,
      projectCode: "AAABA",
      siteId: "1",
      selectedItems: [
        {
          itemId: "2",
          itemType: "message",
          itemMode: "Compose",
          subject: "subjectX",
          item: fakeitem,
        },
      ],
      selectedTag: "TEST",
      sitePath: "https://test.sharepoint.com/sites/testhub/P123",
    };
    const res = await handleAction(meta, "read", apiServices, setFiling, showErrorToast);
    expect(setSubjectSpy).toBeCalled();
  });

  it("should set the subject in onsend mode", async () => {
    const apiServices = new TestApiServices();
    const setSubjectSpy = vi.spyOn(apiServices, "setSubject");
    const setFileSpy = vi.spyOn(apiServices, "fileEmails");
    const meta: ProjectFilingMeta = {
      isConfidential: true,
      isImportant: true,
      projectCode: "AAAA",
      siteId: "1",
      selectedItems: undefined,
      selectedTag: "TEST",
      sitePath: "https://test.sharepoint.com/sites/testhub/P123",
    };
    const res = await handleAction(meta, "onsend", apiServices, setFiling, showErrorToast);
    expect(setSubjectSpy).toHaveBeenCalledTimes(1);
    expect(setFileSpy).not.toBeCalled();
  });

  it("should show error toast and return false if no emails are selected", async () => {
    const apiServices = new TestApiServices();
    const setFileSpy = vi.spyOn(apiServices, "fileEmails");

    const meta: ProjectFilingMeta = {
      isConfidential: false,
      isImportant: false,
      projectCode: "AAAA",
      siteId: "1",
      selectedItems: [],
      selectedTag: "",
      sitePath: "https://test.sharepoint.com/sites/testhub/P123",
    };

    const res = await handleAction(meta, "read", apiServices, setFiling, showErrorToast);

    expect(setFileSpy).not.toHaveBeenCalled();
    expect(setFiling).not.toHaveBeenCalled();
    expect(showErrorToast).toHaveBeenCalledWith("No emails selected for filing. Please select at least one email.");
    expect(res).toBe(false);
  });

  it("should file the email in read mode", async () => {
    const apiServices = new TestApiServices();
    const setSubjectSpy = vi.spyOn(apiServices, "setSubject");
    const setFileSpy = vi.spyOn(apiServices, "fileEmails");

    setFileSpy.mockResolvedValue(true);

    const meta: ProjectFilingMeta = {
      isConfidential: false,
      isImportant: false,
      projectCode: "AAAA",
      siteId: "1",
      selectedItems: [{ itemId: "1", itemType: "message", subject: "subject", conversationId: "1", item: fakeitem }],
      selectedTag: "",
      sitePath: "https://test.sharepoint.com/sites/testhub/P123",
    };
    const res = await handleAction(meta, "read", apiServices, setFiling, showErrorToast);
    expect(setFileSpy).toHaveBeenCalledTimes(1);
    expect(setSubjectSpy).not.toBeCalled();
    expect(setFiling).toHaveBeenCalledWith(true);
    expect(setFiling).toHaveBeenLastCalledWith(false);
  });

  it("should handle errors when fileEmails throws an exception", async () => {
    const apiServices = new TestApiServices();
    const setFileSpy = vi.spyOn(apiServices, "fileEmails");

    setFileSpy.mockRejectedValue(new Error("Test error"));

    const meta: ProjectFilingMeta = {
      isConfidential: false,
      isImportant: false,
      projectCode: "AAAA",
      siteId: "1",
      selectedItems: [{ itemId: "1", itemType: "message", subject: "subject", conversationId: "1", item: fakeitem }],
      selectedTag: "",
      sitePath: "https://test.sharepoint.com/sites/testhub/P123",
    };

    const res = await handleAction(meta, "read", apiServices, setFiling, showErrorToast);

    expect(setFileSpy).toHaveBeenCalledTimes(1);
    expect(res).toBe(false);
    expect(Sentry.captureException).toHaveBeenCalled();
    expect(showErrorToast).toHaveBeenCalled();
    expect(setFiling).toHaveBeenCalledWith(true);
    expect(setFiling).toHaveBeenLastCalledWith(false);
  });

  it("should handle errors when fileEmails returns undefined", async () => {
    const apiServices = new TestApiServices();
    const setFileSpy = vi.spyOn(apiServices, "fileEmails");
    setFileSpy.mockResolvedValue(undefined as any);

    const meta: ProjectFilingMeta = {
      isConfidential: false,
      isImportant: false,
      projectCode: "AAAA",
      siteId: "1",
      selectedItems: [{ itemId: "1", itemType: "message", subject: "subject", conversationId: "1", item: fakeitem }],
      selectedTag: "",
      sitePath: "https://test.sharepoint.com/sites/testhub/P123",
    };

    const res = await handleAction(meta, "read", apiServices, setFiling, showErrorToast);

    expect(setFileSpy).toHaveBeenCalledTimes(1);
    expect(res).toBe(false);
    expect(Sentry.captureException).toHaveBeenCalled();
    expect(showErrorToast).toHaveBeenCalled();
    expect(setFiling).toHaveBeenCalledWith(true);
    expect(setFiling).toHaveBeenLastCalledWith(false);
  });
});
