import { Label, makeStyles, Spinner } from "@fluentui/react-components";
import React from "react";
import Project from "../Project/Project";
import {
  ProjectCategory,
  Project as ProjectType,
  ProjectFilingMeta,
  Tag,
  SelectedItems,
  AttachmentWithMeta,
} from "../../types/types";
import { IApiServices } from "../../adapters/IApiServices";
import { Platform } from "../../services/PlatformService";

interface FavouritesProps {
  tags: Tag[];
  favourites?: ProjectType[];
  onFavouriteProject: (project: ProjectType, category: ProjectCategory) => void;
  favouritesIsLoading: boolean;
  apiServices: IApiServices;
  visible: boolean;
  compose: boolean;
  handleAction: (meta: ProjectFilingMeta) => Promise<boolean>;
  platform?: Platform;
  useTagOnSend: boolean;
  useAttachmentFiling: boolean;
  selectedEmails: SelectedItems[];
  attachments: AttachmentWithMeta[];
}

const Favourites: React.FC<FavouritesProps> = ({
  tags,
  favourites,
  onFavouriteProject,
  favouritesIsLoading,
  apiServices,
  visible,
  compose,
  handleAction,
  platform,
  useTagOnSend,
  useAttachmentFiling,
  selectedEmails,
  attachments,
}) => {
  const styles = useStyles();
  const isApplePlatform = platform?.isIOS || platform?.isMac || false;

  const renderContent = () => {
    if (favouritesIsLoading) {
      return (
        <div className={styles.spinnerContainer}>
          <Spinner className={styles.spinner} />
        </div>
      );
    } else if (!favourites || favourites.length === 0) {
      return (
        <div className={styles.noProjects}>
          <Label className={styles.noProjectsText}>You have no favourite projects.</Label>
          <Label>Navigate to the Project List to start adding Projects to your favourites!</Label>
        </div>
      );
    } else {
      return (
        <div className={styles.scrollableContainer}>
          {favourites.map((project) => (
            <Project
              key={project.ProjectCode}
              tags={tags}
              category={ProjectCategory.Favourite}
              projectCode={project.ProjectCode}
              project={project}
              onFavouriteProject={onFavouriteProject}
              apiServices={apiServices}
              compose={compose}
              handleAction={handleAction}
              useTagOnSend={useTagOnSend}
              useAttachmentFiling={useAttachmentFiling}
              selectedEmails={selectedEmails}
              attachments={attachments}
            />
          ))}
        </div>
      );
    }
  };

  if (!visible) return null;

  const panelContainerStyle = {
    height: isApplePlatform ? "calc(100vh - 75px)" : "calc(100vh - 137px)",
  };

  return (
    <div data-testid="favourites-content" className={styles.container}>
      <div className={styles.panelContainer} style={panelContainerStyle}>
        {renderContent()}
      </div>
    </div>
  );
};

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    width: "100%",
  },
  spinner: {
    marginBottom: "66px",
  },
  scrollableContainer: {
    height: "100%",
    width: "100%",
    overflowY: "auto",
    WebkitOverflowScrolling: "touch",
  },
  spinnerContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    height: "100%",
  },
  noProjectsText: {
    marginBottom: "10px",
  },
  noProjects: {
    marginTop: "20px",
    display: "flex",
    flexDirection: "column",
    textAlign: "center",
  },
  atveroBlue: {
    margin: "5px",
    color: "#0178DB",
  },
  projectTextContainer: {
    justifyContent: "space-between",
    display: "flex",
    flexDirection: "row",
    marginBottom: "5px",
  },
  itemContainer: {
    marginTop: "25px",
    display: "flex",
    flexDirection: "column",
    width: "100%",
    padding: 0,
    margin: 0,
  },
  panelContainer: {
    display: "flex",
    flexGrow: 1,
    width: "100%",
    padding: "0",
    margin: 0,
    boxSizing: "border-box",
    overflow: "hidden",
  },
});

export default Favourites;
