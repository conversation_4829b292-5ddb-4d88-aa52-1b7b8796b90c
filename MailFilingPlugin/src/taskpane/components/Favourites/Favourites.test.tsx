import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import Favourites from "./Favourites";
import { vi, expect, describe, test, beforeEach } from "vitest";
import { Project as ProjectType, Tag, SelectedItems, AttachmentWithMeta } from "../../types/types";
import { TestApiServices } from "../../adapters/TestApiServices";
import { IApiServices } from "../../adapters/IApiServices";
import { DEFAULT_TAGS } from "../DefaultTags";
import OfficeAddinMock from "office-addin-mock";

let apiServices: IApiServices = new TestApiServices();

const tags: Tag[] = DEFAULT_TAGS;

const selectedEmails: SelectedItems[] = [
  {
    internetMessageId: "msg-123",
    subject: "Test subject",
    itemId: "item-123",
    item: {} as Office.MessageRead, // or Office.MessageCompose, depending on your plugin mode
  },
];

const attachments: AttachmentWithMeta[] = [
  {
    name: "test.pdf",
    size: 12345,
    isInline: true,
    attachmentType: "file",
    contentType: "application/pdf",
    id: "att-001",
  },
];

const mockProjects: ProjectType[] = [
  {
    ProjectCode: "P123",
    ProjectTitle: "Test Project 1",
    Rank: 1,
    Id: "1",
    SitePath: "https://test.sharepoint.com/sites/testhub/P123",
  },
  {
    ProjectCode: "P124",
    ProjectTitle: "Another Project",
    Rank: 2,
    Id: "2",
    SitePath: "https://test.sharepoint.com/sites/testhub/P124",
  },
  {
    ProjectCode: "P125",
    ProjectTitle: "Sample Project",
    Rank: 3,
    Id: "3",
    SitePath: "https://test.sharepoint.com/sites/testhub/P125",
  },
];

const onFavouriteProject = vi.fn();

describe("Favourites component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test("renders the favourites content", () => {
    render(
      <Favourites
        tags={tags}
        favourites={mockProjects}
        onFavouriteProject={onFavouriteProject}
        favouritesIsLoading={false}
        apiServices={apiServices}
        visible={true}
        handleAction={vi.fn()}
        compose={false}
        useTagOnSend={false}
        useAttachmentFiling={false}
        selectedEmails={selectedEmails}
        attachments={attachments}
      />
    );
    expect(screen.getByTestId("favourites-content")).toBeInTheDocument();
  });

  test("renders scrollable container when favourites are provided", () => {
    render(
      <Favourites
        tags={tags}
        favourites={mockProjects}
        onFavouriteProject={onFavouriteProject}
        favouritesIsLoading={false}
        apiServices={apiServices}
        visible={true}
        handleAction={vi.fn()}
        compose={false}
        useTagOnSend={false}
        useAttachmentFiling={false}
        selectedEmails={selectedEmails}
        attachments={attachments}
      />
    );

    expect(screen.getByTestId("favourites-content")).toBeInTheDocument();
    mockProjects.forEach((project) => {
      expect(screen.getByText(project.ProjectTitle)).toBeInTheDocument();
    });
  });

  test("displays a message when there are no favourite projects", () => {
    render(
      <Favourites
        tags={tags}
        favourites={[]}
        onFavouriteProject={onFavouriteProject}
        favouritesIsLoading={false}
        apiServices={apiServices}
        visible={true}
        handleAction={vi.fn()}
        compose={false}
        useTagOnSend={false}
        useAttachmentFiling={false}
        selectedEmails={selectedEmails}
        attachments={attachments}
      />
    );
    expect(screen.getByText("You have no favourite projects.")).toBeInTheDocument();
    expect(
      screen.getByText("Navigate to the Project List to start adding Projects to your favourites!")
    ).toBeInTheDocument();
  });

  test("displays a spinner when loading", () => {
    render(
      <Favourites
        tags={tags}
        favourites={[]}
        onFavouriteProject={onFavouriteProject}
        favouritesIsLoading={true}
        apiServices={apiServices}
        visible={true}
        handleAction={vi.fn()}
        compose={false}
        useTagOnSend={false}
        useAttachmentFiling={false}
        selectedEmails={selectedEmails}
        attachments={attachments}
      />
    );
    expect(screen.getByTestId("favourites-content")).toBeInTheDocument();
    expect(screen.getByRole("progressbar")).toBeInTheDocument();
  });

  test("doesn't render anything when not visible", () => {
    render(
      <Favourites
        tags={tags}
        favourites={mockProjects}
        onFavouriteProject={onFavouriteProject}
        favouritesIsLoading={false}
        apiServices={apiServices}
        visible={false}
        handleAction={vi.fn()}
        compose={false}
        useTagOnSend={false}
        useAttachmentFiling={false}
        selectedEmails={selectedEmails}
        attachments={attachments}
      />
    );
    expect(screen.queryByTestId("favourites-content")).not.toBeInTheDocument();
  });

  test("passes platform info to control container height", () => {
    const mockPlatform = {
      isIOS: true,
      isMac: false,
      isWindows: false,
      isWeb: false,
      isMobile: true,
      platform: "iOS",
    };

    render(
      <Favourites
        tags={tags}
        favourites={mockProjects}
        onFavouriteProject={onFavouriteProject}
        favouritesIsLoading={false}
        apiServices={apiServices}
        visible={true}
        handleAction={vi.fn()}
        compose={false}
        platform={mockPlatform}
        useTagOnSend={false}
        useAttachmentFiling={false}
        selectedEmails={selectedEmails}
        attachments={attachments}
      />
    );

    expect(screen.getByText("Test Project 1")).toBeInTheDocument();
  });

  test("renders with attachment filing enabled", () => {
    render(
      <Favourites
        tags={tags}
        favourites={mockProjects}
        onFavouriteProject={onFavouriteProject}
        favouritesIsLoading={false}
        apiServices={apiServices}
        visible={true}
        handleAction={vi.fn()}
        compose={false}
        useTagOnSend={false}
        useAttachmentFiling={true}
        selectedEmails={selectedEmails}
        attachments={attachments}
      />
    );

    expect(screen.getByText("Test Project 1")).toBeInTheDocument();
    // You could also test specific rendering of attachments here if needed
  });
});
