import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { AtveroLogo } from "./AtveroLogo";
import React from "react";

// Mock the makeStyles function
vi.mock("@fluentui/react-components", () => ({
  makeStyles: vi.fn(() => () => ({
    small: "mock-small-class",
  })),
}));

describe("AtveroLogo", () => {
  it("should render a div with the correct class name", () => {
    render(<AtveroLogo />);
    const logoDiv = screen.getByTestId("div-image");
    expect(logoDiv).toBeInTheDocument();
    expect(logoDiv).toHaveClass("mock-small-class");
  });
});
