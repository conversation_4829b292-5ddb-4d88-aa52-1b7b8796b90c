import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { ProjectCategory, Project as ProjectType, SelectedItems, Tag, AttachmentWithMeta } from "../../types/types";
import { IApiServices } from "../../adapters/IApiServices";
import { DEFAULT_TAGS } from "../DefaultTags";
import Project from "./Project";

vi.mock("../../components/ErrorToast/ErrorToast", () => ({
  useErrorToast: () => vi.fn(),
}));

const testProject = {
  SitePath: "/sites/mock-project",
  ProjectCode: "PRJ001",
  ProjectTitle: "Mock Project",
  Id: "1",
  Rank: 1,
};

const selectedEmails: SelectedItems[] = [
  {
    itemId: "1",
    subject: "Test Email",
    item: {} as Office.Item,
  },
];

const mockTags: Tag[] = [
  {
    Name: "Urgent",
    Colour: "#ffffff",
    BackgroundColour: "#ff0000",
    Order: 1,
  },
];

describe("Project Component", () => {
  let apiServices: IApiServices;
  let handleAction: ReturnType<typeof vi.fn>;
  let onFavouriteProject: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    handleAction = vi.fn().mockResolvedValue(true);
    onFavouriteProject = vi.fn();
    apiServices = {
      getUserPermissions: vi.fn().mockResolvedValue({ CanFile: true, CanFileConfidential: true, Licensed: true }),
    } as unknown as IApiServices;
  });

  const renderComponent = (overrides = {}) => {
    return render(
      <Project
        tags={mockTags}
        projectCode={testProject.ProjectCode}
        project={testProject}
        category={ProjectCategory.Standard}
        apiServices={apiServices}
        compose={false}
        handleAction={handleAction}
        onFavouriteProject={onFavouriteProject}
        useTagOnSend={false}
        useAttachmentFiling={false}
        selectedEmails={selectedEmails}
        attachments={[]}
        {...overrides}
      />
    );
  };

  it("renders project title and code", () => {
    renderComponent();
    expect(screen.getByText("Mock Project")).toBeInTheDocument();
    expect(screen.getByText("PRJ001")).toBeInTheDocument();
  });

  it("toggles favourite state", () => {
    renderComponent();
    const favButton = screen.getByTestId("favourite-button");
    fireEvent.click(favButton);
    expect(onFavouriteProject).toHaveBeenCalled();
  });

  it("opens modal on project click", async () => {
    renderComponent();
    fireEvent.click(screen.getByRole("button", { name: /project-button/i }));
    expect(await screen.findByText("Cancel")).toBeInTheDocument();
  });

  it("calls handleAction on File button click", async () => {
    renderComponent();
    fireEvent.click(screen.getByRole("button", { name: /project-button/i }));
    const fileBtn = await screen.findByTestId("file-button");
    fireEvent.click(fileBtn);
    await waitFor(() => expect(handleAction).toHaveBeenCalled());
  });

  it("disables File button when no emails are selected", async () => {
    renderComponent({ selectedEmails: [] });
    fireEvent.click(screen.getByRole("button", { name: /project-button/i }));
    const fileBtn = await screen.findByTestId("file-button");
    expect(fileBtn).toBeDisabled();
  });

  it("toggles Important checkbox", async () => {
    renderComponent();
    fireEvent.click(screen.getByRole("button", { name: /project-button/i }));
    const checkbox = await screen.findByTestId("important-checkbox");
    fireEvent.click(checkbox);
    expect(checkbox).toBeChecked();
  });

  it("closes modal with Cancel", async () => {
    renderComponent();
    fireEvent.click(screen.getByRole("button", { name: /project-button/i }));
    const cancelBtn = await screen.findByText("Cancel");
    fireEvent.click(cancelBtn);
    await waitFor(() => expect(screen.queryByText("Cancel")).not.toBeInTheDocument());
  });

  it("disables Confidential checkbox when not allowed", async () => {
    apiServices = {
      getUserPermissions: vi.fn().mockResolvedValue({ CanFile: true, CanFileConfidential: false, Licensed: true }),
    } as unknown as IApiServices;
    renderComponent({ apiServices });
    fireEvent.click(screen.getByRole("button", { name: /project-button/i }));
    const checkbox = await screen.findByTestId("confidential-checkbox");
    expect(checkbox).toBeDisabled();
  });

  // it("calls handleFileEmails immediately when compose is true and useTagOnSend is false", async () => {
  //   renderComponent({ compose: true, useTagOnSend: false });
  //   fireEvent.click(screen.getByRole("button", { name: /project-button/i }));
  //   await waitFor(() => {
  //     expect(handleAction).toHaveBeenCalled();
  //   });
  // });

  it("updates selectedTag when a default tag is selected", async () => {
    renderComponent({ tags: DEFAULT_TAGS });

    fireEvent.click(screen.getByRole("button", { name: /project-button/i }));

    // Open the tag dropdown (adjust this depending on your actual TagSelector UI)
    fireEvent.click(screen.getByText("No Tag")); // Default selected tag

    // Select another tag from the list
    fireEvent.click(screen.getByText("Structure")); // One of the default tags

    // Expect "Internal" to now be shown as selected
    expect(screen.getByRole("combobox")).toHaveTextContent("Structure");
  });

  it("shows error toast and disables filing when permission fetch fails", async () => {
    const failingApi = {
      getUserPermissions: vi.fn().mockRejectedValue(new Error("Fail")),
    } as unknown as IApiServices;

    renderComponent({ apiServices: failingApi });
    fireEvent.click(screen.getByRole("button", { name: /project-button/i }));

    await waitFor(() => {
      expect(screen.queryByText("Cancel")).not.toBeInTheDocument(); // modal closes on error
    });
  });

  it("disables file button when user lacks file permission", async () => {
    const limitedApi = {
      getUserPermissions: vi.fn().mockResolvedValue({ CanFile: false, CanFileConfidential: false }),
    } as unknown as IApiServices;

    renderComponent({ apiServices: limitedApi });
    fireEvent.click(screen.getByRole("button", { name: /project-button/i }));

    const fileBtn = await screen.findByTestId("file-button");
    expect(fileBtn).toBeDisabled();
  });

  it("triggers favourite toggle from standard", () => {
    renderComponent({ category: ProjectCategory.Standard });

    const favButton = screen.getByTestId("favourite-button");
    fireEvent.click(favButton);

    // Called with current category, not updated one
    expect(onFavouriteProject).toHaveBeenCalledWith(testProject, ProjectCategory.Standard);
  });

  it("triggers favourite toggle from favourite", () => {
    renderComponent({ category: ProjectCategory.Favourite });

    const favButton = screen.getByTestId("favourite-button");
    fireEvent.click(favButton);

    expect(onFavouriteProject).toHaveBeenCalledWith(testProject, ProjectCategory.Favourite);
  });

  it("toggles favourite project on Enter key press", () => {
    renderComponent();
    const favButton = screen.getByTestId("favourite-button");
    fireEvent.keyDown(favButton, { key: "Enter" });
    expect(onFavouriteProject).toHaveBeenCalled();
  });

  it("toggles favourite project on Space key press", () => {
    renderComponent();
    const favButton = screen.getByTestId("favourite-button");
    fireEvent.keyDown(favButton, { key: " " });
    expect(onFavouriteProject).toHaveBeenCalled();
  });
});
