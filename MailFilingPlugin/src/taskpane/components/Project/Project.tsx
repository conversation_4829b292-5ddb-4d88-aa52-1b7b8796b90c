//Prevents ESLint from reporting an error about Office not being defined
/* global  Office */
import React, { useState, useEffect } from "react";
import {
  ProjectCategory,
  Project as ProjectType,
  ProjectFilingMeta,
  Tag,
  AttachmentWithMeta,
  SelectedItems,
} from "../../types/types";
import { Label, makeStyles, Button, Avatar, Checkbox } from "@fluentui/react-components";
import { Dismiss20Regular, Document20Regular, Star24Filled, Star24Regular } from "@fluentui/react-icons";
import TagSelector from "../TagSelector/TagSelector";
import { IApiServices } from "../../adapters/IApiServices";
import { useErrorToast } from "../../components/ErrorToast/ErrorToast";
import * as Sentry from "@sentry/react";
import AttachmentFilingNotice from "../AttachmentsTable/AttachmentsTable";
import EditRecordForm from "../EditRecord/EditRecordModal";
import { handleAttachmentSaveLogic } from "../../services/ProjectUtils";
import { getSelectedEmails } from "../../services/OfficeServices";

interface ProjectProps {
  tags: Tag[];
  projectCode: string;
  project: ProjectType;
  onFavouriteProject: (project: ProjectType, category: ProjectCategory) => void;
  category: ProjectCategory;
  apiServices: IApiServices;
  compose: boolean;
  handleAction: (meta: ProjectFilingMeta) => Promise<boolean>;
  useTagOnSend: boolean;
  useAttachmentFiling: boolean;
  selectedEmails: SelectedItems[];
  attachments: AttachmentWithMeta[];
}

const Project: React.FC<ProjectProps> = ({
  tags,
  projectCode,
  project,
  onFavouriteProject,
  category,
  apiServices,
  compose,
  handleAction,
  useTagOnSend,
  useAttachmentFiling,
  selectedEmails,
  attachments,
}) => {
  const styles = useStyles();
  const [newCategory, setNewCategory] = useState<ProjectCategory>(category);
  const [showModal, setShowModal] = useState(false);
  const [isConfidential, setIsConfidential] = useState(false);
  const [isImportant, setIsImportant] = useState(false);
  const [canFileConfidential, setCanFileConfidential] = useState<boolean | undefined>(undefined);
  const [canFile, setCanFile] = useState<boolean | undefined>(undefined);
  const [selectedTag, setSelectedTag] = useState<string>("No Tag");
  const [localAttachments, setLocalAttachments] = useState<AttachmentWithMeta[]>(attachments);
  const [selectedAttachments, setSelectedAttachments] = useState<string[]>([]);
  const [showAttachmentPreview, setShowAttachmentPreview] = useState(false);
  const [attachmentBeingEdited, setAttachmentBeingEdited] = useState<AttachmentWithMeta | null>(null);

  const showErrorToast = useErrorToast();

  useEffect(() => {
    setLocalAttachments(attachments);
  }, [attachments]);

  const handleFileEmails = async (isConfidential: boolean, isImportant: boolean) => {
    try {
      let emailsToFile = selectedEmails;
      if (compose) {
        console.log("In compose mode, fetching selected emails");
        emailsToFile = await getSelectedEmails();
      }
      if (!emailsToFile || emailsToFile.length === 0) {
        showErrorToast("No emails selected for filing. Please select at least one email.");
        return false;
      }

      const meta = {
        isConfidential,
        isImportant,
        selectedItems: emailsToFile,
        projectCode,
        sitePath: project.SitePath,
        siteId: project.Id,
        selectedTag,
      };

      return await handleAction(meta);
    } catch (error) {
      Sentry.captureException("Error handling file emails: " + error, {
        extra: {
          isConfidential,
          isImportant,
          projectCode,
          selectedTag,
          error: error instanceof Error ? { message: error.message, stack: error.stack } : String(error),
        },
      });
      showErrorToast("There was an error filing your emails. Please contact your administrator.");
      return false;
    }
  };

  const handleAttachmentSave = (newName: string, title: string, revision: string) => {
    const result = handleAttachmentSaveLogic(
      localAttachments,
      selectedAttachments,
      attachmentBeingEdited,
      newName,
      title,
      revision
    );
    setLocalAttachments(result.updated);
    setSelectedAttachments(result.selected);
    setAttachmentBeingEdited(result.editing);
  };

  const handleProjectClick = async () => {
    setCanFile(undefined);
    setCanFileConfidential(undefined);

    if (compose && !useTagOnSend) {
      await handleFileEmails(false, false);
    } else {
      setShowModal(true);
      try {
        const sitePath = project.SitePath || `/sites/${project.ProjectCode}`;
        const perms = await apiServices.getUserPermissions(sitePath);

        if (!perms.CanFile) {
          showErrorToast("You don't have permission to file into this project");
          setShowModal(false);
          setCanFile(false);
          setCanFileConfidential(false);
        } else {
          setCanFile(true);
          setCanFileConfidential(perms.CanFileConfidential);
        }
      } catch {
        showErrorToast("Can't determine if you can access this project");
        setShowModal(false);
        setCanFile(false);
        setCanFileConfidential(false);
      }
    }
  };

  const toggleCategory = () => {
    setNewCategory((prevCategory) =>
      prevCategory === ProjectCategory.Favourite ? ProjectCategory.Standard : ProjectCategory.Favourite
    );
    onFavouriteProject(project, newCategory);
  };

  const closeModalAndFile = async () => {
    const success = await handleFileEmails(isConfidential, isImportant);
    if (success) setShowModal(false);
  };
  const selectedCount = selectedEmails.length;
  const allowFiling = selectedCount > 0 || compose;

  return (
    <div>
      <button className={styles.projectContainer} onClick={handleProjectClick} aria-label="project-button">
        <div className={styles.projectInfoContainer}>
          <div className={styles.personaContainer}>
            <Avatar shape="square" name={project.ProjectTitle} color="colorful" size={36} />
          </div>
          <div className={styles.projectInfo}>
            <Label className={styles.projectTitle}>{project.ProjectTitle ?? "Untitled Project"}</Label>
            <Label className={styles.projectCode}>{projectCode}</Label>
          </div>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            toggleCategory();
          }}
          className={styles.favouriteButton}
          data-testid="favourite-button"
          tabIndex={0}
          aria-label={newCategory === ProjectCategory.Favourite ? "Remove from favorites" : "Add to favorites"}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              e.stopPropagation();
              toggleCategory();
            }
          }}
        >
          {newCategory === ProjectCategory.Favourite ? (
            <Star24Filled color="#FCC70C" />
          ) : (
            <Star24Regular color="#252525" />
          )}
        </button>
      </button>

      {showModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <button className={styles.closeButton} onClick={() => setShowModal(false)} aria-label="Close modal">
              <Dismiss20Regular />
            </button>
            <div className={styles.projectHeader}>
              <div className={styles.personaContainer}>
                <Avatar shape="square" name={project.ProjectTitle} color="colorful" size={36} />
              </div>
              <div className={styles.projectHeaderInfo}>
                <div className={styles.projectHeaderTitle}>{project.ProjectTitle ?? "Untitled Project"}</div>
                <div className={styles.projectHeaderCode}>{projectCode}</div>
              </div>
            </div>
            {attachmentBeingEdited ? (
              <EditRecordForm
                fileName={attachmentBeingEdited.name}
                initialTitle={attachmentBeingEdited.recordTitle}
                initialRevision={attachmentBeingEdited.revision}
                onSave={handleAttachmentSave}
                onBack={() => setAttachmentBeingEdited(null)}
              />
            ) : (
              <>
                <div className={styles.checkbox}>
                  <Checkbox
                    data-testid="important-checkbox"
                    label="Important"
                    checked={isImportant}
                    onChange={(e) => setIsImportant(e.target.checked)}
                    disabled={!allowFiling}
                  />
                </div>
                <div className={styles.checkbox}>
                  <Checkbox
                    data-testid="confidential-checkbox"
                    label="Confidential"
                    checked={isConfidential}
                    onChange={(e) => setIsConfidential(e.target.checked)}
                    disabled={!canFileConfidential || !allowFiling}
                  />
                </div>
                <TagSelector
                  tags={tags}
                  selectedTag={selectedTag}
                  onTagSelect={setSelectedTag}
                  disabled={!allowFiling}
                />
                {useAttachmentFiling && (
                  <AttachmentFilingNotice
                    selectedCount={selectedCount}
                    attachments={localAttachments}
                    selectedAttachments={selectedAttachments}
                    setSelectedAttachments={setSelectedAttachments}
                    showPreview={showAttachmentPreview}
                    onEditAttachment={setAttachmentBeingEdited}
                  />
                )}
                <div className={styles.buttonRow}>
                  <Button
                    appearance="secondary"
                    onClick={() => (showAttachmentPreview ? setShowAttachmentPreview(false) : setShowModal(false))}
                  >
                    {showAttachmentPreview ? "Back" : "Cancel"}
                  </Button>
                  <Button
                    data-testid="file-button"
                    icon={selectedAttachments.length === 0 ? <Document20Regular /> : undefined}
                    appearance="primary"
                    onClick={() => {
                      selectedAttachments.length > 0 ? setShowAttachmentPreview(true) : closeModalAndFile();
                    }}
                    disabled={!allowFiling || !canFile}
                  >
                    {selectedAttachments.length > 0 ? "Next" : "File"}
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const useStyles = makeStyles({
  personaContainer: {
    marginRight: "5px",
  },
  dropdown: {
    marginTop: "10px",
    marginLeft: "10px",
    marginRight: "10px",
  },
  projectHeader: {
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #E7E7E7",
    padding: "10px",
    marginBottom: "10px",
  },
  projectHeaderInfo: {
    display: "flex",
    flexDirection: "column",
    textAlign: "left",
    marginLeft: "5px",
  },
  projectHeaderTitle: {
    fontSize: "12px",
    fontWeight: "500",
  },
  checkbox: {
    textAlign: "left",
    paddingLeft: "10px",
    flexDirection: "row",
    display: "flex",
  },
  projectHeaderCode: {
    color: "#666666",
    fontSize: "11px",
    fontWeight: "400",
  },
  projectEmailsCount: {
    fontSize: "14px",
    marginBottom: "3px",
    fontWeight: 400,
    paddingLeft: "10px",
    textIndent: "-5px",
    color: "#666666",
  },
  projectEmailsCountText: {
    marginLeft: "10px",
    marginTop: "16px",
    fontSize: "14px",
    marginBottom: "3px",
    fontWeight: 600,
  },
  buttonRow: {
    display: "flex",
    justifyContent: "space-between",
    borderTop: "1px solid #E7E7E7",
    marginTop: "10px",
    padding: "10px",
    gap: "10px",
  },
  favouriteButton: {
    cursor: "pointer",
    background: "none",
    backgroundColor: "transparent",
    border: "none",
  },
  projectInfoContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
  },
  projectContainer: {
    padding: "5px 15px",
    borderBottom: "1px solid #E7E7E7",
    width: "100%",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    textAlign: "left",
    cursor: "pointer",
    backgroundColor: "#FFFFFF",
    transition: "background-color 0.3s",
    borderLeft: "none",
    borderRight: "none",
    borderTop: "none",
    "&:hover": {
      backgroundColor: "#f0f0f0",
    },
  },
  projectInfo: {
    display: "flex",
    flexDirection: "column",
    flex: "1",
  },
  projectTitle: {
    fontSize: "14px",
  },
  projectCode: {
    color: "#666666",
    fontSize: "12px",
  },
  modalOverlay: {
    position: "fixed",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  modal: {
    backgroundColor: "#FFFFFF",
    borderRadius: "8px",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    maxWidth: "400px",
    width: "90%",
    position: "relative",
  },
  closeButton: {
    position: "absolute",
    top: "10px",
    right: "10px",
    background: "transparent",
    border: "none",
    cursor: "pointer",
    color: "#666",
    padding: 0,
    "&:hover": {
      color: "#000",
    },
  },
});

export default Project;
