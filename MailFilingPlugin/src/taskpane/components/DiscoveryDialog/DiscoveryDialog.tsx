import { FC } from "react";
import { But<PERSON>, makeStyles } from "@fluentui/react-components";
import { AtveroLogo } from "../AtveroLogo/AtveroLogo";
import { Platform } from "../../services/PlatformService";
import { DiscoveryUrl, Version } from "../../Config";

interface DiscoveryDialogProps {
  platform?: Platform;
}

const DiscoveryDialog: FC<DiscoveryDialogProps> = ({ platform }) => {
  const styles = useStyles();

  const openDialog = async () => {
    try {
      Office.context.ui.displayDialogAsync(
        DiscoveryUrl(), // This needs to be changed to the URL we set in Admin
        { height: 70, width: 95 },
        (result) => {
          if (result.status === Office.AsyncResultStatus.Failed) {
            console.error("Failed to open dialog:", result.error.message);
          }
        }
      );
    } catch (error) {
      console.error("Error opening dialog:", error);
    }
  };

  // iOS can't open due to pop up issues so disable
  if (platform?.isMac || platform?.isIOS) {
    return <></>;
  }

  const buttonText = "Go to CMap Mail";

  return (
    <div className={styles.container}>
      <Button className={styles.button} onClick={openDialog}>
        <span className={styles.logo}>
          <AtveroLogo />
        </span>
        {buttonText}
      </Button>{" "}
      <span className={styles.version}>{Version()}</span>
    </div>
  );
};

const useStyles = makeStyles({
  container: {
    position: "fixed",
    background: "white",
    bottom: 0,
    left: 0,
    right: 0,
    width: "100%",
    padding: "13px",
    boxSizing: "border-box",
    borderTop: "1px solid #E7E7E7",
    minHeight: "55px",
  },
  button: {
    margin: "auto",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  logo: {
    marginRight: "5px",
    alignItems: "center",
    justifyContent: "center",
    height: "18px",
  },
  version: {
    color: "#ccc",
    fontSize: "8px",
    textAlign: "right",
    lineHeight: "8px",
    marginTop: "2px",
    width: "100%",
    display: "block",
  },
});

export default DiscoveryDialog;
