import { describe, it, expect, vi, beforeEach, Mock } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import DiscoveryDialog from "./DiscoveryDialog";
import { Platform } from "../../services/PlatformService";

type MockOffice = {
  context: {
    ui: {
      displayDialogAsync: Mock;
    };
  };
  AsyncResultStatus: {
    Failed: string;
  };
};

const mockOffice: MockOffice = {
  context: {
    ui: {
      displayDialogAsync: vi.fn(),
    },
  },
  AsyncResultStatus: {
    Failed: "failed",
  },
};

// Mock the Office object
vi.mock("office-js", () => mockOffice);

// Make the mock Office object available globally
(global as any).Office = mockOffice;

describe("DiscoveryDialog", () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
  });

  describe("with Atvero tenancy", () => {
    it("renders without crashing", () => {
      expect(() => render(<DiscoveryDialog />)).not.toThrow();
    });

    it("renders with correct button text for Atvero tenancy", () => {
      render(<DiscoveryDialog />);
      expect(screen.getByRole("button", { name: /Go to CMap Mail/i })).toBeInTheDocument();
    });

    it("calls Office.context.ui.displayDialogAsync when button is clicked", () => {
      render(<DiscoveryDialog />);

      const button = screen.getByRole("button", { name: /Go to CMap Mail/i });
      fireEvent.click(button);

      expect(mockOffice.context.ui.displayDialogAsync).toHaveBeenCalledTimes(1);
      expect(mockOffice.context.ui.displayDialogAsync).toHaveBeenCalledWith(
        "https://localhost:3001",
        { height: 70, width: 95 },
        expect.any(Function)
      );
    });
  });

  describe("with CMap tenancy", () => {
    it("renders without crashing", () => {
      expect(() => render(<DiscoveryDialog />)).not.toThrow();
    });

    it("renders with correct button text for CMap tenancy", () => {
      render(<DiscoveryDialog />);
      expect(screen.getByRole("button", { name: /Go to CMap Mail/i })).toBeInTheDocument();
    });

    it("calls Office.context.ui.displayDialogAsync when button is clicked", () => {
      render(<DiscoveryDialog />);

      const button = screen.getByRole("button", { name: /Go to CMap Mail/i });
      fireEvent.click(button);

      expect(mockOffice.context.ui.displayDialogAsync).toHaveBeenCalledTimes(1);
      expect(mockOffice.context.ui.displayDialogAsync).toHaveBeenCalledWith(
        "https://localhost:3001",
        { height: 70, width: 95 },
        expect.any(Function)
      );
    });
  });

  it("logs error when Office.context.ui.displayDialogAsync fails", () => {
    const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    render(<DiscoveryDialog />);

    const button = screen.getByRole("button", { name: /Go to CMap Mail/i });
    fireEvent.click(button);

    // Simulate a failed dialog open
    const callbackArg = { status: mockOffice.AsyncResultStatus.Failed, error: { message: "Test error" } };
    mockOffice.context.ui.displayDialogAsync.mock.calls[0][2](callbackArg);

    expect(consoleErrorSpy).toHaveBeenCalledWith("Failed to open dialog:", "Test error");

    consoleErrorSpy.mockRestore();
  });

  it("applies correct styles", () => {
    render(<DiscoveryDialog />);

    const container = screen.getByRole("button", { name: /Go to CMap Mail/i }).closest("div");
    expect(container).toHaveStyle({
      position: "fixed",
      bottom: "0",
      left: "0",
      right: "0",
      width: "100%",
      padding: "13px",
      boxSizing: "border-box",
      borderTop: "1px solid #E7E7E7",
    });
  });

  it("doesn't render the component on macOS platform", () => {
    const macPlatform: Platform = {
      platform: "Mac",
      isIOS: false,
      isMac: true,
      isWindows: false,
      isWeb: false,
      isMobile: false,
    };

    const { container } = render(<DiscoveryDialog platform={macPlatform} />);
    expect(container.firstChild).toBeNull();
  });

  it("doesn't render the component on iOS platform", () => {
    const iOSPlatform: Platform = {
      platform: "iOS",
      isIOS: true,
      isMac: false,
      isWindows: false,
      isWeb: false,
      isMobile: true,
    };

    const { container } = render(<DiscoveryDialog platform={iOSPlatform} />);
    expect(container.firstChild).toBeNull();
  });

  it("renders the component on Windows platform", () => {
    const windowsPlatform: Platform = {
      platform: "PC",
      isIOS: false,
      isMac: false,
      isWindows: true,
      isWeb: false,
      isMobile: false,
    };

    render(<DiscoveryDialog platform={windowsPlatform} />);
    expect(screen.getByRole("button", { name: /Go to CMap Mail/i })).toBeInTheDocument();
  });
});
