import { render, screen } from "@testing-library/react";
import { describe, it, expect, beforeEach } from "vitest";
import { EmptyStateMessage } from "./EmptyStateMessage";

describe("EmptyStateMessage", () => {
  describe("error messages", () => {
    it("displays correct message for NO_CONTEXT error", () => {
      render(<EmptyStateMessage errorState="NO_CONTEXT" />);
      expect(
        screen.getByText(
          "Unable to initialize Office context. Please try reloading Atvero Mail or contacting your Atvero Mail Admin."
        )
      ).toBeInTheDocument();
    });

    it("displays correct message for NO_MAILBOX error", () => {
      render(<EmptyStateMessage errorState="NO_MAILBOX" />);
      expect(
        screen.getByText("Unable to load your mailbox. Please try reloading Atvero Mail or contact a Atvero Admin.")
      ).toBeInTheDocument();
    });

    it("displays default message for null error state", () => {
      render(<EmptyStateMessage errorState={null} />);
      expect(
        screen.getByText(
          "A unknown error occured. Please send the following error to your local Mail admin - Error State is set to: " +
            null
        )
      ).toBeInTheDocument();
    });
  });

  describe("UI elements", () => {
    beforeEach(() => {
      render(<EmptyStateMessage errorState="NO_CONTEXT" />);
    });

    it("renders the Atvero logo", () => {
      const logo = screen.getByAltText("Atvero Logo");
      expect(logo).toBeInTheDocument();
      expect(logo.tagName.toLowerCase()).toBe("img");
    });

    it("displays the pin tip message", () => {
      expect(screen.getByText("Tip: Pin this add-in by clicking the pin icon above.")).toBeInTheDocument();
    });
  });
});
