import { Text, makeStyles, Image } from "@fluentui/react-components";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "space-between",
    height: "90vh",
    padding: "2rem",
  },
  contentGroup: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    marginTop: "auto",
    marginBottom: "auto",
  },
  tip: {
    fontSize: "12px",
    color: "#666666",
    textAlign: "center",
  },
  logo: {
    width: "50px",
    marginBottom: "10px",
  },
  message: {
    textAlign: "center",
  },
});

export type ErrorState = "NO_CONTEXT" | "NO_MAILBOX" | null;

const getErrorMessage = (errorState: ErrorState): string => {
  switch (errorState) {
    case "NO_CONTEXT":
      return "Unable to initialize Office context. Please try reloading Atvero Mail or contacting your Atvero Mail Admin.";
    case "NO_MAILBOX":
      return "Unable to load your mailbox. Please try reloading Atvero Mail or contact a Atvero Admin.";
    default:
      return (
        "A unknown error occured. Please send the following error to your local Mail admin - Error State is set to: " +
        errorState
      );
  }
};

interface EmptyStateMessageProps {
  errorState: ErrorState;
}

export const EmptyStateMessage = ({ errorState }: EmptyStateMessageProps) => {
  const styles = useStyles();

  return (
    <div className={styles.container}>
      <div className={styles.contentGroup}>
        <Image className={styles.logo} src="./assets/atvero_icon.svg" alt="Atvero Logo" />
        <Text className={styles.message}>{getErrorMessage(errorState)}</Text>
      </div>
      <Text className={styles.tip}>Tip: Pin this add-in by clicking the pin icon above.</Text>
    </div>
  );
};

export default EmptyStateMessage;
