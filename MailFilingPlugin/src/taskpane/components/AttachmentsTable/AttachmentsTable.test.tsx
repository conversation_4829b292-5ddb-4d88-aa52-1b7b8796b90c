import { render, screen, fireEvent } from "@testing-library/react";
import AttachmentFilingNotice from "./AttachmentsTable";
import { describe, it, expect, beforeEach, vi } from "vitest";
import React, { useState } from "react";

const baseAttachment = {
  name: "file1.pdf",
  size: 1024,
  isInline: false,
  recordTitle: undefined,
  revision: undefined,
  attachmentType: "file",
  contentType: "application/pdf",
  id: "1",
};

const renderWithControlledState = (props: Partial<React.ComponentProps<typeof AttachmentFilingNotice>> = {}) => {
  const Wrapper = () => {
    const [selectedAttachments, setSelectedAttachments] = useState<string[]>([]);
    return (
      <AttachmentFilingNotice
        selectedCount={1}
        attachments={[baseAttachment]}
        selectedAttachments={selectedAttachments}
        setSelectedAttachments={setSelectedAttachments}
        {...props}
      />
    );
  };

  return render(<Wrapper />);
};

describe("AttachmentFilingNotice", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("shows message when selectedCount !== 1", () => {
    renderWithControlledState({ selectedCount: 2, attachments: [] });
    expect(screen.getByText(/you can only file attachments/i)).toBeInTheDocument();
  });

  it("shows message when no non-inline attachments and selectedCount === 1", () => {
    renderWithControlledState({ attachments: [] });
    expect(screen.getByText(/no attachments/i)).toBeInTheDocument();
  });

  it("renders attachments when selectedCount === 1", () => {
    renderWithControlledState();
    expect(screen.getByText(/select attachments to file/i)).toBeInTheDocument();
    expect(screen.getByText("file1.pdf")).toBeInTheDocument();
    expect(screen.getByText("1.0 KB")).toBeInTheDocument();
    expect(screen.getAllByRole("checkbox")).toHaveLength(2); // select-all + row
  });

  it("toggles all attachments with header checkbox", () => {
    const attachments = [
      { ...baseAttachment, name: "a.pdf" },
      { ...baseAttachment, name: "b.pdf" },
    ];

    const Wrapper = () => {
      const [selectedAttachments, setSelectedAttachments] = useState<string[]>([]);
      return (
        <AttachmentFilingNotice
          selectedCount={1}
          attachments={attachments}
          selectedAttachments={selectedAttachments}
          setSelectedAttachments={setSelectedAttachments}
        />
      );
    };

    render(<Wrapper />);
    const checkboxes = screen.getAllByRole("checkbox");
    const header = checkboxes[0];

    // Initially unchecked
    expect(checkboxes[1]).not.toBeChecked();
    expect(checkboxes[2]).not.toBeChecked();

    fireEvent.click(header);
    expect(checkboxes[1]).toBeChecked();
    expect(checkboxes[2]).toBeChecked();

    fireEvent.click(header);
    expect(checkboxes[1]).not.toBeChecked();
    expect(checkboxes[2]).not.toBeChecked();
  });

  it("calls onEditAttachment when clicking a row in preview", () => {
    const onEditAttachment = vi.fn();
    renderWithControlledState({
      showPreview: true,
      selectedAttachments: ["file1.pdf"],
      onEditAttachment,
    });

    const row = screen.getByText("file1.pdf").closest("div");
    fireEvent.click(row!);
    expect(onEditAttachment).toHaveBeenCalledWith(expect.objectContaining({ name: "file1.pdf" }));
  });
});
