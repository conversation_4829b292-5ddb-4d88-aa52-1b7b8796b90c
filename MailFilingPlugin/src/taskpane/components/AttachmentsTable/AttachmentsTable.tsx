import React, { useEffect } from "react";
import {
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableHeaderCell,
  TableRow,
  Text,
  Tooltip,
} from "@fluentui/react-components";
import { makeStyles } from "@fluentui/react-components";
import { getFileIconClassFromName } from "../../services/GetAttachmentType";
import { DismissCircle24Filled } from "@fluentui/react-icons";
import { AttachmentWithMeta } from "../../types/types";

interface AttachmentFilingNoticeProps {
  selectedCount: number;
  attachments: AttachmentWithMeta[] | null;
  selectedAttachments: string[];
  setSelectedAttachments: (selected: string[]) => void;
  showPreview?: boolean;
  onEditAttachment?: (attachment: AttachmentWithMeta) => void;
}

const formatSize = (sizeInBytes: number): string => {
  const kb = sizeInBytes / 1024;
  return `${kb.toFixed(1)} KB`;
};

const AttachmentFilingNotice: React.FC<AttachmentFilingNoticeProps> = ({
  selectedCount,
  attachments,
  selectedAttachments,
  setSelectedAttachments,
  showPreview,
  onEditAttachment,
}) => {
  const styles = useStyles();
  const nonInline = attachments?.filter((a) => !a.isInline) ?? [];

  useEffect(() => {
    if (
      selectedCount !== 1 || // not a single email selected
      !attachments || // no attachments
      selectedAttachments.some((name) => !attachments.find((att) => att.name === name)) // selected items no longer valid
    ) {
      setSelectedAttachments([]);
    }
  }, [attachments, selectedCount]);

  const toggleAll = () => {
    if (selectedAttachments.length === nonInline.length) {
      setSelectedAttachments([]);
    } else {
      setSelectedAttachments(nonInline.map((att) => att.name));
    }
  };

  const toggleOne = (name: string) => {
    if (selectedAttachments.includes(name)) {
      setSelectedAttachments(selectedAttachments.filter((s) => s !== name));
    } else {
      setSelectedAttachments([...selectedAttachments, name]);
    }
  };

  if (selectedCount !== 1) {
    return (
      <div className={styles.container}>
        <div>
          <Text className={styles.headerText}>File Attachments</Text>
        </div>
        <div>
          <Text className={styles.infoMessage}>You can only file attachments when a single email is selected</Text>
        </div>
      </div>
    );
  }

  if (nonInline.length === 0) {
    return (
      <div className={styles.container}>
        <div>
          <Text className={styles.headerText}>File Attachments</Text>
        </div>
        <div>
          <Text className={styles.infoMessage}>The selected email has no attachments</Text>
        </div>
      </div>
    );
  }

  if (showPreview && selectedAttachments.length > 0 && attachments) {
    const selected = attachments.filter((att) => selectedAttachments.includes(att.name));

    return (
      <div className={styles.container}>
        <Text className={styles.headerText}>
          <span>File Attachments </span>
          <span className={styles.headerCount}>({selected.length})</span>
        </Text>
        {selected.some((att) => !att.recordTitle || !att.revision) && (
          <div className={styles.containerActionBanner} data-testid="attachment-action-banner">
            <span className={styles.containerActionIcon}>
              <DismissCircle24Filled />
            </span>
            <span className={styles.containerActionText}>
              {selected.filter((att) => !att.recordTitle || !att.revision).length} attachment
              {selected.filter((att) => !att.recordTitle || !att.revision).length !== 1 ? "s" : ""} require action
            </span>
          </div>
        )}
        {selected.map((att) => (
          <button
            key={att.name}
            type="button"
            data-testid={`edit-button-${att.name}`}
            className={styles.previewRow}
            onClick={() => onEditAttachment?.(att)}
          >
            <div className={styles.fileRow}>
              <i className={`ms-Icon ${getFileIconClassFromName(att.name)}`} style={{ fontSize: "16px" }} />
              <Tooltip content={att.name} relationship="label">
                <Text className={styles.attachmentText}>{att.name}</Text>
              </Tooltip>
            </div>
            <div className={styles.recordFieldRow}>
              <span className={styles.label}>Record Title:</span>
              <span className={styles.value}>
                {att.recordTitle ?? <span className={styles.recordFieldRequired}>Entry required</span>}
              </span>
            </div>
            <div className={styles.recordFieldRow}>
              <span className={styles.label}>Revision:</span>
              <span className={styles.value}>
                {att.revision ?? <span className={styles.recordFieldRequired}>Entry required</span>}
              </span>
            </div>
          </button>
        ))}
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div>
        <Text className={styles.headerText}>
          <span>File Attachments </span>
          <span className={styles.headerCount}>({nonInline.length})</span>
        </Text>
      </div>
      <Text className={styles.infoMessage}>Select attachments to file as records</Text>
      <Table className={styles.table}>
        <TableHeader>
          <TableRow>
            <TableHeaderCell className={styles.checkboxCell}>
              <div className={styles.checkboxWrapper}>
                <Checkbox
                  checked={selectedAttachments.length === nonInline.length}
                  onChange={toggleAll}
                  aria-label="Select all attachments"
                />
              </div>
            </TableHeaderCell>
            <TableHeaderCell>
              <Text className={styles.countTextCell}>
                {selectedAttachments.length} of {nonInline.length} selected
              </Text>
            </TableHeaderCell>
            <TableHeaderCell className={styles.sizeCell}></TableHeaderCell>
          </TableRow>
        </TableHeader>
        <TableBody>
          {nonInline.map((att) => (
            <TableRow key={att.name}>
              <TableCell className={styles.checkboxCell}>
                <div className={styles.checkboxWrapper}>
                  <Checkbox
                    checked={selectedAttachments.includes(att.name)}
                    onChange={() => toggleOne(att.name)}
                    aria-label={`Select ${att.name}`}
                  />
                </div>
              </TableCell>
              <TableCell className={styles.nameCell}>
                <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  <i
                    className={`ms-Icon ${getFileIconClassFromName(att.name)}`}
                    style={{ fontSize: "16px" }}
                    aria-hidden="true"
                  ></i>
                  <Tooltip content={att.name} relationship="label">
                    <Text className={styles.attachmentText}>{att.name}</Text>
                  </Tooltip>
                </div>
              </TableCell>
              <TableCell className={styles.sizeCell}>
                <Text className={styles.attachmentText}>{formatSize(att.size)}</Text>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

const useStyles = makeStyles({
  container: {
    marginTop: "16px",
    textAlign: "left",
    paddingLeft: "10px",
    paddingRight: "10px",
  },
  table: {
    tableLayout: "fixed",
    width: "100%",
  },
  checkboxWrapper: {
    display: "flex",
    alignItems: "center",
    height: "100%",
  },
  checkboxCell: {
    width: "36px",
    padding: 0,
  },
  nameCell: {
    width: "calc(100% - 80px)",
    overflow: "hidden",
    textOverflow: "ellipsis",
    padding: "0px",
  },
  sizeCell: {
    width: "68px",
    paddingRight: "0px",
    fontSize: "12px",
    fontWeight: 400,
    color: "#666666",
  },
  countTextCell: {
    color: "#666666",
    paddingLeft: "0px",
    fontSize: "12px",
    display: "flex",
    alignItems: "center",
  },
  headerText: {
    fontSize: "14px",
    fontWeight: 600,
    marginBottom: "12px",
  },
  headerCount: {
    fontSize: "14px",
    fontWeight: 400,
    color: "#666666",
  },
  infoMessage: {
    fontSize: "12px",
    fontWeight: 400,
    color: "#666666",
    marginTop: "6px",
    marginBottom: "6px",
    display: "block",
  },
  attachmentText: {
    fontSize: "12px",
    fontWeight: 400,
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  recordField: {
    fontSize: "12px",
    color: "#666666",
    marginTop: "4px",
  },
  recordFieldRequired: {
    color: "#C50F1F",
    marginLeft: "4px",
    fontWeight: 500,
  },
  previewRow: {
    padding: "10px 0",
    textAlign: "left",
    width: "100%",
    background: "none",
    border: "none",
    cursor: "pointer",
    display: "block",
    borderTop: "1px solid #E7E7E7",
    ":first-of-type": {
      borderTop: "1px solid #E7E7E7",
    },
    ":hover": {
      backgroundColor: "#f0f0f0",
    },
  },
  fileRow: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },
  containerActionBanner: {
    border: "1px solid #EEACB2",
    backgroundColor: "#FFF4F4",
    padding: "10px",
    marginTop: "12px",
    marginBottom: "12px",
    display: "flex",
    alignItems: "center",
    borderRadius: "4px",
  },
  containerActionIcon: {
    color: "#C50F1F",
    marginRight: "8px",
    display: "flex",
    alignItems: "center",
  },
  containerActionText: {
    fontSize: "14px",
    fontWeight: 500,
    color: "#000000",
  },
  recordFieldRow: {
    display: "flex",
    alignItems: "center",
    fontSize: "12px",
    color: "#666666",
    marginTop: "4px",
  },
  label: {
    minWidth: "72px",
    fontWeight: 500,
    flexShrink: 0,
  },
  value: {
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
});

export default AttachmentFilingNotice;
