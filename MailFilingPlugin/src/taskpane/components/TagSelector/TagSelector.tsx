import React, { useEffect } from "react";
import { Dropdown, Option, makeStyles } from "@fluentui/react-components";
import { Tag } from "../../types/types";

interface TagSelectorProps {
  tags: Tag[];
  selectedTag: string | undefined;
  onTagSelect: (tag: string) => void;
  disabled?: boolean;
}

const TagSelector: React.FC<TagSelectorProps> = ({ tags, selectedTag, onTagSelect, disabled = false }) => {
  const styles = useStyles();

  return (
    <Dropdown
      placeholder="Select a tag"
      value={selectedTag}
      onOptionSelect={(_, data) => onTagSelect(data.optionValue ?? "")}
      className={styles.dropdown}
      disabled={disabled}
    >
      <div className={styles.scrollableOptions}>
        {tags.map((tag) => (
          <Option key={tag.Name} value={tag.Name}>
            {tag.Name}
          </Option>
        ))}
      </div>
    </Dropdown>
  );
};

const useStyles = makeStyles({
  dropdown: {
    margin: "10px 10px 0 10px",
    width: "calc(100% - 20px)",
  },
  scrollableOptions: {
    maxHeight: "150px",
    overflowY: "auto",
  },
});

export default TagSelector;
