import { render } from "@testing-library/react";
import TagSelector from "./TagSelector";
import { vi } from "vitest";
import { Tag } from "../../types/types";
import { DEFAULT_TAGS } from "../DefaultTags";
import "@testing-library/jest-dom";

vi.mock("@fluentui/react-components", async (importOriginal) => {
  const actual = await importOriginal(); // Import the original Fluent UI components
  if (typeof actual === "object" && actual !== null) {
    // Ensure actual is an object
    return {
      ...actual, // Spread the original components into the mock
      Dropdown: vi.fn(({ children, disabled, ...props }) => (
        <div data-disabled={disabled} {...props}>
          {children}
        </div>
      )),
      Option: vi.fn(({ children, ...props }) => <div {...props}>{children}</div>),
    };
  }
  throw new Error("Fluent UI module is not an object");
});

describe("TagSelector", () => {
  const tags: Tag[] = DEFAULT_TAGS;

  it("renders the dropdown with the correct placeholder", () => {
    const { getByPlaceholderText } = render(<TagSelector tags={tags} selectedTag={undefined} onTagSelect={() => {}} />);
    const dropdown = getByPlaceholderText("Select a tag");
    expect(dropdown).toBeInTheDocument();
  });

  it("renders the correct number of options", () => {
    const { container } = render(<TagSelector tags={tags} selectedTag={undefined} onTagSelect={() => {}} />);
    const options = container.querySelectorAll("div");
    expect(options.length).toBe(tags.length + 2);
  });

  it("displays the selected tag correctly", () => {
    const { getByText } = render(<TagSelector tags={tags} selectedTag="Internal" onTagSelect={() => {}} />);
    const selectedOption = getByText("Internal");
    expect(selectedOption).toBeInTheDocument();
  });

  it("passes the disabled prop to the Dropdown when disabled", () => {
    const { container } = render(
      <TagSelector tags={tags} selectedTag="Internal" onTagSelect={() => {}} disabled={true} />
    );

    // Find the root dropdown div with the data-disabled attribute
    const dropdownElement = container.querySelector('[data-disabled="true"]');
    expect(dropdownElement).toBeInTheDocument();
  });

  it("is not disabled by default", () => {
    const { container } = render(<TagSelector tags={tags} selectedTag="Internal" onTagSelect={() => {}} />);

    // Find the root dropdown div with the data-disabled attribute
    const dropdownElement = container.querySelector('[data-disabled="false"]');
    expect(dropdownElement).toBeInTheDocument();
  });
});
