import React from "react";
import { Label, makeSty<PERSON>, <PERSON><PERSON>, Spinner } from "@fluentui/react-components";

import { IApiServices } from "../../adapters/IApiServices";

import { WebHookSubscription } from "../../types/types";
import { BackendUrl, ClientId } from "../../Config";

interface SettingsProps {
  apiServices: IApiServices;
  visible: boolean;
}

const Settings: React.FC<SettingsProps> = ({ apiServices, visible }) => {
  const styles = useStyles();

  const [useWebHooks, setUseWebHooks] = React.useState<boolean>(false);
  const [activeSubscriptions, setActiveSubscriptions] = React.useState<WebHookSubscription[] | undefined>(undefined);

  const [busy, setBusy] = React.useState<boolean>(false);

  React.useEffect(() => {
    const getHooksFlag = async (): Promise<void> => {
      const enableWebhooks = await apiServices.getSetting("useWebhooks");
      setUseWebHooks(enableWebhooks !== undefined && enableWebhooks !== "disabled");
    };

    getHooksFlag();
  }, []);

  React.useEffect(() => {
    const getActiveSubs = async (): Promise<void> => {
      const sharedMailbox = await apiServices.getSharedInfo();
      const activeSubs = await apiServices.getActiveSubscriptions(sharedMailbox);
      setActiveSubscriptions(activeSubs);
    };

    if (useWebHooks) {
      getActiveSubs();
    }
  }, [useWebHooks]);

  const clearSubscriptions = async () => {
    console.log("Clear subscriptions");
    setBusy(true);
    const sharedMailbox = await apiServices.getSharedInfo();
    const result = await apiServices.clearActiveSubscriptions(sharedMailbox);

    const activeSubs = await apiServices.getActiveSubscriptions(sharedMailbox);

    if (activeSubs && activeSubs.length > 0) {
      setActiveSubscriptions(activeSubs);
    } else {
      setActiveSubscriptions(undefined);
    }
    setBusy(false);
  };

  const launchConsent = async () => {
    // check again in case we have a subscription set up

    setBusy(true);

    const sharedMailbox = await apiServices.getSharedInfo();
    const activeSubs = await apiServices.getActiveSubscriptions(sharedMailbox);

    if (activeSubs && activeSubs.length > 0) {
      setActiveSubscriptions(activeSubs);
      return;
    }

    const encodedUrl = encodeURIComponent(BackendUrl());
    const state = {
      clientId: ClientId(),
      sharedMailbox: sharedMailbox,
    };
    let url =
      `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id=${ClientId()}&response_type=code&redirect_uri=${encodedUrl}%2Fapi%2FStoreUserCredentials&response_mode=query&scope=offline_access openid Sites.ReadWrite.All&state=` +
      encodeURIComponent(JSON.stringify(state));

    try {
      Office.context.ui.displayDialogAsync(url, { height: 70, width: 95 }, (result) => {
        if (result.status === Office.AsyncResultStatus.Failed) {
          console.error("Failed to open dialog:", result.error.message);
        }
      });
    } catch (error) {
      console.error("Error opening dialog:", error);
    }

    setBusy(false);
  };

  let renderSettings = () => {
    if (!useWebHooks)
      return (
        <div className={styles.noSettings}>
          <Label className={styles.noSettingsText}>There are currently no settings enabled.</Label>
        </div>
      );

    if (activeSubscriptions && activeSubscriptions.length > 0) {
      return (
        <div className={styles.automationSettings}>
          <Label className={styles.automationText}>Automation</Label>
          <Label>You have an active subscription.</Label>
          <Button className={styles.automationButton} onClick={clearSubscriptions} disabled={busy}>
            End All Subscriptions
          </Button>
          {busy && (
            <div className={styles.spinnerContainer}>
              <Spinner className={styles.spinner} />
            </div>
          )}
        </div>
      );
    }

    return (
      <div className={styles.automationSettings}>
        <Label className={styles.automationText}>Automation</Label>
        <Label>Enable Automation to have email filing processed without Outlook being open.</Label>
        <Label>A new window will open. Please follow any prompts.</Label>
        <Button className={styles.automationButton} onClick={launchConsent} disabled={busy}>
          Enable Automation
        </Button>
      </div>
    );
  };

  if (!visible) return null;

  return (
    <div data-testid="settings-content">
      <div className={styles.panel}>{renderSettings()}</div>
    </div>
  );
};

const useStyles = makeStyles({
  noSettingsText: {
    marginBottom: "10px",
  },
  noSettings: {
    marginTop: "20px",
    display: "flex",
    flexDirection: "column",
    textAlign: "center",
  },
  automationText: {
    marginBottom: "10px",
    fontWeight: "bold",
  },
  automationSettings: {
    marginTop: "20px",
    marginLeft: "10px",
    marginRight: "10px",
    display: "flex",
    flexDirection: "column",
    textAlign: "left",
  },
  automationButton: {
    marginTop: "20px",
  },
  itemContainer: {
    marginTop: "25px",
    display: "flex",
    flexDirection: "column",
    width: "100%",
    padding: 0,
    margin: 0,
  },
  panel: {
    marginTop: "25px",
    overflowY: "auto",
    flexGrow: 1,
    width: "100%",
    padding: 0,
    margin: 0,
    boxSizing: "border-box",
  },
  spinnerContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    height: "100%",
  },
  spinner: {},
});

export default Settings;
