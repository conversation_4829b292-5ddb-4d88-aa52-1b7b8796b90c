import React from "@vitejs/plugin-react";

import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import Settings from "./Settings";
import { vi, expect, describe, test } from "vitest";
//import { Project as ProjectType, Tag } from "../../types/types";
import { TestApiServices } from "../../adapters/TestApiServices";
import { IApiServices } from "../../adapters/IApiServices";

let apiServices: IApiServices = new TestApiServices();

global.Office = {
  context: {
    mailbox: {
      addHandlerAsync: vi.fn(),
      removeHandlerAsync: vi.fn(),
      getSelectedItemsAsync: vi.fn(),
    },
  },
  AsyncResultStatus: {
    Succeeded: "succeeded",
    Failed: "failed",
  },
  EventType: {
    SelectedItemsChanged: "selectedItemsChanged",
  },
} as unknown as typeof Office;

describe("Settings component", () => {
  test("renders the settings content", () => {
    render(<Settings apiServices={apiServices} visible={true} />);
    expect(screen.getByTestId("settings-content")).toBeInTheDocument();
  });

  test("renders no settings if webooks not enabled", async () => {
    render(<Settings apiServices={apiServices} visible={true} />);
    await waitFor(() => {
      expect(screen.getByText("There are currently no settings enabled."));
    });
  });

  test("renders automation settings if webooks enabled", async () => {
    apiServices.getSetting = vi.fn().mockResolvedValue("enabled");
    apiServices.getSharedInfo = vi.fn().mockResolvedValue(undefined);
    apiServices.getActiveSubscriptions = vi.fn().mockResolvedValue([]);
    render(<Settings apiServices={apiServices} visible={true} />);
    await waitFor(() => {
      expect(screen.getByText("Enable Automation"));
    });

    const actionButton = screen.getByText("Enable Automation");
    fireEvent.click(actionButton);
  });

  test("renders end subscriptions button if subscriptions", async () => {
    apiServices.getSetting = vi.fn().mockResolvedValue("enabled");
    apiServices.getSharedInfo = vi.fn().mockResolvedValue(undefined);
    apiServices.getActiveSubscriptions = vi.fn().mockResolvedValue([{ subscription: "xxx" }]);

    render(<Settings apiServices={apiServices} visible={true} />);
    await waitFor(() => {
      expect(screen.getByText("End All Subscriptions"));
    });

    const actionButton = screen.getByText("End All Subscriptions");
    apiServices.getActiveSubscriptions = vi.fn().mockResolvedValue([]);

    fireEvent.click(actionButton);

    await waitFor(() => {
      expect(screen.getByText("Enable Automation"));
    });
  });
});
