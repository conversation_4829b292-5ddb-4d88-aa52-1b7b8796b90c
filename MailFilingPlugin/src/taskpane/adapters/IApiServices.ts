import { FiledEmail, Hubsite, Project, SelectedItems, UserPermissions, Tag, WebHookSubscription } from "../types/types";

export interface IApiServices {
  login(): Promise<string | undefined>;

  getProjects(hubsite: string, searchTerm: string | undefined): Promise<Project[]>;
  getHubsites(): Promise<Hubsite[]>;
  getTags(): Promise<Tag[]>;
  fileEmails(
    selectedItems: SelectedItems[],
    projectCode: string,
    sitePath: string,
    tag: string,
    isConfidential: boolean,
    isImportant: boolean
  ): Promise<boolean>;

  syncEmails(): Promise<boolean>;
  getFavouriteProjects(): Promise<Project[]>;
  setFavouriteProjects(project: Project): Promise<boolean>;
  getFiledEmailInfo(internetMessageId: string): Promise<FiledEmail[]>;
  deleteFavouriteProject(project: Project): Promise<boolean>;
  getUserPermissions(sitePath: string): Promise<UserPermissions>;

  getSetting(setting: string): Promise<string | undefined>;

  setSubject(projectCode: string): Promise<string | undefined>;
  setFilingProperty(key: string, value: string): Promise<boolean>;
  setFilingMessage(projectCode: string): Promise<boolean>;

  getActiveSubscriptions(sharedMailbox: string | undefined): Promise<WebHookSubscription[] | undefined>;

  clearActiveSubscriptions(sharedMailbox: string | undefined): Promise<boolean>;

  getSharedInfo(): Promise<string | undefined>;

  isConversationFiled(sharedMailbox: string | undefined, conversationId: string): Promise<boolean>;
}
