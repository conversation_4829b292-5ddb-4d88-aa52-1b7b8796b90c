import { IAuthService } from "../adapters/IAuthService";
import { jwtDecode } from "jwt-decode";
import * as Sentry from "@sentry/react";
import { IPublicClientApplication } from "@azure/msal-browser";
import { BackendScope } from "../Config";
import { IApiServices } from "./IApiServices";

const checkIfExpired5MinsEarly = (exp: number | undefined): boolean => {
  if (exp === undefined) {
    return true;
  }

  const diff = 5; // 5 minutes added below

  if (exp !== undefined) {
    const checkTime = new Date(new Date().getTime() + diff * 60000); // add 5 mins to the current time to expire early
    const expiryTime = exp * 1000; // times 1000 to match the millisecond precision

    if (checkTime.getTime() > expiryTime) {
      return true;
    } else return false;
  } else {
    return true;
  }
};

function getTokenExpiry(token: string) {
  const decoded = jwtDecode(token);
  if (decoded != null) {
    const expires = decoded.exp;
    return expires;
  }
  return undefined;
}

async function getOfficeAccessToken(options?: OfficeRuntime.AuthOptions): Promise<string | undefined> {
  try {
    return await OfficeRuntime.auth.getAccessToken(options);
  } catch (exception: any) {
    // Always fails on mobile
    console.log("Failed to obtain token", exception);
    return undefined;
  }
}

export class AuthService implements IAuthService {
  middletierToken: string | undefined = undefined;
  tokenExpires: number | undefined = undefined;
  pca: IPublicClientApplication | undefined;
  useNaa: boolean | undefined;

  constructor(pca: IPublicClientApplication | undefined) {
    this.pca = pca;
    this.useNaa = undefined;
  }

  async getOldToken(): Promise<string | undefined> {
    if (checkIfExpired5MinsEarly(this.tokenExpires)) {
      console.log("Token has expired");
      this.middletierToken = undefined;
      this.tokenExpires = undefined;
    }

    if (this.middletierToken === undefined) {
      console.log("Obtaining new middletierToken");
      try {
        this.middletierToken = await getOfficeAccessToken({
          allowSignInPrompt: true,
          allowConsentPrompt: false, // users shouldn't be consenting (MR)
          forMSGraphAccess: false, // can't call graph direct
        });

        if (this.middletierToken === undefined) {
          console.log("Failed to obtain a middletier token");
        }
      } catch (exception: any) {
        this.middletierToken = undefined;
        this.tokenExpires = undefined;

        console.log("Failed with exception whilst using old token fetch");
        Sentry.captureException(exception);
      }
    }

    if (this.middletierToken !== undefined) {
      const newExpires = getTokenExpiry(this.middletierToken);
      if (newExpires != null) {
        this.tokenExpires = newExpires;
        return this.middletierToken;
      }
    }
    console.log("Unable to obtain a token");
    return undefined;
  }

  async getTokenWithNAA(pca: IPublicClientApplication | undefined) {
    if (pca === undefined) return undefined;

    const loginHint = Office.context.mailbox.userProfile.emailAddress;

    const account = pca.getAccountByUsername(loginHint);

    console.log("Account for PCA is ", account);
    const redirectUri = `https://${BackendScope()}`;

    console.log("redirect is", redirectUri);

    const backendScopeUri = BackendScope();

    console.log("backendScopeUri is", backendScopeUri);

    const scopes = [backendScopeUri, "openid", "offline_access"];
    const tokenRequest = {
      scopes: scopes,
    };
    let accessToken = undefined;
    try {
      console.log("Trying to acquire token silently...");
      //  const userAccount = await pca.acquireTokenSilent(tokenRequest);
      const acquireTokenSilent = account
        ? await pca.acquireTokenSilent({
            scopes,
            account,
            redirectUri,
          })
        : await pca.ssoSilent({
            loginHint,
            scopes,
            redirectUri,
          });

      console.log("Acquired token silently.");
      accessToken = acquireTokenSilent.accessToken;
    } catch (error) {
      console.log(`Unable to acquire token silently: ${error}`);
    }

    if (accessToken === undefined) {
      // Acquire token silent failure. Send an interactive request via popup.
      try {
        console.log("Trying to acquire token interactively...");
        const userAccount = await pca.acquireTokenPopup(tokenRequest);
        console.log("Acquired token interactively.");
        accessToken = userAccount.accessToken;
      } catch (popupError) {
        // Acquire token interactive failure.
        console.log(`Unable to acquire token interactively: ${popupError}`);
      }
    }
    return accessToken;
  }

  async getToken(apiService: IApiServices): Promise<string | undefined> {
    // only use NAA for Mobile and Mac

    const contextInfo = Office.context.diagnostics;
    const platformType: Office.PlatformType = contextInfo.platform;

    if (platformType === Office.PlatformType.Android || platformType === Office.PlatformType.iOS) {
      console.log("Mobile, we only use NAA");
      if (this.pca !== undefined) {
        console.log("Trying to get an NAA token");
        const naaToken = await this.getTokenWithNAA(this.pca);
        if (naaToken !== undefined) return naaToken;
      }

      console.log("Failed to get an NAA token, failing");
      return undefined;
    }

    // useNAA is not the default
    let useNAA = false;

    if (this.useNaa !== undefined) {
      useNAA = this.useNaa;
    } else {
      const useNaaSetting = await apiService.getSetting("useNAA");
      if (useNaaSetting === undefined) {
        this.useNaa = false;
      } else {
        if (useNaaSetting === "enabled") {
          this.useNaa = true;
          useNAA = true;
        } else {
          this.useNaa = false;
        }
      }
    }

    if (useNAA) {
      if (this.pca !== undefined) {
        const naaToken = await this.getTokenWithNAA(this.pca);
        if (naaToken !== undefined) return naaToken;
      }

      console.log("Failed to get an NAA token, falling back");
      return await this.getOldToken();
    }

    console.log("Using Office auth to get token");
    return await this.getOldToken();
  }
}
