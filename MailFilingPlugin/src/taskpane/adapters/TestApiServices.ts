import {
  Hubsite,
  Project,
  SelectedItems,
  UserPermissions,
  Project as ProjectType,
  FiledEmail,
  Tag,
  WebHookSubscription,
} from "../types/types";

import { IApiServices } from "./IApiServices";

const mockProjects: ProjectType[] = [
  {
    ProjectCode: "P123",
    ProjectTitle: "Test Project 1",
    Rank: 1,
    Id: "3",
    SitePath: "https://test.sharepoint.com/sites/testhub/P123",
  },
  {
    ProjectCode: "P124",
    ProjectTitle: "Another Project",
    Rank: 2,
    Id: "2",
    SitePath: "https://test.sharepoint.com/sites/testhub/P124",
  },
  {
    ProjectCode: "P125",
    ProjectTitle: "Sample Project",
    Rank: 3,
    Id: "3",
    SitePath: "https://test.sharepoint.com/sites/testhub/P125",
  },
];

const mockOneHubSite: Hubsite[] = [
  {
    Name: "Test Hub",
    Title: "Test Hub",
    ID: "0",
    WebUrl: "https://testhub.com/sites/testhub",
    CustomTags: false,
  },
];

const mockFavourites: ProjectType[] = [
  {
    ProjectCode: "P123",
    ProjectTitle: "Test Project 1",
    Rank: 1,
    Id: "1",
    SitePath: "https://test.sharepoint.com/sites/testhub/P123",
  },
  {
    ProjectCode: "P124",
    ProjectTitle: "Another Project",
    Rank: 2,
    Id: "2",
    SitePath: "https://test.sharepoint.com/sites/testhub/P124",
  },
];

const mockFiledEmails: FiledEmail[] = [
  {
    ProjectCode: "ACM001",
    InternetMessageId: "\<EMAIL>\u003E",
    Tag: "Client",
    Confidential: false,
    Important: false,
    SharedMailbox: null,
    FiledBy: "Allan Chaplin",
    Timestamp: "2025-01-30T15:23:23.0287112+00:00",
  },
  {
    ProjectCode: "100033",
    InternetMessageId: "\<EMAIL>\u003E",
    Tag: "Internal",
    Confidential: false,
    Important: false,
    SharedMailbox: null,
    FiledBy: "Allan Chaplin",
    Timestamp: "2025-01-30T15:23:21.2694462+00:00",
  },
  {
    ProjectCode: "ACM001",
    InternetMessageId: "\<EMAIL>\u003E",
    Tag: "Internal",
    Confidential: false,
    Important: true,
    SharedMailbox: null,
    FiledBy: "Allan Chaplin",
    Timestamp: "2025-01-30T15:23:18.7764073+00:00",
  },
];

export class TestApiServices implements IApiServices {
  async login() {
    return "token";
  }

  async getSetting(_setting: string): Promise<string | undefined> {
    return undefined;
  }

  async getProjects(_hubsite: string, searchTerm: string | undefined): Promise<Project[]> {
    if (searchTerm === undefined) return Promise.resolve(mockProjects);

    return Promise.resolve(
      mockProjects.filter((p) => {
        return p.ProjectCode.includes(searchTerm) || p.ProjectTitle.includes(searchTerm);
      })
    );
  }

  // by default return one hubsite so it auto selects

  async getHubsites(): Promise<Hubsite[]> {
    return Promise.resolve(mockOneHubSite);
  }

  async getTags(): Promise<Tag[]> {
    return Promise.resolve([]);
  }

  async fileEmails(
    _selectedItems: SelectedItems[],
    _projectCode: string,
    _sitePath: string,
    _tag: string,
    _isConfidential: boolean,
    _isImportant: boolean
  ): Promise<boolean> {
    return Promise.resolve(true);
  }

  async syncEmails(): Promise<boolean> {
    return Promise.resolve(true);
  }

  async getFavouriteProjects(): Promise<Project[]> {
    return Promise.resolve(mockFavourites);
  }
  async setFavouriteProjects(_project: Project): Promise<boolean> {
    return Promise.resolve(true);
  }
  async deleteFavouriteProject(_project: Project): Promise<boolean> {
    return Promise.resolve(true);
  }
  async getUserPermissions(_sitePath: string): Promise<UserPermissions> {
    return Promise.resolve({
      CanFileConfidential: true,
      Licensed: true,
      CanFile: true,
    });
  }

  async setSubject(_projectCode: string) {
    return Promise.resolve("");
  }

  async setFilingProperty(_key: string, _value: string): Promise<boolean> {
    return Promise.resolve(false);
  }

  async setFilingMessage(_projectCode: string): Promise<boolean> {
    return Promise.resolve(false);
  }

  async getFiledEmailInfo(): Promise<FiledEmail[]> {
    return Promise.resolve(mockFiledEmails);
  }

  async getActiveSubscriptions(_sharedMailbox: string | undefined): Promise<WebHookSubscription[] | undefined> {
    return Promise.resolve([]);
  }

  async getSharedInfo(): Promise<string | undefined> {
    return undefined;
  }

  async clearActiveSubscriptions(_sharedMailbox: string | undefined): Promise<boolean> {
    return true;
  }

  async isConversationFiled(_sharedMailbox: string | undefined, _conversationId: string): Promise<boolean> {
    return false;
  }
}
