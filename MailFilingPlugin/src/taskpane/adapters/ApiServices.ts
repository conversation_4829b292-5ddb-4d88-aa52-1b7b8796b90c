import {
  FiledEmail,
  FiledEmailResponse,
  Hubsite,
  Project,
  SelectedItems,
  UserPermissions,
  Tag,
  WebHookSubscription,
} from "../types/types";

import { IApiServices } from "./IApiServices";
import * as Sentry from "@sentry/react";
import { IAuthService } from "./IAuthService";

interface IHubsiteResponse {
  DisplayName: string;
  Name: string;
  SiteUrl: string;
  CustomTags: boolean;
}

import { prependSubject } from "../components/AppHelpers";
import { BackendUrl } from "../Config";

export class ApiServices implements IApiServices {
  authService: IAuthService;
  activeToken: string | undefined;
  // memoize last search
  lastHubsite: string | undefined;
  lastSearch: string | undefined;
  lastResult: Project[] | undefined;

  loadedHubsites: Hubsite[] | undefined;

  loadedTags: Tag[] | undefined;

  constructor(authService: IAuthService) {
    this.authService = authService;
    this.lastHubsite = undefined;
    this.lastSearch = undefined;
    this.lastResult = undefined;
    this.loadedHubsites = undefined;
    this.activeToken = undefined;
  }

  async login() {
    const token = await this.authService.getToken(this);
    this.activeToken = token;
    return token;
  }

  async getSetting(setting: string): Promise<string | undefined> {
    const url = `${BackendUrl()}/api/AppSettings`;

    const email = Office.context.mailbox.userProfile.emailAddress;

    if (email === undefined || email === null) return undefined;

    const at = email.indexOf("@");

    if (at < 0) return undefined;

    const domain = email.slice(at + 1).replace(/\./g, "");

    try {
      // const response = await fetch(url + "?setting=" + setting + "&domain=" + domain, {
      //   method: "GET",
      // });

      // special case for useNAA as we aren't authenticated

      const response =
        setting === "useNAA"
          ? await this.fetchUnauthenticatedEndpoint(url + "?setting=" + setting + "&domain=" + domain, "GET", undefined)
          : await this.fetchEndpoint(url + "?setting=" + setting + "&domain=" + domain, "GET", undefined);

      if (!response?.ok) {
        Sentry.captureMessage(
          "Failed to get setting " + setting + " : " + response.status + " " + (await response.text())
        );
        return undefined;
      }

      const settingVal = await response.json();

      if (settingVal.Message === "") return undefined;

      return settingVal.Message;
    } catch (error: unknown) {
      console.log("Error ", error);
      if (error instanceof Error && error.message !== null) {
        Sentry.captureException(error);
        console.log(error.message);
        return undefined;
      } else {
        return undefined;
      }
    }
  }

  async fetchEndpoint(url: string, method: string, body: string | undefined): Promise<any> {
    // to stop token race conditions, don't try and get another one if we don't already have one
    if (this.activeToken === undefined) {
      return;
    }

    // this will get the active token, or refresh it
    let token: string | undefined = await this.authService.getToken(this);

    if (token === undefined) {
      console.log("Unable to get a token");
      return undefined;
    }

    try {
      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
          "ngrok-skip-browser-warning": "true", // leave this there for local machine developers
        },
        body: body,
      });

      if (!response.ok) {
        Sentry.captureMessage("Failed to fetchEndpoint " + response.status + " " + response.text);
        throw new Error(response.status.toString() + "," + response.text);
      }
      return response;
    } catch (error: unknown) {
      if (error instanceof Error && error.message !== null) {
        Sentry.captureException(error);
        throw new Error("Failed to call to " + url + " endpoint");
      } else {
        throw new Error("Failed to call to " + url + " endpoint");
      }
    }
  }

  async fetchUnauthenticatedEndpoint(url: string, method: string, body: string | undefined): Promise<any> {
    try {
      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
        },
        body: body,
      });

      if (!response.ok) {
        Sentry.captureMessage("Failed to fetchEndpoint " + response.status + " " + response.text);
        throw new Error(response.status.toString() + "," + response.text);
      }
      return response;
    } catch (error: unknown) {
      if (error instanceof Error && error.message !== null) {
        Sentry.captureException(error);
        throw new Error("Failed to call to " + url + " endpoint");
      } else {
        throw new Error("Failed to call to " + url + " endpoint");
      }
    }
  }

  async getProjects(hubsitPath: string, searchTerm: string = ""): Promise<Project[]> {
    if (this.lastHubsite === hubsitPath && this.lastSearch === searchTerm && this.lastResult !== undefined) {
      return this.lastResult;
    }

    try {
      const searchQuery = searchTerm ? `&search=${encodeURIComponent(searchTerm)}` : "";
      const url = `${BackendUrl()}/api/projects?hubsitepath=${hubsitPath}${searchQuery}`;
      const response = await this.fetchEndpoint(url, "GET", undefined);

      if (response === undefined || !response.ok) {
        throw new Error("Failed to get a list of projects");
      }

      const data = await response.json();

      // memoize
      this.lastHubsite = hubsitPath;
      this.lastSearch = searchTerm;
      this.lastResult = data.Data;

      return data.Data;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error("Failed to call to Projects endpoint: " + error.message);
      } else {
        throw new Error("Failed to call to Projects endpoint");
      }
    }
  }

  async getHubsites(): Promise<Hubsite[]> {
    if (this.loadedHubsites !== undefined) return this.loadedHubsites;

    try {
      const url = `${BackendUrl()}/api/hubsites`;
      const response = await this.fetchEndpoint(url, "GET", undefined);

      if (response === undefined || !response.ok) {
        throw new Error("Failed to get a list of hubsites");
      }

      const data = await response.json();

      const hubsites = data.Data.map((hub: IHubsiteResponse) => ({
        WebUrl: hub.SiteUrl,
        Name: hub.Name,
        Title: hub.DisplayName ?? "No Hubsite Name",
        CustomTags: hub.CustomTags ?? false,
      }));

      this.loadedHubsites = hubsites;

      return hubsites;
    } catch (error: unknown) {
      console.log("caught ", error);
      if (error instanceof Error) {
        throw new Error("Failed to call to hubsites endpoint: " + error.message);
      } else {
        throw new Error("Failed to call to hubsites endpoint");
      }
    }
  }

  async getActiveSubscriptions(sharedMailbox: string | undefined): Promise<WebHookSubscription[] | undefined> {
    try {
      let url = `${BackendUrl()}/api/CheckUserSubscription`;

      if (sharedMailbox) {
        url = url + "?sharedmailbox=" + sharedMailbox;
      }
      const response = await this.fetchEndpoint(url, "GET", undefined);

      if (response === undefined || !response.ok) {
        throw new Error("Failed to get active subscriptions");
      }

      const data = await response.json();

      const subscriptions = data.Data;

      return subscriptions;
    } catch (error: unknown) {
      console.log("caught ", error);
      if (error instanceof Error) {
        throw new Error("Failed to call to subscriptions endpoint: " + error.message);
      } else {
        throw new Error("Failed to call to subscriptions endpoint");
      }
    }
  }

  async getTags(): Promise<Tag[]> {
    if (this.loadedTags !== undefined) return this.loadedTags;

    try {
      const url = `${BackendUrl()}/api/customtags`;
      const response = await this.fetchEndpoint(url, "GET", undefined);

      if (response === undefined || !response.ok) {
        throw new Error("Failed to get a list of custom tags");
      }

      const data = await response.json();

      // not sure we need to do this
      const tags = data.Data.map((tag: Tag) => ({
        Name: tag.Name,
        Colour: tag.Colour,
        BackgroundColour: tag.BackgroundColour,
        Order: tag.Order,
      }));

      this.loadedTags = tags;

      return tags;
    } catch (error: unknown) {
      console.log("caught ", error);
      if (error instanceof Error) {
        throw new Error("Failed to call to custom tags endpoint: " + error.message);
      } else {
        throw new Error("Failed to call to custom tags endpoint");
      }
    }
  }

  async getSharedInfo() {
    return new Promise<string | undefined>((resolve) => {
      if (Office.context.mailbox.item && Office.context.mailbox.item.getSharedPropertiesAsync) {
        Office.context.mailbox.item.getSharedPropertiesAsync((result) => {
          if (result.status === Office.AsyncResultStatus.Failed) {
            console.error("The current folder or mailbox isn't shared.");
            resolve(undefined);
          }
          const sharedProperties = result.value;
          // console.log(`Owner: ${sharedProperties.owner}`);
          // console.log(`Permissions: ${sharedProperties.delegatePermissions}`);
          console.log(`Target mailbox: ${sharedProperties.targetMailbox}`);
          resolve(sharedProperties.targetMailbox);
        });
      } else {
        resolve(undefined);
      }
    });
  }

  async fileEmails(
    selectedItems: SelectedItems[],
    projectCode: string,
    sitePath: string,
    tag: string,
    isConfidential: boolean,
    isImportant: boolean
  ): Promise<boolean> {
    // need to determine the mailbox type

    const sharedMailbox = await this.getSharedInfo();

    const entries = selectedItems.map((item: SelectedItems) => ({
      itemId: Office.context.mailbox.convertToRestId(item.itemId, Office.MailboxEnums.RestVersion.v1_0),
    }));

    if (entries.length === 0) {
      return true;
    }

    let payload = {
      projectCode: projectCode,
      sitePath: sitePath,
      tag: tag,
      emails: entries,
      isConfidential: isConfidential,
      isImportant: isImportant,
      sharedMailbox: sharedMailbox,
    };

    try {
      const url = `${BackendUrl()}/api/EnqueueEmailRequest`;
      const response = await this.fetchEndpoint(url, "POST", JSON.stringify(payload));

      if (response === undefined || !response.ok) {
        Sentry.captureException("Failed to file emails: " + response, {
          extra: {
            projectCode,
            sitePath,
            tag,
            isConfidential,
            isImportant,
            selectedItems: selectedItems,
            selectedItemsCount: selectedItems?.length,
            response: response,
          },
        });
        throw new Error("Failed to file email batch");
      }
      return true;
    } catch (error: unknown) {
      Sentry.captureException("Failed to file emails: " + error, {
        extra: {
          projectCode,
          sitePath,
          tag,
          isConfidential,
          isImportant,
          selectedItems: selectedItems,
          selectedItemsCount: selectedItems?.length,
          error:
            error instanceof Error
              ? {
                  message: error.message,
                  stack: error.stack,
                }
              : String(error),
        },
      });

      if (error instanceof Error) {
        throw new Error("Failed to file emails: " + error.message);
      } else {
        throw new Error("Failed to file emails");
      }
    }
  }

  async getFiledEmailInfo(internetMessageId: string): Promise<FiledEmail[]> {
    try {
      const url = `${BackendUrl()}/api/GetInfo?internetMessageId=${internetMessageId}`;
      const response = await this.fetchEndpoint(url, "GET", undefined);
      if (response === undefined || !response.ok) {
        throw new Error("Failed to get filed email info");
      }

      const data: FiledEmailResponse = await response.json();
      return data.Data;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error("Failed to get filed email info: " + error.message);
      } else {
        throw new Error("Failed to get filed email info");
      }
    }
  }

  async syncEmails(): Promise<boolean> {
    try {
      const url = `${BackendUrl()}/api/SyncEmails`;
      const response = await this.fetchEndpoint(url, "POST", undefined);

      if (response === undefined || !response.ok) {
        console.error(response.body);
        throw new Error("Failed to sync emails ");
      }
      return true;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error("Failed to call sync emails: " + error.message);
      } else {
        throw new Error("Failed to call sync emails");
      }
    }
  }

  async getFavouriteProjects(): Promise<Project[]> {
    try {
      const url = `${BackendUrl()}/api/Favourites?`;
      const response = await this.fetchEndpoint(url, "GET", undefined);
      if (response === undefined || !response.ok) {
        console.error(response.body);
        throw new Error("Failed to get a list of Favourite projects");
      }
      // The API returns nothing and a 200 if favourites have never existed.

      const responseText = await response.text();
      if (responseText) {
        const data = JSON.parse(responseText);

        console.log("@@@@favourites returned", data);
        if (data) {
          return data;
        } else {
          return [];
        }
      } else {
        return [];
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error("Failed to call to Favourite Projects endpoint: " + error.message);
      } else {
        throw new Error("Failed to call to Favourite Projects endpoint");
      }
    }
  }

  async setFavouriteProjects(project: Project): Promise<boolean> {
    try {
      const payload = [project];
      const url = `${BackendUrl()}/api/Favourites?`;
      const response = await this.fetchEndpoint(url, "POST", JSON.stringify(payload));

      if (response === undefined || !response.ok) {
        console.error(response.body);
        throw new Error("Failed to set favourites ");
      }
      return true;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error("Failed to call to Favourite Projects endpoint: " + error.message);
      } else {
        throw new Error("Failed to call to Favourite Projects endpoint");
      }
    }
  }

  async deleteFavouriteProject(project: Project): Promise<boolean> {
    try {
      const url = `${BackendUrl()}/api/Favourites?`;
      const response = await this.fetchEndpoint(url, "DELETE", JSON.stringify([project]));

      if (response === undefined || !response.ok) {
        throw new Error("Failed to file email batch");
      }
      return true;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error("Failed to call to Favourite Projects endpoint: " + error.message);
      } else {
        throw new Error("Failed to call to Favourite Projects endpoint");
      }
    }
  }

  async getUserPermissions(sitePath: string): Promise<UserPermissions> {
    try {
      const url = `${BackendUrl()}/api/UserPermissions?sitePath=${sitePath}`;
      const response = await this.fetchEndpoint(url, "GET", undefined);

      if (response === undefined || !response.ok) {
        throw new Error("Failed to get users permissions");
      }

      const data = await response.json();

      return data.Data;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error("Failed to call to User Permissions endpoint: " + error.message);
      } else {
        throw new Error("Failed to call to User Permissions endpoint");
      }
    }
  }

  // return the new subject if successful
  async setSubject(projectCode: string): Promise<string | undefined> {
    return new Promise<string | undefined>((resolve) => {
      if (
        Office.context !== undefined &&
        Office.context.mailbox !== undefined &&
        Office.context.mailbox.item !== undefined
      ) {
        const item = Office.context.mailbox.item;
        item.subject.getAsync((asyncResult) => {
          const subject = asyncResult.value;
          const modifiedSubject = prependSubject(projectCode, subject);
          item.subject.setAsync(
            modifiedSubject,

            (asyncResult) => {
              if (asyncResult.status === Office.AsyncResultStatus.Failed) {
                console.log("Failed to set subject");
                resolve(undefined);
              } else resolve(modifiedSubject);
            }
          );
        });
      } else {
        resolve(undefined);
      }
    });
  }

  async setFilingMessage(projectCode: string): Promise<boolean> {
    console.log("Setting setFilingMessage " + projectCode);
    return new Promise<boolean>((resolve) => {
      if (
        Office.context !== undefined &&
        Office.context.mailbox !== undefined &&
        Office.context.mailbox.item !== undefined
      ) {
        const details = {
          type: Office.MailboxEnums.ItemNotificationMessageType.InformationalMessage,
          message: "Your message will be filed to " + projectCode,
          icon: "PG.Icon.16",
          persistent: true,
        };
        Office.context.mailbox.item.notificationMessages.addAsync("CmapMail", details, (_result: any) => {
          resolve(true);
        });
      } else {
        resolve(false);
      }
    });
  }

  async setFilingProperty(key: string, value: string): Promise<boolean> {
    console.log("Setting filing property " + key + " " + value);
    return new Promise<boolean>((resolve) => {
      if (
        Office.context !== undefined &&
        Office.context.mailbox !== undefined &&
        Office.context.mailbox.item !== undefined
      ) {
        Office.context.mailbox.item.loadCustomPropertiesAsync((result) => {
          if (result.status === Office.AsyncResultStatus.Failed) {
            console.error(`loadCustomPropertiesAsync failed with message ${result.error.message}`);
            resolve(false);
          }

          let customProps = result.value;

          customProps.set(key, value);

          customProps.saveAsync((result) => {
            if (result.status === Office.AsyncResultStatus.Failed) {
              console.error(`saveAsync failed with message ${result.error.message}`);
              resolve(false);
            }

            console.log(`Custom properties saved with status: ${result.status}`);
            resolve(true);
          });
        });
      } else {
        resolve(false);
      }
    });
  }

  async clearActiveSubscriptions(sharedMailbox: string | undefined): Promise<boolean> {
    try {
      let url = `${BackendUrl()}/api/ClearSubscriptions?`;

      if (sharedMailbox !== undefined) {
        url = url + "sharedmailbox=" + sharedMailbox;
      }
      const response = await this.fetchEndpoint(url, "POST", "");

      if (response === undefined || !response.ok) {
        console.error(response.body);
        throw new Error("Failed to clear subscriptions");
      }
      return true;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error("Failed to call to Clear Subscriptions endpoint: " + error.message);
      } else {
        throw new Error("Failed to call to Clear Subscriptions endpoint");
      }
    }
  }

  async isConversationFiled(sharedMailbox: string | undefined, conversationId: string): Promise<boolean> {
    console.log("checking if conversation is already filed");
    try {
      let url = `${BackendUrl()}/api/CheckConversationStatus?conversationId=${conversationId}`;

      if (sharedMailbox !== undefined) {
        url = url + "&sharedmailbox=" + sharedMailbox;
      }

      const response = await this.fetchEndpoint(url, "GET", undefined);

      if (response === undefined || !response.ok) {
        console.error(response.body);
        return false;
      }
      const data = await response.json();
      return data.Data.status;
    } catch (error: unknown) {
      console.log("Failed to check conversation filing status");
      console.log(error);
      return false;
    }
  }
}
