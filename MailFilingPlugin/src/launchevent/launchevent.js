/* v8 ignore next 100 */
// Sorry, I have no clue how to test this

// Keep this JS simple - no ternaries, no async!

function onMessageSendHandler(event) {
  Office.context.mailbox.item.subject.getAsync({ asyncContext: event }, getSubjectCallback);
}

function getSubjectCallback(asyncResult) {
  const event = asyncResult.asyncContext;
  const subject = asyncResult.value;
  const isAReply = subject.toLowerCase().startsWith("re:");

  if (
    Office.context !== undefined &&
    Office.context.mailbox !== undefined &&
    Office.context.mailbox.item !== undefined
  ) {
    Office.context.mailbox.item.loadCustomPropertiesAsync(async (result) => {
      if (result.status === Office.AsyncResultStatus.Failed) {
        console.error(`loadCustomPropertiesAsync failed with message ${result.error.message}`);
        if (isAReply) {
          // fail safe
          event.completed({ allowEvent: true });
        }
      } else {
        let customProps = result.value;
        const filing = customProps.get("CmapMailFilingLocation");

        if (filing !== undefined) {
          console.log("Already tagged for filing");
          event.completed({ allowEvent: true });
        }
      }

      if (subject.startsWith("[")) {
        event.completed({ allowEvent: true });
      } else {
        event.completed({
          allowEvent: false,
          errorMessage: "Your organisation requires you file project emails when sending",
          // TIP: In addition to the formatted message, it's recommended to also set a
          // plain text message in the errorMessage property for compatibility on
          // older versions of Outlook clients.
          // not using markdown as it's in preview errorMessageMarkdown: "Need to tag with project",
          cancelLabel: "File to Project",
          commandId: "msgComposeOpenPaneButton",
          // useful to know you can change this dynamicallysendModeOverride: Office.MailboxEnums.SendModeOverride.PromptUser,
          contextData: "onsend",
        });
      }
    });
  }
}

Office.actions.associate("onMessageSendHandler", onMessageSendHandler);
