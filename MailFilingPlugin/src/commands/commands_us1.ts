/*
 * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
 * See LICEN<PERSON> in the project root for license information.
 */

/* global Office */

// This file is part of the Office Add-in framework.
// It handles commands that this add-in might need to execute.

Office.onReady(() => {
  // Office.js is fully loaded and ready to be used.
  // Place any initialization code that needs to interact with the Office application here.
  // Example:
  // console.log("Office.js is ready");
});
