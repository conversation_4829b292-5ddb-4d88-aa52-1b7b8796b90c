<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp
  xmlns="http://schemas.microsoft.com/office/appforoffice/1.1"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0"
  xmlns:mailappor="http://schemas.microsoft.com/office/mailappversionoverrides/1.0"
  xsi:type="MailApp"
>
  <Id><%=fosid%></Id>
  <Version><%=version%></Version>
  <ProviderName>CMap</ProviderName>
  <DefaultLocale>en-GB</DefaultLocale>
  <DisplayName DefaultValue="<%=environment%><%=productName%> Mail (S)" />
  <Description DefaultValue="<%=productName%> Mail - With Send Automation" />
  <IconUrl DefaultValue="https://<%=server%>/assets/atvero-icon-64.png" />
  <HighResolutionIconUrl DefaultValue="https://<%=server%>/assets/atvero-icon-128.png" />
  <SupportUrl DefaultValue="https://www.cmap.io/atvero-pim" />
  <AppDomains>
    <AppDomain>https://<%=server%></AppDomain>
    <AppDomain><%=discovery%></AppDomain>
    <AppDomain>https://login.microsoftonline.com</AppDomain>
  </AppDomains>
  <Hosts>
    <Host Name="Mailbox" />
  </Hosts>
  <Requirements>
    <Sets>
      <Set Name="Mailbox" MinVersion="1.1" />
    </Sets>
  </Requirements>
  <FormSettings>
    <Form xsi:type="ItemRead">
      <DesktopSettings>
        <SourceLocation DefaultValue="https://<%=server%>/taskpane.html" />
        <RequestedHeight>250</RequestedHeight>
      </DesktopSettings>
    </Form>
  </FormSettings>
  <Permissions>ReadWriteMailbox</Permissions>
  <Rule xsi:type="RuleCollection" Mode="Or">
    <Rule xsi:type="ItemIs" ItemType="Message" FormType="Read" />
  </Rule>
  <DisableEntityHighlighting>false</DisableEntityHighlighting>
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/mailappversionoverrides" xsi:type="VersionOverridesV1_0">
    <Requirements>
      <bt:Sets DefaultMinVersion="1.5">
        <bt:Set Name="Mailbox" />
      </bt:Sets>
    </Requirements>
    <Hosts>
      <Host xsi:type="MailHost">
        <DesktopFormFactor>
          <FunctionFile resid="Commands.Url" />
          <ExtensionPoint xsi:type="MessageReadCommandSurface">
            <OfficeTab id="TabDefault">
              <Group id="msgReadGroup">
                <Label resid="GroupLabel" />
                <Control xsi:type="Button" id="msgReadOpenPaneButton">
                  <Label resid="TaskpaneButton.Label" />
                  <Supertip>
                    <Title resid="TaskpaneButton.Label" />
                    <Description resid="TaskpaneButton.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16" />
                    <bt:Image size="32" resid="Icon.32x32" />
                    <bt:Image size="80" resid="Icon.80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <SourceLocation resid="Taskpane.Url" />
                  </Action>
                </Control>
              </Group>
            </OfficeTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
    </Hosts>
    <Resources>
      <bt:Images>
        <bt:Image id="Icon.16x16" DefaultValue="https://<%=server%>/assets/atvero-icon-16.png" />
        <bt:Image id="Icon.32x32" DefaultValue="https://<%=server%>/assets/atvero-icon-32.png" />
        <bt:Image id="Icon.80x80" DefaultValue="https://<%=server%>/assets/atvero-icon-80.png" />
      </bt:Images>
      <bt:Urls>
        <bt:Url id="Commands.Url" DefaultValue="https://<%=server%>/commands.html" />
        <bt:Url id="Taskpane.Url" DefaultValue="https://<%=server%>/taskpane.html" />
      </bt:Urls>
      <bt:ShortStrings>
        <bt:String id="GroupLabel" DefaultValue="<%=productName%>" />
        <bt:String id="TaskpaneButton.Label" DefaultValue="<%=productName%>" />
        <bt:String id="ActionButton.Label" DefaultValue="File attachments" />
      </bt:ShortStrings>
      <bt:LongStrings>
        <bt:String id="TaskpaneButton.Tooltip" DefaultValue="Open <%=productName%> Mail" />
        <bt:String id="ActionButton.Tooltip" DefaultValue="File attachments into records." />
      </bt:LongStrings>
    </Resources>

    <VersionOverrides
      xmlns="http://schemas.microsoft.com/office/mailappversionoverrides/1.1"
      xsi:type="VersionOverridesV1_1"
    >
      <Requirements>
        <bt:Sets DefaultMinVersion="1.14">
          <bt:Set Name="Mailbox" />
        </bt:Sets>
      </Requirements>
      <Hosts>
        <Host xsi:type="MailHost">
          <Runtimes>
            <!-- HTML file including reference to or inline JavaScript event handlers.
                This is used by Outlook on the web and on the new Mac UI. -->
            <Runtime resid="WebViewRuntime.Url">
              <!-- JavaScript file containing event handlers. This is used by Outlook on Windows. -->
              <Override type="javascript" resid="JSRuntime.Url" />
            </Runtime>
          </Runtimes>
          <DesktopFormFactor>
            <SupportsSharedFolders>true</SupportsSharedFolders>
            <FunctionFile resid="Commands.Url" />
            <ExtensionPoint xsi:type="MessageComposeCommandSurface">
              <OfficeTab id="TabDefault">
                <Group id="msgComposeGroup">
                  <Label resid="GroupLabel" />
                  <Control xsi:type="Button" id="msgComposeOpenPaneButton">
                    <Label resid="TaskpaneButton.Label" />
                    <Supertip>
                      <Title resid="TaskpaneButton.Label" />
                      <Description resid="TaskpaneButton.Tooltip" />
                    </Supertip>
                    <Icon>
                      <bt:Image size="16" resid="Icon.16x16" />
                      <bt:Image size="32" resid="Icon.32x32" />
                      <bt:Image size="80" resid="Icon.80x80" />
                    </Icon>
                    <Action xsi:type="ShowTaskpane">
                      <SourceLocation resid="Taskpane.Url" />
                    </Action>
                  </Control>
                  <Control xsi:type="Button" id="ActionButton">
                    <Label resid="ActionButton.Label" />
                    <Supertip>
                      <Title resid="ActionButton.Label" />
                      <Description resid="ActionButton.Tooltip" />
                    </Supertip>
                    <Icon>
                      <bt:Image size="16" resid="Icon.16x16" />
                      <bt:Image size="32" resid="Icon.32x32" />
                      <bt:Image size="80" resid="Icon.80x80" />
                    </Icon>
                    <Action xsi:type="ExecuteFunction">
                      <FunctionName>action</FunctionName>
                    </Action>
                  </Control>
                </Group>
              </OfficeTab>
            </ExtensionPoint>
            <ExtensionPoint xsi:type="MessageReadCommandSurface">
              <OfficeTab id="TabDefault">
                <Group id="msgReadGroup">
                  <Label resid="GroupLabel" />
                  <Control xsi:type="Button" id="msgReadOpenPaneButton">
                    <Label resid="TaskpaneButton.Label" />
                    <Supertip>
                      <Title resid="TaskpaneButton.Label" />
                      <Description resid="TaskpaneButton.Tooltip" />
                    </Supertip>
                    <Icon>
                      <bt:Image size="16" resid="Icon.16x16" />
                      <bt:Image size="32" resid="Icon.32x32" />
                      <bt:Image size="80" resid="Icon.80x80" />
                    </Icon>
                    <Action xsi:type="ShowTaskpane">
                      <SourceLocation resid="Taskpane.Url" />
                      <SupportsPinning>true</SupportsPinning>
                      <SupportsNoItemContext>true</SupportsNoItemContext>
                    </Action>
                  </Control>
                </Group>
              </OfficeTab>
            </ExtensionPoint>
            <ExtensionPoint xsi:type="LaunchEvent">
              <LaunchEvents>
                <LaunchEvent Type="OnMessageSend" FunctionName="onMessageSendHandler" SendMode="PromptUser" />
              </LaunchEvents>
              <!-- Identify the runtime to be used (also referenced by the Runtime element). -->
              <SourceLocation resid="WebViewRuntime.Url" />
            </ExtensionPoint>
          </DesktopFormFactor>
          <MobileFormFactor>
            <FunctionFile resid="Commands.Url" />
            <ExtensionPoint xsi:type="MobileMessageReadCommandSurface">
              <Group id="mobileMsgRead">
                <Label resid="GroupLabel" />
                <Control xsi:type="MobileButton" id="TaskPaneBtn">
                  <Label resid="MobileTaskpaneButton.Label" />
                  <Icon xsi:type="bt:MobileIconList">
                    <bt:Image size="25" scale="1" resid="MobileIcon-16-1" />
                    <bt:Image size="25" scale="2" resid="MobileIcon-16-2" />
                    <bt:Image size="25" scale="3" resid="MobileIcon-16-3" />

                    <bt:Image size="32" scale="1" resid="MobileIcon-32-1" />
                    <bt:Image size="32" scale="2" resid="MobileIcon-32-2" />
                    <bt:Image size="32" scale="3" resid="MobileIcon-32-3" />

                    <bt:Image size="48" scale="1" resid="MobileIcon-48-1" />
                    <bt:Image size="48" scale="2" resid="MobileIcon-48-2" />
                    <bt:Image size="48" scale="3" resid="MobileIcon-48-3" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <SourceLocation resid="Taskpane.Url" />
                  </Action>
                </Control>
              </Group>
            </ExtensionPoint>
          </MobileFormFactor>
        </Host>
      </Hosts>
      <Resources>
        <bt:Images>
          <bt:Image id="Icon.16x16" DefaultValue="https://<%=server%>/assets/atvero-icon-16.png" />
          <bt:Image id="Icon.32x32" DefaultValue="https://<%=server%>/assets/atvero-icon-32.png" />
          <bt:Image id="Icon.80x80" DefaultValue="https://<%=server%>/assets/atvero-icon-80.png" />
          <bt:Image id="MobileIcon-16-1" DefaultValue="https://<%=server%>/assets/mobile-atvero-icon-16-1.png" />
          <bt:Image id="MobileIcon-16-2" DefaultValue="https://<%=server%>/assets/mobile-atvero-icon-16-2.png" />
          <bt:Image id="MobileIcon-16-3" DefaultValue="https://<%=server%>/assets/mobile-atvero-icon-16-3.png" />
          <bt:Image id="MobileIcon-32-1" DefaultValue="https://<%=server%>/assets/mobile-atvero-icon-32-1.png" />
          <bt:Image id="MobileIcon-32-2" DefaultValue="https://<%=server%>/assets/mobile-atvero-icon-32-2.png" />
          <bt:Image id="MobileIcon-32-3" DefaultValue="https://<%=server%>/assets/mobile-atvero-icon-32-3.png" />
          <bt:Image id="MobileIcon-48-1" DefaultValue="https://<%=server%>/assets/mobile-atvero-icon-48-1.png" />
          <bt:Image id="MobileIcon-48-2" DefaultValue="https://<%=server%>/assets/mobile-atvero-icon-48-2.png" />
          <bt:Image id="MobileIcon-48-3" DefaultValue="https://<%=server%>/assets/mobile-atvero-icon-48-3.png" />
        </bt:Images>
        <bt:Urls>
          <bt:Url id="Commands.Url" DefaultValue="https://<%=server%>/commands.html" />
          <bt:Url id="Taskpane.Url" DefaultValue="https://<%=server%>/taskpane.html" />
          <bt:Url id="WebViewRuntime.Url" DefaultValue="https://<%=server%>/commands.html" />
          <!-- Entry needed for Outlook on Windows. -->
          <bt:Url id="JSRuntime.Url" DefaultValue="https://<%=server%>/launchevent.js" />
        </bt:Urls>
        <bt:ShortStrings>
          <bt:String id="GroupLabel" DefaultValue="<%=productName%>" />
          <bt:String id="TaskpaneButton.Label" DefaultValue="<%=productName%> Mail" />
          <bt:String id="ActionButton.Label" DefaultValue="Save attachments" />
          <bt:String id="MobileTaskpaneButton.Label" DefaultValue="<%=productName%> Mail" />
        </bt:ShortStrings>
        <bt:LongStrings>
          <bt:String id="TaskpaneButton.Tooltip" DefaultValue="<%=productName%> Mail" />
          <bt:String id="ActionButton.Tooltip" DefaultValue="Save attachments into Records" />
          <bt:String id="MobileTaskpaneButton.Label" DefaultValue="<%=productName%> Mail" />
        </bt:LongStrings>
      </Resources>
      <WebApplicationInfo>
        <Id><%=appid%></Id>
        <Resource>api://<%=server%>/<%=appid%></Resource>
        <Scopes>
          <Scope>https://<%=server%>/<%=appid%>/access_as_user</Scope>
        </Scopes>
      </WebApplicationInfo>
    </VersionOverrides>
  </VersionOverrides>
</OfficeApp>
