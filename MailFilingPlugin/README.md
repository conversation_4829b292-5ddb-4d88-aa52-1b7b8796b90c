## Atvero Outlook Filing Plugin

# To get started

## Once Only

`npm run build`

## Linking Shared hooks
`cd ..`
`cd shared`
`npm link ../MailFilingPlugin/node_modules/react`

## Development

`npm run watch`

Install the Outlook plugin from the manifest.xml file

To Start the dev server for development run the following:

`npm run dev-server`

## Unit tests

When Creating test, create them inside the `__tests__` folder

# Test Naming

`{file-name.test.js}`

if the test require the use of jsx elements then use the following:

`{file-name.test.jsx}`

# Running tests

`npm test`