<!DOCTYPE html>
<html lang="en-GB">
  <head>
    <title>Install the Atvero Mail Plugin</title>
    <link
      rel="stylesheet"
      href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css"
    />
  </head>
  <body>
    <div class="ms-Grid" dir="ltr">
      <div class="ms-Grid-row">
        <div class="ms-Grid-col ms-sm12 ms-md12 ms-lg12">
          <h1 class="ms-font-xxl">Install the Atvero Mail Plugin</h1>
          <p class="ms-font-m">
            Follow the steps below to install the Office.js plugin using the integrated app mechanism in the Office 365
            admin:
          </p>
          <ol class="ms-List">
            <li class="ms-ListItem">
              <a href="./manifest.xml" class="ms-ListItem-primaryText">Download the Office.js plugin package.</a>
            </li>
            <li class="ms-ListItem">
              <a href="./manifest-file-on-send.xml" class="ms-ListItem-primaryText">Download the Office.js plugin package with File On Send.</a>
            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText">Sign in to your Office 365 admin account.</span>
            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText">Go to the Admin center.</span>
            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText">Navigate to the <strong>Settings</strong> section.</span>
            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                >Click on <strong>Integrated apps</strong> depending on your Office 365 version.</span
              >
            </li>

            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                >Click on "Upload custom apps"</span
              >
            </li>  
                   <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                >Select the <strong>Office Add-in</strong> type</span
              >
            </li>
                 </li>  
                   <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                ><strong>Upload manifest file (.xml) from device</strong> 
                and the select the plugin package downloaded in the first step</span
              >
            </li>
            <li class="ms-ListItem">
              <span class="ms-ListItem-primaryText"
                >Follow the on-screen instructions to complete the installation.</span
              >
            </li>
          </ol>
        </div>
      </div>
    </div>
  </body>
</html>
