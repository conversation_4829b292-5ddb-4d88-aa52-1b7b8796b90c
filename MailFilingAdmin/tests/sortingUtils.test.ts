import { describe, it, expect } from "vitest";
import { isSortableColumn } from "../src/utils/sortingUtils";

describe("isSortableColumn", () => {
  it('should return true for "code"', () => {
    expect(isSortableColumn("code")).toBe(true);
  });

  it('should return true for "title"', () => {
    expect(isSortableColumn("title")).toBe(true);
  });

  it("should return false for undefined", () => {
    expect(isSortableColumn(undefined)).toBe(false);
  });

  it("should return false for invalid column keys", () => {
    expect(isSortableColumn("invalid")).toBe(false);
    expect(isSortableColumn("anotherInvalidKey")).toBe(false);
  });

  it("should return false for empty string", () => {
    expect(isSortableColumn("")).toBe(false);
  });

  it("should return false for case-sensitive mismatched keys", () => {
    expect(isSortableColumn("Code")).toBe(false);
    expect(isSortableColumn("Title")).toBe(false);
  });
});
