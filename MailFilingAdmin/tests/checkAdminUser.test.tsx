import { describe, it, expect, vi, beforeEach } from "vitest";
import { checkIfUserIsAdmin } from "../src/services/checkAdminUser";
import { GraphFI } from "@pnp/graph";

describe("checkIfUserIsAdmin", () => {
  let mockGraph: {
    groups: { filter: ReturnType<typeof vi.fn> };
    me: { checkMemberGroups: ReturnType<typeof vi.fn> };
  };

  beforeEach(() => {
    mockGraph = {
      groups: {
        filter: vi.fn(),
      },
      me: {
        checkMemberGroups: vi.fn(),
      },
    };
  });

  it("should return if user is admin", async () => {
    const adminGroup = {
      id: "1",
    };

    mockGraph.groups.filter.mockReturnValue(() =>
      Promise.resolve([adminGroup])
    );

    mockGraph.me.checkMemberGroups.mockResolvedValue([adminGroup]);

    const result = await checkIfUserIsAdmin(mockGraph as unknown as GraphFI);

    expect(result).toEqual(true);
  });

  it("should return false if user is not", async () => {
    const adminGroup = {
      id: "1",
    };

    mockGraph.groups.filter.mockReturnValue(() =>
      Promise.resolve([adminGroup])
    );

    mockGraph.me.checkMemberGroups.mockResolvedValue([]);

    const result = await checkIfUserIsAdmin(mockGraph as unknown as GraphFI);

    expect(result).toEqual(false);
  });
});
