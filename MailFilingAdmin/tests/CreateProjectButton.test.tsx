import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import CreateProjectButton from "../src/components/CreateProjectButton";
import { SPFI } from "@pnp/sp";

vi.mock("../src/services/getProjects", async () => {
  return {
    getProjectList: vi.fn().mockResolvedValue({
      projects: [],
      nextLink: undefined,
      page: 0,
      tableSize: 100,
      sortField: undefined,
      sortDirection: "ascending",
      moreResultsAvailable: undefined,
      total: undefined,
    }),
  };
});

vi.mock("@fluentui/react-components", async () => {
  const actual = await vi.importActual("@fluentui/react-components");
  return {
    ...actual,
    ProgressBar: () => <div data-testid="mock-progress-bar" />,
  };
});

describe("CreateProjectButton", () => {
  const mockSharepoint: SPFI = {} as unknown as SPFI;
  const hubsite = { displayName: "Atvero Mail", name: "AtveroMail", url: "http://example.com" };
  const mockOnCreateProject = vi.fn();
  const mockOnProjectUrlChange = vi.fn();
  const mockOnOpenUsersModal = vi.fn();

  beforeEach(() => {
    mockOnCreateProject.mockClear();
    mockOnProjectUrlChange.mockClear();
    mockOnOpenUsersModal.mockClear();
  });

  const renderComponent = () => {
    return render(
      <CreateProjectButton
        onCreateProject={mockOnCreateProject}
        onProjectUrlChange={mockOnProjectUrlChange}
        onOpenUsersModal={mockOnOpenUsersModal}
        hubsite={hubsite}
        sharepoint={mockSharepoint}
      />
    );
  };

  const openDialog = async () => {
    fireEvent.click(screen.getByText("Create Project"));
  };

  const fillProjectForm = async () => {
    await waitFor(() => {
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "Test Project" },
      });
      fireEvent.change(screen.getByPlaceholderText("Project Code"), {
        target: { value: "Test_Code" },
      });
    });
  };

  const setupSuccessfulProjectCreation = () => {
    mockOnCreateProject.mockResolvedValue({
      success: true,
      message: "Project created successfully!",
      projectURL: "https://test-project.com"
    });
  };

  const createProject = async () => {
    await openDialog();
    await fillProjectForm();
    fireEvent.click(screen.getByText("Create"));
  };
  
  it("renders the Create Project button", () => {
    renderComponent();
    expect(screen.getByText("Create Project")).toBeInTheDocument();
  });

  it("opens the dialog when the button is clicked", async () => {
    renderComponent();
    fireEvent.click(screen.getByText("Create Project"));
    await waitFor(() => {
      expect(screen.getByText("Create New Project")).toBeInTheDocument();
    });
  });

  it("disables the Create button when inputs are empty", async () => {
    renderComponent();
    fireEvent.click(screen.getByText("Create Project"));
    await waitFor(() => {
      expect(screen.getByText("Create")).toBeDisabled();
    });
  });

  it("enables the Create button when inputs are filled", async () => {
    renderComponent();
    fireEvent.click(screen.getByText("Create Project"));
    await waitFor(() => {
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "Test-Project" },
      });
      fireEvent.change(screen.getByPlaceholderText("Project Code"), {
        target: { value: "Test-Code" },
      });
      expect(screen.getByText("Create")).not.toBeDisabled();
    });
  });

  it("closes the dialog and resets form state", async () => {
    renderComponent();

    fireEvent.click(screen.getByText("Create Project"));

    await waitFor(() => {
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "Test-Project" },
      });
      fireEvent.change(screen.getByPlaceholderText("Project Code"), {
        target: { value: "Test-Code" },
      });
    });

    fireEvent.click(screen.getByText("Close"));

    expect(screen.queryByText("Create New Project")).not.toBeInTheDocument();

    fireEvent.click(screen.getByText("Create Project"));

    await waitFor(() => {
      expect(screen.getByPlaceholderText("Project Title")).toHaveValue("");
      expect(screen.getByPlaceholderText("Project Code")).toHaveValue("");
      expect(
        screen.queryByText("Project created successfully!")
      ).not.toBeInTheDocument();
    });
  });

  it("calls handleSubmit when Create button is clicked", async () => {
    mockOnCreateProject.mockResolvedValue({
      success: true,
      message: "Project created successfully!",
    });

    renderComponent();
    await createProject();

    await waitFor(() => {
      expect(mockOnCreateProject).toHaveBeenCalledWith(
        "Test Project",
        "Test_Code",
        undefined,
        expect.any(Function),
        undefined,
        expect.objectContaining({ cancelled: false })
      );
    });
  });

  it("renders the project search Combobox", () => {
    renderComponent();

    fireEvent.click(screen.getByText("Create Project"));

    const comboboxInput = screen.getByRole("combobox");

    expect(comboboxInput).toBeInTheDocument();

    expect(comboboxInput).toHaveAttribute("placeholder", "Search projects...");

    const expandButton = screen.getByLabelText("Open");
    expect(expandButton).toBeInTheDocument();
  });

  it("updates selected project when an option is selected", async () => {
    renderComponent();

    fireEvent.click(screen.getByText("Create Project"));

    const comboboxInput = screen.getByRole("combobox");

    fireEvent.change(comboboxInput, { target: { value: "Project 1" } });

    fireEvent.keyDown(comboboxInput, { key: "Enter", code: "Enter" });

    expect(comboboxInput).toHaveValue("Project 1");
  });

  describe("Project Creation and Users Modal Integration", () => {
    beforeEach(() => {
      setupSuccessfulProjectCreation();
    });

    it("shows Manage Users button after successful project creation", async () => {
      renderComponent();
      await createProject();

      await waitFor(() => {
        expect(screen.getByText("Manage Users")).toBeInTheDocument();
      });
    });

    it("calls necessary callbacks when Manage Users is clicked", async () => {
      renderComponent();
      await createProject();

      await waitFor(() => {
        fireEvent.click(screen.getByText("Manage Users"));
      });

      expect(mockOnProjectUrlChange).toHaveBeenCalledWith("https://test-project.com");
      expect(mockOnOpenUsersModal).toHaveBeenCalled();
    });

    it("closes dialog when Manage Users is clicked", async () => {
      renderComponent();
      await createProject();

      await waitFor(() => {
        fireEvent.click(screen.getByText("Manage Users"));
      });

      expect(screen.queryByText("Create New Project")).not.toBeInTheDocument();
    });

    it("resets form state after Manage Users is clicked", async () => {
      renderComponent();
      await createProject();

      await waitFor(() => {
        fireEvent.click(screen.getByText("Manage Users"));
      });

      await openDialog();

      await waitFor(() => {
        expect(screen.getByPlaceholderText("Project Title")).toHaveValue("");
        expect(screen.getByPlaceholderText("Project Code")).toHaveValue("");
        expect(screen.queryByText("Manage Users")).not.toBeInTheDocument();
      });
    });

    it("sets project URL when creation is successful", async () => {
      renderComponent();
      await createProject();

      await waitFor(() => {
        expect(mockOnProjectUrlChange).toHaveBeenCalledWith("https://test-project.com");
      });
    });
  });
  describe("Special Characters and Spaces Validation", () => {
    const testSpecialCharactersInCode = async (value: string) => {
      renderComponent();
      await openDialog();
      
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "Test Title" }
      });
      fireEvent.change(screen.getByPlaceholderText("Project Code"), {
        target: { value: value }
      });
      
      await waitFor(() => {
        expect(screen.getByText("Create")).toBeDisabled();
        expect(screen.getByText("Special characters and emojis are not allowed in the project code")).toBeInTheDocument();
      });
    };
     
    it("disables Create button when project title contains special characters", async () => {
      await testSpecialCharactersInCode("Test😊Project");
    });
     
    it("disables Create button when project code contains special characters", async () => {
      await testSpecialCharactersInCode("Test@Code"); 
    });

    it("allows special characters in project title", async () => {
      renderComponent();
      await openDialog();
      
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "R&D / Project & Testing" },
      });
      fireEvent.change(screen.getByPlaceholderText("Project Code"), {
        target: { value: "TEST001" },
      });
   
      await waitFor(() => {
        expect(screen.getByText("Create")).not.toBeDisabled();
      });
    });
  
    it("maintains space warning for project code when title is changed", async () => {
      renderComponent();
      await openDialog();
      
      fireEvent.change(screen.getByPlaceholderText("Project Code"), {
        target: { value: "Test Code" },
      });
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "Any Title" },
      });
   
      await waitFor(() => {
        expect(screen.getByText("Spaces will be replaced with underscores")).toBeInTheDocument();
        expect(screen.getByText("Project Code: Test_Code")).toBeInTheDocument();
      });
    });

    it("shows error when project title or code contains only spaces", async () => {
      renderComponent();
      await openDialog();
      
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "   " },
      });
   
      await waitFor(() => {
        expect(screen.getByText("Project title cannot be only spaces")).toBeInTheDocument();
        expect(screen.getByText("Create")).toBeDisabled();
      });
   
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "Valid Title" },
      });
      fireEvent.change(screen.getByPlaceholderText("Project Code"), {
        target: { value: "    " },
      });
   
      await waitFor(() => {
        expect(screen.getByText("Project code cannot be only spaces")).toBeInTheDocument();
        expect(screen.getByText("Create")).toBeDisabled();
      });
    });

    it("only replaces spaces in project code on submission", async () => {
      mockOnCreateProject.mockResolvedValue({
        success: true,
        message: "Project created successfully!",
      });
   
      renderComponent();
      await openDialog();
   
      await waitFor(() => {
        fireEvent.change(screen.getByPlaceholderText("Project Title"), {
          target: { value: "Test Project & Title" },
        });
        fireEvent.change(screen.getByPlaceholderText("Project Code"), {
          target: { value: "Test Code" },
        });
      });
   
      fireEvent.click(screen.getByText("Create"));
   
      await waitFor(() => {
        expect(mockOnCreateProject).toHaveBeenCalledWith(
          "Test Project & Title",
          "Test_Code",
          undefined,
          expect.any(Function),
          undefined,
          expect.objectContaining({ cancelled: false })
        );
      });
    });
    
    it("disables Create button when project code exceeds 255 characters", async () => {
      renderComponent();
      await openDialog();
   
      const longCode = "a".repeat(256);
      
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "Valid Title" },
      });
      fireEvent.change(screen.getByPlaceholderText("Project Code"), {
        target: { value: longCode },
      });
   
      await waitFor(() => {
        expect(screen.getByText("Create")).toBeDisabled();
        expect(screen.getByText("Project code cannot exceed 255 characters")).toBeInTheDocument();
      });
    });
  
    it("maintains error message when project code is too long even after valid title input", async () => {
      renderComponent();
      await openDialog();
   
      const longCode = "a".repeat(256);
      
      fireEvent.change(screen.getByPlaceholderText("Project Code"), {
        target: { value: longCode },
      });
      fireEvent.change(screen.getByPlaceholderText("Project Title"), {
        target: { value: "Valid Title" },
      });
   
      await waitFor(() => {
        expect(screen.getByText("Create")).toBeDisabled();
        expect(screen.getByText("Project code cannot exceed 255 characters")).toBeInTheDocument();
      });
    });
  });
});