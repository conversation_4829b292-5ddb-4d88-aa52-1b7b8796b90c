import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { CMapProjectStatus, Project, Tenancy } from "../src/types";
import EnableAtveroMail from "../src/components/EnableAtveroMail";

const mockProjects: Project[] = [
  {
    code: "PRJ-001",
    title: "Test Project 1",
    id: "1",
    cMapId: 123,
    favorite: false,
    cmapProject: CMapProjectStatus.CMapCreated,
    webUrl: "http://test.com/project1",
  },
  {
    code: "PRJ-002",
    title: "Test Project 2",
    id: "2",
    cMapId: 124,
    favorite: true,
    cmapProject: CMapProjectStatus.CMapNotCreated,
    webUrl: "http://test.com/project2",
  },
];

describe("EnableAtveroMail", () => {
  const mockOnEnableAtveroMail = vi.fn();

  beforeEach(() => {
    mockOnEnableAtveroMail.mockReset();
  });

  it("should not render button when no projects are selected", () => {
    render(
      <EnableAtveroMail
        selectedProjects={[]}
        onEnableAtveroMail={mockOnEnableAtveroMail}
        tenancy={Tenancy.CMap}
      />
    );

    expect(
      screen.queryByRole("button", { name: /enable/i })
    ).not.toBeInTheDocument();
  });

  it("should render button when projects are selected", () => {
    render(
      <EnableAtveroMail
        selectedProjects={mockProjects}
        onEnableAtveroMail={mockOnEnableAtveroMail}
        tenancy={Tenancy.CMap}
      />
    );

    expect(screen.getByRole("button", { name: /enable/i })).toBeInTheDocument();
  });

  it("should open modal when button is clicked", async () => {
    render(
      <EnableAtveroMail
        selectedProjects={mockProjects}
        onEnableAtveroMail={mockOnEnableAtveroMail}
        tenancy={Tenancy.CMap}
      />
    );

    const button = screen.getByRole("button", { name: /enable/i });
    fireEvent.click(button);

    await waitFor(() => {
      expect(screen.getByRole("dialog")).toBeInTheDocument();
      expect(
        screen.getByRole("heading", { name: /enable cmap mail/i })
      ).toBeInTheDocument();
      expect(screen.getByText("Test Project 1")).toBeInTheDocument();
      expect(screen.getByText("Test Project 2")).toBeInTheDocument();
    });
  });

  it("should handle successful enablement", async () => {
    mockOnEnableAtveroMail.mockResolvedValue({
      success: true,
      message: "Successfully enabled",
      details: "All projects processed",
    });

    render(
      <EnableAtveroMail
        selectedProjects={mockProjects}
        onEnableAtveroMail={mockOnEnableAtveroMail}
        tenancy={Tenancy.CMap}
      />
    );

    fireEvent.click(screen.getByRole("button", { name: /enable/i }));

    const enableButton = screen.getByRole("button", {
      name: "Enable",
    });
    fireEvent.click(enableButton);

    await waitFor(() => {
      expect(
        screen.getByText("Initializing project creation...")
      ).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText("Successfully enabled")).toBeInTheDocument();
    });
  });

  it("should handle failed enablement", async () => {
    const errorMessage = "Failed to enable";
    mockOnEnableAtveroMail.mockResolvedValue({
      success: false,
      message: errorMessage,
      details: "Error details",
    });

    render(
      <EnableAtveroMail
        selectedProjects={mockProjects}
        onEnableAtveroMail={mockOnEnableAtveroMail}
        tenancy={Tenancy.CMap}
      />
    );

    fireEvent.click(screen.getByRole("button", { name: /enable/i }));

    const enableButton = screen.getByRole("button", {
      name: "Enable",
    });
    fireEvent.click(enableButton);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it("should handle unexpected errors", async () => {
    mockOnEnableAtveroMail.mockRejectedValue(new Error("Unexpected error"));

    render(
      <EnableAtveroMail
        selectedProjects={mockProjects}
        onEnableAtveroMail={mockOnEnableAtveroMail}
        tenancy={Tenancy.CMap}
      />
    );

    fireEvent.click(screen.getByRole("button", { name: /enable/i }));

    const enableButton = screen.getByRole("button", {
      name: "Enable",
    });
    fireEvent.click(enableButton);

    await waitFor(() => {
      expect(
        screen.getByText(
          "An unexpected error occurred while enabling Atvero for the selected projects."
        )
      ).toBeInTheDocument();
    });
  });

  it("should close modal when close button is clicked", async () => {
    render(
      <EnableAtveroMail
        selectedProjects={mockProjects}
        onEnableAtveroMail={mockOnEnableAtveroMail}
        tenancy={Tenancy.CMap}
      />
    );

    fireEvent.click(screen.getByRole("button", { name: /enable/i }));

    expect(screen.getByRole("dialog")).toBeInTheDocument();

    const closeButton = screen.getByRole("button", { name: "Close" });
    fireEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
    });
  });
});
