import { describe, it, expect } from 'vitest';
import { getMessageClassName, StylesType } from '../src/services/getMessageClassName';

describe('getMessageClassName', () => {
  const mockStyles: StylesType = {
    errorMessage: 'error-class',
    successMessage: 'success-class',
    deleteSuccessMessage: 'delete-success-class',
  };

  it('returns correct class name for error type', () => {
    expect(getMessageClassName('error', mockStyles)).toBe('error-class');
  });

  it('returns correct class name for success type', () => {
    expect(getMessageClassName('success', mockStyles)).toBe('success-class');
  });

  it('returns correct class name for deleteSuccess type', () => {
    expect(getMessageClassName('deleteSuccess', mockStyles)).toBe('delete-success-class');
  });

  it('returns success class name for unknown type', () => {
    expect(getMessageClassName('unknown' as any, mockStyles)).toBe('success-class');
  });
});