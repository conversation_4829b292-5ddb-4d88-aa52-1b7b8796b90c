import React from 'react';
import { render, screen } from '@testing-library/react';
import { expect, describe, it, beforeEach } from 'vitest';
import StatusMessage from '../src/components/StatusMessage';

vi.mock('@fluentui/react-components', () => ({
  makeStyles: () => () => ({
    button: 'mock-button-class',
    message: 'mock-message-class',
    messageBar: 'mock-messageBar-class',
    detailsContainer: 'mock-detailsContainer-class',
    details: 'mock-details-class',
  }),
  Text: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <span className={className}>{children}</span>
  ),
  MessageBar: ({ children, intent, className }: { children: React.ReactNode; intent: string; className?: string }) => (
    <div data-testid="message-bar" data-intent={intent} className={className}>
      {children}
    </div>
  ),
  MessageBarBody: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="message-bar-body">{children}</div>
  ),
}));

describe('StatusMessage Component', () => {
  const defaultProps = {
    message: 'Test Message',
    type: 'info' as const,
  };

  beforeEach(() => {
    // Clear any previous renders
    document.body.innerHTML = '';
  });

  it('renders with basic props', () => {
    render(<StatusMessage {...defaultProps} />);
    
    const messageBar = screen.getByTestId('message-bar');
    const message = screen.getByText('Test Message');
    
    expect(messageBar).toBeDefined();
    expect(message).toBeDefined();
    expect(messageBar).toHaveAttribute('data-intent', 'info');
  });

  it('renders with details when provided', () => {
    const props = {
      ...defaultProps,
      details: 'Additional details here',
    };

    render(<StatusMessage {...props} />);
    
    const details = screen.getByText('Additional details here');
    expect(details).toBeDefined();
    expect(details.className).toContain('mock-details-class');
  });

  it('does not render details section when details prop is not provided', () => {
    render(<StatusMessage {...defaultProps} />);
    
    const detailsElements = screen.queryAllByTestId('details');
    expect(detailsElements.length).toBe(0);
  });

  it('renders with different message types', () => {
    const messageTypes = ['error', 'success', 'info', 'warning'] as const;
    
    messageTypes.forEach(type => {
      render(<StatusMessage message="Test Message" type={type} />);
      const messageBar = screen.getByTestId('message-bar');
      expect(messageBar).toHaveAttribute('data-intent', type);
      document.body.innerHTML = ''; // Clean up for next iteration
    });
  });
});
