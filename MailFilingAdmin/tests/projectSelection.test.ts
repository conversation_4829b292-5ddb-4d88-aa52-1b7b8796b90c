import { describe, it, expect, vi } from 'vitest';
import { handleProjectSelect } from '../src/services/projectSelection';
import { Project, CMapProjectStatus } from '../src/types';
import { Dispatch, SetStateAction } from 'react';

describe('handleProjectSelect', () => {
    const createMockProject = (overrides?: Partial<Project>): Project => ({
      code: 'TEST-001',
      title: 'Test Project',
      id: '1',
      cMapId: undefined,
      favorite: false,
      cmapProject: CMapProjectStatus.NotInCmap,
      webUrl: 'https://example.com/project',
      ...overrides
    });
  
    const createSetStateMock = (initialState: Project[] = []) => {
        let currentState = [...initialState];
        const mockFn = vi.fn();
        
        const setSelectedProjects: Dispatch<SetStateAction<Project[]>> = (value) => {
          if (typeof value === 'function') {
            currentState = value(currentState);
          } else {
            currentState = value;
          }
          mockFn(currentState);
        };
        
        return { mockFn, setSelectedProjects, currentState };
      };
  
    it('should add a project when not already selected (using id)', () => {
      const { mockFn, setSelectedProjects } = createSetStateMock();
      const project = createMockProject();
      
      mockFn.mockReturnValue([]);
      handleProjectSelect(project, setSelectedProjects);
      
      expect(mockFn).toHaveBeenCalledWith([project]);
    });
  
    it('should add a project when not already selected (using cMapId)', () => {
      const { mockFn, setSelectedProjects } = createSetStateMock();
      const project = createMockProject({ 
        cMapId: 123,
        cmapProject: CMapProjectStatus.CMapCreated 
      });
      
      mockFn.mockReturnValue([]);
      handleProjectSelect(project, setSelectedProjects);
      
      expect(mockFn).toHaveBeenCalledWith([project]);
    });
  
    it('should add a project when not already selected (using id)', () => {
        const { mockFn, setSelectedProjects } = createSetStateMock();
        const project = createMockProject();
        
        handleProjectSelect(project, setSelectedProjects);
        
        expect(mockFn).toHaveBeenCalledWith([project]);
    });

    it('should add a project when not already selected (using cMapId)', () => {
        const { mockFn, setSelectedProjects } = createSetStateMock();
        const project = createMockProject({ 
            cMapId: 123,
            cmapProject: CMapProjectStatus.CMapCreated 
        });
        
        handleProjectSelect(project, setSelectedProjects);
        
        expect(mockFn).toHaveBeenCalledWith([project]);
    });
  
    it('should handle multiple projects with different statuses correctly', () => {
      const { mockFn, setSelectedProjects } = createSetStateMock();
      const project1 = createMockProject({
        id: '1',
        cmapProject: CMapProjectStatus.NotInCmap
      });
      const project2 = createMockProject({
        id: '2',
        cMapId: 123,
        cmapProject: CMapProjectStatus.CMapCreated
      });
      const project3 = createMockProject({
        id: '3',
        cmapProject: CMapProjectStatus.CMapNotCreated
      });
      
      mockFn.mockReturnValue([]);
      handleProjectSelect(project1, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([project1]);
      
      mockFn.mockReturnValue([project1]);
      handleProjectSelect(project2, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([project1, project2]);
      
      mockFn.mockReturnValue([project1, project2]);
      handleProjectSelect(project1, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([project2]);
      
      mockFn.mockReturnValue([project2]);
      handleProjectSelect(project3, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([project2, project3]);
    });
  
    it('should handle projects with different CMap statuses', () => {
      const { mockFn, setSelectedProjects } = createSetStateMock();
      const notInCmapProject = createMockProject({
        id: '1',
        cmapProject: CMapProjectStatus.NotInCmap
      });
      const cmapNotCreatedProject = createMockProject({
        id: '2',
        cmapProject: CMapProjectStatus.CMapNotCreated
      });
      const cmapCreatedProject = createMockProject({
        id: '3',
        cMapId: 123,
        cmapProject: CMapProjectStatus.CMapCreated
      });
      
      mockFn.mockReturnValue([]);
      handleProjectSelect(notInCmapProject, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([notInCmapProject]);
      
      mockFn.mockReturnValue([notInCmapProject]);
      handleProjectSelect(cmapNotCreatedProject, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([notInCmapProject, cmapNotCreatedProject]);
      
      mockFn.mockReturnValue([notInCmapProject, cmapNotCreatedProject]);
      handleProjectSelect(cmapCreatedProject, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([notInCmapProject, cmapNotCreatedProject, cmapCreatedProject]);
    });
  
    it('should handle projects with same id but different cMapIds', () => {
      const { mockFn, setSelectedProjects } = createSetStateMock();
      const project1 = createMockProject({
        id: '1',
        cMapId: 123,
        cmapProject: CMapProjectStatus.CMapCreated
      });
      const project2 = createMockProject({
        id: '1',
        cMapId: 456,
        cmapProject: CMapProjectStatus.CMapCreated
      });
      
      mockFn.mockReturnValue([]);
      handleProjectSelect(project1, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([project1]);
      
      mockFn.mockReturnValue([project1]);
      handleProjectSelect(project2, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([project1, project2]);
    });
  
    it('should handle favorite and non-favorite projects', () => {
      const { mockFn, setSelectedProjects } = createSetStateMock();
      const favoriteProject = createMockProject({
        id: '1',
        favorite: true
      });
      const nonFavoriteProject = createMockProject({
        id: '2',
        favorite: false
      });
      
      mockFn.mockReturnValue([]);
      handleProjectSelect(favoriteProject, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([favoriteProject]);
      
      mockFn.mockReturnValue([favoriteProject]);
      handleProjectSelect(nonFavoriteProject, setSelectedProjects);
      expect(mockFn).toHaveBeenCalledWith([favoriteProject, nonFavoriteProject]);
    });
});