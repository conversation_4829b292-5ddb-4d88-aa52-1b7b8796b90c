import { describe, it, expect } from "vitest";
import { sortProjects } from "../src/utils/sortProjects";
import { Project, CMapProjectStatus } from "../src/types";

const makeProject = (
  id: string,
  code: string,
  title: string,
  cMapId: number
): Project => ({
  id,
  code,
  title,
  favorite: false,
  webUrl: "",
  cmapProject: CMapProjectStatus.NotInCmap,
  cMapId,
});

const assertSortedCodes = (
  projects: Project[],
  expectedAsc: string[],
  expectedDesc: string[]
) => {
  expect(sortProjects(projects, "code", "asc").map((p) => p.code)).toEqual(
    expectedAsc
  );
  expect(sortProjects(projects, "code", "desc").map((p) => p.code)).toEqual(
    expectedDesc
  );
};

describe("sortProjects", () => {
  const baseProjects: Project[] = [
    makeProject("1", "P2", "Project B", 2),
    makeProject("2", "P1", "Project A", 1),
    makeProject("3", "P3", "Project C", 3),
  ];

  const numericCodeProjects: Project[] = [
    makeProject("1", "0001", "A", 1),
    makeProject("2", "2", "B", 2),
    makeProject("3", "003", "C", 3),
    makeProject("4", "0010", "D", 4),
  ];

  const mixedAlphaNumeric: Project[] = [
    makeProject("1", "K001", "K Project", 1),
    makeProject("2", "001", "Project A", 2),
    makeProject("3", "010", "Project B", 3),
    makeProject("4", "100", "Project C", 4),
  ];

  const simpleAlpha: Project[] = [
    makeProject("3", "c", "Project B", 3),
    makeProject("1", "a", "K Project", 1),
    makeProject("4", "d", "Project C", 4),
    makeProject("2", "b", "Project A", 2),
  ];

  it("returns original array if no sort field is provided", () => {
    expect(sortProjects(baseProjects)).toEqual(baseProjects);
  });

  it("returns original array if array is empty", () => {
    expect(sortProjects([], "code", "asc")).toEqual([]);
  });

  it("returns original array if direction is invalid", () => {
    // @ts-expect-error
    expect(sortProjects(baseProjects, "code", "bad")).toEqual(baseProjects);
  });

  it("returns original array if field is invalid", () => {
    // @ts-expect-error
    expect(sortProjects(baseProjects, "wrong", "asc")).toEqual(baseProjects);
  });

  describe("sorts by title", () => {
    it("asc", () => {
      expect(
        sortProjects(baseProjects, "title", "asc").map((p) => p.title)
      ).toEqual(["Project A", "Project B", "Project C"]);
    });

    it("desc", () => {
      expect(
        sortProjects(baseProjects, "title", "desc").map((p) => p.title)
      ).toEqual(["Project C", "Project B", "Project A"]);
    });
  });

  describe("sorts codes", () => {
    it("numeric-looking", () => {
      assertSortedCodes(
        numericCodeProjects,
        ["0001", "2", "003", "0010"],
        ["0010", "003", "2", "0001"]
      );
    });

    it("mixed numeric and alphanumeric", () => {
      assertSortedCodes(
        mixedAlphaNumeric,
        ["001", "010", "100", "K001"],
        ["K001", "100", "010", "001"]
      );
    });

    it("alphabetical only", () => {
      assertSortedCodes(
        simpleAlpha,
        ["a", "b", "c", "d"],
        ["d", "c", "b", "a"]
      );
    });
  });
});
