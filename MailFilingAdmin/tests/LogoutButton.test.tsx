import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import userEvent from '@testing-library/user-event';
import { FluentProvider, webLightTheme } from '@fluentui/react-components';
import { LogoutButton } from '../src/components/LogoutButton';

const mockReload = vi.fn();
Object.defineProperty(window, 'location', {
  value: { reload: mockReload },
  writable: true,
});

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <FluentProvider theme={webLightTheme}>
      {component}
    </FluentProvider>
  );
};

describe('LogoutButton', () => {
  const mockLogout = vi.fn(() => Promise.resolve());
  
  const openLogoutDialog = async () => {
    const logoutButton = screen.getByRole('button', { name: /log out/i });
    await userEvent.click(logoutButton);
    return screen.getByRole('dialog');
  };

  const getConfirmButton = (dialog: HTMLElement) => {
    return within(dialog).getByRole('button', { 
      name: /(log out|logging out)/i 
    });
  };

  const clickConfirmLogout = async (dialog: HTMLElement) => {
    const confirmButton = getConfirmButton(dialog);
    await userEvent.click(confirmButton);
  };
  
  beforeEach(() => {
    vi.resetAllMocks();
    mockReload.mockClear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders the logout button', () => {
    renderWithProvider(<LogoutButton onLogout={mockLogout} />);
    expect(screen.getByRole('button', { name: /log out/i })).toBeInTheDocument();
  });

  it('opens confirmation dialog when clicked', async () => {
    renderWithProvider(<LogoutButton onLogout={mockLogout} />);
    await openLogoutDialog();
    
    expect(screen.getByText('Confirm Logout')).toBeInTheDocument();
    expect(screen.getByText(/are you sure you want to log out\?/i)).toBeInTheDocument();
  });

  it('closes dialog without logging out when Cancel is clicked', async () => {
    renderWithProvider(<LogoutButton onLogout={mockLogout} />);
    await openLogoutDialog();
    
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await userEvent.click(cancelButton);
    
    await waitFor(() => {
      expect(screen.queryByText('Confirm Logout')).not.toBeInTheDocument();
    });
    expect(mockLogout).not.toHaveBeenCalled();
  });

  it('calls onLogout and closes dialog when confirmed', async () => {
    renderWithProvider(<LogoutButton onLogout={mockLogout} />);
    const dialog = await openLogoutDialog();
    await clickConfirmLogout(dialog);
    
    expect(mockLogout).toHaveBeenCalledTimes(1);
    await waitFor(() => {
      expect(screen.queryByText('Confirm Logout')).not.toBeInTheDocument();
    });
  });

  it('shows loading state and disables buttons during logout', async () => {
    const delayedLogout = vi.fn().mockImplementation(() => 
      new Promise<void>((resolve) => setTimeout(resolve, 100))
    );
  
    renderWithProvider(<LogoutButton onLogout={delayedLogout} />);
    const dialog = await openLogoutDialog();
    await clickConfirmLogout(dialog);
    
    expect(screen.getByText('Logging out...')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeDisabled();
    expect(screen.getByRole('button', { name: /logging out/i })).toBeDisabled();
    
    await waitFor(() => {
      expect(screen.queryByText('Logging out...')).not.toBeInTheDocument();
    });
  });

  it('handles logout errors gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const failedLogout = vi.fn(() => Promise.reject(new Error('Logout failed')));
    
    renderWithProvider(<LogoutButton onLogout={failedLogout} />);
    const dialog = await openLogoutDialog();
    await clickConfirmLogout(dialog);
    
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Logout failed:', expect.any(Error));
    });
    
    await waitFor(() => {
      expect(screen.queryByText('Confirm Logout')).not.toBeInTheDocument();
    });
    
    consoleErrorSpy.mockRestore();
  });

  it('prevents multiple logout attempts', async () => {
    const delayedLogout = vi.fn().mockImplementation(() => 
      new Promise<void>((resolve) => setTimeout(resolve, 100))
    );
  
    renderWithProvider(<LogoutButton onLogout={delayedLogout} />);
    const dialog = await openLogoutDialog();
    
    await clickConfirmLogout(dialog);
    const confirmButton = getConfirmButton(dialog);
    await userEvent.click(confirmButton);
    await userEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(delayedLogout).toHaveBeenCalledTimes(1);
    });
  });
});