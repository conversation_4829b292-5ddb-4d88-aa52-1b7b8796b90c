import { describe, it, expect, vi, beforeEach } from "vitest";
import { SPFI } from "@pnp/sp";
import { Web } from "@pnp/sp/webs";
import {
  searchUsers,
  addUserToSite,
  removeUserFromSite,
  searchUserGroups,
  addUserGroupToSite,
} from "../src/services/getUsers";
import { SiteGroupTypes } from "../src/types";

vi.mock("@pnp/sp", () => ({
  SPFI: vi.fn(),
}));

vi.mock("@pnp/sp/webs", () => ({
  Web: vi.fn(),
}));

describe("getUsers", () => {
  let mockSharepoint: SPFI;
  const mockProjectUrl = "https://example.com/project";

  beforeEach(() => {
    mockSharepoint = {
      profiles: {
        clientPeoplePickerSearchUser: vi.fn().mockReturnValue([]),
      },
      web: {
        siteUsers: {
          filter: vi.fn().mockReturnThis(),
          top: vi.fn().mockReturnValue(vi.fn()),
        },
        ensureUser: vi.fn(),
      },
    } as any;

    (Web as any).mockReturnValue({
      associatedMemberGroup: vi.fn().mockResolvedValue({ Id: "1" }),
      associatedOwnerGroup: vi.fn().mockResolvedValue({ Id: "2" }),
      siteGroups: {
        getById: vi.fn().mockReturnValue({
          users: {
            add: vi.fn(),
            removeByLoginName: vi.fn(),
          },
        }),
      },
    });
  });

  describe("searchUsers", () => {
    it("should return filtered users", async () => {
      mockSharepoint.profiles.clientPeoplePickerSearchUser.mockReturnValue([
        {
          Key: "john doe login name",
          DisplayText: "John Doe",
          EntityData: {
            Email: "<EMAIL>",
          },
        },
        {
          Key: "jane smith login name",
          DisplayText: "Jane Smith",
          EntityData: {
            Email: "<EMAIL>",
          },
        },
      ]);
      const result = await searchUsers(mockSharepoint, "John");

      expect(result).toEqual([
        {
          id: "john doe login name",
          loginName: "john doe login name",
          name: "John Doe",
          email: "<EMAIL>",
        },
        {
          id: "jane smith login name",
          loginName: "jane smith login name",
          name: "Jane Smith",
          email: "<EMAIL>",
        },
      ]);
    });

    it("should throw an error if sharepoint is not initialized", async () => {
      await expect(searchUsers(null as any, "John")).rejects.toThrow(
        "sharepoint instance not initialized"
      );
    });

    it("should handle errors during search", async () => {
      mockSharepoint.web.siteUsers.top.mockReturnValue(() =>
        Promise.reject(new Error("API error"))
      );

      mockSharepoint.profiles.clientPeoplePickerSearchUser.mockRejectedValue(
        "API error"
      );

      await expect(searchUsers(mockSharepoint, "John")).rejects.toThrow(
        "API error"
      );
    });
  });

  describe("searchUserGroups", () => {
    it("should return user group keys", async () => {
      mockSharepoint.profiles.clientPeoplePickerSearchUser.mockReturnValue([
        {
          Key: "c:0t.c|tenant|<EMAIL>",
          DisplayText: "Group 1",
          EntityData: {
            Email: "<EMAIL>",
          },
        },
        {
          Key: "c:0t.c|tenant|<EMAIL>",
          DisplayText: "Group 2",
          EntityData: {
            Email: "<EMAIL>",
          },
        },
      ]);

      const result = await searchUserGroups(
        mockSharepoint,
        "Atvero Mail All Users"
      );

      expect(result).toEqual([
        "c:0t.c|tenant|<EMAIL>",
        "c:0t.c|tenant|<EMAIL>",
      ]);

      expect(
        mockSharepoint.profiles.clientPeoplePickerSearchUser
      ).toHaveBeenCalledWith({
        AllowEmailAddresses: true,
        AllowMultipleEntities: false,
        MaximumEntitySuggestions: 10,
        QueryString: "Atvero Mail All Users",
      });
    });

    it("should throw an error if sharepoint is not initialized", async () => {
      await expect(searchUserGroups(null as any, "Group")).rejects.toThrow(
        "sharepoint instance not initialized"
      );
    });

    it("should handle errors during user group search", async () => {
      mockSharepoint.profiles.clientPeoplePickerSearchUser.mockRejectedValue(
        new Error("API error")
      );

      const consoleErrorSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      await expect(searchUserGroups(mockSharepoint, "Group")).rejects.toThrow(
        "API error"
      );

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Error searching users:",
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe("addUserToSite", () => {
    it("should add user to site group", async () => {
      const userLoginName = "<EMAIL>";
      mockSharepoint.web.ensureUser.mockResolvedValue({
        LoginName: userLoginName,
      });

      await addUserToSite(
        mockSharepoint,
        {
          loginName: userLoginName,
          id: "1",
          name: "John Doe",
          email: "<EMAIL>",
        },
        mockProjectUrl
      );

      expect(Web).toHaveBeenCalledWith([mockSharepoint.web, mockProjectUrl]);
      const mockWeb = Web([mockSharepoint.web, mockProjectUrl]);
      expect(mockWeb.associatedMemberGroup).toHaveBeenCalled();
      expect(mockWeb.siteGroups.getById("1").users.add).toHaveBeenCalledWith(
        userLoginName
      );
    });

    it("should throw an error if sharepoint is not initialized", async () => {
      await expect(
        addUserToSite(null as any, "<EMAIL>", mockProjectUrl)
      ).rejects.toThrow("sharepoint instance not initialized");
    });

    it("should handle errors during user addition", async () => {
      const mockWeb = Web([mockSharepoint.web, mockProjectUrl]);
      mockWeb.associatedMemberGroup.mockRejectedValue(new Error("API error"));
      mockSharepoint.web = mockWeb;
      await expect(
        addUserToSite(
          mockSharepoint,
          {
            loginName: "Login",
            id: "1",
            name: "John Doe",
            email: "<EMAIL>",
          },
          mockProjectUrl
        )
      ).rejects.toThrow("API error");
    });
  });

  describe("addUserGroupToSite", () => {
    it("should add groups to site Member group", async () => {
      const groupKeys = [
        "c:0t.c|tenant|<EMAIL>",
        "c:0t.c|tenant|<EMAIL>",
      ];

      await addUserGroupToSite(
        mockSharepoint,
        groupKeys,
        mockProjectUrl,
        SiteGroupTypes.Member
      );

      expect(Web).toHaveBeenCalledWith([mockSharepoint.web, mockProjectUrl]);
      const mockWeb = Web([mockSharepoint.web, mockProjectUrl]);
      expect(mockWeb.associatedMemberGroup).toHaveBeenCalled();
      expect(mockWeb.associatedOwnerGroup).not.toHaveBeenCalled();

      // Should add each group to the site group
      expect(mockWeb.siteGroups.getById).toHaveBeenCalledWith("1");
      expect(mockWeb.siteGroups.getById().users.add).toHaveBeenCalledTimes(2);
      expect(mockWeb.siteGroups.getById().users.add).toHaveBeenCalledWith(
        "c:0t.c|tenant|<EMAIL>"
      );
      expect(mockWeb.siteGroups.getById().users.add).toHaveBeenCalledWith(
        "c:0t.c|tenant|<EMAIL>"
      );
    });

    it("should add groups to site Owner group", async () => {
      const groupKeys = ["c:0t.c|tenant|<EMAIL>"];

      await addUserGroupToSite(
        mockSharepoint,
        groupKeys,
        mockProjectUrl,
        SiteGroupTypes.Owner
      );

      expect(Web).toHaveBeenCalledWith([mockSharepoint.web, mockProjectUrl]);
      const mockWeb = Web([mockSharepoint.web, mockProjectUrl]);
      expect(mockWeb.associatedMemberGroup).not.toHaveBeenCalled();
      expect(mockWeb.associatedOwnerGroup).toHaveBeenCalled();

      // Should add each group to the site group
      expect(mockWeb.siteGroups.getById).toHaveBeenCalledWith("2");
      expect(mockWeb.siteGroups.getById().users.add).toHaveBeenCalledTimes(1);
      expect(mockWeb.siteGroups.getById().users.add).toHaveBeenCalledWith(
        "c:0t.c|tenant|<EMAIL>"
      );
    });

    it("should throw an error if sharepoint is not initialized", async () => {
      await expect(
        addUserGroupToSite(
          null as any,
          ["group"],
          mockProjectUrl,
          SiteGroupTypes.Member
        )
      ).rejects.toThrow("sharepoint instance not initialized");
    });

    it("should handle errors during group addition", async () => {
      const mockWeb = Web([mockSharepoint.web, mockProjectUrl]);
      mockWeb.associatedMemberGroup.mockRejectedValue(new Error("API error"));
      mockSharepoint.web = mockWeb;

      const consoleErrorSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      await expect(
        addUserGroupToSite(
          mockSharepoint,
          ["c:0t.c|tenant|<EMAIL>"],
          mockProjectUrl,
          SiteGroupTypes.Member
        )
      ).rejects.toThrow("API error");

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Error adding user to site:",
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe("removeUserFromSite", () => {
    it("should remove user from site group", async () => {
      const userLoginName = "<EMAIL>";
      mockSharepoint.web.ensureUser.mockResolvedValue({
        LoginName: userLoginName,
      });

      await removeUserFromSite(mockSharepoint, userLoginName, mockProjectUrl);

      expect(Web).toHaveBeenCalledWith([mockSharepoint.web, mockProjectUrl]);
      expect(mockSharepoint.web.ensureUser).toHaveBeenCalledWith(userLoginName);
      const mockWeb = Web([mockSharepoint.web, mockProjectUrl]);
      expect(mockWeb.associatedMemberGroup).toHaveBeenCalled();
      expect(
        mockWeb.siteGroups.getById("1").users.removeByLoginName
      ).toHaveBeenCalledWith(userLoginName);
    });

    it("should throw an error if sharepoint is not initialized", async () => {
      await expect(
        removeUserFromSite(null as any, "<EMAIL>", mockProjectUrl)
      ).rejects.toThrow("sharepoint instance not initialized");
    });

    it("should handle errors during user removal", async () => {
      mockSharepoint.web.ensureUser.mockRejectedValue(new Error("API error"));

      await expect(
        removeUserFromSite(mockSharepoint, "<EMAIL>", mockProjectUrl)
      ).rejects.toThrow("API error");
    });
  });
});
