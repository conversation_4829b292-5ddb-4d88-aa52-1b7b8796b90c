import { describe, it, expect, vi, beforeEach, Mock } from "vitest";
import {
  getProjectDesign,
  createProject,
  applySiteDesign,
  applyConfidentialUserPermissions,
  importUsers,
  addDefaultUserGroups,
} from "../src/services/createProject";
import * as CreateProjectModule from "../src/services/createProject";
import { SPFI } from "@pnp/sp";
import { ISiteDesignInfo } from "@pnp/sp/site-designs/types";
import { IWeb, Web } from "@pnp/sp/webs";
import { GraphFI } from "@pnp/graph";
import { Hubsite, Project, SiteGroupTypes, Tenancy } from "../src/types";
import { addUserGroupToSite, searchUserGroups } from "../src/services/getUsers";
import { toggleUserToConfidentialGroup } from "../src/services/toggleUserToConfidentialGroup";
import { IBackendAdapter } from "../src/adapters/IBackendAdapters";

vi.mock("@pnp/sp", () => ({
  SPFI: vi.fn(),
}));

vi.mock("@pnp/graph", () => ({
  GraphFI: vi.fn(),
}));

vi.mock("@pnp/sp/webs", () => ({
  Web: vi.fn(),
}));

vi.mock("../src/services/getHubsiteFromGraph", () => ({
  getHubsiteFromPath: vi.fn().mockResolvedValue({ id: "mock-hubsite-id" }),
}));

vi.mock("../src/services/getUsers", async () => {
  const actual = await vi.importActual("../src/services/getUsers");
  return {
    ...actual,
    searchUserGroups: vi.fn(),
    addUserGroupToSite: vi.fn(),
  };
});

vi.mock("../src/services/toggleUserToConfidentialGroup", async () => {
  return {
    toggleUserToConfidentialGroup: vi.fn(),
  };
});

const hubsite = {
  displayName: "Atvero Mail",
  name: "AtveroMail",
  url: "http://example.com",
};
const mockOnProgressUpdate = vi.fn();
const mockGetSiteDesigns = vi.fn();

describe("Project Creation Functions", () => {
  let mockSharepoint: any;
  let mockGraph: any;

  beforeEach(() => {
    mockSharepoint = {
      siteDesigns: {
        getSiteDesigns: mockGetSiteDesigns,
        getSiteDesignRun: vi.fn(),
        applySiteDesign: vi.fn(),
      },
      site: {
        getWebUrlFromPageUrl: vi.fn(),
        createModernTeamSiteFromProps: vi.fn(),
      },
      web: {},
    };

    mockGraph = {
      sites: {
        getByUrl: vi.fn().mockReturnValue({
          id: "mock-site-id",
        }),
      },
    };

    vi.mocked(Web).mockReturnValue({
      addSiteDesignTask: vi.fn().mockResolvedValue({ ID: "mock-task-id" }),
      getSiteDesignRunStatus: vi.fn().mockResolvedValue(true),
      lists: {
        ensure: vi.fn().mockResolvedValue({
          list: {
            breakRoleInheritance: vi.fn(),
            roleAssignments: { add: vi.fn() },
          },
        }),
      },
      roleDefinitions: vi
        .fn()
        .mockResolvedValue([null, null, null, { Id: "mock-role-id" }]),
      siteGroups: {
        add: vi.fn().mockResolvedValue({ Id: "mock-group-id" }),
      },
    } as unknown as IWeb);
  });

  describe("getProjectDesign", () => {
    it("should return the correct site design", async () => {
      const mockDesigns: ISiteDesignInfo[] = [
        { Title: "Design 1", Id: "1" } as ISiteDesignInfo,
        { Title: "Design 2", Id: "2" } as ISiteDesignInfo,
      ];

      mockSharepoint.siteDesigns.getSiteDesigns.mockResolvedValue(mockDesigns);
      const fallbackSiteDesignName = "Design 2";

      const result = await getProjectDesign(
        mockSharepoint as unknown as SPFI,
        "Design 2",
        fallbackSiteDesignName
      );
      expect(result).toEqual({ Title: "Design 2", Id: "2" });
    });
    it("uses fallback site design when primary is not found", async () => {
      const mockSiteDesigns: ISiteDesignInfo[] = [
        {
          Id: "2",
          Title: "Fallback Design Template",
          WebTemplate: "68",
          SiteScriptIds: ["script-2"],
          Description: "Fallback site design",
          PreviewImageUrl: "https://example.com/fallback.png",
          PreviewImageAltText: "Fallback preview",
          IsDefault: false,
          Version: "1.0",
        },
      ];

      mockGetSiteDesigns.mockResolvedValue(mockSiteDesigns);

      const result = await getProjectDesign(
        mockSharepoint,
        "Primary Design",
        "Fallback Design"
      );

      expect(result).toBeDefined();
      expect(result?.Title).toBe("Fallback Design Template");
    });
  });

  describe("createProject", () => {
    const mockCancelToken = { cancelled: false };

    it("should throw an error if project creation fails", async () => {
      mockSharepoint.site.getWebUrlFromPageUrl.mockRejectedValueOnce(
        new Error("Not found")
      );
      mockSharepoint.site.createModernTeamSiteFromProps.mockRejectedValue(
        new Error("Failed to create project")
      );

      const mockBackendAdapter = {
        getSetting: vi.fn().mockImplementation(async (setting: string) => {
          if (setting === "useNewProjectCreation") {
            return undefined;
          }
          return undefined;
        }),
        createSite: vi.fn().mockResolvedValue(false),
      };

      await expect(
        createProject(
          mockGraph as GraphFI,
          mockSharepoint as unknown as SPFI,
          mockBackendAdapter as unknown as IBackendAdapter,
          Tenancy.Atvero,
          "sharepoint.com",
          "design-id",
          "Project Title",
          "project-description",
          undefined,
          hubsite,
          undefined,
          mockOnProgressUpdate,
          mockCancelToken
        )
      ).rejects.toThrow("Failed to create project");
    });

    it("should create a project with the backend based on the app setting", async () => {
      mockSharepoint.site.getWebUrlFromPageUrl.mockRejectedValueOnce(
        new Error("Not found")
      );

      const mockBackendAdapter = {
        getSetting: vi.fn().mockImplementation(async (setting: string) => {
          if (setting === "useNewProjectCreation") {
            return "enabled";
          }
          return undefined;
        }),
        createSite: vi.fn().mockResolvedValue(false),
      };

      await expect(
        createProject(
          mockGraph as GraphFI,
          mockSharepoint as unknown as SPFI,
          mockBackendAdapter as unknown as IBackendAdapter,
          Tenancy.Atvero,
          "sharepoint.com",
          "design-id",
          "Project Title",
          "project-description",
          undefined,
          hubsite,
          undefined,
          mockOnProgressUpdate,
          mockCancelToken
        )
      ).rejects.toThrow("Failed to create project");
    });
  });

  describe("applySiteDesign", () => {
    it('should return "already applied" if site design is already applied', async () => {
      mockSharepoint.siteDesigns.getSiteDesignRun.mockResolvedValue([
        { SiteDesignID: "existing-design-id" },
      ]);

      const result = await applySiteDesign(
        mockSharepoint as unknown as SPFI,
        "https://project.com",
        "existing-design-id",
        false
      );
      expect(result).toBe("already applied");
    });

    it("should apply the site design", async () => {
      mockSharepoint.siteDesigns.getSiteDesignRun.mockResolvedValue([
        { SiteDesignID: "non-existing-design-id" },
      ]);
      const mockWeb = {
        addSiteDesignTask: vi.fn().mockResolvedValue({ id: "mock-task-id" }),
      };
      vi.mocked(Web).mockReturnValue(mockWeb as unknown as IWeb);

      const result = await applySiteDesign(
        mockSharepoint as unknown as SPFI,
        "https://project.com",
        "existing-design-id",
        false
      );
      expect(result).toBe(true);
    });

    it("should return false if applying site design fails", async () => {
      mockSharepoint.siteDesigns.getSiteDesignRun.mockResolvedValue([]);

      const mockWeb = {
        addSiteDesignTask: vi.fn().mockRejectedValue(new Error("Apply failed")),
        getSiteDesignRunStatus: vi
          .fn()
          .mockRejectedValue(new Error("Status check failed")),
      };
      vi.mocked(Web).mockReturnValue(mockWeb as unknown as IWeb);

      const result = await applySiteDesign(
        mockSharepoint as unknown as SPFI,
        "https://project.com",
        "new-design-id",
        false
      );
      expect(result).toBe(false);
    });
  });

  describe("applyConfidentialUserPermissions", () => {
    it("should call necessary methods to apply confidential user permissions", async () => {
      const mockProjectUrl = "https://newproject.sharepoint.com";

      const mockList = {
        breakRoleInheritance: vi.fn().mockResolvedValue(undefined),
        roleAssignments: {
          add: vi.fn().mockResolvedValue(undefined),
        },
      };

      // @ts-expect-error  TS Ignore here due to IWeb being quite verbose to mock and outside the scope of this test.
      const mockWeb = {
        siteGroups: {
          add: vi.fn().mockResolvedValue({ Id: "mock-group-id" }),
        },
        lists: {
          ensure: vi.fn().mockResolvedValue({ list: mockList }),
        },
        roleDefinitions: vi
          .fn()
          .mockResolvedValue([null, null, null, { Id: "mock-role-id" }]),
      } as IWeb;

      vi.mocked(Web).mockReturnValue(mockWeb);

      await applyConfidentialUserPermissions(mockSharepoint, mockProjectUrl);

      expect(Web).toHaveBeenCalledWith([mockSharepoint.web, mockProjectUrl]);
      expect(mockWeb.siteGroups.add).toHaveBeenCalledWith({
        Title: "Confidential Users",
        Description:
          "Group for users with access to see confidential documents",
      });
      expect(mockWeb.lists.ensure).toHaveBeenCalledWith(
        "Confidential Filed Email Content"
      );
      expect(mockWeb.roleDefinitions).toHaveBeenCalled();
      expect(mockList.breakRoleInheritance).toHaveBeenCalled();
      expect(mockList.roleAssignments.add).toHaveBeenCalledWith(
        "mock-group-id",
        "mock-role-id"
      );
    });

    it("should throw an error when applying confidential user permissions fails", async () => {
      const mockSharepoint = {} as SPFI;
      const mockProjectUrl = "https://newproject.sharepoint.com";

      // @ts-expect-error TS Ignore here due to IWeb being quite verbose to mock and outside the scope of this test.
      const mockWeb = {
        siteGroups: {
          add: vi.fn().mockRejectedValue(new Error("Mock error")),
        },
      } as IWeb;

      vi.mocked(Web).mockReturnValue(mockWeb);

      const consoleErrorSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      await expect(
        applyConfidentialUserPermissions(mockSharepoint, mockProjectUrl)
      ).rejects.toThrow("Mock error");

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "Failed to create Confidential Users group:",
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe("importUsers", () => {
    let mockSharepoint: SPFI;
    let mockImportProjectWeb: any;
    let mockNewProjectWeb: any;

    beforeEach(() => {
      mockSharepoint = {} as SPFI;

      const mockUsers = [
        { Title: "User 1", LoginName: "<EMAIL>" },
        { Title: "User 2", LoginName: "<EMAIL>" },
      ];

      mockImportProjectWeb = {
        associatedMemberGroup: {
          users: vi.fn().mockResolvedValue(mockUsers),
        },
      };

      mockNewProjectWeb = {
        associatedMemberGroup: vi
          .fn()
          .mockResolvedValue({ Id: "mock-group-id" }),
        ensureUser: vi
          .fn()
          .mockImplementation((loginName) =>
            Promise.resolve({ LoginName: loginName })
          ),
        siteGroups: {
          getById: vi.fn().mockReturnValue({
            users: {
              add: vi.fn().mockResolvedValue(undefined),
            },
          }),
        },
      };

      vi.mocked(Web).mockImplementation((args) => {
        const url = Array.isArray(args) ? args[1] : args;
        if (
          typeof url === "string" &&
          url === "https://import-project.sharepoint.com"
        ) {
          return mockImportProjectWeb;
        } else {
          return mockNewProjectWeb;
        }
      });
    });

    it("should successfully import users", async () => {
      const result = await importUsers(
        mockSharepoint,
        "https://import-project.sharepoint.com",
        "https://new-project.sharepoint.com"
      );

      expect(result).toBe(true);
      expect(
        mockImportProjectWeb.associatedMemberGroup.users
      ).toHaveBeenCalled();
      expect(mockNewProjectWeb.associatedMemberGroup).toHaveBeenCalled();
      expect(mockNewProjectWeb.ensureUser).toHaveBeenCalledTimes(2);
      expect(mockNewProjectWeb.ensureUser).toHaveBeenCalledWith(
        "<EMAIL>"
      );
      expect(mockNewProjectWeb.ensureUser).toHaveBeenCalledWith(
        "<EMAIL>"
      );
      expect(mockNewProjectWeb.siteGroups.getById).toHaveBeenCalledWith(
        "mock-group-id"
      );
      expect(
        mockNewProjectWeb.siteGroups.getById().users.add
      ).toHaveBeenCalledTimes(2);
    });

    it("should return false if an error occurs during import", async () => {
      mockNewProjectWeb.ensureUser.mockRejectedValueOnce(
        new Error("User not found")
      );

      const result = await importUsers(
        mockSharepoint,
        "https://import-project.sharepoint.com",
        "https://new-project.sharepoint.com"
      );

      expect(result).toBe(false);
    });
  });

  describe("addDefaultUserGroups", () => {
    let mockSharepoint: SPFI;
    const projectUrl = "https://example.com/sites/project";

    beforeEach(() => {
      mockSharepoint = {} as SPFI;
      vi.resetAllMocks();
    });

    it("should add member user groups to the site", async () => {
      vi.mocked(searchUserGroups)
        .mockResolvedValueOnce(["atvero-member-key"]) // Atvero Mail All Users
        .mockResolvedValueOnce(["cmap-member-key"]) // CMap Mail All Users
        .mockResolvedValueOnce(["atvero-admin-key"]) // Atvero Mail Admins
        .mockResolvedValueOnce(["cmap-admin-key"]) // CMap Mail Admins
        .mockResolvedValueOnce(["atvero-confidential-key"]) // Atvero Mail All Confidential Users
        .mockResolvedValueOnce(["cmap-confidential-key"]); // CMap Mail All Confidential Users

      await addDefaultUserGroups(mockSharepoint, projectUrl);

      expect(searchUserGroups).toHaveBeenCalledWith(
        mockSharepoint,
        "Atvero Mail All Users"
      );
      expect(searchUserGroups).toHaveBeenCalledWith(
        mockSharepoint,
        "CMap Mail All Users"
      );

      expect(addUserGroupToSite).toHaveBeenCalledWith(
        mockSharepoint,
        ["atvero-member-key", "cmap-member-key"],
        projectUrl,
        SiteGroupTypes.Member
      );
    });

    it("should add admin user groups to the site", async () => {
      vi.mocked(searchUserGroups)
        .mockResolvedValueOnce([]) // Empty Atvero Mail All Users
        .mockResolvedValueOnce([]) // Empty CMap Mail All Users
        .mockResolvedValueOnce(["atvero-admin-key"]) // Atvero Mail Admins
        .mockResolvedValueOnce(["cmap-admin-key"]) // CMap Mail Admins
        .mockResolvedValueOnce([]) // Empty Atvero Mail All Confidential Users
        .mockResolvedValueOnce([]); // Empty CMap Mail All Confidential Users

      await addDefaultUserGroups(mockSharepoint, projectUrl);

      expect(searchUserGroups).toHaveBeenCalledWith(
        mockSharepoint,
        "Atvero Mail Admins"
      );
      expect(searchUserGroups).toHaveBeenCalledWith(
        mockSharepoint,
        "CMap Mail Admins"
      );

      expect(addUserGroupToSite).toHaveBeenCalledWith(
        mockSharepoint,
        ["atvero-admin-key", "cmap-admin-key"],
        projectUrl,
        SiteGroupTypes.Owner
      );
    });

    it("should add confidential user groups to the site", async () => {
      vi.mocked(searchUserGroups)
        .mockResolvedValueOnce([]) // Empty Atvero Mail All Users
        .mockResolvedValueOnce([]) // Empty CMap Mail All Users
        .mockResolvedValueOnce([]) // Empty Atvero Mail Admins
        .mockResolvedValueOnce([]) // Empty CMap Mail Admins
        .mockResolvedValueOnce(["atvero-confidential-key"]) // Atvero Mail All Confidential Users
        .mockResolvedValueOnce(["cmap-confidential-key"]); // CMap Mail All Confidential Users

      await addDefaultUserGroups(mockSharepoint, projectUrl);

      expect(searchUserGroups).toHaveBeenCalledWith(
        mockSharepoint,
        "Atvero Mail All Confidential Users"
      );
      expect(searchUserGroups).toHaveBeenCalledWith(
        mockSharepoint,
        "CMap Mail All Confidential Users"
      );

      expect(toggleUserToConfidentialGroup).toHaveBeenCalledWith({
        sharepoint: mockSharepoint,
        projectUrl: projectUrl,
        groupKeys: ["atvero-confidential-key", "cmap-confidential-key"],
        canAccessConfidentialEmails: true,
      });
    });

    it("should not call addUserGroupToSite if no member groups are found", async () => {
      vi.mocked(searchUserGroups)
        .mockResolvedValueOnce([]) // Empty Atvero Mail All Users
        .mockResolvedValueOnce([]) // Empty CMap Mail All Users
        .mockResolvedValueOnce(["atvero-admin-key"]) // Atvero Mail Admins
        .mockResolvedValueOnce(["cmap-admin-key"]) // CMap Mail Admins
        .mockResolvedValueOnce(["atvero-confidential-key"]) // Atvero Mail All Confidential Users
        .mockResolvedValueOnce(["cmap-confidential-key"]); // CMap Mail All Confidential Users

      await addDefaultUserGroups(mockSharepoint, projectUrl);

      // Check that addUserGroupToSite was not called for member groups
      const addUserGroupCalls = vi.mocked(addUserGroupToSite).mock.calls;
      const memberCalls = addUserGroupCalls.filter(
        (call) => call[3] === SiteGroupTypes.Member
      );
      expect(memberCalls.length).toBe(0);
    });

    it("should not call addUserGroupToSite if no admin groups are found", async () => {
      vi.mocked(searchUserGroups)
        .mockResolvedValueOnce(["atvero-member-key"]) // Atvero Mail All Users
        .mockResolvedValueOnce(["cmap-member-key"]) // CMap Mail All Users
        .mockResolvedValueOnce([]) // Empty Atvero Mail Admins
        .mockResolvedValueOnce([]) // Empty CMap Mail Admins
        .mockResolvedValueOnce(["atvero-confidential-key"]) // Atvero Mail All Confidential Users
        .mockResolvedValueOnce(["cmap-confidential-key"]); // CMap Mail All Confidential Users

      await addDefaultUserGroups(mockSharepoint, projectUrl);

      // Check that addUserGroupToSite was not called for admin groups
      const addUserGroupCalls = vi.mocked(addUserGroupToSite).mock.calls;
      const ownerCalls = addUserGroupCalls.filter(
        (call) => call[3] === SiteGroupTypes.Owner
      );
      expect(ownerCalls.length).toBe(0);
    });

    it("should always call toggleUserToConfidentialGroup even with empty array", async () => {
      vi.mocked(searchUserGroups)
        .mockResolvedValueOnce(["atvero-member-key"]) // Atvero Mail All Users
        .mockResolvedValueOnce(["cmap-member-key"]) // CMap Mail All Users
        .mockResolvedValueOnce(["atvero-admin-key"]) // Atvero Mail Admins
        .mockResolvedValueOnce(["cmap-admin-key"]) // CMap Mail Admins
        .mockResolvedValueOnce([]) // Empty Atvero Mail All Confidential Users
        .mockResolvedValueOnce([]); // Empty CMap Mail All Confidential Users

      await addDefaultUserGroups(mockSharepoint, projectUrl);

      expect(toggleUserToConfidentialGroup).toHaveBeenCalledWith({
        sharepoint: mockSharepoint,
        projectUrl: projectUrl,
        groupKeys: [],
        canAccessConfidentialEmails: true,
      });
    });

    it("should handle errors from searchUserGroups", async () => {
      const testError = new Error("Test error");
      vi.mocked(searchUserGroups).mockRejectedValueOnce(testError);

      await expect(
        addDefaultUserGroups(mockSharepoint, projectUrl)
      ).rejects.toThrow(testError);
    });
  });

  describe("repairProject", () => {
    let mockSharepoint: SPFI;
    let mockOnProgressUpdate: Mock;
    let mockHubsite: Hubsite;
    let mockProject: Project;

    beforeEach(() => {
      // Create a mock Hubsite
      mockHubsite = {
        displayName: "Test Hubsite",
        name: "TestHubsite",
        url: "https://example.sharepoint.com/sites/hubsite",
      };

      // Create a mock Project
      mockProject = {
        code: "TEST-001",
        title: "Test Project",
        id: "project-123",
        cMapId: 456,
        favorite: false,
        cmapProject: {} as any,
        webUrl: "https://example.sharepoint.com/sites/test-001",
      };

      // Create a comprehensive mock for Sharepoint
      mockSharepoint = {
        web: {},
        siteDesigns: {
          getSiteDesigns: vi.fn().mockResolvedValue([
            {
              Title: "Test Site Design",
              Id: "design-123",
            },
          ]),
        },
        site: {},
      } as unknown as SPFI;

      // Mock progress update function
      mockOnProgressUpdate = vi.fn();

      // Mock other methods
      vi.spyOn(CreateProjectModule, "addProjectListRow").mockResolvedValue(
        undefined
      );
      vi.spyOn(
        CreateProjectModule,
        "createConfidentialEmailFilimg"
      ).mockResolvedValue(undefined);
      vi.spyOn(
        CreateProjectModule,
        "applyConfidentialUserPermissions"
      ).mockResolvedValue(undefined);
      vi.spyOn(CreateProjectModule, "applySiteDesign").mockResolvedValue(true);
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    describe("site design retrieval", () => {
      it("should handle site design not found", async () => {
        // Mock getSiteDesigns to return an empty array
        mockSharepoint.siteDesigns.getSiteDesigns = vi
          .fn()
          .mockResolvedValue([]);

        const result = await CreateProjectModule.repairProject(
          mockSharepoint,
          "TestTenancy",
          "Nonexistent Design",
          mockHubsite,
          mockProject,
          mockOnProgressUpdate,
          "Fallback Design"
        );

        expect(result.success).toBe(false);
        expect(result.message).toBe("Failed to repair the project.");
      });
    });
  });
});
