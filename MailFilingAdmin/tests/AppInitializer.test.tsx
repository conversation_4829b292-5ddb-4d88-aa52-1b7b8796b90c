import { render, screen, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import userEvent from "@testing-library/user-event";
import { GraphFI } from "@pnp/graph";
import { SPFI } from "@pnp/sp";
import AppInitializer from "../src/AppInitializer";
import { useGraph } from "../src/services/useGraph";
import { useSharePointApi } from "../src/services/useSharePoint";
import { Tenancy } from "../src/types";
import { detectTenancy } from "../src/services/TenancyService";

// Mock the adapter factory and backend adapter
const mockBackendAdapter = {
  getHubsites: vi.fn(),
};

const mockLegacyAdapter = {
  setSharepointHostName: vi.fn(),
  getSharepointHostName: vi.fn(),
};

vi.mock("../src/adapters/AdapterFactory", () => ({
  adapterFactory: vi.fn(() =>
    Promise.resolve({
      legacyAdapter: mockLegacyAdapter,
      backendAdapter: mockBackendAdapter,
    })
  ),
  Adapters: {},
}));

vi.mock("../src/services/TenancyService", () => ({
  detectTenancy: vi.fn(),
}));

vi.mock("@azure/msal-react", () => ({
  MsalProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
  MsalAuthenticationTemplate: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

vi.mock("@fluentui/react-components", () => ({
  FluentProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
  webLightTheme: {},
  Spinner: ({ label }: { label?: string }) => (
    <div data-testid="spinner">{label ?? "Loading..."}</div>
  ),
  makeStyles: () => () => ({
    loginContainer: "mock-login-container",
    loaderContainer: "mock-loader-container",
    errorHeading: "mock-error-heading",
    errorMessage: "mock-error-message",
    logo: "mock-logo",
  }),
  shorthands: {
    gap: () => "mock-gap",
  },
}));

vi.mock("../src/services/useGraph", () => ({
  useGraph: vi.fn(),
}));

vi.mock("../src/services/useSharePoint", () => ({
  useSharePointApi: vi.fn(),
  msalInstance: {},
}));

vi.mock("../src/App", () => ({
  default: ({
    hubsites,
    selectedHubsite,
    hubsiteError,
    onHubsiteSelect,
    tenancy,
  }: any) => (
    <div data-testid="app-component">
      <div data-testid="hubsites-data">
        {JSON.stringify({ hubsites, selectedHubsite, hubsiteError })}
      </div>
      <div data-testid="tenancy-data">{tenancy}</div>
      <button onClick={() => onHubsiteSelect?.(hubsites?.[0])}>
        Select Hubsite
      </button>
    </div>
  ),
}));

vi.mock("../src/assets/AtveroLogo", () => ({
  AtveroLogo: () => <div data-testid="atvero-logo">Atvero Logo</div>,
}));

vi.mock(
  "../src/assets/CMap PIM & CMap Mail/CMap Mail/Black_Text/SVG/CMap_Mail_black_text_svg.svg",
  () => ({
    default: "cmap-logo-path",
  })
);

// Mock window.location
const originalLocation = window.location;

const setupTest = (forcedTenancy: Tenancy = Tenancy.CMap) => {
  const mockGraph = {} as GraphFI;
  const mockSharepoint = {} as SPFI;
  const mockSharepointHostName = "https://example.sharepoint.com";

  // Directly set the tenancy detection mock to return the specified tenancy
  (detectTenancy as any).mockReturnValue(forcedTenancy);

  (useGraph as any).mockReturnValue({
    isInitialized: true,
    loginError: false,
    graph: mockGraph,
    manualLogin: vi.fn(),
  });

  (useSharePointApi as any).mockReturnValue({
    sharepoint: mockSharepoint,
    sharepointHostName: mockSharepointHostName,
    changeTenant: vi.fn(),
  });

  return { mockGraph, mockSharepoint, mockSharepointHostName };
};

const renderAndWaitForHubsites = async () => {
  render(<AppInitializer />);
  await screen.findByTestId("hubsites-data");
  return JSON.parse(screen.getByTestId("hubsites-data").textContent ?? "{}");
};

describe("AppInitializer", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockBackendAdapter.getHubsites.mockReset();
    localStorage.clear();

    // Reset window.location
    Object.defineProperty(window, "location", {
      writable: true,
      value: originalLocation,
    });
  });

  afterEach(() => {
    localStorage.clear();
  });

  describe("Tenancy Detection", () => {
    it("should detect CMap tenancy by default", () => {
      setupTest(Tenancy.CMap);
      render(<AppInitializer />);
      expect(detectTenancy).toHaveBeenCalled();
    });

    it("should detect Atvero tenancy", () => {
      setupTest(Tenancy.Atvero);
      render(<AppInitializer />);
      expect(detectTenancy).toHaveBeenCalled();
    });

    it("should use forced tenancy from localStorage", () => {
      // Directly set localStorage item before test
      localStorage.setItem("forcedTenancy", Tenancy.Atvero);

      // Setup test with CMap as default, but localStorage should override
      setupTest(Tenancy.CMap);

      // Verify that localStorage is used
      render(<AppInitializer />);
      expect(detectTenancy).toHaveBeenCalled();
    });
  });

  describe("Logo Display", () => {
    it("should show CMap logo when tenancy is CMap", () => {
      setupTest(Tenancy.CMap);
      render(<AppInitializer />);
      const logoImg = screen.getByAltText("CMap Mail");
      expect(logoImg).toBeInTheDocument();
      expect(logoImg).toHaveAttribute("src", "cmap-logo-path");
      expect(logoImg).toHaveClass("mock-logo");
    });

    it("should show Atvero logo when tenancy is Atvero", () => {
      setupTest(Tenancy.Atvero);
      render(<AppInitializer />);
      expect(screen.getByTestId("atvero-logo")).toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("should show CMap error message when login fails", () => {
      setupTest(Tenancy.CMap);
      (useGraph as any).mockReturnValue({
        isInitialized: true,
        loginError: true,
        graph: undefined,
        manualLogin: vi.fn(),
      });

      render(<AppInitializer />);
      expect(screen.getByText("Login Failed")).toBeInTheDocument();
      expect(
        screen.getByText("Unable to load CMap Mail: Admin. Please try again.")
      ).toBeInTheDocument();
    });

    it("should show Atvero error message when login fails", () => {
      setupTest(Tenancy.Atvero);
      (useGraph as any).mockReturnValue({
        isInitialized: true,
        loginError: true,
        graph: undefined,
        manualLogin: vi.fn(),
      });

      render(<AppInitializer />);
      expect(screen.getByText("Login Failed")).toBeInTheDocument();
      expect(
        screen.getByText("Unable to load Atvero Mail: Admin. Please try again.")
      ).toBeInTheDocument();
    });
  });

  describe("Initialization and Loading", () => {
    it("should show loading spinner while initializing", () => {
      setupTest();
      (useGraph as any).mockReturnValue({
        isInitialized: false,
        loginError: false,
        graph: undefined,
        manualLogin: vi.fn(),
      });

      (useSharePointApi as any).mockReturnValue({
        sharepoint: undefined,
        sharepointHostName: undefined,
      });

      render(<AppInitializer />);
      expect(screen.getByTestId("spinner")).toHaveTextContent(
        "Loading Atvero Mail Admin..."
      );
    });

    it("should show loading state while fetching hubsites", async () => {
      setupTest();
      mockBackendAdapter.getHubsites.mockImplementation(
        () => new Promise(() => {})
      );
      render(<AppInitializer />);
      expect(
        await screen.findByText("Fetching your hubsites...")
      ).toBeInTheDocument();
    });
  });

  describe("Hubsite Handling", () => {
    it("should handle successful hubsites fetch", async () => {
      setupTest();
      const mockHubsites = [
        { displayName: "Hubsite 1", name: "hub1", url: "https://hub1.com" },
        { displayName: "Hubsite 2", name: "hub2", url: "https://hub2.com" },
      ];
      mockBackendAdapter.getHubsites.mockResolvedValue({
        hubsites: mockHubsites,
      });

      const hubsitesData = await renderAndWaitForHubsites();
      expect(hubsitesData.hubsites).toEqual(mockHubsites);
    });

    it("should auto-select single hubsite", async () => {
      setupTest();
      const mockHubsite = {
        displayName: "Single Hubsite",
        name: "hub1",
        url: "https://hub1.com",
      };
      mockBackendAdapter.getHubsites.mockResolvedValue({
        hubsites: [mockHubsite],
      });

      const hubsitesData = await renderAndWaitForHubsites();
      expect(hubsitesData.selectedHubsite).toEqual(mockHubsite);
    });

    it("should handle hubsite fetch error", async () => {
      setupTest();
      const errorMessage = "Failed to fetch hubsites";
      mockBackendAdapter.getHubsites.mockResolvedValue({ error: errorMessage });

      const hubsitesData = await renderAndWaitForHubsites();
      expect(hubsitesData.hubsiteError).toBe(errorMessage);
    });

    it("should handle no hubsites available", async () => {
      setupTest();
      mockBackendAdapter.getHubsites.mockResolvedValue({ hubsites: [] });

      const hubsitesData = await renderAndWaitForHubsites();
      expect(hubsitesData.hubsiteError).toBe("No hubsites available");
    });

    it("should handle hubsite selection", async () => {
      setupTest();
      const mockHubsites = [
        { displayName: "Hubsite 1", name: "hub1", url: "https://hub1.com" },
        { displayName: "Hubsite 2", name: "hub2", url: "https://hub2.com" },
      ];
      mockBackendAdapter.getHubsites.mockResolvedValue({
        hubsites: mockHubsites,
      });

      mockLegacyAdapter.getSharepointHostName.mockResolvedValue(
        "https://hub1.com"
      );

      render(<AppInitializer />);
      await screen.findByTestId("hubsites-data");
      await userEvent.click(screen.getByText("Select Hubsite"));

      await waitFor(() => {
        const hubsitesData = JSON.parse(
          screen.getByTestId("hubsites-data").textContent ?? "{}"
        );
        expect(hubsitesData.selectedHubsite).toEqual(mockHubsites[0]);
      });
    });
  });
});
