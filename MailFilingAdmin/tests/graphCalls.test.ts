// @ts-nocheck
import { describe, it, expect, vi } from "vitest";
import { GraphFI } from "@pnp/graph";
import { getSharepointHostNameSingleGeo } from "../src/services/graphCalls";

describe("graphCalls", () => {
  describe("getSharepointHostName", () => {
    it("should return the SharePoint host name", async () => {
      const mockGraph = {
        sites: {
          getById: vi
            .fn()
            .mockReturnValue(() =>
              Promise.resolve({ webUrl: "https://example.sharepoint.com" })
            ),
        },
      } as unknown as GraphFI;

      const result = await getSharepointHostNameSingleGeo(mockGraph);

      expect(mockGraph.sites.getById).toHaveBeenCalledWith("root");
      expect(result).toBe("https://example.sharepoint.com");
    });

    it("should handle errors gracefully", async () => {
      const mockGraph = {
        sites: {
          getById: vi
            .fn()
            .mockReturnValue(() =>
              Promise.reject(new Error("Failed to fetch site info"))
            ),
        },
      } as unknown as GraphFI;

      try {
        await getSharepointHostNameSingleGeo(mockGraph);
      } catch (error) {
        expect(mockGraph.sites.getById).toHaveBeenCalledWith("root");
        expect(error.message).toBe("Failed to fetch site info");
      }
    });
  });
});
