import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import HubSiteSelector from "../src/components/HubsiteSelector";
import { Hubsite } from "../src/types";

const mockHubsites: Hubsite[] = [
  {
    displayName: "Atvero Mail",
    name: "HubSite-1",
    url: "https://example.com/hub1",
  },
  {
    displayName: "Atvero Hub",
    name: "HubSite-2",
    url: "https://example.com/hub2",
  },
  {
    displayName: "Atvero Cloud",
    name: "HubSite-3",
    url: "https://example.com/hub3",
  },
];

describe("HubSiteSelector", () => {
  it("renders loading spinner when isLoading is true", () => {
    const { container } = render(
      <HubSiteSelector
        hubsites={mockHubsites}
        onHubsiteSelect={() => {}}
        isLoading={true}
      />
    );

    const spinnerDiv = container.firstChild as HTMLElement;
    expect(spinnerDiv).toHaveStyle({
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100px",
    });
    expect(screen.getByRole("progressbar")).toBeInTheDocument();
  });

  it("renders combobox with placeholder when not loading", () => {
    render(
      <HubSiteSelector hubsites={mockHubsites} onHubsiteSelect={() => {}} />
    );

    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Select a Hubsite")).toBeInTheDocument();
  });

  it("renders all hubsite options with Avatars", () => {
    render(
      <HubSiteSelector hubsites={mockHubsites} onHubsiteSelect={() => {}} />
    );

    // Open the combobox
    const combobox = screen.getByRole("combobox");
    fireEvent.click(combobox);

    // Check if all options are rendered with their names and avatars
    mockHubsites.forEach((hubsite) => {
      const option = screen.getByText(hubsite.displayName);
      expect(option).toBeInTheDocument();

      // Get the parent div with flex styling
      const avatarParent = option.closest("div");
      expect(avatarParent).toHaveStyle({
        display: "flex",
        alignItems: "center",
      });

      // Verify Avatar is present
      const avatar = avatarParent?.querySelector('[role="img"]');
      expect(avatar).toBeInTheDocument();
    });
  });

  it("calls onHubsiteSelect with correct hubsite when option is selected", () => {
    const handleSelect = vi.fn();

    render(
      <HubSiteSelector hubsites={mockHubsites} onHubsiteSelect={handleSelect} />
    );

    // Open the combobox
    const combobox = screen.getByRole("combobox");
    fireEvent.click(combobox);

    // Select the first option
    const option = screen.getByText("Atvero Mail");
    fireEvent.click(option);

    // Check if callback was called with correct hubsite
    expect(handleSelect).toHaveBeenCalledWith(mockHubsites[0]);
  });

  it("handles empty hubsites array", () => {
    render(<HubSiteSelector hubsites={[]} onHubsiteSelect={() => {}} />);

    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.queryByRole("option")).not.toBeInTheDocument();
  });

  it("handles undefined hubsites", () => {
    render(<HubSiteSelector hubsites={undefined} onHubsiteSelect={() => {}} />);

    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.queryByRole("option")).not.toBeInTheDocument();
  });

  it("maintains selected value after selection", () => {
    render(
      <HubSiteSelector hubsites={mockHubsites} onHubsiteSelect={() => {}} />
    );

    // Open and select an option
    const combobox = screen.getByRole("combobox");
    fireEvent.click(combobox);
    const option = screen.getByText("Atvero Mail");
    fireEvent.click(option);

    // Check if the selected value is maintained
    expect(screen.getByDisplayValue("Atvero Mail")).toBeInTheDocument();
  });

  it("renders option content with correct styling", () => {
    render(
      <HubSiteSelector hubsites={mockHubsites} onHubsiteSelect={() => {}} />
    );

    // Open the combobox
    const combobox = screen.getByRole("combobox");
    fireEvent.click(combobox);

    // Get the first option's content div
    const optionContent = screen.getByText("Atvero Mail").closest("div");
    expect(optionContent).toHaveStyle({
      display: "flex",
      alignItems: "center",
    });
  });

  it("handles keyboard events", () => {
    const handleSelect = vi.fn();

    render(
      <HubSiteSelector hubsites={mockHubsites} onHubsiteSelect={handleSelect} />
    );

    const combobox = screen.getByRole("combobox");
    fireEvent.keyDown(combobox, { key: "Escape" });

    // Verify the handler wasn't called
    expect(handleSelect).not.toHaveBeenCalled();
  });
});