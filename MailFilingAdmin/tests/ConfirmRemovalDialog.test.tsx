import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import '@testing-library/jest-dom';
import ConfirmRemovalDialog from '../src/components/ConfirmRemovalDialog';

describe('ConfirmRemovalDialog', () => {
  const mockOnConfirm = vi.fn();
  const mockOnCancel = vi.fn();
  const userToDelete = { name: '<PERSON>' };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly when open', () => {
    render(
      <ConfirmRemovalDialog
        isOpen={true}
        userToDelete={userToDelete}
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText('Confirm Removal')).toBeInTheDocument();
    expect(screen.getByText(`Are you sure you want to remove ${userToDelete.name} from this project?`)).toBeInTheDocument();
    expect(screen.getByText('Close')).toBeInTheDocument();
    expect(screen.getByText('Confirm')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(
      <ConfirmRemovalDialog
        isOpen={false}
        userToDelete={userToDelete}
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.queryByText('Confirm Removal')).not.toBeInTheDocument();
  });

  it('calls onConfirm when Confirm button is clicked', async () => {
    render(
      <ConfirmRemovalDialog
        isOpen={true}
        userToDelete={userToDelete}
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    fireEvent.click(screen.getByText('Confirm'));
    expect(mockOnConfirm).toHaveBeenCalledTimes(1);
  });

  it('calls onCancel when Close button is clicked', async () => {
    render(
      <ConfirmRemovalDialog
        isOpen={true}
        userToDelete={userToDelete}
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    fireEvent.click(screen.getByText('Close'));
    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it('handles null userToDelete gracefully', () => {
    render(
      <ConfirmRemovalDialog
        isOpen={true}
        userToDelete={null}
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText('Are you sure you want to remove from this project?')).toBeInTheDocument();
  });
});