import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import {
  CMapProjectStatus,
  Column,
  Project,
  SortDirection,
  Tenancy,
} from "../src/types";
import Projects from "../src/components/Projects";
import { SPFI } from "@pnp/sp";

interface InputProps {
  placeholder?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  value?: string;
  className?: string;
}

interface ButtonProps {
  children?: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

interface DialogProps {
  children?: React.ReactNode;
  open?: boolean;
  className?: string;
}

interface DialogChildProps {
  children?: React.ReactNode;
  className?: string;
}

interface ProgressBarProps {
  value?: number;
  className?: string;
}

interface EnableAtveroMailProps {
  selectedProjects: Project[];
  onEnableAtveroMail: (
    projects: Project[],
    onProgressUpdate: (progress: number, message: string) => void
  ) => Promise<{ success: boolean; message: string; details?: string }>;
}

// Mock Fluent UI Components
vi.mock("@fluentui/react-components", () => ({
  Input: ({
    placeholder,
    onChange,
    onKeyDown,
    value,
    className,
  }: InputProps) => (
    <input
      placeholder={placeholder}
      onChange={onChange}
      onKeyDown={onKeyDown}
      value={value}
      className={className}
    />
  ),
  makeStyles: () => () => ({
    wrapper: "wrapper-class",
    topContainer: "topContainer-class",
    searchInput: "searchInput-class",
    switchContainer: "switchContainer-class",
    switchWrapper: "switchWrapper-class",
    switchLabel: "switchLabel-class",
    visible: "visible-class",
    switchTooltipWrapper: "switchTooltipWrapper-class",
    messageBar: "messageBar-class",
    selectorSearchContainer: "selectorSearchContainer-class",
  }),
  mergeClasses: (...classes: string[]) => classes.filter(Boolean).join(" "),
  tokens: {
    colorPaletteRedForeground1: "#d13438",
    colorNeutralForeground2BrandSelected: "#test-color",
  },
  Button: ({ children, onClick, className }: ButtonProps) => (
    <button onClick={onClick} className={className}>
      {children}
    </button>
  ),
  Dialog: ({ children, open, className }: DialogProps) =>
    open ? <div className={className}>{children}</div> : null,
  DialogTrigger: ({ children, className }: DialogChildProps) => (
    <div className={className}>{children}</div>
  ),
  DialogSurface: ({ children, className }: DialogChildProps) => (
    <div className={className}>{children}</div>
  ),
  DialogBody: ({ children, className }: DialogChildProps) => (
    <div className={className}>{children}</div>
  ),
  DialogTitle: ({ children, className }: DialogChildProps) => (
    <div className={className}>{children}</div>
  ),
  DialogContent: ({ children, className }: DialogChildProps) => (
    <div className={className}>{children}</div>
  ),
  DialogActions: ({ children, className }: DialogChildProps) => (
    <div className={className}>{children}</div>
  ),
  List: ({ children, className }: DialogChildProps) => (
    <ul className={className}>{children}</ul>
  ),
  ListItem: ({ children, className }: DialogChildProps) => (
    <li className={className}>{children}</li>
  ),
  Text: ({ children, className }: DialogChildProps) => (
    <span className={className}>{children}</span>
  ),
  Spinner: () => <div>Loading...</div>,
  ProgressBar: ({ value, className }: ProgressBarProps) => {
    const currentValue = value || 0;
    return (
      <div
        role="progressbar"
        aria-valuenow={currentValue}
        aria-valuemin={0}
        aria-valuemax={100}
        aria-label="Progress"
        title="Progress indicator"
        className={className}
      />
    );
  },
  Switch: ({ checked, onChange, className, title }: any) => (
    <input
      title={title || "Test Input"}
      type="checkbox"
      checked={checked}
      onChange={(e) => onChange(e, { checked: e.target.checked })}
      data-testid="atvero-switch"
      className={className}
    />
  ),
  Tooltip: ({ content, children, className }: any) => (
    <div className={className}>
      <span data-testid="tooltip-content" hidden>
        {typeof content === "object" ? content.children : content}
      </span>
      {children}
    </div>
  ),
  useId: () => "test-id",
  MessageBar: ({ children, className }: DialogChildProps) => (
    <div data-testid="message-bar" className={className}>
      {children}
    </div>
  ),
  Label: ({ children, className }: DialogChildProps) => (
    <label className={className}>{children}</label>
  ),
}));

// Mock other components
vi.mock("@fluentui/react-icons", () => ({
  Info16Regular: () => <span data-testid="info-icon">Info</span>,
}));

vi.mock("../src/components/CreateProjectButton", () => ({
  default: ({ onProjectUrlChange, onOpenUsersModal }: any) => (
    <button
      data-testid="mock-create-project"
      onClick={() => {
        onProjectUrlChange("https://test-project.com");
        onOpenUsersModal();
      }}
    >
      Create Project
    </button>
  ),
}));

vi.mock("../src/components/UsersModal", () => ({
  default: ({ projectUrl, initialOpen, onClose }: any) => (
    <div data-testid="mock-users-modal">
      {initialOpen && (
        <div>
          <div>Modal Content for {projectUrl}</div>
          <button onClick={onClose}>Close Modal</button>
        </div>
      )}
    </div>
  ),
}));

vi.mock("../src/components/EnableAtveroMail", () => ({
  default: ({
    selectedProjects,
    onEnableAtveroMail,
  }: EnableAtveroMailProps) => (
    <button onClick={() => onEnableAtveroMail(selectedProjects, () => {})}>
      Enable Atvero Mail
    </button>
  ),
}));

vi.mock("../src/components/ProjectsTable", () => ({
  default: () => <div data-testid="projects-table-content">Projects Table</div>,
}));

vi.mock("../src/components/HubsiteSelector", () => ({
  default: ({ onHubsiteSelect }: any) => (
    <select
      title="test"
      data-testid="hubsite-selector"
      onChange={(e) =>
        onHubsiteSelect({ id: e.target.value, name: e.target.value })
      }
    >
      <option value="">Select Hubsite</option>
      <option value="hubsite1">Hubsite 1</option>
      <option value="hubsite2">Hubsite 2</option>
    </select>
  ),
}));

const hubsites = [
  {
    displayName: "Atvero Mail",
    name: "AtveroMail",
    url: "https://example.com",
  },
  { displayName: "Atvero Hub", name: "AtveroHub", url: "https://example.com2" },
];
const hubsite = {
  displayName: "Atvero Mail",
  name: "AtveroMail",
  url: "http://example.com",
};

describe("Projects", () => {
  const mockSharepoint = {
    web: vi.fn(),
    sites: vi.fn(),
    profiles: vi.fn(),
    search: vi.fn(),
  } as unknown as SPFI;

  const mockOnCreateProject = vi.fn();

  const mockProjects: Project[] = [
    {
      code: "P1",
      title: "Project 1",
      id: "1",
      favorite: false,
      cmapProject: CMapProjectStatus.NotInCmap,
      webUrl: "",
      cMapId: undefined,
    },
    {
      code: "P2",
      title: "Project 2",
      id: "2",
      favorite: true,
      cmapProject: CMapProjectStatus.NotInCmap,
      webUrl: "",
      cMapId: undefined,
    },
  ];

  const mockColumns: Column[] = [
    { columnKey: "code", label: "Code", sortable: true },
    { columnKey: "title", label: "Title", sortable: true },
  ];

  const defaultProps = {
    sharepoint: mockSharepoint,
    onHubsiteSelect: vi.fn(),
    clearProjects: vi.fn(),
    hubsites: hubsites,
    hubsite: hubsite,
    allProjects: mockProjects,
    projects: mockProjects,
    loading: false,
    hasNextPage: false,
    loadNextPage: vi.fn(),
    isNextPageLoading: false,
    onSortChange: vi.fn(),
    currentSort: { columnKey: "code", direction: SortDirection.Ascending },
    columns: mockColumns,
    onSearch: vi.fn(),
    searchString: "",
    onCreateProject: vi.fn(),
    onFetchUsers: vi.fn(),
    onRepairProject: vi.fn(),
    hasCMapProjects: false,
    selectedProjects: [],
    onProjectSelect: vi.fn(),
    onEnableAtveroMail: vi
      .fn()
      .mockResolvedValue({ success: true, message: "Success" }),
    onToggleAtveroProjects: vi.fn(),
    showAtveroProjects: false,
    isAdmin: true,
    tenancy: Tenancy.CMap,
    useTagOnSend: false,
  };

  beforeEach(() => {
    mockOnCreateProject.mockClear();
    vi.clearAllMocks();
  });

  describe("Basic Rendering and Functionality", () => {
    it("renders correctly", () => {
      render(<Projects {...defaultProps} />);

      expect(
        screen.getByPlaceholderText("Search projects...")
      ).toBeInTheDocument();
      expect(screen.getByTestId("projects-table-content")).toBeInTheDocument();
    });

    it("handles search input change", () => {
      render(<Projects {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText("Search projects...");
      fireEvent.change(searchInput, { target: { value: "test search" } });
      expect(defaultProps.onSearch).toHaveBeenCalledWith("test search");
    });
  });

  describe("Project Management UI", () => {
    it("shows CreateProjectButton when hasCMapProjects is false", () => {
      render(<Projects {...defaultProps} hasCMapProjects={false} />);

      expect(screen.getByText("Create Project")).toBeInTheDocument();
      expect(screen.queryByText("Enable Atvero Mail")).not.toBeInTheDocument();
      expect(screen.queryByTestId("atvero-switch")).not.toBeInTheDocument();
    });

    it("shows EnableAtveroMail button and switch when hasCMapProjects is true", () => {
      render(<Projects {...defaultProps} hasCMapProjects={true} />);

      expect(screen.queryByText("Create Project")).not.toBeInTheDocument();
      expect(screen.getByText("Enable Atvero Mail")).toBeInTheDocument();
      expect(screen.getByTestId("atvero-switch")).toBeInTheDocument();
    });

    it("handles project selection", () => {
      render(<Projects {...defaultProps} />);

      const project = mockProjects[0];
      defaultProps.onProjectSelect(project);
      expect(defaultProps.onProjectSelect).toHaveBeenCalledWith(project);
    });
  });

  describe("Atvero Mail Integration", () => {
    it("enables Atvero Mail for selected projects", async () => {
      const selectedProjects = [mockProjects[0]];
      const onEnableAtveroMail = vi
        .fn()
        .mockResolvedValue({ success: true, message: "Success" });

      render(
        <Projects
          {...defaultProps}
          hasCMapProjects={true}
          selectedProjects={selectedProjects}
          onEnableAtveroMail={onEnableAtveroMail}
        />
      );

      const enableButton = screen.getByText("Enable Atvero Mail");
      await fireEvent.click(enableButton);

      expect(onEnableAtveroMail).toHaveBeenCalledWith(
        selectedProjects,
        expect.any(Function)
      );
    });
  });

  describe("Users Modal Integration", () => {
    it("shows UsersModal when project URL is set", () => {
      render(
        <Projects
          {...defaultProps}
          onCreateProject={mockOnCreateProject}
          hubsite={hubsite}
          sharepoint={mockSharepoint}
        />
      );

      const createButton = screen.getByTestId("mock-create-project");
      fireEvent.click(createButton);

      const usersModal = screen.getByTestId("mock-users-modal");
      expect(usersModal).toBeInTheDocument();
      expect(
        screen.getByText("Modal Content for https://test-project.com")
      ).toBeInTheDocument();
    });

    it("opens and closes UsersModal correctly", async () => {
      render(
        <Projects
          {...defaultProps}
          onCreateProject={mockOnCreateProject}
          hubsite={hubsite}
          sharepoint={mockSharepoint}
        />
      );

      expect(screen.queryByText(/Modal Content for/)).toBeNull();

      const createButton = screen.getByTestId("mock-create-project");
      fireEvent.click(createButton);

      expect(
        screen.getByText("Modal Content for https://test-project.com")
      ).toBeInTheDocument();

      const closeButton = screen.getByText("Close Modal");
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByText(/Modal Content for/)).toBeNull();
      });
    });
  });

  describe("Switch and Tooltip functionality", () => {
    it("shows switch and tooltip when hasCMapProjects is true", () => {
      render(<Projects {...defaultProps} hasCMapProjects={true} />);

      expect(screen.getByTestId("atvero-switch")).toBeInTheDocument();
      expect(screen.getByText("Not CMap Mail Enabled")).toBeInTheDocument();
      expect(screen.getByTestId("info-icon")).toBeInTheDocument();
      expect(screen.getByTestId("tooltip-content")).toHaveTextContent(
        "Only show CMap projects that don't have a Atvero project."
      );
    });

    it("handles switch state changes correctly", () => {
      const onToggleAtveroProjects = vi.fn();
      render(
        <Projects
          {...defaultProps}
          hasCMapProjects={true}
          onToggleAtveroProjects={onToggleAtveroProjects}
          showAtveroProjects={false}
        />
      );

      const switchElement = screen.getByTestId("atvero-switch");
      fireEvent.click(switchElement);
      expect(onToggleAtveroProjects).toHaveBeenCalledWith(true);
    });

    it("reflects current state of showAtveroProjects", () => {
      render(
        <Projects
          {...defaultProps}
          hasCMapProjects={true}
          showAtveroProjects={true}
        />
      );

      const switchElement = screen.getByTestId(
        "atvero-switch"
      ) as HTMLInputElement;
      expect(switchElement.checked).toBe(true);
    });

    it("displays correct tooltip content on hover", async () => {
      render(<Projects {...defaultProps} hasCMapProjects={true} />);

      const infoIcon = screen.getByTestId("info-icon");
      fireEvent.mouseEnter(infoIcon);

      expect(screen.getByTestId("tooltip-content")).toHaveTextContent(
        "Only show CMap projects that don't have a Atvero project."
      );
    });
  });

  describe("HubSite Selection and MessageBar", () => {
    it("shows MessageBar with correct class when no hubsite is selected", () => {
      render(<Projects {...defaultProps} hubsite={undefined} />);
      const messageBar = screen.getByTestId("message-bar");
      expect(messageBar).toHaveClass("messageBar-class");
      expect(messageBar).toHaveTextContent(
        "Please select a hubsite to view your projects."
      );
    });

    it("shows HubsiteSelector when multiple hubsites are available", () => {
      render(
        <Projects
          {...defaultProps}
          hubsites={[
            { displayName: "Hub 1", name: "Hub 1", url: "url1" },
            { displayName: "Hub 1", name: "Hub 2", url: "url2" },
          ]}
        />
      );

      expect(screen.getByTestId("hubsite-selector")).toBeInTheDocument();
    });

    it("calls onHubsiteSelect and clearProjects when hubsite is selected", () => {
      const onHubsiteSelect = vi.fn();
      const clearProjects = vi.fn();

      render(
        <Projects
          {...defaultProps}
          hubsites={[
            { displayName: "Hub 1", name: "Hub 1", url: "url1" },
            { displayName: "Hub 1", name: "Hub 2", url: "url2" },
          ]}
          onHubsiteSelect={onHubsiteSelect}
          clearProjects={clearProjects}
        />
      );

      const selector = screen.getByTestId("hubsite-selector");
      fireEvent.change(selector, { target: { value: "hubsite1" } });

      expect(onHubsiteSelect).toHaveBeenCalledWith({
        id: "hubsite1",
        name: "hubsite1",
      });
      expect(clearProjects).toHaveBeenCalled();
    });
  });
});
