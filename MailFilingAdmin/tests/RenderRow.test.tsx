import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { Project } from "../src/types";
import RenderRow, {
  generateShimmerRows,
  ShimmerRow,
} from "../src/components/RenderRow/RenderRow";
import { TableCell } from "@fluentui/react-components";
import React from "react";

vi.mock("@fluentui/react-components", () => ({
  TableRow: ({
    children,
    ...props
  }: {
    children: React.ReactNode;
  } & React.HTMLAttributes<HTMLTableRowElement>) => (
    <tr {...props}>{children}</tr>
  ),
  TableCell: ({
    children,
    ...props
  }: {
    children?: React.ReactNode;
  } & React.TdHTMLAttributes<HTMLTableCellElement>) => (
    <td {...props}>{children}</td>
  ),
  TableCellLayout: ({
    children,
    ...props
  }: {
    children?: React.ReactNode;
  } & React.TdHTMLAttributes<HTMLTableCellElement>) => (
    <td {...props}>{children}</td>
  ),
  Input: ({
    type,
    checked,
    onChange,
    ...props
  }: React.InputHTMLAttributes<HTMLInputElement>) => (
    <input
      data-testid="fluent-input"
      type={type}
      checked={checked}
      onChange={onChange}
      {...props}
    />
  ),
  TableSelectionCell: ({
    children,
    checked,
    checkboxIndicator,
    onClick,
    ...props
  }: {
    children?: React.ReactNode;
    checked?: boolean;
    checkboxIndicator?: { "aria-label"?: string };
    onClick?: () => void;
  } & React.TdHTMLAttributes<HTMLTableCellElement>) => (
    <TableCell
      {...props}
      onClick={onClick}
      role="cell"
      aria-label={checkboxIndicator?.["aria-label"]}
    >
      <input
        title="Test Input"
        type="checkbox"
        checked={checked}
        onChange={() => {}}
      />
      {children}
    </TableCell>
  ),
  makeStyles: () => () => ({
    tableRow: "mocked-table-row-class",
    centeredMessage: "mocked-centered-message-class",
    avatar: "mocked-avatar-class",
    projectCode: "mocked-project-code-class",
  }),
  SkeletonItem: ({ shape, size }: { shape: string; size: number }) => (
    <div data-testid="skeleton-item-row" data-shape={shape} data-size={size} />
  ),
  Avatar: ({
    name,
    ...props
  }: { name: string } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="avatar" {...props}>
      {name}
    </div>
  ),
  tokens: {
    colorPaletteRedForeground1: "mocked-red-color",
    colorPaletteGreenForeground1: "mocked-green-color",
    colorNeutralForeground3: "mocked-neutral-color",
  },
  Dialog: ({
    children,
    ...props
  }: { children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="dialog" {...props}>
      {children}
    </div>
  ),
  DialogTrigger: ({
    children,
    ...props
  }: { children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="dialog-trigger" {...props}>
      {children}
    </div>
  ),
  DialogSurface: ({
    children,
    ...props
  }: { children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="dialog-surface" {...props}>
      {children}
    </div>
  ),
  DialogBody: ({
    children,
    ...props
  }: { children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="dialog-body" {...props}>
      {children}
    </div>
  ),
  DialogTitle: ({
    children,
    ...props
  }: { children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="dialog-title" {...props}>
      {children}
    </div>
  ),
  DialogContent: ({
    children,
    ...props
  }: { children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="dialog-content" {...props}>
      {children}
    </div>
  ),
  DialogActions: ({
    children,
    ...props
  }: { children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="dialog-actions" {...props}>
      {children}
    </div>
  ),
  Button: ({
    children,
    ...props
  }: {
    children: React.ReactNode;
  } & React.ButtonHTMLAttributes<HTMLButtonElement>) => (
    <button data-testid="button" {...props}>
      {children}
    </button>
  ),
  Text: ({
    children,
    ...props
  }: { children: React.ReactNode } & React.HTMLAttributes<HTMLSpanElement>) => (
    <span data-testid="text" {...props}>
      {children}
    </span>
  ),
  Spinner: ({
    label,
    ...props
  }: { label?: string } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="spinner" {...props}>
      {label}
    </div>
  ),
  Combobox: ({
    children,
    ...props
  }: { children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) => (
    <div data-testid="combobox" {...props}>
      {children}
    </div>
  ),
}));

vi.mock("../src/components/UsersModal", () => ({
  default: ({ projectUrl }: { projectUrl: string }) => (
    <div data-testid="users-modal">{projectUrl}</div>
  ),
}));

vi.mock("../src/components/ProjectSettings", () => ({
  default: ({ project }: { project: Project }) => (
    <div data-testid="project-settings">{project.title}</div>
  ),
}));

vi.mock("./RenderRow/EmailBadge", () => ({
  EmailBadge: ({ project }: { project: Project }) => (
    <div data-testid="email-badge">{project.code}</div>
  ),
}));

vi.spyOn(navigator, "languages", "get").mockReturnValue([]);
vi.spyOn(navigator, "language", "get").mockImplementation(() => "en-GB");

describe("RenderRow", () => {
  const mockProject: Project = {
    id: "1",
    code: "P1",
    title: "Project 1",
    favorite: false,
    webUrl: "https://example.com",
    cmapProject: 0,
    cMapId: undefined,
  };

  const createProps = (overrides = {}) => ({
    index: 0,
    style: {},
    projects: [mockProject],
    selectedProjects: [],
    isItemLoaded: () => true,
    onFetchUsers: vi.fn(),
    onRepairProject: vi.fn(),
    onCreateProject: vi.fn(),
    onClick: vi.fn(),
    hasCMapProjects: false,
    ...overrides,
  });

  it("renders a ShimmerRow when the item is not loaded", () => {
    render(<RenderRow {...createProps({ isItemLoaded: () => false })} />);
    expect(screen.getByTestId("loading-item")).toBeInTheDocument();
  });

  it("renders the row with project data", () => {
    render(<RenderRow {...createProps()} />);
    expect(screen.getByText("P1")).toBeInTheDocument();
    const cells = screen.getAllByRole("cell");
    expect(cells[2]).toHaveTextContent("Project 1");
    expect(screen.getByTestId("avatar")).toHaveTextContent("Project 1");
  });

  it("does not throw error when optional fields are missing", () => {
    const incompleteProject: Partial<Project> = {
      id: "2",
      code: "P2",
      favorite: false,
      webUrl: "https://example.com",
      cmapProject: 0,
      cMapId: undefined,
    };

    const props = createProps({
      projects: [incompleteProject as Project],
    });

    expect(() => render(<RenderRow {...props} />)).not.toThrow();
  });

  it("renders loading message for the first item when not loaded", () => {
    const props = createProps({ isItemLoaded: () => false });
    render(<RenderRow {...props} />);
    expect(screen.getByText("Loading your projects.")).toBeInTheDocument();
  });

  it("renders the avatar with margin right", () => {
    render(<RenderRow {...createProps()} />);
    const avatar = screen.getByTestId("avatar");
    expect(avatar).toHaveClass("mocked-avatar-class");
  });

  it("handles project selection", () => {
    const onClick = vi.fn();
    const props = createProps({ onClick });
    render(<RenderRow {...props} />);

    const selectionCell = screen.getByRole("cell", { name: /select row/i });
    selectionCell.click();

    expect(onClick).toHaveBeenCalledWith(mockProject);
  });

  it("handles project selection with matching cMapId", () => {
    const projectWithCMapId = {
      ...mockProject,
      cMapId: 123,
    };
    const selectedProject = {
      ...mockProject,
      cMapId: 123,
    };

    render(
      <RenderRow
        {...createProps({
          projects: [projectWithCMapId],
          selectedProjects: [selectedProject],
        })}
      />
    );

    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).toBeChecked();
  });

  it("renders UsersModal and ProjectSettings", () => {
    render(<RenderRow {...createProps()} />);
    expect(screen.getByTestId("users-modal")).toBeInTheDocument();
    expect(screen.getByTestId("project-settings")).toBeInTheDocument();
  });
});

describe("ShimmerRow", () => {
  it("renders shimmer effect placeholders", () => {
    render(<ShimmerRow style={{}} />);
    const skeletonItems = screen.getAllByTestId("skeleton-item-row");
    expect(skeletonItems).toHaveLength(2);
  });

  it("applies the provided style", () => {
    const customStyle = { marginTop: "10px" };
    render(<ShimmerRow style={customStyle} />);
    const row = screen.getByRole("row");
    expect(row).toHaveStyle(customStyle);
  });
});

describe("generateShimmerRows", () => {
  it("generates the correct number of ShimmerRow components", () => {
    const count = 5;
    const { container } = render(<table>{generateShimmerRows(count)}</table>);
    const rows = container.querySelectorAll("tr");
    expect(rows).toHaveLength(count);
  });

  it("generates ShimmerRow components with unique keys", () => {
    const count = 3;
    const rows = generateShimmerRows(count);
    const keys = rows.map((row) => row.key);
    const uniqueKeys = new Set(keys);
    expect(uniqueKeys.size).toBe(count);
  });
});
