import { describe, it, expect, vi, beforeEach } from "vitest";
import { enableAtveroMail } from "../src/services/enableAtveroService";
import { getProjectDesign, createProject } from "../src/services/createProject";
import { CMapProjectStatus, Hubsite, Project, User } from "../src/types";
import { GraphFI } from "@pnp/graph";
import { SPFI } from "@pnp/sp";
import { Adapters } from "../src/adapters/AdapterFactory";
import { ISiteDesignInfo } from "@pnp/sp/site-designs";

const createMockSiteDesign = (title: string): ISiteDesignInfo => ({
  Id: "design-1",
  Title: title,
  WebTemplate: "TeamSite",
  SiteScriptIds: ["script-id"],
  Description: "Test design",
  PreviewImageUrl: "",
  PreviewImageAltText: "",
  IsDefault: false,
  Version: "1",
});

vi.mock("../src/services/createProject", () => ({
  getProjectDesign: vi.fn(),
  createProject: vi.fn(),
}));

describe("enableAtveroMail", () => {
  const mockProjects: Project[] = [
    {
      code: "P1",
      title: "Project 1",
      id: "1",
      cMapId: 1,
      favorite: false,
      cmapProject: CMapProjectStatus.NotInCmap,
      webUrl: "https://test.sharepoint.com/sites/p1",
    },
    {
      code: "P2",
      title: "Project 2",
      id: "2",
      cMapId: 2,
      favorite: false,
      cmapProject: CMapProjectStatus.NotInCmap,
      webUrl: "https://test.sharepoint.com/sites/p2",
    },
  ];

  const mockDependencies = {
    adapter: {
      legacyAdapter: {
        getGraph: () => ({} as GraphFI),
        getSharepoint: () => ({} as SPFI),
        getSharepointHostName: () => "test.sharepoint.com",
        setSharepointHostName: vi.fn(),
      },
      backendAdapter: {
        getHubsites: vi.fn(),
        createSite: vi.fn(),
        getSetting: vi.fn(),
      },
      sharepointAdapter: {
        getMemberUsers: vi.fn(),
        getConfidentialUsers: vi.fn(),
      },
    } as Adapters,
    selectedHubsite: {} as Hubsite,
    projects: mockProjects,
    onProgressUpdate: vi.fn(),
    tenancyName: "CMap",
    siteDesignName: "CMap Mail - Project",
    fallbackSiteDesignName: "AtveroMail - Project",
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Initialize mocks with proper values
    vi.mocked(getProjectDesign).mockResolvedValue(
      createMockSiteDesign("AtveroMail - Project")
    );
    vi.mocked(createProject).mockResolvedValue(
      "https://test.sharepoint.com/sites/project"
    );
    mockDependencies.onProgressUpdate.mockClear();
  });

  it("should handle missing dependencies", async () => {
    const result = await enableAtveroMail({
      ...mockDependencies,
      adapter: {
        legacyAdapter: {
          getGraph: () => undefined as unknown as GraphFI,
          getSharepoint: () => undefined as unknown as SPFI,
          getSharepointHostName: () => undefined as unknown as string,
          setSharepointHostName: () => undefined as unknown as void,
        },
        backendAdapter: mockDependencies.adapter.backendAdapter,
      } as unknown as Adapters,
    });

    expect(result.success).toBe(false);
    expect(result.message).toBe(
      "Unable to enable CMap Mail due to missing dependencies"
    );
    expect(result.details).toBe(
      "It looks like we couldn't find some of your details. Please refresh your browser."
    );
  });

  it("should handle missing site design", async () => {
    //@ts-ignore
    vi.mocked(getProjectDesign).mockResolvedValue(undefined);

    const result = await enableAtveroMail(mockDependencies);

    expect(result.success).toBe(false);
    expect(result.details).toContain(
      "Site design 'CMap Mail - Project' not found"
    );
  });

  it("should handle successful creation of all projects", async () => {
    const result = await enableAtveroMail(mockDependencies);

    expect(result.success).toBe(true);
    expect(result.message).toBe(
      "Successfully enabled CMap Mail for all 2 projects"
    );
    expect(createProject).toHaveBeenCalledTimes(2);
  });

  it("should handle projects that already exist", async () => {
    vi.mocked(createProject).mockRejectedValue(
      new Error("Site already exists")
    );

    const result = await enableAtveroMail(mockDependencies);

    expect(result.success).toBe(false);
    expect(result.message).toBe(
      "Completed with 0 successful, 2 already exists, and 0 failed"
    );
    expect(result.details).toContain("Projects already enabled (2)");
  });

  it("should handle mixed results with successes, existing and failed projects", async () => {
    vi.mocked(createProject)
      .mockResolvedValueOnce("https://test.sharepoint.com/sites/project1")
      .mockRejectedValueOnce(new Error("already exists"))
      .mockRejectedValueOnce(new Error("some error"));

    const result = await enableAtveroMail({
      ...mockDependencies,
      projects: [...mockProjects, { ...mockProjects[0], code: "P3" }],
    });

    expect(result.success).toBe(false);
    expect(result.message).toBe(
      "Completed with 1 successful, 1 already exists, and 1 failed"
    );
    expect(result.details).toContain("Projects already enabled (1)");
    expect(result.details).toContain("Failed projects (1)");
  });

  it("should update progress during project creation", async () => {
    await enableAtveroMail(mockDependencies);

    expect(mockDependencies.onProgressUpdate).toHaveBeenNthCalledWith(
      1,
      0,
      'Creating CMap site for "Project 1" (1/2)'
    );
    expect(mockDependencies.onProgressUpdate).toHaveBeenNthCalledWith(
      2,
      0.5,
      'Creating CMap site for "Project 2" (2/2)'
    );
    expect(mockDependencies.onProgressUpdate).toHaveBeenNthCalledWith(
      3,
      1,
      "Completed creating CMap sites"
    );
  });

  it("should handle unexpected errors during execution", async () => {
    vi.mocked(getProjectDesign).mockRejectedValue(
      new Error("Unexpected error")
    );

    const result = await enableAtveroMail(mockDependencies);

    expect(result.success).toBe(false);
    expect(result.message).toBe(
      "An error occurred while enabling CMap for the projects"
    );
    expect(result.details).toBe("Unexpected error");
  });

  it("should handle all projects failing", async () => {
    vi.mocked(createProject).mockRejectedValue(new Error("Critical error"));

    const result = await enableAtveroMail(mockDependencies);

    expect(result.success).toBe(false);
    expect(result.message).toBe("Failed to enable CMap Mail for any projects");
    expect(result.details).toContain("Critical error");
  });
});
