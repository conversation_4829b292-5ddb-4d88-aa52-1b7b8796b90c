import { describe, it, expect, vi, beforeEach } from "vitest";
import { toggleUserToConfidentialGroup } from "../src/services/toggleUserToConfidentialGroup";
import { SPFI } from "@pnp/sp";
import { User, CanAccessConfidentialEmails } from "../src/types";
import { Web } from "@pnp/sp/webs";

vi.mock("@pnp/sp/webs", () => ({
  Web: vi.fn(),
}));

vi.mock("@pnp/sp", () => ({
  SPFI: vi.fn(),
}));

describe("toggleUserToConfidentialGroup", () => {
  let mockSharepoint: SPFI;
  let mockWeb: any;
  const mockProjectUrl = "https://example.com/project";

  // Create a valid user object
  const mockUser: User = {
    id: "1",
    loginName: "i:0#.f|membership|<EMAIL>",
    name: "Test User",
    email: "<EMAIL>",
    canAccessConfidentialEmails: CanAccessConfidentialEmails.DontKnow,
  };

  // Create a test array of site group keys
  const mockGroupKeys = [
    "c:0t.c|tenant|<EMAIL>",
    "c:0t.c|tenant|<EMAIL>",
  ];

  beforeEach(() => {
    vi.resetAllMocks();

    mockSharepoint = {
      web: {},
    } as unknown as SPFI;

    // Setup mock confidential group
    const mockConfidentialGroup = {
      Id: "conf-group-id",
    };

    // Setup mock for group users operations
    const mockGroupUsers = {
      add: vi.fn().mockResolvedValue(undefined),
      removeByLoginName: vi.fn().mockResolvedValue(undefined),
    };

    // Setup mock for group by ID
    const mockGroupById = {
      users: mockGroupUsers,
    };

    mockWeb = {
      siteGroups: {
        getByName: vi
          .fn()
          .mockReturnValue(() => Promise.resolve(mockConfidentialGroup)),
        getById: vi.fn().mockReturnValue(mockGroupById),
      },
    };

    vi.mocked(Web).mockReturnValue(mockWeb);
  });

  it("should add a user to the Confidential Users group", async () => {
    await toggleUserToConfidentialGroup({
      sharepoint: mockSharepoint,
      projectUrl: mockProjectUrl,
      user: mockUser,
      canAccessConfidentialEmails: true,
    });

    expect(Web).toHaveBeenCalledWith([mockSharepoint.web, mockProjectUrl]);

    expect(mockWeb.siteGroups.getByName).toHaveBeenCalledWith(
      "Confidential Users"
    );

    expect(mockWeb.siteGroups.getById).toHaveBeenCalledWith("conf-group-id");
    expect(mockWeb.siteGroups.getById().users.add).toHaveBeenCalledWith(
      mockUser.loginName
    );
  });

  it("should remove a user from the Confidential Users group", async () => {
    await toggleUserToConfidentialGroup({
      sharepoint: mockSharepoint,
      projectUrl: mockProjectUrl,
      user: mockUser,
      canAccessConfidentialEmails: false,
    });

    expect(Web).toHaveBeenCalledWith([mockSharepoint.web, mockProjectUrl]);

    expect(mockWeb.siteGroups.getByName).toHaveBeenCalledWith(
      "Confidential Users"
    );

    expect(mockWeb.siteGroups.getById).toHaveBeenCalledWith("conf-group-id");
    expect(
      mockWeb.siteGroups.getById().users.removeByLoginName
    ).toHaveBeenCalledWith(mockUser.loginName);
  });

  it("should add site group keys to the Confidential Users group", async () => {
    await toggleUserToConfidentialGroup({
      sharepoint: mockSharepoint,
      projectUrl: mockProjectUrl,
      groupKeys: mockGroupKeys,
      canAccessConfidentialEmails: true,
    });

    expect(Web).toHaveBeenCalledWith([mockSharepoint.web, mockProjectUrl]);

    expect(mockWeb.siteGroups.getByName).toHaveBeenCalledWith(
      "Confidential Users"
    );

    expect(mockWeb.siteGroups.getById).toHaveBeenCalledWith("conf-group-id");
    expect(mockWeb.siteGroups.getById().users.add).toHaveBeenCalledTimes(2);
    expect(mockWeb.siteGroups.getById().users.add).toHaveBeenCalledWith(
      mockGroupKeys[0]
    );
    expect(mockWeb.siteGroups.getById().users.add).toHaveBeenCalledWith(
      mockGroupKeys[1]
    );
  });

  it("should remove site group keys from the Confidential Users group", async () => {
    await toggleUserToConfidentialGroup({
      sharepoint: mockSharepoint,
      projectUrl: mockProjectUrl,
      groupKeys: mockGroupKeys,
      canAccessConfidentialEmails: false,
    });

    expect(Web).toHaveBeenCalledWith([mockSharepoint.web, mockProjectUrl]);

    expect(mockWeb.siteGroups.getByName).toHaveBeenCalledWith(
      "Confidential Users"
    );

    expect(mockWeb.siteGroups.getById).toHaveBeenCalledWith("conf-group-id");
    expect(
      mockWeb.siteGroups.getById().users.removeByLoginName
    ).toHaveBeenCalledTimes(2);
    expect(
      mockWeb.siteGroups.getById().users.removeByLoginName
    ).toHaveBeenCalledWith(mockGroupKeys[0]);
    expect(
      mockWeb.siteGroups.getById().users.removeByLoginName
    ).toHaveBeenCalledWith(mockGroupKeys[1]);
  });

  it("should handle errors when the Confidential Users group cannot be found", async () => {
    mockWeb.siteGroups.getByName = vi
      .fn()
      .mockReturnValue(() =>
        Promise.reject(new Error("Group cannot be found"))
      );

    const consoleErrorSpy = vi
      .spyOn(console, "error")
      .mockImplementation(() => {});

    await expect(
      toggleUserToConfidentialGroup({
        sharepoint: mockSharepoint,
        projectUrl: mockProjectUrl,
        user: mockUser,
        canAccessConfidentialEmails: true,
      })
    ).rejects.toThrow("Group cannot be found");

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      "Confidential Users group does not exist in this site"
    );

    consoleErrorSpy.mockRestore();
  });

  it("should handle other errors that might occur", async () => {
    mockWeb.siteGroups.getByName = vi
      .fn()
      .mockReturnValue(() => Promise.reject(new Error("Some other error")));

    const consoleErrorSpy = vi
      .spyOn(console, "error")
      .mockImplementation(() => {});

    await expect(
      toggleUserToConfidentialGroup({
        sharepoint: mockSharepoint,
        projectUrl: mockProjectUrl,
        user: mockUser,
        canAccessConfidentialEmails: true,
      })
    ).rejects.toThrow("Some other error");

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      "Error toggling user in Confidential Users group:",
      "Some other error"
    );

    consoleErrorSpy.mockRestore();
  });
});
