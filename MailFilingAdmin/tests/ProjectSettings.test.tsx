import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi } from "vitest";
import RepairProjectsButton from "../src/components/ProjectSettings";
import {
  CMapProjectStatus,
  CreateProjectFunction,
  Environment,
  Project,
} from "../src/types";
import { ReactNode } from "react";
import { CurrentEnvironment } from "../src/utils/Config";

vi.mock("../src/utils/Config", () => ({
  Environment: {
    Development: "development",
    Production: "production",
  },
  CurrentEnvironment: vi.fn(() => Environment.Production),
}));

const mockedCurrentEnvironment = vi.mocked(CurrentEnvironment);

vi.mock("@fluentui/react-components", async () => {
  const actual = await vi.importActual("@fluentui/react-components");
  return {
    ...actual,
    Dialog: ({ children }: { children: ReactNode }) => (
      <div data-testid="mock-dialog">{children}</div>
    ),
    DialogTrigger: ({ children }: { children: ReactNode }) => (
      <div data-testid="mock-dialog-trigger">{children}</div>
    ),
    DialogSurface: ({ children }: { children: ReactNode }) => (
      <div data-testid="mock-dialog-surface">{children}</div>
    ),
    DialogBody: ({ children }: { children: ReactNode }) => (
      <div data-testid="mock-dialog-body">{children}</div>
    ),
    DialogTitle: ({ children }: { children: ReactNode }) => (
      <div data-testid="mock-dialog-title">{children}</div>
    ),
    DialogContent: ({ children }: { children: ReactNode }) => (
      <div data-testid="mock-dialog-content">{children}</div>
    ),
    DialogActions: ({ children }: { children: ReactNode }) => (
      <div data-testid="mock-dialog-actions">{children}</div>
    ),
    ProgressBar: () => <div data-testid="mock-progress-bar" />,
    Spinner: () => <div data-testid="mock-spinner" />,
  };
});

vi.mock("../src/components/StatusMessage", () => ({
  default: ({
    message,
    type,
    details,
  }: {
    message: string;
    type: string;
    details?: string;
  }) => (
    <div data-testid="mock-error-message">
      <span>Message: {message}</span>
      <span>Type: {type}</span>
      {details && <span>Details: {details}</span>}
    </div>
  ),
}));

vi.mock("@fluentui/react-icons", () => ({
  Info16Regular: () => <span data-testid="info-icon">Info</span>,
  ArrowSync16Filled: () => <span data-testid="sync-icon">Sync</span>,
  Wrench20Regular: () => <span data-testid="wrench-icon">Wrench</span>,
}));

beforeEach(() => {
  vi.mock("import.meta", () => ({
    env: {
      MODE: "production",
    },
  }));
});

afterEach(() => {
  vi.clearAllMocks();
});

describe("RepairProjectsButton", () => {
  const mockProject = {
    code: "TEST-001",
    title: "Test Project",
    cmapProject: CMapProjectStatus.CMapNotCreated,
    cMapId: undefined,
  } as Project;

  const mockOnRepairProject = vi.fn(
    (
      _project: Project,
      onProgressUpdate: (progress: number, message: string) => void
    ) => {
      return new Promise<{
        success: boolean;
        message: string;
        details?: string;
      }>((resolve) => {
        onProgressUpdate(25, "Starting repair...");
        setTimeout(() => {
          onProgressUpdate(50, "Halfway there...");
        }, 100);
        setTimeout(() => {
          onProgressUpdate(100, "Completed!");
          resolve({ success: true, message: "Project repaired successfully" });
        }, 200);
      });
    }
  );

  const mockOnCreateProject: CreateProjectFunction = vi.fn(
    (
      _projectTitle: string,
      _projectCode: string,
      _cmapId: number | undefined,
      onProgressUpdate: (progress: number, message: string) => void,
      _selectedProject: Project | undefined
    ) => {
      return new Promise<{
        success: boolean;
        message: string;
        details?: string;
      }>((resolve) => {
        onProgressUpdate(25, "Starting repair...");
        setTimeout(() => {
          onProgressUpdate(50, "Halfway there...");
        }, 100);
        setTimeout(() => {
          onProgressUpdate(100, "Completed!");
          resolve({ success: true, message: "Project repaired successfully" });
        }, 200);
      });
    }
  );

  const defaultProps = {
    project: mockProject,
    onRepairProject: mockOnRepairProject,
    onCreateProject: mockOnCreateProject,
    hasCMapProjects: false,
    showSync: true,
  };

  it("shows dialogTrigger button when showSync is true", () => {
    render(<RepairProjectsButton {...defaultProps} showSync={true} />);
    const dialogTrigger = screen.getByTestId("mock-dialog-trigger");
    expect(dialogTrigger).toBeInTheDocument();
    const triggerButton = dialogTrigger.querySelector("button");
    expect(triggerButton).toBeInTheDocument();
  });

  it("shows no button in dialogTrigger when showSync is false in production", () => {
    mockedCurrentEnvironment.mockReturnValue(Environment.Production);

    render(<RepairProjectsButton {...defaultProps} showSync={false} />);
    const dialogTrigger = screen.getByTestId("mock-dialog-trigger");
    expect(dialogTrigger).toBeInTheDocument();
    expect(dialogTrigger.children).toHaveLength(0);
  });
  it("shows dialogTrigger button when in development mode regardless of showSync", () => {
    mockedCurrentEnvironment.mockReturnValue(Environment.Development);

    render(<RepairProjectsButton {...defaultProps} showSync={false} />);
    const dialogTrigger = screen.getByTestId("mock-dialog-trigger");
    expect(dialogTrigger).toBeInTheDocument();
    const triggerButton = dialogTrigger.querySelector("button");
    expect(triggerButton).toBeInTheDocument();
  });
  it("renders the repair button with wrench icon when hasCMapProjects is false", () => {
    render(<RepairProjectsButton {...defaultProps} />);
    expect(screen.getByTestId("mock-dialog-trigger")).toBeInTheDocument();
    expect(screen.getByTestId("wrench-icon")).toBeInTheDocument();
  });

  it("renders the atvero filing button when its a cmap project and not created", () => {
    const project = {
      ...mockProject,
      cmapProject: CMapProjectStatus.CMapNotCreated,
      cMapId: 1,
    };

    render(<RepairProjectsButton {...defaultProps} project={project} />);
    expect(screen.getByTestId("createProjectButton")).toBeInTheDocument();
    expect(screen.getByText("Enable")).toBeInTheDocument();
  });
  it("renders the repair button with wrench icon when hasCMapProjects is false", () => {
    render(<RepairProjectsButton {...defaultProps} />);
    expect(screen.getByTestId("mock-dialog-trigger")).toBeInTheDocument();
    expect(screen.getByTestId("wrench-icon")).toBeInTheDocument();
  });

  it("renders the repair button with sync icon when hasCMapProjects is true", () => {
    render(<RepairProjectsButton {...defaultProps} hasCMapProjects={true} />);
    expect(screen.getByTestId("mock-dialog-trigger")).toBeInTheDocument();
    expect(screen.getByTestId("sync-icon")).toBeInTheDocument();
  });

  it("renders the repair button when its a created cmap project", () => {
    const project = {
      ...mockProject,
      cmapProject: CMapProjectStatus.CMapCreated,
      cMapId: 1,
    };

    render(<RepairProjectsButton {...defaultProps} project={project} />);
    expect(screen.getByTestId("repairButton")).toBeInTheDocument();
    expect(screen.getByText("Repair")).toBeInTheDocument();
  });

  it("shows 'Sync CMap with Atvero' text for default case", () => {
    const project = {
      ...mockProject,
      cmapProject: undefined, // Force unknown status
    };

    // @ts-ignore
    // We add this ts ignore because this is the only way to have a undefined cmap project status
    render(<RepairProjectsButton {...defaultProps} project={project} />);
    expect(screen.getByTestId("repairButton")).toBeInTheDocument();
    expect(screen.getByText("Sync CMap with Atvero")).toBeInTheDocument();
  });

  it("opens the dialog when the button is clicked", async () => {
    render(<RepairProjectsButton {...defaultProps} />);
    fireEvent.click(screen.getByTestId("mock-dialog-trigger"));
    await waitFor(() => {
      expect(screen.getByTestId("mock-dialog")).toBeInTheDocument();
    });
  });

  it("displays the correct project code in the dialog title", async () => {
    render(<RepairProjectsButton {...defaultProps} />);
    fireEvent.click(screen.getByTestId("mock-dialog-trigger"));
    await waitFor(() => {
      expect(screen.getByTestId("mock-dialog-title")).toHaveTextContent(
        `${mockProject.code} - ${mockProject.title}`
      );
    });
  });

  it("calls onRepairProject when Start Repair button is clicked", async () => {
    render(<RepairProjectsButton {...defaultProps} />);
    fireEvent.click(screen.getByTestId("mock-dialog-trigger"));

    await waitFor(() => {
      fireEvent.click(screen.getByTestId("repairButton"));
    });

    expect(mockOnRepairProject).toHaveBeenCalledWith(
      mockProject,
      expect.any(Function)
    );
  });

  // Update remaining tests to use defaultProps...
  it("displays progress bar and status message during repair", async () => {
    render(<RepairProjectsButton {...defaultProps} />);

    const repairButton = screen.getByTestId("repairButton");
    fireEvent.click(repairButton);

    await waitFor(() => {
      fireEvent.click(screen.getByTestId("repairButton"));
    });

    await waitFor(() => {
      expect(screen.getByTestId("mock-progress-bar")).toBeInTheDocument();
      expect(screen.getByTestId("mock-spinner")).toBeInTheDocument();
      expect(screen.getByText("Starting repair...")).toBeInTheDocument();
    });
  });

  it("renders ErrorMessage component when a message is present", async () => {
    const mockOnRepairProjectWithError = vi.fn().mockResolvedValue({
      success: false,
      message: "An error occurred",
      details: "Error details",
    });

    render(
      <RepairProjectsButton
        {...defaultProps}
        onRepairProject={mockOnRepairProjectWithError}
      />
    );

    fireEvent.click(screen.getByTestId("mock-dialog-trigger"));

    await waitFor(() => {
      fireEvent.click(screen.getByTestId("repairButton"));
    });

    await waitFor(() => {
      const errorMessage = screen.getByTestId("mock-error-message");
      expect(errorMessage).toBeInTheDocument();
      expect(errorMessage).toHaveTextContent("Message: An error occurred");
      expect(errorMessage).toHaveTextContent("Type: error");
      expect(errorMessage).toHaveTextContent("Details: Error details");
    });
  });
});
