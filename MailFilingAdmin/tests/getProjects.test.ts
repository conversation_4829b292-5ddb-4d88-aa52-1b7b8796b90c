import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  getAtveroProjectList,
  getCMapProjectList,
  getProjectList,
} from "../src/services/getProjects";
import { SPFI } from "@pnp/sp";
import { CMapProjectStatus, Hubsite, SortDirection } from "../src/types";
import { atveroMailBackend } from "../src/services/backendCall/backendCall";

vi.mock("../src/services/backendCall/backendCall", () => ({
  atveroMailBackend: vi
    .fn()
    .mockImplementation((url: string, method: string) => {
      if (url.includes("search=test")) {
        return Promise.resolve([
          { Id: 1, Code: "P1", Title: "Project 1" },
          { Id: 2, Code: "P2", Title: "Project 2" },
        ]);
      }
      return Promise.resolve([]);
    }),
}));

vi.mock("@pnp/sp/webs", () => ({
  Web: vi.fn().mockImplementation(() => ({
    lists: {
      getByTitle: vi.fn().mockReturnThis(),
      renderListDataAsStream: vi.fn().mockResolvedValue({
        Row: [
          {
            ProjectCode: "P1",
            Description: "Project 1",
            ID: "1",
            ATVImportedSourceID: undefined,
          },
          {
            ProjectCode: "P2",
            Description: "Project 2",
            ID: "2",
            ATVImportedSourceID: undefined,
          },
        ],
      }),
    },
  })),
}));

describe("getProjects service", () => {
  let sharepoint: SPFI;
  let hubsite: Hubsite;

  beforeEach(() => {
    sharepoint = {
      web: {},
    } as SPFI;

    hubsite = {
      displayName: "Test Hubsite",
      name: "hubsite1",
      url: "https://example.com",
    };

    vi.clearAllMocks();
  });

  describe("getAtveroProjectList", () => {
    it("fetches and returns project list with search and sort", async () => {
      const response = await getAtveroProjectList(
        sharepoint,
        hubsite,
        10,
        1,
        "code",
        "asc",
        "Project"
      );

      expect(response.projects).toHaveLength(2);
      expect(response.projects[0]).toEqual(
        expect.objectContaining({
          code: "P1",
          title: "Project 1",
          cmapProject: CMapProjectStatus.NotInCmap,
          webUrl: "https://example.com/sites/P1",
        })
      );
    });

    it("handles empty search string correctly", async () => {
      const response = await getAtveroProjectList(sharepoint, hubsite, 10, 1);

      expect(response.sortDirection).toBe(SortDirection.Ascending);
      expect(response.projects).toBeDefined();
    });

    it("throws error if hubsite url is undefined", async () => {
      //@ts-ignore
      const invalidHubsite = {
        id: "1",
        name: "Test Hubsite",
        url: undefined,
      } as Hubsite;

      await expect(
        getAtveroProjectList(sharepoint, invalidHubsite, 10, 1)
      ).rejects.toThrow("Hubsite webUrl is undefined");
    });
  });

  describe("getCMapProjectList", () => {
    it("fetches and returns CMap projects with search term", async () => {
      const response = await getCMapProjectList("test", "code", "asc");

      expect(response.projects).toHaveLength(2);
      expect(response.projects[0]).toEqual(
        expect.objectContaining({
          code: "P1",
          title: "Project 1",
          cmapProject: CMapProjectStatus.CMapNotCreated,
        })
      );
    });

    it("handles failed response", async () => {
      vi.mocked(atveroMailBackend).mockResolvedValueOnce(undefined);

      await expect(getCMapProjectList("test", "code", "asc")).rejects.toThrow(
        "Failed to fetch projects"
      );
    });

    it("handles empty search term", async () => {
      const response = await getCMapProjectList(undefined);

      expect(response.projects).toHaveLength(0);
      expect(response).toEqual(
        expect.objectContaining({
          nextLink: undefined,
          page: 0,
          tableSize: 0,
          sortField: "",
          sortDirection: SortDirection.Ascending,
          moreResultsAvailable: false,
        })
      );
    });
  });

  describe("getProjectList", () => {
    it("treats project codes starting with 0 correctly", async () => {
      vi.mocked(atveroMailBackend).mockResolvedValueOnce([
        { Id: 1, Code: "00123", Title: "Project One" },
        { Id: 2, Code: "00001", Title: "Project Two" },
        { Id: 3, Code: "042", Title: "Project Three" },
      ]);

      const response = await getProjectList(
        sharepoint,
        hubsite,
        10,
        1,
        "code",
        "asc",
        "0",
        false
      );

      expect(response.projects).toBeDefined();
      expect(response.projects).toHaveLength(3);

      const codes = response.projects.map((p) => p.code);
      expect(codes).toContain("00123");
      expect(codes).toContain("00001");
      expect(codes).toContain("042");

      for (const code of codes) {
        expect(typeof code).toBe("string");
        expect(/^[0]+/.test(code)).toBe(true);
      }
    });

    it("combines Atvero and CMap projects correctly", async () => {
      const response = await getProjectList(
        sharepoint,
        hubsite,
        10,
        1,
        "code",
        "asc",
        "test",
        false
      );

      expect(response.projects).toBeDefined();
      expect(
        response.projects.some(
          (p) => p.cmapProject === CMapProjectStatus.CMapCreated
        )
      ).toBeTruthy();
    });

    it("handles CMap project fetch failure gracefully", async () => {
      vi.mocked(atveroMailBackend).mockRejectedValueOnce(
        new Error("API Error")
      );

      const response = await getProjectList(
        sharepoint,
        hubsite,
        10,
        1,
        "title",
        "asc",
        "test"
      );

      expect(response.projects).toBeDefined();
      expect(response).toEqual(
        expect.objectContaining({
          sortDirection: SortDirection.Ascending,
        })
      );
    });

    it("filters projects when showAtveroProjects is true", async () => {
      const response = await getProjectList(
        sharepoint,
        hubsite,
        10,
        1,
        "title",
        "asc",
        "test",
        true
      );

      expect(response.projects).toBeDefined();
      expect(
        response.projects.every((p) => !p.webUrl || p.webUrl === "")
      ).toBeTruthy();
    });

    it("returns combined projects when Atvero and CMap are merged", async () => {
      const response = await getProjectList(
        sharepoint,
        hubsite,
        10,
        1,
        "code",
        "asc",
        "test",
        false
      );

      expect(response.projects).toHaveLength(2);
      expect(
        response.projects.some(
          (project) => project.cmapProject === CMapProjectStatus.CMapCreated
        )
      ).toBe(true);
    });

    it("returns only CMap projects when showAtveroProjects is true", async () => {
      const response = await getProjectList(
        sharepoint,
        hubsite,
        10,
        1,
        "title",
        "asc",
        "test",
        true
      );

      expect(response.projects.every((p) => !p.webUrl)).toBe(true);
    });
  });
});
