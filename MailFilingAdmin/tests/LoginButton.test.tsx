import { describe, it, expect, vi } from 'vitest';
import { render, fireEvent } from '@testing-library/react';
import LoginButton from '../src/components/LoginButton';

describe('LoginButton', () => {
  it('renders correctly', () => {
    const { getByText } = render(<LoginButton onLogin={() => {}} />);
    expect(getByText('Log in')).toBeDefined();
  });

  it('calls onLogin prop when clicked', () => {
    const mockOnLogin = vi.fn();
    const { getByText } = render(<LoginButton onLogin={mockOnLogin} />);
    const button = getByText('Log in');
    
    fireEvent.click(button);
    
    expect(mockOnLogin).toHaveBeenCalledTimes(1);
  });

  it('is a button element', () => {
    const { getByRole } = render(<LoginButton onLogin={() => {}} />);
    const button = getByRole('button');
    expect(button).toBeDefined();
  });
});