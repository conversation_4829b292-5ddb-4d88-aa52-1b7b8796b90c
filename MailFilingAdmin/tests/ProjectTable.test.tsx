import { describe, it, expect, vi } from "vitest";
import { fireEvent, render, screen } from "@testing-library/react";
import { Column, Project, SortDirection } from "../src/types";
import ProjectsTable from "../src/components/ProjectsTable";

vi.mock("react-window-infinite-loader", () => ({
  default: ({ children, loadMoreItems }: any) => {
    (global as any).loadMoreItems = loadMoreItems;
    return children({
      onItemsRendered: () => {},
      ref: () => {},
    });
  },
}));

vi.mock("react-window", () => ({
  FixedSizeList: ({ children, itemCount }: any) => (
    <div data-testid="fixed-size-list">
      {Array.from({ length: itemCount }).map((_, index) =>
        children({ index, style: {} })
      )}
    </div>
  ),
}));

vi.mock("react-virtualized-auto-sizer", () => ({
  default: ({ children }: any) => children({ height: 500, width: 500 }),
}));
const hasCMapProjects = false;
const mockProjects: Project[] = [
  {
    id: "1",
    code: "PR001",
    title: "Test Project 1",
    favorite: false,
    cMapId: undefined,
    cmapProject: 0,
    webUrl: "http://test.com/1"
  },
  {
    id: "2",
    code: "PR002",
    title: "Test Project 2",
    favorite: true,
    cMapId: undefined,
    cmapProject: 0,
    webUrl: "http://test.com/2"
  },
];

const columns: Column[] = [
  { columnKey: "code", label: "Code", sortable: true },
  { columnKey: "title", label: "Title", sortable: true },
];

const setup = (propsOverride: Partial<any> = {}) => {
  const defaultProps = {
    projects: mockProjects,
    selectedProjects: [],
    columns,
    loading: false,
    hasNextPage: false,
    loadNextPage: vi.fn(),
    isNextPageLoading: false,
    onSortChange: vi.fn(),
    currentSort: { columnKey: "code", direction: SortDirection.Ascending },
    onFetchUsers: vi.fn().mockResolvedValue([]),
    onRepairProject: vi.fn().mockResolvedValue({ success: true }),
    onCreateProject: vi.fn().mockResolvedValue({ success: true }),
    onProjectSelect: vi.fn(),
    ...propsOverride,
    hasCMapProjects
  };

  return render(<ProjectsTable {...defaultProps} />);
};

describe("ProjectsTable", () => {
  it("calls loadNextPage when reaching the end of the list and hasMoreItems is true", () => {
    const loadNextPage = vi.fn();
    setup({
      hasNextPage: true,
      loadNextPage,
      isNextPageLoading: false,
    });

    (global as any).loadMoreItems(0, 10);
    
    expect(loadNextPage).toHaveBeenCalledWith(0, 10);
  });

  it("does not call loadNextPage when isNextPageLoading is true", () => {
    const loadNextPage = vi.fn();
    setup({
      hasNextPage: true,
      isNextPageLoading: true,
      loadNextPage
    });

    // Try to call loadMoreItems when loading
    (global as any).loadMoreItems(0, 10);

    expect(loadNextPage).not.toHaveBeenCalled();
  });

  it("does not render any rows if no items are present", () => {
    setup({ projects: [] });

    const list = screen.getByTestId("fixed-size-list");
    expect(list.children.length).toBe(0);
  });

  it("applies correct styles to table container", () => {
    const { container } = setup();
    const table = container.querySelector('[aria-label="Custom table"]');

    expect(table).toHaveStyle({
      display: "flex",
      flexDirection: "column",
      flex: "1 1 auto",
      overflow: "hidden",
    });
  });

  it("renders table headers with correct labels", () => {
    setup();
    
    columns.forEach(column => {
      expect(screen.getByText(column.label)).toBeInTheDocument();
    });
  });

  it("handles sort changes correctly", () => {
    const onSortChange = vi.fn();
    setup({ onSortChange });

    const codeHeader = screen.getByText("Code");
    fireEvent.click(codeHeader);

    expect(onSortChange).toHaveBeenCalledWith("code", SortDirection.Descending);
  });

  it("renders loading state correctly", () => {
    setup({ loading: true });
    expect(screen.queryByTestId("fixed-size-list")).not.toBeInTheDocument();
  });
});