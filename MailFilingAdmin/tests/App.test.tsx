import { describe, it, expect, vi, beforeEach, MockInstance } from "vitest";
import { render, screen, waitFor, act, fireEvent } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import App from "../src/App";
import { SortDirection, Tenancy } from "../src/types";

// Mocked data
const mockProjects = [
  {
    id: "1",
    code: "P1",
    title: "Project 1",
    favorite: false,
    webUrl: "",
    cmapProject: "NotInCmap",
    cMapId: undefined,
  },
];

const mockHubsites = [
  {
    displayName: "Test Hubsite",
    name: "hubsite1",
    url: "https://example.com",
  },
];

// Common Mocks
vi.mock("../src/components/Projects", () => ({
  default: ({ onRepairProject, onSortChange, hubsiteError }: any) => (
    <div>
      Projects Component
      {hubsiteError && (
        <div data-testid="hubsite-error-message">{hubsiteError}</div>
      )}
      <button onClick={() => onRepairProject(mockProjects[0], () => {})}>
        Repair Project
      </button>
      <button
        data-testid="sort-code-asc"
        onClick={() => onSortChange("ProjectCode", SortDirection.Ascending)}
      >
        Sort Code Ascending
      </button>
      <button
        data-testid="sort-code-desc"
        onClick={() => onSortChange("ProjectCode", SortDirection.Descending)}
      >
        Sort Code Descending
      </button>
      <button
        data-testid="toggle-atvero"
        onClick={() => onSortChange("Description", SortDirection.Ascending)}
      >
        Sort Description
      </button>
    </div>
  ),
}));


vi.mock("../src/services/createProject", () => ({
  getProjectDesign: vi.fn().mockResolvedValue({
    Id: "design-123",
    Title: "AtveroMail - Project",
  }),
  createProject: vi.fn().mockResolvedValue("https://example.com/project"),
}));

vi.mock("../src/services/checkAdminUser", () => ({
  checkIfUserIsAdmin: vi.fn().mockResolvedValue(true),
}));

// Shared render function
const renderWithProvider = (props: any) => {
  return render(
    <FluentProvider theme={webLightTheme}>
      <App {...props} />
    </FluentProvider>
  );
};

// Reusable function to assert projects are rendered
const assertProjectsRendered = async () => {
  await waitFor(() => {
    expect(screen.getByText("Projects Component")).toBeInTheDocument();
  });
};

// Reusable function for sorting tests
const testSorting = async (buttonTestId: string, props: any) => {
  await act(async () => {
    renderWithProvider(props);
  });
  fireEvent.click(screen.getByTestId(buttonTestId));
  await assertProjectsRendered();
};

describe("App Component", () => {
  let consoleErrorSpy: MockInstance;

  const mockAdapter = {
    legacyAdapter: {
      getGraph: vi.fn(),
      getSharepoint: vi.fn(),
      getSharepointHostName: vi
        .fn()
        .mockReturnValue("https://example.sharepoint.com"),
    },
    backendAdapter: {
      getHubsites: vi.fn().mockResolvedValue({ hubsites: mockHubsites }),
    },
    sharepointAdapter: {
      getMemberUsers: vi.fn(),
      getConfidentialUsers: vi.fn(),
    },
  };

  const defaultProps = {
    adapter: mockAdapter,
    hubsites: mockHubsites,
    selectedHubsite: mockHubsites[0],
    hubsiteError: undefined,
    onHubsiteSelect: vi.fn(),
    tenancy: Tenancy.Atvero,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    consoleErrorSpy.mockRestore();
  });

  it("renders without crashing", async () => {
    await act(async () => {
      renderWithProvider(defaultProps);
    });
    await assertProjectsRendered();
  });

  it("displays hubsite error message when present", async () => {
    const errorProps = {
      ...defaultProps,
      hubsites: undefined,
      hubsiteError:
        "No hubsites were found for your account. Please contact your administrator to get access to the required hubsites.",
    };
  
    await act(async () => {
      renderWithProvider(errorProps);
    });
  
    const errorElement = screen.getByTestId("hubsite-error-message");
    expect(errorElement).toBeInTheDocument();
    expect(errorElement).toHaveTextContent(
      "No hubsites were found for your account. Please contact your administrator to get access to the required hubsites."
    );
  });
  
  it("displays not an admin error when not an admin", async () => {
    vi.mock("../src/services/checkAdminUser", () => ({
      checkIfUserIsAdmin: vi.fn().mockResolvedValue(false),
    }));

    await act(async () => {
      renderWithProvider(defaultProps);
    });

    const errorElement = screen.getByTestId("admin-access-error-message");
    expect(errorElement).toBeInTheDocument();
  });

  // Use reusable sorting function
  it("sorts by project code in ascending order", async () => {
    await testSorting("sort-code-asc", defaultProps);
  });

  it("sorts by project code in descending order", async () => {
    await testSorting("sort-code-desc", defaultProps);
  });

  it("toggles Atvero projects", async () => {
    await testSorting("toggle-atvero", defaultProps);
  });

  it("repairs project", async () => {
    await act(async () => {
      renderWithProvider(defaultProps);
    });
    fireEvent.click(screen.getByText("Repair Project"));
    await assertProjectsRendered();
  });

  it("creates project successfully", async () => {
    await act(async () => {
       renderWithProvider(defaultProps);
    });
    const result = await defaultProps.adapter.legacyAdapter.getGraph();
    expect(result).toBeUndefined();
  });

  it("loads next page", async () => {
    await act(async () => {
      renderWithProvider(defaultProps);
    });
    fireEvent.click(screen.getByText("Projects Component"));
    await assertProjectsRendered();
  });
});
