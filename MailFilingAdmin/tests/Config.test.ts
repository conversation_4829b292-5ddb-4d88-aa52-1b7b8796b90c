import { describe, it, expect } from "vitest";
import {
  BackendScope,
  BackendUrl,
  ClientId,
  RedirectUri,
} from "../src/utils/Config";

import config from "../config.json";

describe("BackendUrl", () => {
  it("returns cmap backendUrl when tenancy is cmap", () => {
    const result = BackendUrl();

    expect(result).toBe(config.backendUrl);
  });
});

describe("ClientId", () => {
  it("returns cmap backendUrl when tenancy is cmap", () => {
    const result = ClientId();

    expect(result).toBe(config.appid);
  });

  it("returns atvero backendUrl when tenancy is atvero", () => {
    const result = ClientId();

    expect(result).toBe(config.appid);
  });
});

describe("RedirectUri", () => {
  it("returns cmap backendUrl when tenancy is cmap", () => {
    const result = RedirectUri();

    expect(result).toBe(config.redirectUrl);
  });
});

describe("BackendScope", () => {
  it("returns cmap backendUrl when tenancy is cmap", () => {
    const result = BackendScope();

    expect(result).toBe(
      "api://localhost:3000/2d9bf732-ebf3-43a2-b7b2-a22803eb5a97/access_as_user"
    );
  });

  it("returns atvero backendUrl when tenancy is atvero", () => {
    const result = BackendScope();

    expect(result).toBe(
      "api://localhost:3000/2d9bf732-ebf3-43a2-b7b2-a22803eb5a97/access_as_user"
    );
  });
});
