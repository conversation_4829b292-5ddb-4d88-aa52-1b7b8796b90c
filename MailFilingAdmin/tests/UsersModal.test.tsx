import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { useSharePointApi } from "../src/services/useSharePoint";
import UsersModal from "../src/components/UsersModal";
import { CanAccessConfidentialEmails, User } from "../src/types";
import { removeUserFromSite, searchUsers } from "../src/services/getUsers";
import { toggleUserToConfidentialGroup } from "../src/services/toggleUserToConfidentialGroup";

vi.mock("../src/services/toggleUserToConfidentialGroup", () => ({
  toggleUserToConfidentialGroup: vi.fn(),
}));

vi.mock("@fluentui/react-components", async () => {
  const actual = await vi.importActual("@fluentui/react-components");
  return {
    ...actual,
    Checkbox: ({
      checked,
      onChange,
      disabled,
      label,
    }: {
      checked: boolean;
      onChange: (ev: any, data: { checked: boolean }) => void;
      disabled: boolean;
      label: string;
    }) => (
      <label>
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => onChange(e, { checked: e.target.checked })}
          disabled={disabled}
          data-testid="mock-checkbox"
        />
        {label}
      </label>
    ),
    Spinner: ({ size }: { size: string }) => (
      <div data-testid={`mock-spinner-${size}`}>Loading Users...</div>
    ),
  };
});

vi.mock("../src/services/useSharePoint", () => ({
  useSharePointApi: vi.fn().mockReturnValue({
    sharepoint: {},
    sharepointHostName: "https://example.sharepoint.com",
  }),
}));

vi.mock("../src/services/getUsers", () => ({
  addUserToSite: vi.fn(),
  removeUserFromSite: vi.fn(),
  searchUsers: vi.fn(),
}));

describe("UsersModal", () => {
  const mockProjectUrl = "https://example.com/project";
  const mockOnFetchUsers = vi.fn().mockResolvedValue([]);
  const mockOnClose = vi.fn();

  beforeEach(() => {
    (useSharePointApi as any).mockReturnValue({
      sharepoint: {} as any,
      sharepointHostName: "https://example.sharepoint.com",
    });
    vi.clearAllMocks();
  });

  it("renders without crashing", () => {
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
      />
    );
    expect(screen.getByTestId("users-model")).toBeDefined();
  });

  it("opens dialog when button is clicked", async () => {
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
      />
    );
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);
    expect(await screen.findByText("Manage Members")).toBeDefined();
  });

  it("displays loading state when fetching users", async () => {
    const delay = 100;
    const createDelayedPromise = (ms: number) =>
      new Promise((resolve) => setTimeout(resolve, ms));
    const slowFetchUsers = vi
      .fn()
      .mockImplementation(() => createDelayedPromise(delay).then(() => []));

    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={slowFetchUsers}
      />
    );
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    await expect(screen.findByText("Loading Users...")).resolves.toBeDefined();
  });

  it("displays error message when user fetch fails", async () => {
    const failingFetchUsers = vi
      .fn()
      .mockRejectedValue(new Error("Failed to fetch users."));
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={failingFetchUsers}
      />
    );
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);
    expect(
      await screen.findByText(
        "Failed to fetch users."
      )
    ).toBeDefined();
  });

  it("displays users when they are successfully fetched", async () => {
    const mockUsers: User[] = [
      {
        loginName: "Login",
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
      },
      {
        loginName: "Login",
        id: "2",
        name: "Jane Smith",
        email: "<EMAIL>",
      },
    ];
    const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);
    expect(await screen.findByText("John Doe")).toBeDefined();
    expect(screen.getByText("<EMAIL>")).toBeDefined();
  });

  it("fetches users when dialog opens", async () => {
    const mockUsers: User[] = [
      {
        loginName: "Login",
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
      },
      {
        loginName: "Login",
        id: "2",
        name: "Jane Smith",
        email: "<EMAIL>",
      },
    ];
    const mockFetchUsers = vi.fn().mockResolvedValue(mockUsers);
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockFetchUsers}
      />
    );

    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    await waitFor(() => {
      expect(mockFetchUsers).toHaveBeenCalledWith(mockProjectUrl);
    });

    expect(await screen.findByText("John Doe")).toBeDefined();
    expect(screen.getByText("<EMAIL>")).toBeDefined();
  });

  it("does not render loading option when isUserSearchLoading is false", async () => {
    const mockUsers: User[] = [
      {
        loginName: "Login",
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
      },
    ];
    const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);

    vi.mocked(searchUsers).mockResolvedValue(mockUsers);

    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );

    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    await screen.findByText("Manage Members");

    const input = screen.getByPlaceholderText("Add users");
    fireEvent.change(input, { target: { value: "John" } });

    await screen.findByText("John Doe");

    const loadingOption = screen.queryByText("Loading users...");
    expect(loadingOption).toBeNull();

    const spinner = screen.queryByRole("progressbar");
    expect(spinner).toBeNull();
  });

  it("shows spinner when updating confidential access", async () => {
    const mockUsers = [
      {
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
        canAccessConfidentialEmails: CanAccessConfidentialEmails.No
      },
    ];
    const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);
    vi.mocked(toggleUserToConfidentialGroup).mockImplementation(
      () => new Promise((resolve) => setTimeout(resolve, 100))
    );

    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    await screen.findByText("John Doe");

    const checkbox = screen.getByTestId("mock-checkbox");
    fireEvent.click(checkbox);

    expect(await screen.findByTestId("mock-spinner-extra-tiny")).toBeDefined();
  });

  it("updates user confidential access when checkbox is clicked", async () => {
    const mockUsers = [
      {
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
        canAccessConfidentialEmails: CanAccessConfidentialEmails.No
      },
    ];
    const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);
    vi.mocked(toggleUserToConfidentialGroup).mockResolvedValue();

    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    await screen.findByText("John Doe");

    const checkbox = screen.getByTestId("mock-checkbox");
    fireEvent.click(checkbox);

    await waitFor(() => {
      expect(toggleUserToConfidentialGroup).toHaveBeenCalledWith({
        sharepoint: expect.anything(),
        projectUrl: mockProjectUrl,
        user: mockUsers[0],
        canAccessConfidentialEmails: true,
      });
    });
  });

  it("displays error message when updating confidential access fails", async () => {
    const mockUsers = [
      {
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
        canAccessConfidentialEmails: CanAccessConfidentialEmails.No
      },
    ];
    const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);
    vi.mocked(toggleUserToConfidentialGroup).mockRejectedValue(
      new Error("Failed to update")
    );

    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    await screen.findByText("John Doe");

    const checkbox = screen.getByTestId("mock-checkbox");
    fireEvent.click(checkbox);

    expect(
      await screen.findByText(
        "Failed to update confidential access for John Doe. Please try again."
      )
    ).toBeDefined();
  });

  it("clamps long email addresses in TableCellLayout", async () => {
    const mockUsers: User[] = [
      {
        loginName: "Login",
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
      },
    ];
    const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);

    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );

    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    await screen.findByText("John Doe");

    const clampedEmail = await screen.findByText("john.doe.email@example...");
    expect(clampedEmail).toBeDefined();
  });

  it("updates user confidential access when checkbox is clicked and updates local state", async () => {
    const mockUsers = [
      {
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
        canAccessConfidentialEmails: CanAccessConfidentialEmails.No
      },
      {
        id: "2",
        name: "Jane Smith",
        email: "<EMAIL>",
        canAccessConfidentialEmails: CanAccessConfidentialEmails.Yes
      },
    ];
    const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);
    vi.mocked(toggleUserToConfidentialGroup).mockResolvedValue();

    const { rerender } = render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    await screen.findByText("John Doe");

    const checkbox = screen.getAllByTestId("mock-checkbox")[0]; // John's checkbox
    fireEvent.click(checkbox);

    await waitFor(() => {
      expect(toggleUserToConfidentialGroup).toHaveBeenCalledWith({
        sharepoint: expect.anything(),
        projectUrl: mockProjectUrl,
        user: mockUsers[0],
        canAccessConfidentialEmails: true,
      });
    });

    // Force a re-render to ensure the state has been updated
    rerender(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );

    // Check if the local state has been updated
    const updatedCheckbox = screen.getAllByTestId("mock-checkbox")[0];
    expect(updatedCheckbox).toHaveProperty("checked", true);

    // Verify that other users' states remain unchanged
    const otherCheckbox = screen.getAllByTestId("mock-checkbox")[1]; // Jane's checkbox
    expect(otherCheckbox).toHaveProperty("checked", true);
  });

  it("does not render the trigger button when showButton is false", () => {
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
        showButton={false}
      />
    );
    
    const button = screen.queryByRole("button", { name: /Users/<USER>
    expect(button).toBeNull();
  });

  it("renders the trigger button by default when showButton is not specified", () => {
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
      />
    );
    
    const button = screen.getByRole("button", { name: /Users/<USER>
    expect(button).toBeDefined();
  });

  it("opens dialog immediately when initialOpen is true", async () => {
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
        initialOpen={true}
      />
    );

    // Dialog should be open immediately
    expect(await screen.findByText("Manage Members")).toBeDefined();
    // Should fetch users immediately
    expect(mockOnFetchUsers).toHaveBeenCalledWith(mockProjectUrl);
  });

  it("calls onClose callback when dialog is closed", async () => {
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
        initialOpen={true}
        onClose={mockOnClose}
      />
    );

    // Wait for dialog to open
    await screen.findByText("Manage Members");

    // Find and click close button
    const closeButton = screen.getByLabelText("Close");
    fireEvent.click(closeButton);

    // Check if onClose was called
    expect(mockOnClose).toHaveBeenCalled();
  });

  it("calls onClose when clicking outside the dialog", async () => {
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
        initialOpen={true}
        onClose={mockOnClose}
      />
    );

    // Wait for dialog to open
    await screen.findByText("Manage Members");

    // Find and click the backdrop
    const backdrop = document.querySelector('.fui-DialogSurface__backdrop');
    if (backdrop) {
      fireEvent.click(backdrop);
    }

    // Wait for onClose to be called
    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it("maintains dialog state when initialOpen changes", async () => {
    const { rerender } = render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
        initialOpen={false}
      />
    );

    // Dialog should be closed initially
    expect(screen.queryByText("Manage Members")).toBeNull();

    // Change initialOpen to true
    rerender(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
        initialOpen={true}
      />
    );

    // Dialog should open
    expect(await screen.findByText("Manage Members")).toBeDefined();
  });

  it("properly hides trigger button and opens dialog when used programmatically", async () => {
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
        showButton={false}
        initialOpen={true}
        onClose={mockOnClose}
      />
    );

    // Button should not exist
    const button = screen.queryByRole("button", { name: /Users/<USER>
    expect(button).toBeNull();

    // Dialog should be open
    expect(await screen.findByText("Manage Members")).toBeDefined();

    // Hidden span should exist
    const hiddenSpan = document.querySelector('.hiddenSpan');
    expect(hiddenSpan).toBeDefined();
  });

  it("shows error message when attempting to delete user without SharePoint initialized", async () => {
    (useSharePointApi as any).mockReturnValue({
      sharepoint: null,
      sharepointHostName: "https://example.sharepoint.com",
    });
    
    const mockUsers = [{
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
    }];
    
    const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);
    
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );
  
    // Open dialog
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);
  
    // Wait for users to load
    await screen.findByText("John Doe");
    
    // Wait for loading spinner to disappear
    await waitFor(() => {
      expect(screen.queryByTestId("mock-spinner-small")).not.toBeInTheDocument();
    });
  
    // Click remove user button
    const removeButton = screen.getByLabelText("Remove user");
    fireEvent.click(removeButton);
  
    // Confirm deletion
    const confirmButton = await screen.findByText("Confirm");
    fireEvent.click(confirmButton);
  
    // Verify error message
    expect(await screen.findByText(
      "SharePoint is not initialized. Please refresh and again later. If this issue persists please contact a Atvero administrator."
    )).toBeDefined();
  });

  it("handles dialog open/close correctly with onOpenChange", async () => {
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockOnFetchUsers}
        onClose={mockOnClose}
      />
    );

    // Open dialog using onOpenChange
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    // Verify dialog is open
    expect(await screen.findByText("Manage Members")).toBeDefined();

    // Close dialog using onOpenChange
    const closeButton = screen.getByLabelText("Close");
    fireEvent.click(closeButton);

    // Verify onClose was called
    expect(mockOnClose).toHaveBeenCalled();

    // Verify dialog is closed
    await waitFor(() => {
      expect(screen.queryByText("Manage Members")).toBeNull();
    });
  });

  it("resets message state when dialog is closed", async () => {
    const mockUsers = [{
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
    }];
    
    const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);
    vi.mocked(removeUserFromSite).mockRejectedValue(new Error("Failed to remove"));

    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={successfulFetchUsers}
      />
    );

    // Open dialog
    const button = screen.getByRole("button", { name: /Users/<USER>
    fireEvent.click(button);

    // Wait for users to load
    await screen.findByText("John Doe");

    // Trigger an error message
    const removeButton = screen.getByLabelText("Remove user");
    fireEvent.click(removeButton);
    
    const confirmButton = screen.getByText("Confirm");
    fireEvent.click(confirmButton);

    // Wait for error message
    await screen.findByText(/Failed to remove John Doe/);

    // Close dialog
    const closeButton = screen.getByLabelText("Close");
    fireEvent.click(closeButton);

    // Reopen dialog
    fireEvent.click(button);

    // Verify error message is gone
    expect(screen.queryByText(/Failed to remove John Doe/)).toBeNull();
  });

  it('does not render checkbox column when all users have DontKnow status', async () => {
    const mockUsers = [{
      id: '1',
      loginName: 'john.doe',
      name: 'John Doe',
      email: '<EMAIL>',
      canAccessConfidentialEmails: CanAccessConfidentialEmails.DontKnow
    }];
    
    const mockFetchUsers = vi.fn().mockResolvedValue(mockUsers);
  
    render(
      <UsersModal
        disabled={false}
        projectUrl={mockProjectUrl}
        onFetchUsers={mockFetchUsers}
      />
    );
  
    const button = screen.getByRole('button', { name: /Users/<USER>
    fireEvent.click(button);
  
    await screen.findByText('John Doe');
  
    // Check that checkbox is not rendered
    expect(screen.queryByTestId('mock-checkbox')).toBeNull();
  
    // Check that column header is not rendered
    expect(screen.queryByText('Can Confidentially File')).toBeNull();
  });
  
it('enables confidential toggle when confidential fetch succeeds', async () => {
  const mockUsers = [{
    id: '1',
    loginName: 'john.doe',
    name: 'John Doe',
    email: '<EMAIL>',
    canAccessConfidentialEmails: CanAccessConfidentialEmails.No
  }];
  
  const successfulFetchUsers = vi.fn().mockResolvedValue(mockUsers);

  render(
    <UsersModal
      disabled={false}
      projectUrl={mockProjectUrl}
      onFetchUsers={successfulFetchUsers}
    />
  );

  const button = screen.getByRole('button', { name: /Users/<USER>
  fireEvent.click(button);

  const nameCell = await screen.findByRole('cell', { name: /John Doe/i });
  expect(nameCell).toBeInTheDocument();

  const checkbox = screen.getByTestId('mock-checkbox');
  expect(checkbox).not.toBeDisabled();
  expect(checkbox).toBeInTheDocument();

  expect(screen.queryByText('Unable to fetch confidential access. Checkbox disabled.')).toBeNull();
});
});
