import { describe, it, expect, vi, beforeEach } from "vitest";
import { getHubsiteFromPath } from "../src/services/getHubsiteFromGraph";
import { GraphFI } from "@pnp/graph";
import { Site } from "@microsoft/microsoft-graph-types";

describe("getHubsiteFromPath", () => {
 let mockGraph: { sites: { getByUrl: ReturnType<typeof vi.fn> } };

 beforeEach(() => {
   mockGraph = {
     sites: {
       getByUrl: vi.fn()
     }
   };
 });

 it("should return hubsite with ID", async () => {
   const mockHubsite: Site = { id: "mock-site-id" };
   mockGraph.sites.getByUrl.mockReturnValue(() => Promise.resolve(mockHubsite));

   const result = await getHubsiteFromPath(mockGraph as unknown as GraphFI, "sharepoint.com", "/sites/hubsite");
   
   expect(result).toEqual(mockHubsite);
   expect(mockGraph.sites.getByUrl).toHaveBeenCalledWith("sharepoint.com", "/sites/hubsite");
 });

 it("should throw error if getByUrl fails", async () => {
   mockGraph.sites.getByUrl.mockReturnValue(() => Promise.reject(new Error("Failed to get hubsite")));

   await expect(getHubsiteFromPath(mockGraph as unknown as GraphFI, "sharepoint.com", "/sites/hubsite"))
     .rejects.toThrow("Failed to get hubsite");
 });
});