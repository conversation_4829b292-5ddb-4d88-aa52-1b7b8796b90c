import { describe, it, expect } from "vitest";
import { containsSpecialCharacters, clampText, containsOnlySpaces } from "../src/utils/textFieldUtils";

describe("TextFieldUtils", () => {
  describe("containsSpecialCharacters", () => {
    it("correctly identifies strings with and without special characters", () => {
      expect(containsSpecialCharacters("Hello")).toBe(false);
      expect(containsSpecialCharacters("Hello-World")).toBe(false);
      expect(containsSpecialCharacters("Hello 😊")).toBe(true);
      expect(containsSpecialCharacters("Hello@World")).toBe(true);
      expect(containsSpecialCharacters("Hello#123")).toBe(true);
      expect(containsSpecialCharacters("Hello_World")).toBe(false);
    });
  });

  describe("clampText", () => {
    it("clamps text to specified length with ellipsis", () => {
      expect(clampText("Hello World", 5)).toBe("He...");
      expect(clampText("Hello", 10)).toBe("Hello");
      expect(clampText("Hello World", 8)).toBe("Hello...");
      expect(clampText("", 5)).toBe("");
      expect(clampText("A", 1)).toBe("A");
    });
  });

  describe("containsOnlySpaces", () => {
    it("correctly identifies strings with only spaces", () => {
      expect(containsOnlySpaces("   ")).toBe(true);
      expect(containsOnlySpaces("Hello")).toBe(false);
      expect(containsOnlySpaces("  Hello  ")).toBe(false);
      expect(containsOnlySpaces("")).toBe(true);
      expect(containsOnlySpaces(" \t\n")).toBe(true);
    });
  });
});