import { describe, it, vi, beforeEach, expect } from 'vitest';
import { TestSharepointAdapter } from "../src/adapters/TestSharepointAdapter";
import { fetchProjectUsers } from "../src/services/getProjectUsers";
import "@pnp/sp/site-groups/web";
import "@pnp/sp/site-users/web";
import { User, CanAccessConfidentialEmails } from '../src/types';

describe('fetchProjectUsers', () => {
  const mockProjectUrl = 'https://example.com/project';
  const mockMemberUsers: User[] = [
    { 
      id: '1',
      loginName: 'john.doe',
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    { 
      id: '2',
      loginName: 'jane.doe',
      name: '<PERSON>',
      email: '<EMAIL>',
    },
  ];

  const mockConfidentialUsers: User[] = [
    {
      id: '1',
      loginName: 'john.doe',
      name: '<PERSON>',
      email: '<EMAIL>',
    }
  ];

  let adapter: TestSharepointAdapter;

  beforeEach(() => {
    adapter = new TestSharepointAdapter();
    vi.resetAllMocks();
  });

  it('should return members when confidential users is empty but not undefined', async () => {
    adapter.getConfidentialUsers = vi
      .fn()
      .mockResolvedValueOnce([]);

    adapter.getMemberUsers = vi
      .fn()
      .mockResolvedValueOnce(mockMemberUsers);

    const result = await fetchProjectUsers(adapter, mockProjectUrl);

    expect(result).toEqual(mockMemberUsers.map(user => ({
      ...user,
      canAccessConfidentialEmails: CanAccessConfidentialEmails.No
    })));
  });

  it('should return members when confidential users is undefined', async () => {
    adapter.getConfidentialUsers = vi
      .fn()
      .mockResolvedValueOnce(undefined);

    adapter.getMemberUsers = vi
      .fn()
      .mockResolvedValueOnce(mockMemberUsers);

    const result = await fetchProjectUsers(adapter, mockProjectUrl);

    expect(result).toEqual(mockMemberUsers.map(user => ({
      ...user,
      canAccessConfidentialEmails: CanAccessConfidentialEmails.DontKnow
    })));
  });

  it('should return both confidential users and members', async () => {
    adapter.getConfidentialUsers = vi
      .fn()
      .mockResolvedValueOnce(mockConfidentialUsers);

    adapter.getMemberUsers = vi
      .fn()
      .mockResolvedValueOnce(mockMemberUsers);

    const result = await fetchProjectUsers(adapter, mockProjectUrl);

    expect(result).toEqual([
      {
        ...mockMemberUsers[0],
        canAccessConfidentialEmails: CanAccessConfidentialEmails.Yes
      },
      {
        ...mockMemberUsers[1],
        canAccessConfidentialEmails: CanAccessConfidentialEmails.No
      }
    ]);
  });

  it('should return empty array if both are empty', async () => {
    adapter.getConfidentialUsers = vi
      .fn()
      .mockResolvedValueOnce([]);

    adapter.getMemberUsers = vi
      .fn()
      .mockResolvedValueOnce([]);

    const result = await fetchProjectUsers(adapter, mockProjectUrl);

    expect(result).toEqual([]);
  });

  it('should set DontKnow status when confidential users fails', async () => {
    adapter.getConfidentialUsers = vi
      .fn()
      .mockRejectedValueOnce(new Error('Failed to get confidential users'));

    adapter.getMemberUsers = vi
      .fn()
      .mockResolvedValueOnce(mockMemberUsers);

    const result = await fetchProjectUsers(adapter, mockProjectUrl);

    expect(result).toEqual(mockMemberUsers.map(user => ({
      ...user,
      canAccessConfidentialEmails: CanAccessConfidentialEmails.DontKnow
    })));
  });

  it('should throw error when members fetch fails', async () => {
    adapter.getConfidentialUsers = vi
      .fn()
      .mockResolvedValueOnce(mockConfidentialUsers);

    adapter.getMemberUsers = vi
      .fn()
      .mockRejectedValueOnce(new Error('Failed to get members'));

    await expect(fetchProjectUsers(adapter, mockProjectUrl))
      .rejects
      .toThrow('Failed to get members');
  });

  it('should throw error when members fetch returns undefined', async () => {
    adapter.getConfidentialUsers = vi
      .fn()
      .mockResolvedValueOnce(mockConfidentialUsers);

    adapter.getMemberUsers = vi
      .fn()
      .mockResolvedValueOnce(undefined);

    await expect(fetchProjectUsers(adapter, mockProjectUrl))
      .rejects
      .toThrow('Failed to fetch project members');
  });
});