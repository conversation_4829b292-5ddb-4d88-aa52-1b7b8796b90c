import { describe, it, expect, vi, beforeEach } from "vitest";
import { getSiteGuidFromGraphGuid, fetchHubsites, checkHubsiteAccess } from "../src/services/graphHelpers";
import { GraphFI } from "@pnp/graph";
import { SPFI } from "@pnp/sp";
import { IHubSiteInfo } from "@pnp/sp/hubsites";
import { Site } from "@microsoft/microsoft-graph-types";
import { ISite } from "@pnp/graph/sites";

vi.mock("@pnp/sp/webs", () => ({
  Web: () => ({
    lists: {
      getByTitle: vi.fn().mockImplementation(() => {
        throw new Error("List not found");
      }),
    },
  }),
}));

describe("getSiteGuidFromGraphGuid", () => {
  it("returns the correct site guid", () => {
    const graphSiteId = "test.sharepoint.com, 12345,67890";
    const expectedSiteGuid = "12345";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });

  it("returns an empty string if graphSiteId is empty", () => {
    const graphSiteId = "";
    const expectedSiteGuid = "";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });

  it("returns the first part if graphSiteId does not follow the right format (one part in id)", () => {
    const graphSiteId = "12345";
    const expectedSiteGuid = "";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });

  it("returns the first part if graphSiteId does not follow the right format (two parts in id)", () => {
    const graphSiteId = "test.sharepoint.com,12345";
    const expectedSiteGuid = "";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });

  it("returns the first part if graphSiteId does not follow the right format (,)", () => {
    const graphSiteId = ",";
    const expectedSiteGuid = "";

    const result = getSiteGuidFromGraphGuid(graphSiteId);

    expect(result).toBe(expectedSiteGuid);
  });
});

describe("fetchHubsites", () => {
  let mockSharepoint: SPFI;
  let mockGraph: GraphFI;
  let mockHubsites: IHubSiteInfo[];
  
  beforeEach(() => {
    mockSharepoint = {
      hubSites: vi.fn(),
    } as unknown as SPFI;
    
    mockGraph = {
      sites: {
        getById: vi.fn(),
      },
    } as unknown as GraphFI;

    mockHubsites = [
      {
        ID: "site1",
        Title: "Hubsite 1",
        SiteUrl: "https://test.sharepoint.com/sites/hub1",
      },
      {
        ID: "site2",
        Title: "Hubsite 2",
        SiteUrl: "https://test.sharepoint.com/sites/hub2",
      },
    ] as IHubSiteInfo[];

    vi.resetAllMocks();
  });

  it("returns null when no hubsites are found", async () => {
    vi.mocked(mockSharepoint.hubSites).mockResolvedValue([]);

    const result = await fetchHubsites(mockSharepoint, mockGraph);
    expect(result).toBeNull();
  });

  it("returns null when graph is not provided", async () => {
    vi.mocked(mockSharepoint.hubSites).mockResolvedValue(mockHubsites);

    const result = await fetchHubsites(mockSharepoint, undefined as unknown as GraphFI);
    expect(result).toBeNull();
  });

  it("returns null when all hubsites fail validation", async () => {
    vi.mocked(mockSharepoint.hubSites).mockResolvedValue(mockHubsites);

    const result = await fetchHubsites(mockSharepoint, mockGraph);
    expect(result).toBeNull();
  });

  it("throws error when hubSites() fails", async () => {
    const errorMessage = "Failed to fetch hubsites";
    vi.mocked(mockSharepoint.hubSites).mockRejectedValue(new Error(errorMessage));

    await expect(fetchHubsites(mockSharepoint, mockGraph)).rejects.toThrow("Failed to fetch hubsites");
  });

  describe("checkHubsiteAccess", () => {
    let mockGraph: GraphFI;
    let mockHubsite: IHubSiteInfo;
    let mockSite: Site;
  
    beforeEach(() => {
      mockGraph = {
        sites: {
          getById: vi.fn().mockImplementation((id: string) => {
            const executor = (() => Promise.resolve(mockSite)) as unknown as ISite;
            return executor;
          }),
        },
      } as unknown as GraphFI;

      mockHubsite = {
        ID: "test-site-id",
        Title: "Test Hubsite",
        SiteUrl: "https://test.sharepoint.com/sites/hub",
      } as IHubSiteInfo;
  
      mockSite = {
        id: "test-site-id",
        displayName: "Test Site",
        webUrl: "https://test.sharepoint.com/sites/hub",
      };
  
      vi.spyOn(console, "warn").mockImplementation(() => {});
    });
  
    it("returns the site when access is granted", async () => {
      const result = await checkHubsiteAccess(mockHubsite, mockGraph);
  
      expect(result).toEqual(mockSite);
      expect(mockGraph.sites.getById).toHaveBeenCalledWith(mockHubsite.ID);
      expect(console.warn).not.toHaveBeenCalled();
    });
  
    it("returns null when access is denied", async () => {
      mockGraph.sites.getById = vi.fn().mockImplementation(() => {
        const executor = (() => Promise.reject(new Error("Access Denied"))) as unknown as ISite;
        return executor;
      });
  
      const result = await checkHubsiteAccess(mockHubsite, mockGraph);
  
      expect(result).toBeNull();
      expect(mockGraph.sites.getById).toHaveBeenCalledWith(mockHubsite.ID);
      expect(console.warn).toHaveBeenCalledWith(
        "Graph access failed for hubsite Test Hubsite: Access Denied"
      );
    });
  
    it("returns null and handles non-Error objects in catch", async () => {
      mockGraph.sites.getById = vi.fn().mockImplementation(() => {
        const executor = (() => Promise.reject("Unknown error object")) as unknown as ISite;
        return executor;
      });
  
      const result = await checkHubsiteAccess(mockHubsite, mockGraph);
  
      expect(result).toBeNull();
      expect(mockGraph.sites.getById).toHaveBeenCalledWith(mockHubsite.ID);
      expect(console.warn).toHaveBeenCalledWith(
        "Graph access failed for hubsite Test Hubsite: Unknown error"
      );
    });
  
    it("handles undefined graph client", async () => {
      const result = await checkHubsiteAccess(
        mockHubsite,
        undefined as unknown as GraphFI
      );
  
      expect(result).toBeNull();
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining("Graph access failed for hubsite Test Hubsite")
      );
    });
  });
});