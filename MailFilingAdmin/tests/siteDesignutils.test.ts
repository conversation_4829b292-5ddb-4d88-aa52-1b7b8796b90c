import { describe, it, expect } from "vitest";
import { Tenancy } from "../src/types";
import {
  getSiteDesignName,
  getFallbackSiteDesignName,
} from "../src/utils/siteDesignUtils";

describe("Site Design Utils", () => {
  it("returns correct design name for CMap tenancy", () => {
    expect(getSiteDesignName(Tenancy.CMap)).toBe("CMap Mail - Project");
    expect(getFallbackSiteDesignName(Tenancy.CMap)).toBe(
      "AtveroMail - Project"
    );
  });

  it("returns correct design name for Atvero tenancy", () => {
    expect(getSiteDesignName(Tenancy.Atvero)).toBe("AtveroMail - Project");
    expect(getFallbackSiteDesignName(Tenancy.Atvero)).toBe(
      "CMap Mail - Project"
    );
  });
});
