import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import basicSsl from "@vitejs/plugin-basic-ssl";
import { nodePolyfills } from "vite-plugin-node-polyfills";
import { viteStaticCopy } from "vite-plugin-static-copy";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    basicSsl(),
    nodePolyfills(),
    viteStaticCopy({
      targets: [
        {
          src: "./staticwebapp.config.json",
          dest: "./", // 2️⃣
        },
        {
          src: "./assets",
          dest: "./assets",
        },
      ],
    }),
  ],
  server: {
    host: "localhost",
    port: 3002,
  },
  esbuild: {
    target: "esnext",
  },
});
