{"name": "mailfilingadmin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env NODE_ENV=development npm run dev-config && vite", "build": "cross-env NODE_ENV=production tsc && vite build", "build:prep": "cross-env NODE_ENV=production npm run dev-config", "dev-config": "cp config/dev.json config.json", "test-config": "cp config/test.json config.json", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "npm run test-config && vitest", "coverage": "vitest run --coverage", "security-scan": "snyk test", "check:formatting": "npx prettier 'src/**/*.{js,ts,tsx,jsx}' --check"}, "dependencies": {"@azure/msal-react": "^2.1.1", "@fluentui/react-components": "^9.54.13", "@fluentui/react-icons": "^2.0.258", "@fluentui/react-list-preview": "^0.3.9", "@pnp/common": "^2.15.0", "@pnp/graph": "^4.2.0", "@pnp/logging": "^4.2.0", "@pnp/msaljsclient": "^4.2.0", "@pnp/odata": "^2.15.0", "@pnp/sp": "^4.5.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "12.1.5", "@testing-library/user-event": "^14.5.2", "@types/lodash": "^4.17.9", "@vitejs/plugin-basic-ssl": "^1.1.0", "lodash": "^4.17.21", "react": "^17.0.1", "react-dom": "^17.0.1", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.10", "react-window-infinite-loader": "^1.0.9"}, "devDependencies": {"@types/node": "^22.3.0", "@types/react": "^17", "@types/react-dom": "^17", "@types/react-window-infinite-loader": "^1.0.9", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.0.5", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-promise": "^7.1.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "install": "^0.13.0", "jsdom": "^24.1.1", "npm": "^11.0.0", "prettier": "3.5.3", "typescript": "^4.7.0", "vite": "^5.3.4", "vite-plugin-node-polyfills": "^0.22.0", "vite-plugin-static-copy": "^1.0.6", "vitest": "^2.0.5"}}