# MailFilingAdmin

MailFilingAdmin is the admin interface for the CMap Mail system, built with React and TypeScript using Vite.

## Prerequisites

- Node.js (version 20.12.1 or later recommended)
- npm (comes with Node.js)

## Getting Started

1. Copy `.env.example` to `.env` for local development

2. Install dependencies:
   `npm install`

3. Start the development server:
   `npm run dev`

## Available Scripts

In the project directory, you can run:

- `npm run dev`: Runs the app in development mode
- `npm run build`: Builds the app for production
- `npm run lint`: Lints the source files
- `npm test`: Runs the test suite
- `npm run test:coverage`: Runs tests with coverage report
- `npm run preview`: Previews the production build locally
- `npm run security-scan`: Runs a Snyk security scan

## Project Structure

- `src/`: Source files
- `public/`: Static assets
- `tests/`: Test files

## Writing Tests

We use Vitest for unit and integration testing. Test files should be placed in the `tests/` directory and named with a `.test.tsx` or `.test.ts` extension.

Example of a test file structure:

```
tests/
├── App.test.tsx
└── components/
└── Button.test.tsx
```

## Testing

This project uses Vitest for unit and component testing. Run tests with:
`npm test`

## Test Coverage

To generate a test coverage report:
`npm run test:coverage`
This will create a coverage report and display a summary in the console. A detailed report will be generated in the `coverage/` directory.

## Building for Production

To create a production build:
`npm run build`
The build artifacts will be stored in the `dist/` directory.

## Security

We use Snyk for vulnerability scanning. To run a security scan:
`npm run security-scan`
Note: You'll need to set up a Snyk account and authenticate locally to use this feature.

## TypeScript Configuration

This project is configured to work with SharePoint SPFx projects. The TypeScript version is set to 4.5 for compatibility.

## Continuous Integration

This project is part of a monorepo that uses GitHub Actions for CI/CD. The workflows run tests, build the project, and perform security scans on pull requests and pushes to the main branch.

## Contributing

1. Create a new branch for your feature or bug fix
2. Make your changes
3. Push your branch and create a pull request
4. Ensure all checks pass in the pull request
