import React, { useEffect, useState } from "react";
import {
  FluentProvider,
  webLightTheme,
  Spinner,
  makeStyles,
  shorthands,
} from "@fluentui/react-components";
import { MsalAuthenticationTemplate, MsalProvider } from "@azure/msal-react";
import { InteractionType } from "@azure/msal-browser";
import { useGraph } from "./services/useGraph";
import { useSharePointApi } from "./services/useSharePoint";
import { msalInstance } from "./services/useSharePoint";
import App from "./App";
import { AtveroLogo } from "./assets/AtveroLogo";
import CMapLogo from "./assets/CMap PIM & CMap Mail/CMap Mail/Black_Text/SVG/CMap_Mail_black_text_svg.svg";
import { Hubsite, Tenancy } from "./types";
import { adapterFactory, Adapters } from "./adapters/AdapterFactory";
import { detectTenancy } from "./services/TenancyService";

const useStyles = makeStyles({
  loginContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100vh",
  },
  loaderContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: "100vh",
    flexDirection: "column",
    ...shorthands.gap("10px"),
  },
  errorHeading: {
    marginBottom: "16px",
  },
  errorMessage: {
    marginBottom: "24px",
  },
  logo: {
    width: "169px",
  },
});

const AuthenticatedContent: React.FC<{
  adapter: Adapters;
  tenancy: Tenancy;
  changeTenant: (huburl: string) => Promise<void>;
}> = ({ adapter, tenancy, changeTenant }) => {
  const styles = useStyles();
  const [hubsites, setHubsites] = useState<Hubsite[] | undefined>();
  const [selectedHubsite, setSelectedHubsite] = useState<Hubsite | undefined>();
  const [hubsiteError, setHubsiteError] = useState<string | undefined>();
  const [isHubsiteLoading, setIsHubsiteLoading] = useState(true);

  const switchHubsite = (selectedHubsite: Hubsite | undefined) => {
    console.log("Selecting hubsite", selectedHubsite?.url);
    if (selectedHubsite) {
      const host = "https://" + new URL(selectedHubsite.url).host;
      console.log(
        "would change sharepointhostname to ",
        adapter.legacyAdapter.getSharepointHostName(),
        host
      );
      changeTenant(host);
      adapter.legacyAdapter.setSharepointHostName(host);
    }
    setSelectedHubsite(selectedHubsite);
  };

  useEffect(() => {
    const fetchHubsites = async () => {
      try {
        const hubsiteResult = await adapter.backendAdapter.getHubsites();

        if (hubsiteResult.error) {
          setHubsiteError(hubsiteResult.error);
          setHubsites(undefined);
          setSelectedHubsite(undefined);
        } else if (
          !hubsiteResult.hubsites ||
          hubsiteResult.hubsites.length === 0
        ) {
          setHubsiteError("No hubsites available");
          setHubsites(undefined);
          setSelectedHubsite(undefined);
        } else {
          setHubsiteError(undefined);
          setHubsites(hubsiteResult.hubsites);

          if (hubsiteResult.hubsites.length === 1) {
            setSelectedHubsite(hubsiteResult.hubsites[0]);
          }
        }
      } catch (error) {
        setHubsiteError("Failed to fetch hubsites");
      } finally {
        setIsHubsiteLoading(false);
      }
    };
    fetchHubsites();
  }, [adapter]);

  if (isHubsiteLoading) {
    return (
      <div className={styles.loaderContainer}>
        {tenancy === Tenancy.CMap ? (
          <img src={CMapLogo} alt="CMap Mail" className={styles.logo} />
        ) : (
          <AtveroLogo />
        )}
        <Spinner size="tiny" label="Fetching your hubsites..." />
      </div>
    );
  }
  return (
    <App
      adapter={adapter}
      hubsites={hubsites}
      selectedHubsite={selectedHubsite}
      hubsiteError={hubsiteError}
      onHubsiteSelect={switchHubsite}
      tenancy={tenancy}
    />
  );
};

const AppInitializer: React.FC = () => {
  const styles = useStyles();
  const [isLoading, setIsLoading] = useState(true);
  const [adapter, setAdapter] = useState<Adapters>();
  const { graph, loginError, manualLogin } = useGraph();
  const { sharepoint, sharepointHostName, changeTenant } =
    useSharePointApi(graph);
  const [tenancy, _setTenancy] = useState<Tenancy>(() => detectTenancy());

  useEffect(() => {
    const initializeApp = async () => {
      if (graph && sharepoint && sharepointHostName) {
        const adapter = await adapterFactory(
          graph,
          sharepoint,
          sharepointHostName
        );
        setAdapter(adapter);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, [graph, sharepoint]);

  if (loginError) {
    return (
      <div className={styles.loginContainer}>
        <h2 className={styles.errorHeading}>Login Failed</h2>
        <p className={styles.errorMessage}>
          {tenancy === Tenancy.CMap
            ? "Unable to load CMap Mail: Admin. Please try again."
            : "Unable to load Atvero Mail: Admin. Please try again."}
        </p>
        <button onClick={manualLogin}>Login</button>
      </div>
    );
  }

  if (isLoading || !adapter) {
    return (
      <div className={styles.loaderContainer}>
        {tenancy === Tenancy.CMap ? (
          <img src={CMapLogo} alt="CMap Mail" className={styles.logo} />
        ) : (
          <AtveroLogo />
        )}
        <Spinner size="tiny" label="Loading Atvero Mail Admin..." />
      </div>
    );
  }

  return (
    <AuthenticatedContent
      adapter={adapter}
      tenancy={tenancy}
      changeTenant={changeTenant}
    />
  );
};

const Root: React.FC = () => {
  return (
    <React.StrictMode>
      <MsalProvider instance={msalInstance}>
        <MsalAuthenticationTemplate interactionType={InteractionType.Redirect}>
          <FluentProvider theme={webLightTheme}>
            <AppInitializer />
          </FluentProvider>
        </MsalAuthenticationTemplate>
      </MsalProvider>
    </React.StrictMode>
  );
};

export default Root;
