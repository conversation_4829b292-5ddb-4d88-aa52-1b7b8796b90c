export enum SortDirection {
  Ascending = "ascending",
  Descending = "descending",
}

export enum Environment {
  Development = "development",
  Production = "production",
}

export enum CanAccessConfidentialEmails {
  Yes = "yes",
  No = "no",
  DontKnow = "dont_know",
}

export type SortableProjectKeys = "code" | "title";

export enum CMapProjectStatus {
  NotInCmap,
  CMapNotCreated,
  CMapCreated,
}

export type Project = {
  code: string;
  title: string | undefined;
  id: string;
  cMapId: number | undefined;
  favorite: boolean;
  cmapProject: CMapProjectStatus;
  webUrl: string;
};

export enum Tenancy {
  Atvero = "atvero",
  CMap = "cmap",
}

export type ProjectResponse = {
  projects: Project[];
  nextLink: string | undefined;
  page: number;
  tableSize: number;
  sortField: string | undefined;
  sortDirection: SortDirection;
  moreResultsAvailable: boolean | undefined;
  total?: number;
};

export type Column = {
  columnKey: string;
  label: string;
  sortable: boolean;
};

export interface User {
  id: string;
  loginName: string;
  name: string;
  email: string;
  canAccessConfidentialEmails?: CanAccessConfidentialEmails;
}

export type CreateProjectPromise = {
  success: boolean;
  message: string;
  details?: string;
  projectURL?: string;
};

export type CreateProjectFunction = (
  projectTitle: string,
  projectCode: string,
  cmapProjectId: number | undefined,
  onProgressUpdate: (progress: number, message: string) => void,
  selectedProject: Project | undefined,
  cancelToken?: { cancelled: boolean }
) => Promise<CreateProjectPromise>;

export type EnableAtveroMailFunction = (
  projects: Project[],
  onProgressUpdate: (progress: number, message: string) => void
) => Promise<{
  success: boolean;
  message: string;
  details?: string;
}>;

export type RepartProjectFunction = (
  project: Project,
  onProgressUpdate: (progress: number, message: string) => void
) => Promise<{
  success: boolean;
  message: string;
  details?: string;
}>;

export interface ICMapProject {
  Id: number;
  Code: string;
  Title: string | undefined;
}

export interface IProjectMessageStatus {
  projectCode: string;
  projectTitle: string;
  status: "success" | "error" | "exists";
  details?: string;
  inProgress?: boolean;
}

export interface IMultiStatusMessageProps {
  successCount: number;
  failureCount: number;
  existsCount: number;
  projectStatuses: IProjectMessageStatus[];
  currentProject?: string;
}

export type Hubsite = {
  displayName: string;
  name: string;
  url: string;
};

export enum SiteGroupTypes {
  Owner,
  Member,
}
