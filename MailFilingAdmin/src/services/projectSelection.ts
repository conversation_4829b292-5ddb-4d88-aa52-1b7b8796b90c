import { Project } from "../types";

export const handleProjectSelect = (
  project: Project,
  setSelectedProjects: React.Dispatch<React.SetStateAction<Project[]>>
) => {
  setSelectedProjects((prevSelectedProjects) => {
    const isAlreadySelected = prevSelectedProjects.some((p) => {
      if (p.cMapId && project.cMapId) {
        return p.cMapId === project.cMapId;
      }
      return p.id === project.id;
    });

    if (isAlreadySelected) {
      return prevSelectedProjects.filter((p) => {
        if (p.cMapId && project.cMapId) {
          return p.cMapId !== project.cMapId;
        }
        return p.id !== project.id;
      });
    } else {
      return [...prevSelectedProjects, project];
    }
  });
};
