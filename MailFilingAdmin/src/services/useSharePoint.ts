/* v8 ignore next 135 */
import { useEffect, useState, useRef } from "react";
import { MSAL, MSALOptions } from "@pnp/msaljsclient";
import { ClientId, RedirectUri } from "../utils/Config";
import { spfi, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@pnp/sp";
import { getSharepointHostNameSingleGeo } from "./graphCalls";
import { PublicClientApplication } from "@azure/msal-browser";
import { GraphFI } from "@pnp/graph";

export const options: MSALOptions = {
  configuration: {
    auth: {
      authority: "https://login.microsoftonline.com/common",
      clientId: ClientId(),
      redirectUri: RedirectUri(),
    },
    cache: {
      claimsBasedCachingEnabled: true,
    },
  },
  authParams: {
    forceRefresh: false,
    scopes: [],
  },
};

export const msalInstance = new PublicClientApplication(options.configuration);

interface ISharePointApi {
  sharepoint: SPFI;
  sharepointHostName: string | undefined;
  changeTenant: (huburl: string) => Promise<void>;
}

interface ISharePointState {
  sharepoint: SPFI;
  sharepointHostName: string | undefined;
}

const REFRESH_INTERVAL = 45 * 60 * 1000; // 45 minutes in milliseconds
let sharepointInstance: SPFI = spfi();
let storedHostName: string | undefined;
let lastInitTime: number = 0;
let isInitializing = false;

export const useSharePointApi = (
  graph: GraphFI | undefined
): ISharePointApi => {
  const [state, setState] = useState<ISharePointState>({
    sharepoint: sharepointInstance,
    sharepointHostName: storedHostName,
  });

  const changeTenant = async (host: string) => {
    console.log("Change tenant for hubsite ", host);
    const updatedOptions = { ...options };
    updatedOptions.authParams.scopes = [`${host}/.default`];
    updatedOptions.authParams.forceRefresh = true;
    const now = Date.now();

    sharepointInstance = spfi(host).using(SPBrowser(), MSAL(updatedOptions));
    lastInitTime = now;

    setState({
      sharepoint: sharepointInstance,
      sharepointHostName: host,
    });
  };

  const timerRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    let isMounted = true;

    async function initializeSharePoint(force: boolean = false): Promise<void> {
      const now = Date.now();
      if (
        !force &&
        !isInitializing &&
        storedHostName &&
        sharepointInstance &&
        now - lastInitTime < REFRESH_INTERVAL
      ) {
        setState({
          sharepoint: sharepointInstance,
          sharepointHostName: storedHostName,
        });
        return;
      }

      if (!graph || isInitializing) return;

      try {
        isInitializing = true;
        const sharepointHost = await getSharepointHostNameSingleGeo(graph);

        console.log("sharepointHost", sharepointHost);

        if (!isMounted) return;

        if (sharepointHost) {
          storedHostName = sharepointHost;
          const sharepointUrl = `${sharepointHost}`;

          const updatedOptions = { ...options };
          updatedOptions.authParams.scopes = [`${sharepointUrl}/.default`];
          updatedOptions.authParams.forceRefresh = true;

          sharepointInstance = spfi(sharepointUrl).using(
            SPBrowser(),
            MSAL(updatedOptions)
          );
          lastInitTime = now;

          setState({
            sharepoint: sharepointInstance,
            sharepointHostName: storedHostName,
          });
        }
      } catch (error) {
        console.error("Failed to initialize SharePoint:", error);
        if (isMounted) {
          sharepointInstance = spfi();
          storedHostName = undefined;
          setState({
            sharepoint: sharepointInstance,
            sharepointHostName: storedHostName,
          });
        }
      } finally {
        isInitializing = false;
      }
    }

    // Initial initialization
    initializeSharePoint(false);

    // Setup refresh timer
    timerRef.current = setInterval(() => {
      console.log("Refreshing SharePoint instance");
      initializeSharePoint(true);
    }, REFRESH_INTERVAL);

    return () => {
      isMounted = false;
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [graph]);

  return {
    sharepoint: state.sharepoint,
    sharepointHostName: state.sharepointHostName,
    changeTenant,
  };
};

export const clearSharePointInstance = () => {
  sharepointInstance = spfi();
  storedHostName = undefined;
  isInitializing = false;
  lastInitTime = 0;
};
