import { <PERSON>FI } from "@pnp/sp";
import "@pnp/sp/site-users/web";
import { SiteGroupTypes, User } from "../types";
import { Web } from "@pnp/sp/webs";
import "@pnp/sp/profiles";
import { IPeoplePickerEntity } from "@pnp/sp/profiles";

export const searchUserGroups = async (
  sharepoint: SPFI,
  productName: string
): Promise<string[]> => {
  if (!sharepoint) {
    throw new Error("sharepoint instance not initialized");
  }
  try {
    const userGroup = await sharepoint.profiles.clientPeoplePickerSearchUser({
      AllowEmailAddresses: true,
      AllowMultipleEntities: false,
      MaximumEntitySuggestions: 10,
      QueryString: productName,
    });

    const mapperUserGroup = userGroup.map((group: IPeoplePickerEntity) => {
      return group.Key;
    });

    return mapperUserGroup;
  } catch (error) {
    console.error("Error searching users:", error);
    throw error;
  }
};

export const searchUsers = async (
  sharepoint: SPFI,
  searchTerm: string
): Promise<User[]> => {
  if (!sharepoint) {
    throw new Error("sharepoint instance not initialized");
  }
  try {
    const users = await sharepoint.profiles.clientPeoplePickerSearchUser({
      AllowEmailAddresses: true,
      AllowMultipleEntities: false,
      MaximumEntitySuggestions: 10,
      QueryString: searchTerm,
    });

    return users.slice(0, 10).map((user) => ({
      loginName: user.Key,
      id: user.Key,
      name: user.DisplayText,
      email: user.EntityData.Email ? user.EntityData.Email : "Group",
    }));
  } catch (error) {
    console.error("Error searching users:", error);
    throw error;
  }
};

export const addUserToSite = async (
  sharepoint: SPFI,
  user: User,
  projectUrl: string
): Promise<void> => {
  if (!sharepoint) {
    throw new Error("sharepoint instance not initialized");
  }
  try {
    const web = Web([sharepoint.web, projectUrl]);
    const memberGroup = await web.associatedMemberGroup();
    await web.siteGroups.getById(memberGroup.Id).users.add(user.loginName);
  } catch (error) {
    console.error("Error adding user to site:", error);
    throw error;
  }
};

export const addUserGroupToSite = async (
  sharepoint: SPFI,
  groups: string[],
  projectUrl: string,
  groupType: SiteGroupTypes
): Promise<void> => {
  if (!sharepoint) {
    throw new Error("sharepoint instance not initialized");
  }
  try {
    const web = Web([sharepoint.web, projectUrl]);

    let siteGroup;

    if (groupType === SiteGroupTypes.Member) {
      siteGroup = await web.associatedMemberGroup();
    } else if (groupType === SiteGroupTypes.Owner) {
      siteGroup = await web.associatedOwnerGroup();
    }

    if (siteGroup) {
      for (const group of groups) {
        await web.siteGroups.getById(siteGroup.Id).users.add(group);
      }
    }
  } catch (error) {
    console.error("Error adding user to site:", error);
    throw error;
  }
};

export const removeUserFromSite = async (
  sharepoint: SPFI,
  userLoginName: string,
  projectUrl: string
): Promise<void> => {
  if (!sharepoint) {
    throw new Error("sharepoint instance not initialized");
  }
  try {
    const web = Web([sharepoint.web, projectUrl]);
    const memberGroup = await web.associatedMemberGroup();
    const user = await sharepoint.web.ensureUser(userLoginName);
    await web.siteGroups
      .getById(memberGroup.Id)
      .users.removeByLoginName(user.LoginName);
  } catch (error) {
    console.error("Error removing user from site:", error);
    throw error;
  }
};
