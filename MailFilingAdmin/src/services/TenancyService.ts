import { Tenancy } from "../types";
import { Product } from "../utils/Config";

export const detectTenancy = (): Tenancy => {
  switch (Product()?.toLowerCase()) {
    case "cmap":
      return Tenancy.CMap;
    case "atvero":
      return Tenancy.Atvero;
    default:
      return Tenancy.CMap; // Default to Atvero for safety
  }
};

export const productName = (): string => {
  const tenancy = detectTenancy();
  switch (tenancy) {
    case Tenancy.CMap:
      return "CMap Mail";
    case Tenancy.Atvero:
      return "Atvero Mail";
    default:
      return "Atvero Mail";
  }
};
