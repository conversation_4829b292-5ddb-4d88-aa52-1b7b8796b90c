import { User, CanAccessConfidentialEmails } from "../types";
import "@pnp/sp/site-groups/web";
import "@pnp/sp/site-users/web";
import { ISharepointAdapter } from "../adapters/ISharepointAdapter";

export const fetchProjectUsers = async (
  sharepointAdapter: ISharepointAdapter,
  projectUrl: string
): Promise<User[]> => {
  let members: User[] | undefined;
  let confidentialMembers: User[] | undefined;

  try {
    members = await sharepointAdapter.getMemberUsers(projectUrl);
    if (members === undefined) {
      throw new Error(
        "Failed to fetch project members. Please ensure youre a member of this project."
      );
    }
  } catch (error) {
    console.error("Failed to fetch member users:", error);
    throw error;
  }

  try {
    confidentialMembers =
      await sharepointAdapter.getConfidentialUsers(projectUrl);
  } catch (error) {
    console.error("Failed to fetch confidential users:", error);
    confidentialMembers = undefined;
  }

  if (confidentialMembers === undefined) {
    return members.map((member) => ({
      ...member,
      canAccessConfidentialEmails: CanAccessConfidentialEmails.DontKnow,
    }));
  }

  const confidentialUserIds = new Set(
    confidentialMembers.map((user) => user.id)
  );

  return members.map((member) => ({
    ...member,
    canAccessConfidentialEmails: confidentialUserIds.has(member.id)
      ? CanAccessConfidentialEmails.Yes
      : CanAccessConfidentialEmails.No,
  }));
};
