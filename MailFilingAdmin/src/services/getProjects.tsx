import "@pnp/graph/search";
import "@pnp/graph/list-item";
import "@pnp/graph/lists";
import { IRenderListDataParameters } from "@pnp/sp/lists";
import {
  CMapProjectStatus,
  Hubsite,
  ICMapProject,
  Project,
  ProjectResponse,
  SortDirection,
  SortableProjectKeys,
} from "../types";

import { atveroMailBackend } from "./backendCall/backendCall";
import { SPFI } from "@pnp/sp";
import { Web } from "@pnp/sp/webs";
import { extractSharepointDomain } from "../utils/urlUtils";
import { sortProjects } from "../utils/sortProjects";

interface IRenderListDataAsStreamProps {
  ID: string;
  ProjectCode: string;
  Description: string;
  ATVImportedSourceID: string | undefined;
}

export const getAtveroProjectList = async (
  sharepoint: SPFI,
  hubsite: Hubsite,
  tableSize: number,
  page: number,
  sortColumn?: SortableProjectKeys,
  sortDirection?: "asc" | "desc",
  searchString?: string
): Promise<ProjectResponse> => {
  let whereClause = "";
  if (searchString) {
    whereClause = `
      <Or>
        <Contains>
          <FieldRef Name='ProjectCode'/>
            <Value Type='Text'>${searchString}</Value>
        </Contains>
        <Contains>
          <FieldRef Name='Description'/>
            <Value Type='Text'>${searchString}</Value>
        </Contains>
      </Or>
    `;
  }

  let orderByClause = "";

  if (sortColumn) {
    const ascending = sortDirection === "asc" ? "True" : "False";
    const fieldName = sortColumn === "code" ? "ProjectCode" : "Description";
    orderByClause = `<OrderBy><FieldRef Name="${fieldName}" Ascending="${ascending}"/></OrderBy>`;
  }

  if (sharepoint) {
    const viewXml = `<View Scope="RecursiveAll">
      <Query>
        <Where>${whereClause}</Where>
        ${orderByClause}
      </Query>
      <ViewFields>
        <FieldRef Name="ProjectCode"/>
        <FieldRef Name="Description"/>
        <FieldRef Name="ATVImportedSourceID"/>
      </ViewFields>
      <RowLimit Paged="TRUE">${tableSize}</RowLimit>
    </View>`;

    const renderListDataParams: IRenderListDataParameters = {
      ViewXml: viewXml,
      Paging: "Paged=TRUE",
    };

    const hubsiteWeb = Web([sharepoint.web, hubsite?.url]);

    const r = await hubsiteWeb.lists
      .getByTitle("Projects")
      .renderListDataAsStream(renderListDataParams);

    if (!hubsite?.url) {
      throw new Error("Hubsite webUrl is undefined");
    }
    const hubsiteWebUrl: string = hubsite.url;

    const projects = r.Row.map((project: IRenderListDataAsStreamProps) => ({
      code: project.ProjectCode,
      title: project.Description,
      id: project.ID,
      favorite: false,
      cmapProject: CMapProjectStatus.NotInCmap,
      webUrl: `https://${extractSharepointDomain(hubsiteWebUrl)}/sites/${project.ProjectCode}`,
      cMapId: undefined,
    }));

    return {
      nextLink: undefined,
      projects,
      page,
      tableSize,
      sortField: sortColumn,
      sortDirection:
        sortDirection === "desc"
          ? SortDirection.Descending
          : SortDirection.Ascending,
      moreResultsAvailable: false,
      total: 0,
    };
  }

  return {
    nextLink: undefined,
    projects: [],
    page,
    tableSize,
    sortField: sortColumn ?? "",
    sortDirection:
      sortDirection === "desc"
        ? SortDirection.Descending
        : SortDirection.Ascending,
    moreResultsAvailable: false,
    total: 0,
  };
};

export const getCMapProjectList = async (
  searchTerm: string | undefined,
  sortField?: SortableProjectKeys,
  sortDirection?: "asc" | "desc"
): Promise<ProjectResponse> => {
  const response = (await atveroMailBackend(
    `/api/CmapProjects?search=${searchTerm ?? ""}`,
    "POST"
  )) as ICMapProject[] | undefined;

  if (!response) {
    throw new Error("Failed to fetch projects");
  }

  try {
    const projects: Project[] = response.map((cmapProject) => ({
      code: cmapProject.Code,
      title: cmapProject.Title,
      id: "",
      cMapId: cmapProject.Id,
      favorite: false,
      cmapProject: CMapProjectStatus.CMapNotCreated,
      webUrl: "",
    }));

    const sortedProjects = sortProjects(projects, sortField, sortDirection);

    return {
      projects: sortedProjects,
      nextLink: undefined,
      page: 0,
      tableSize: 0,
      sortField: sortField ?? "",
      sortDirection:
        sortDirection === "desc"
          ? SortDirection.Descending
          : SortDirection.Ascending,
      moreResultsAvailable: false,
    };
  } catch (error) {
    console.error(`Failed to fetch CMap projects. Error: ${error}`);
    return {
      projects: [],
      nextLink: undefined,
      page: 0,
      tableSize: 0,
      sortField: "",
      sortDirection: SortDirection.Ascending,
      moreResultsAvailable: false,
    };
  }
};

export const getProjectList = async (
  sharepoint: SPFI,
  hubsite: Hubsite,
  tableSize: number,
  page: number,
  orderColumn?: SortableProjectKeys,
  orderDirection?: "asc" | "desc",
  searchString?: string,
  showAtveroProjects?: boolean
): Promise<ProjectResponse> => {
  const projectResponse = await getAtveroProjectList(
    sharepoint,
    hubsite,
    tableSize,
    page,
    orderColumn,
    orderDirection,
    searchString
  );

  try {
    const cmapProjectsResponse = await getCMapProjectList(
      searchString,
      orderColumn,
      orderDirection
    );

    const atveroProjects = projectResponse.projects;
    const cmapProjects = cmapProjectsResponse.projects;

    if (cmapProjects && cmapProjects.length !== 0) {
      const projects = cmapProjects.map((cmapProject) => {
        const atveroProject = atveroProjects.find(
          (atveroProject) => atveroProject.code === cmapProject.code
        );

        return {
          ...cmapProject,
          id: atveroProject?.id || "",
          favorite: atveroProject?.favorite || false,
          cmapProject: atveroProject
            ? CMapProjectStatus.CMapCreated
            : CMapProjectStatus.CMapNotCreated,
          webUrl: atveroProject?.webUrl || "",
        };
      });

      if (showAtveroProjects) {
        const filteredProjects = projects.filter(
          (project) => !project.webUrl || project.webUrl === ""
        );
        return { ...projectResponse, projects: filteredProjects };
      }

      return { ...cmapProjectsResponse, projects };
    }
  } catch (error) {
    console.warn(`Failed to fetch CMap projects. Error: ${error}`);
  }

  return projectResponse;
};
