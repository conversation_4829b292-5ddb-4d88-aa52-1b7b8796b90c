import { makeStyles, tokens } from "@fluentui/react-components";

type MessageType = "error" | "success" | "deleteSuccess";

const useStyles = makeStyles({
  errorMessage: {
    color: tokens.colorPaletteRedForeground1,
  },
  successMessage: {
    color: tokens.colorPaletteGreenForeground1,
  },
  deleteSuccessMessage: {
    color: tokens.colorPaletteRedForeground1,
  },
});

export type StylesType = ReturnType<typeof useStyles>;

export const getMessageClassName = (
  type: MessageType,
  styles: StylesType
): string => {
  const classNameMap: Record<MessageType, keyof StylesType> = {
    error: "errorMessage",
    success: "successMessage",
    deleteSuccess: "deleteSuccessMessage",
  };
  return styles[classNameMap[type]] || styles.successMessage;
};

export { useStyles };
