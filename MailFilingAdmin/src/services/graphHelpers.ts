import { <PERSON>raph<PERSON> } from "@pnp/graph";
import { Site } from "@microsoft/microsoft-graph-types";
import { IHubSiteInfo } from "@pnp/sp/hubsites";
import { SPFI } from "@pnp/sp";
import { Web } from "@pnp/sp/webs";
import "@pnp/sp/lists";
import "@pnp/graph/sites";
import "@pnp/sp/hubsites";

export const getSiteGuidFromGraphGuid = (
  graphSiteId: string | undefined
): string => {
  const parts = graphSiteId?.split(",");
  const newParts = parts?.map((part) => part.trim());
  if (newParts?.length == 3) {
    return newParts[1];
  } else {
    return "";
  }
};

export async function fetchShareRootURL(graph: GraphFI) {
  try {
    return await getSharePointRootUrl(graph);
  } catch (error) {
    console.error("Error fetching sharepoint root url: ", error);
    return "";
  }
}

export const getSharePointRootUrl = async (graph: GraphFI) => {
  const root = await graph.sites.root();
  const rootUrl = new URL(root.webUrl as string);
  return rootUrl.hostname;
};

export async function fetchHubsites(
  sharepoint: SPFI,
  graph: GraphFI
): Promise<Site | null> {
  try {
    const hubsites = await sharepoint.hubSites();

    if (!hubsites?.length) {
      console.log("No hubsites found");
      return null;
    }

    if (!graph) {
      console.warn("Graph instance not provided, skipping access check");
      return null;
    }

    for (const hubsite of hubsites) {
      const validSite = await validateHubsite(hubsite, graph);
      if (validSite) {
        // Currently returns the first valid site but we will need to
        // change this to a collection of valid sites the user has access to
        // and then allow the user to select from them.
        return validSite;
      }
    }

    console.log("No accessible hubsite found with 'Filed Email Content' list");
    return null;
  } catch (error) {
    console.error("Error fetching hubsites:", error);
    throw new Error("Failed to fetch hubsites");
  }
}

async function checkHubsiteList(hubsite: IHubSiteInfo): Promise<boolean> {
  try {
    const hubWeb = Web(hubsite.SiteUrl);
    hubWeb.lists.getByTitle("Filed Email Content");
    return true;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.warn(
      `List check failed for hubsite ${hubsite.Title}: ${errorMessage}`
    );
    return false;
  }
}

export async function checkHubsiteAccess(
  hubsite: IHubSiteInfo,
  graph: GraphFI
): Promise<Site | null> {
  try {
    return await graph.sites.getById(hubsite.ID)();
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.warn(
      `Graph access failed for hubsite ${hubsite.Title}: ${errorMessage}`
    );
    return null;
  }
}

async function validateHubsite(
  hubsite: IHubSiteInfo,
  graph: GraphFI
): Promise<Site | null> {
  const hasValidList = await checkHubsiteList(hubsite);
  if (!hasValidList) {
    return null;
  }

  return await checkHubsiteAccess(hubsite, graph);
}
