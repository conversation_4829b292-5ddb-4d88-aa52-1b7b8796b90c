import { <PERSON><PERSON> } from "@pnp/sp";
import { Web } from "@pnp/sp/webs";
import "@pnp/sp/site-groups/web";
import "@pnp/sp/site-users/web";
import { User } from "../types";

interface ToggleUserToConfidentialGroupParams {
  sharepoint: SPFI;
  projectUrl: string;
  groupKeys?: string[];
  user?: User;
  canAccessConfidentialEmails: boolean;
}

export const toggleUserToConfidentialGroup = async ({
  sharepoint,
  projectUrl,
  groupKeys,
  user,
  canAccessConfidentialEmails,
}: ToggleUserToConfidentialGroupParams): Promise<void> => {
  /* v8 ignore next 25 */
  // Ignoring this due to it only using sharepoint API and not any other testable logic
  const web = Web([sharepoint.web, projectUrl]);
  try {
    const confidentialGroup =
      await web.siteGroups.getByName("Confidential Users")();

    if (canAccessConfidentialEmails) {
      // Add to confidential group
      if (user) {
        // If adding a user
        await web.siteGroups
          .getById(confidentialGroup.Id)
          .users.add(user.loginName);
      } else if (groupKeys && groupKeys.length > 0) {
        // If adding site group
        for (const groupKey of groupKeys) {
          await web.siteGroups
            .getById(confidentialGroup.Id)
            .users.add(groupKey);
        }
      }
    } else {
      // Remove from confidential group
      if (user) {
        // If removing a user
        await web.siteGroups
          .getById(confidentialGroup.Id)
          .users.removeByLoginName(user.loginName);
      } else if (groupKeys && groupKeys.length > 0) {
        // If removing a site group
        for (const groupKey of groupKeys) {
          await web.siteGroups
            .getById(confidentialGroup.Id)
            .users.removeByLoginName(groupKey);
        }
      }
    }
  } catch (error) {
    if (error && typeof error === "object" && "message" in error) {
      const errorMessage = (error as Error).message;
      if (errorMessage.includes("Group cannot be found")) {
        console.error("Confidential Users group does not exist in this site");
      } else {
        console.error(
          "Error toggling user in Confidential Users group:",
          errorMessage
        );
      }
    } else {
      console.error(
        "An unknown error occurred while toggling user in Confidential Users group"
      );
    }
    throw error;
  }
};
