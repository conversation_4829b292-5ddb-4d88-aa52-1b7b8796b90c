import { Site } from "@microsoft/microsoft-graph-types";
import { GraphFI } from "@pnp/graph";

export const getHubsiteFromPath = async (
  graph: GraphFI,
  sharePointTenant: string,
  hubsitePath: string
): Promise<Site> => {
  // this should work multi-geo if the root site is on the main tenant
  if ("/" === hubsitePath) {
    return await graph.sites.root();
  } else {
    const hubsite = await graph.sites.getByUrl(sharePointTenant, hubsitePath);
    return hubsite();
  }
};
