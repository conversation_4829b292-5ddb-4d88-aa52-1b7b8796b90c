import { GraphFI } from "@pnp/graph";
import "@pnp/graph/groups";

export async function checkIfUserIsAdmin(graph: GraphFI) {
  try {
    console.log("Checking if user is an admin");

    // future proofing product name change
    const atveroGroups = await graph.groups.filter(
      "startswith(displayName, 'Atvero Mail Admins')"
    )();

    const cmapGroups = await graph.groups.filter(
      "startswith(displayName, 'CMAP Mail Admins')"
    )();

    const allGroups = atveroGroups.concat(cmapGroups);

    const groupIds = allGroups.map((grp) => grp.id);

    const filteredGroups = groupIds.filter((g) => g !== undefined) as string[];

    const userGroups = await graph.me.checkMemberGroups(filteredGroups);

    if (userGroups.length > 0) {
      console.log("Is an admin");
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error checking admin level", error);
    return false;
  }
}
