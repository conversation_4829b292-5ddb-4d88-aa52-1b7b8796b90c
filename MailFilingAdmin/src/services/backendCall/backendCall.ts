import { BackendScope, BackendUrl } from "../../utils/Config";
import { msalInstance } from "../useSharePoint";
import { IApiRequest } from "./IApiRequest";

const getApiUrl = (apiCall: string): string => {
  return `${BackendUrl()}${apiCall}`;
};

export const getApiRequest = (apiCall: string): IApiRequest => {
  return {
    url: getApiUrl(apiCall),
    scopes: [BackendScope()],
  };
};

export async function bearerToken(apiCall: string): Promise<string> {
  const account = msalInstance.getActiveAccount();
  if (!account) {
    throw Error(
      "No active account! Verify a user has been signed in and setActiveAccount has been called."
    );
  }
  const apiRequest = getApiRequest(apiCall);

  const response = await msalInstance.acquireTokenSilent({
    ...apiRequest,
    account: account,
  });

  return response.accessToken;
}

export async function atveroMailBackend(
  apiCall: string,
  method: string
): Promise<unknown> {
  const token = await bearerToken(apiCall);

  const headers = new Headers();
  const bearer = `Bearer ${token}`;

  headers.append("Authorization", bearer);

  const url = getApiUrl(apiCall);

  const options = {
    method: method,
    headers: headers,
  };

  try {
    const response = await fetch(url, options);
    return await response.json();
  } catch (error) {
    return undefined;
  }
}

export async function atveroMailBackendPost(
  apiCall: string,
  body: string
): Promise<unknown> {
  const token = await bearerToken(apiCall);

  const headers = new Headers();
  const bearer = `Bearer ${token}`;

  headers.append("Authorization", bearer);

  const url = getApiUrl(apiCall);

  const options = {
    method: "POST",
    headers: headers,
    body: body,
  };

  try {
    const response = await fetch(url, options);

    if (response.status >= 200 && response.status < 300)
      return await response.json();
    else return undefined;
  } catch (error) {
    console.error("atveroMailBackendPost failed");
    return undefined;
  }
}
