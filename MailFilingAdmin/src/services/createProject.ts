import { SPFI } from "@pnp/sp";
import { ISiteDesignInfo } from "@pnp/sp/site-designs/types";
import { Web } from "@pnp/sp/webs";
import { ISiteCreationResponse } from "@pnp/sp/sites/types";
import { IList } from "@pnp/sp/lists";
import "@pnp/sp/site-designs";
import "@pnp/sp/sites";
import "@pnp/sp/security/list";
import "@pnp/sp/site-users/web";
import "@pnp/sp/lists/web";
import "@pnp/sp/webs";
import "@pnp/sp/lists";
import "@pnp/sp/site-groups/web";
import "@pnp/sp/items";
import "@pnp/sp/hubsites/site";
import { ISiteGroupInfo } from "@pnp/sp/site-groups/types";
import { Hubsite, Project, SiteGroupTypes } from "../types";
import { ISiteUserInfo } from "@pnp/sp/site-users/types";
import { addUserGroupToSite, searchUserGroups } from "./getUsers";
import { toggleUserToConfidentialGroup } from "./toggleUserToConfidentialGroup";
import { GraphFI } from "@pnp/graph";
import { getHubsiteFromPath } from "./getHubsiteFromGraph";
import { extractSiteId } from "../utils/urlUtils";
import { IBackendAdapter } from "../adapters/IBackendAdapters";

const sleep = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const getProjectDesign = async (
  sharepoint: SPFI,
  siteDesignName: string,
  fallbackSiteDesignName: string
): Promise<ISiteDesignInfo | undefined> => {
  const allSiteDesigns = await sharepoint?.siteDesigns.getSiteDesigns();

  let siteDesign = allSiteDesigns?.filter((s) =>
    s.Title.startsWith(siteDesignName)
  )[0];

  // If not found and fallback is provided, try the fallback
  if (!siteDesign && fallbackSiteDesignName) {
    siteDesign = allSiteDesigns?.filter((s) =>
      s.Title.startsWith(fallbackSiteDesignName)
    )[0];
  }

  return siteDesign;
};

export const importUsers = async (
  sharepoint: SPFI,
  importProjectUrl: string,
  newProjectUrl: string
): Promise<boolean | string> => {
  try {
    const importProjectWeb = Web([sharepoint.web, importProjectUrl]);
    const newProjectWeb = Web([sharepoint.web, newProjectUrl]);
    const usersToImport = await importProjectWeb.associatedMemberGroup.users();
    const newProjectMembersGroup: ISiteGroupInfo =
      await newProjectWeb.associatedMemberGroup();
    for (const user of usersToImport) {
      try {
        const ensuredUser: ISiteUserInfo = await newProjectWeb.ensureUser(
          user.LoginName
        );
        await newProjectWeb.siteGroups
          .getById(newProjectMembersGroup.Id)
          .users.add(ensuredUser.LoginName);
      } catch (userError) {
        console.warn(
          `Failed to add user ${user.Title} (${user.LoginName}):`,
          userError
        );
        throw userError;
      }
    }
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
};

export const applySiteDesign = async (
  sharepoint: SPFI,
  projectUrl: string,
  siteDesignId: string,
  waitForCompletion: boolean
): Promise<boolean | string> => {
  const runs = await sharepoint.siteDesigns.getSiteDesignRun(projectUrl);

  if (runs.some((r) => r.SiteDesignID === siteDesignId)) {
    return "already applied";
  } else {
    try {
      const web = Web([sharepoint.web, projectUrl]);
      await web.addSiteDesignTask(siteDesignId);

      const task = await web.addSiteDesignTask(siteDesignId);
      if (waitForCompletion) {
        let complete = false;
        while (!complete) {
          try {
            const runningTask = await web.getSiteDesignRunStatus(task.ID);
            complete = !!runningTask;
          } catch (error) {
            //ignore this error
          }
          await sleep(2500);
        }
      }

      return true;
    } catch (error) {
      console.error(error);
      return false;
    }
  }
};

export const addProjectListRow = async (
  sharepoint: SPFI,
  hubsite: Hubsite,
  projectCode: string,
  projectTitle: string,
  cmapProjectId: number | undefined
): Promise<void> => {
  const hubWeb = Web([sharepoint.web, hubsite?.url]);

  const items = await hubWeb.lists
    .getByTitle("Projects")
    .items.filter(`ProjectCode eq '${projectCode}'`)();

  if (items.length > 0) {
    return;
  } else {
    // add an item to the list
    await hubWeb.lists.getByTitle("Projects").items.add({
      ProjectCode: projectCode,
      Title: projectCode,
      Description: projectTitle,
      ATVImportedSourceID: cmapProjectId?.toString(),
    });
  }
};

export const applyConfidentialUserPermissions = async (
  sharepoint: SPFI,
  newProjectUrl: string
): Promise<void> => {
  try {
    const web = Web([sharepoint.web, newProjectUrl]);

    // check the group doesn't already exist for th time we are making a project again

    let confidentialGroupInfo: ISiteGroupInfo | undefined = undefined;

    try {
      confidentialGroupInfo =
        await web.siteGroups.getByName("Confidential Users")();
    } catch (error) {
      // doesn't already exist, so carry on
    }

    if (confidentialGroupInfo === undefined || confidentialGroupInfo === null) {
      // need to create it

      confidentialGroupInfo = await web.siteGroups.add({
        Title: "Confidential Users",
        Description:
          "Group for users with access to see confidential documents",
      });
    }

    const listEnsureResults = await web.lists.ensure(
      "Confidential Filed Email Content"
    );
    const list: IList = listEnsureResults.list;
    await list.breakRoleInheritance();
    const defs = await web.roleDefinitions();
    //TODO: Change defs[3].Id to the actual Confidential filing permissions role def once it has been created.
    await list.roleAssignments.add(confidentialGroupInfo.Id, defs[3].Id);
  } catch (error) {
    console.error("Failed to create Confidential Users group:", error);
    throw error;
  }
};

export const createConfidentialEmailFilimg = async (
  sharepoint: SPFI,
  newProjectUrl: string
): Promise<void> => {
  try {
    const web = Web([sharepoint.web, newProjectUrl]);
    const lists = await web.lists();
    if (
      lists.some((list) => list.Title === "Confidential Filed Email Content")
    ) {
      // list exists, return
      return;
    } else {
      await web.lists.add("Confidential Filed Email Content", "", 101, true);
    }
  } catch (error) {
    console.error("Failed to create Confidential Filed Email Content:", error);
    throw error;
  }
};

export const addDefaultUserGroups = async (
  sharepoint: SPFI,
  newProjectUrl: string
): Promise<void> => {
  // Fetch members groups keys
  const atveroMemberUserGroupKeys = await searchUserGroups(
    sharepoint,
    "Atvero Mail All Users"
  );
  const cmapMemberUserGroupKeys = await searchUserGroups(
    sharepoint,
    "CMap Mail All Users"
  );

  const memberUserGroupsKeys = atveroMemberUserGroupKeys.concat(
    cmapMemberUserGroupKeys
  );

  if (memberUserGroupsKeys?.length > 0) {
    await addUserGroupToSite(
      sharepoint,
      memberUserGroupsKeys,
      newProjectUrl,
      SiteGroupTypes.Member
    );
  }

  // Fetch admin groups keys
  const atveroAdminUserGroupKeys = await searchUserGroups(
    sharepoint,
    "Atvero Mail Admins"
  );
  const cmapAdminUserGroupKeys = await searchUserGroups(
    sharepoint,
    "CMap Mail Admins"
  );

  const adminUserGroupsKeys = atveroAdminUserGroupKeys.concat(
    cmapAdminUserGroupKeys
  );

  if (adminUserGroupsKeys?.length > 0) {
    await addUserGroupToSite(
      sharepoint,
      adminUserGroupsKeys,
      newProjectUrl,
      SiteGroupTypes.Owner
    );
  }

  // Fetch confidential group keys
  const atveroConfidentailUserGroupKeys = await searchUserGroups(
    sharepoint,
    "Atvero Mail All Confidential Users"
  );
  const cmapConfidentailUserGroupKeys = await searchUserGroups(
    sharepoint,
    "CMap Mail All Confidential Users"
  );
  const confidentailUserGroupsKeys = atveroConfidentailUserGroupKeys.concat(
    cmapConfidentailUserGroupKeys
  );

  await toggleUserToConfidentialGroup({
    sharepoint: sharepoint,
    projectUrl: newProjectUrl,
    groupKeys: confidentailUserGroupsKeys,
    canAccessConfidentialEmails: true,
  });
};

export const repairProject = async (
  sharepoint: SPFI,
  tenancyName: string,
  siteDesignName: string,
  hubsite: Hubsite,
  project: Project,
  onProgressUpdate: (progress: number, message: string) => void,
  fallbackSiteDesignName: string
): Promise<{ success: boolean; message: string; details?: string }> => {
  try {
    await addProjectListRow(
      sharepoint,
      hubsite,
      project.code,
      project.title ?? project.code,
      project.cMapId
    );

    onProgressUpdate(0.1, `Setting up ${tenancyName}...`);
    let siteDesign;
    try {
      siteDesign = await getProjectDesign(
        sharepoint,
        siteDesignName,
        fallbackSiteDesignName
      );
      if (!siteDesign) {
        return {
          success: false,
          message: "Failed to repair the project.",
          details: `This action requires setup '${siteDesignName}' which was not found. Please double check the ${tenancyName} Mail Bootstrap process was correctly ran.`,
        };
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes("Access is denied")) {
        return {
          success: false,
          message: "Failed to repair the project.",
          details:
            "Access denied. Please check your permissions and try again.",
        };
      } else {
        return {
          success: false,
          message: "Failed to repair the project.",
          details: `Failed to fetch setup: ${errorMessage}`,
        };
      }
    }

    await createConfidentialEmailFilimg(sharepoint, project.webUrl);

    onProgressUpdate(0.7, "Applying confidential user permissions...");
    try {
      await applyConfidentialUserPermissions(sharepoint, project.webUrl);
      onProgressUpdate(
        0.9,
        "Confidential user permissions applied successfully."
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes("Access is denied")) {
        return {
          success: false,
          message: "Failed to repair the project.",
          details:
            "Access denied. Please check your permissions and try again.",
        };
      }
    }

    try {
      await applySiteDesign(sharepoint, project.webUrl, siteDesign.Id, false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes("Access is denied")) {
        return {
          success: false,
          message: "Failed to repair the project.",
          details:
            "Access denied. Please check your permissions and try again.",
        };
      }
    }

    onProgressUpdate(1, "Project repair completed successfully.");
    return {
      success: true,
      message: "Project repaired successfully.",
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(
      `Error repairing project ${project.title ?? project.code}:`,
      errorMessage
    );
    return {
      success: false,
      message: "Failed to repair the project.",
      details: errorMessage,
    };
  }
};

export const createProject = async (
  graph: GraphFI,
  sharepoint: SPFI,
  backendAdapter: IBackendAdapter,
  tenancyName: string,
  sharepointHostName: string,
  siteDesignId: string,
  projectTitle: string,
  projectCode: string,
  cmapProjectId: number | undefined,
  hubsite: Hubsite,
  importUsersFrom: Project | undefined,
  onProgressUpdate: (progress: number, message: string) => void,
  cancelToken: { cancelled: boolean }
): Promise<string> => {
  if (cancelToken.cancelled) return "";

  const cleanHostname = sharepointHostName
    .replace(/^https?:\/\//, "")
    .replace(/\/$/, "");

  onProgressUpdate(0.3, "Checking project name is valid...");
  if (cancelToken.cancelled) return "";
  let siteExists = false;

  try {
    await sharepoint.site.getWebUrlFromPageUrl(
      `https://${cleanHostname}/sites/${projectCode}`
    );

    siteExists = true;
  } catch (error) {
    siteExists = false;
  }

  const startTime = performance.now();
  console.time("site creation");
  onProgressUpdate(0.4, "Creating project site...");

  const formattedHubsiteUrl = new URL(hubsite.url).pathname;
  const hubsiteWithID = await getHubsiteFromPath(
    graph,
    cleanHostname,
    formattedHubsiteUrl
  );

  const hubsiteId = extractSiteId(hubsiteWithID.id);

  const useNewProjectCreation = await backendAdapter.getSetting(
    "useNewProjectCreation"
  );

  if (cancelToken.cancelled) return "";
  if (!siteExists) {
    if (useNewProjectCreation === "enabled") {
      // switch to using the back end to create the project not associated with a group
      const createSite = await backendAdapter.createSite(
        hubsite.url,
        projectCode,
        projectTitle,
        hubsiteId
      );
      if (createSite) {
      } else {
        throw new Error("Failed to create project");
      }
    } else {
      const newProjectSite: ISiteCreationResponse =
        await sharepoint.site.createModernTeamSiteFromProps({
          displayName: projectCode + " - " + projectTitle,
          isPublic: false,
          alias: projectCode,
          description: projectTitle,
          // siteDesignId: siteDesignId,  // don't apply now, it runs too early on fast tenants
          hubSiteId: extractSiteId(hubsiteWithID.id),
        });
      console.timeEnd("site creation");
      if (newProjectSite) {
      } else {
        throw new Error("Failed to create project");
      }
    }
    if (cancelToken.cancelled) return "";
  }
  console.info("Time to create project site:", performance.now() - startTime);

  console.time("add list row");
  await addProjectListRow(
    sharepoint,
    hubsite,
    projectCode,
    projectTitle,
    cmapProjectId
  );
  console.timeEnd("add list row");
  if (cancelToken.cancelled) return "";

  onProgressUpdate(0.5, `Setting up ${tenancyName} Mail for your project...`);

  const newProjectUrl: string = await sharepoint.site.getWebUrlFromPageUrl(
    `https://${cleanHostname}/sites/${projectCode}`
  );

  if (cancelToken.cancelled) return "";

  onProgressUpdate(0.6, "Creating confidential filing...");
  console.time("create confidential email filing");
  await createConfidentialEmailFilimg(sharepoint, newProjectUrl);
  if (cancelToken.cancelled) return "";
  console.timeEnd("create confidential email filing");
  onProgressUpdate(0.7, "Applying confidential user permissions...");
  console.time("apply confidential user permissions");
  await applyConfidentialUserPermissions(sharepoint, newProjectUrl);
  if (cancelToken.cancelled) return "";
  console.timeEnd("apply confidential user permissions");
  onProgressUpdate(0.8, "Setting default users...");
  console.time("create default users");
  await addDefaultUserGroups(sharepoint, newProjectUrl);
  if (cancelToken.cancelled) return "";
  console.timeEnd("create default users");
  if (importUsersFrom) {
    onProgressUpdate(
      0.9,
      `Importing users from ${importUsersFrom.title || importUsersFrom.code}...`
    );
    console.time("import users");
    await importUsers(sharepoint, importUsersFrom?.webUrl, newProjectUrl);
    if (cancelToken.cancelled) return "";
    console.timeEnd("import users");
  }
  console.time("start apply site design");
  await applySiteDesign(sharepoint, newProjectUrl, siteDesignId, false);
  if (cancelToken.cancelled) return "";
  console.timeEnd("start apply site design");
  console.info("Project created in:", performance.now() - startTime);

  onProgressUpdate(1, "Project created successfully!");
  return newProjectUrl;
};
