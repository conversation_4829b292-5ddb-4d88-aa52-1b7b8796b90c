import { getProjectDesign, createProject } from "./createProject";
import {
  Project,
  EnableAtveroMailFunction,
  IProjectMessageStatus,
  Hubsite,
} from "../types";
import { Adapters } from "../adapters/AdapterFactory";

interface EnableAtveroParams {
  adapter: Adapters;
  selectedHubsite: Hubsite | undefined;
  projects: Project[];
  onProgressUpdate: (progress: number, message: string) => void;
  tenancyName: string;
  siteDesignName: string;
  fallbackSiteDesignName: string;
}

export const enableAtveroMail = async ({
  adapter,
  selectedHubsite,
  projects,
  onProgressUpdate,
  tenancyName,
  siteDesignName,
  fallbackSiteDesignName,
}: EnableAtveroParams): ReturnType<EnableAtveroMailFunction> => {
  if (
    !adapter.legacyAdapter.getGraph() ||
    !selectedHubsite ||
    !adapter.legacyAdapter.getSharepoint()
  ) {
    return {
      success: false,
      message: `Unable to enable ${tenancyName} Mail due to missing dependencies`,
      details:
        "It looks like we couldn't find some of your details. Please refresh your browser.",
    };
  }

  try {
    const siteDesign = await getProjectDesign(
      adapter.legacyAdapter.getSharepoint(),
      siteDesignName,
      fallbackSiteDesignName
    );
    if (!siteDesign) {
      return {
        success: false,
        message: `Failed to enable ${tenancyName} Mail for the selected projects`,
        details: `Site design '${siteDesignName}' not found. Please check the Atvero Mail Bootstrap process was correctly ran.`,
      };
    }

    const results = await processProjects(projects, tenancyName, {
      adapter,
      siteDesign,
      selectedHubsite,
      onProgressUpdate,
    });

    return generateFinalResult(results, tenancyName);
  } catch (error) {
    return {
      success: false,
      message: `An error occurred while enabling ${tenancyName} for the projects`,
      details: error instanceof Error ? error.message : String(error),
    };
  }
};

async function processProjects(
  projects: Project[],
  tenancyName: string,
  context: {
    adapter: Adapters;
    siteDesign: { Id: string };
    selectedHubsite: Hubsite;
    onProgressUpdate: (progress: number, message: string) => void;
  }
): Promise<IProjectMessageStatus[]> {
  const results: IProjectMessageStatus[] = [];

  for (let i = 0; i < projects.length; i++) {
    const project = projects[i];
    const baseProgress = i / projects.length;

    try {
      context.onProgressUpdate(
        baseProgress,
        `Creating ${tenancyName} site for "${project.title || project.code}" (${
          i + 1
        }/${projects.length})`
      );

      await createProject(
        context.adapter.legacyAdapter.getGraph(),
        context.adapter.legacyAdapter.getSharepoint(),
        context.adapter.backendAdapter,
        tenancyName,
        context.adapter.legacyAdapter.getSharepointHostName(),
        context.siteDesign.Id,
        project.title || project.code,
        project.code,
        project.cMapId,
        context.selectedHubsite,
        undefined,
        (itemProgress, message) => {
          const scaledProgress = baseProgress + itemProgress / projects.length;
          context.onProgressUpdate(
            scaledProgress,
            `${message} - "${project.title || project.code}" (${i + 1}/${
              projects.length
            })`
          );
        },
        { cancelled: false }
      );

      results.push({
        projectCode: project.code,
        projectTitle: project.title || project.code,
        status: "success",
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      results.push({
        projectCode: project.code,
        projectTitle: project.title || project.code,
        status: errorMessage.includes("already exists") ? "exists" : "error",
        details: errorMessage.includes("already exists")
          ? `Project already has ${tenancyName} Mail enabled`
          : errorMessage,
      });
    }
  }

  context.onProgressUpdate(1, `Completed creating ${tenancyName} sites`);
  return results;
}

function generateFinalResult(
  results: IProjectMessageStatus[],
  tenancyName: string
): {
  success: boolean;
  message: string;
  details?: string;
} {
  const successCount = results.filter((r) => r.status === "success").length;
  const existsCount = results.filter((r) => r.status === "exists").length;
  const failedResults = results.filter((r) => r.status === "error");

  if (successCount === 0 && existsCount === 0) {
    return {
      success: false,
      message: `Failed to enable ${tenancyName} Mail for any projects`,
      details: failedResults
        .map((r) => `${r.projectTitle}:\n${r.details}`)
        .join("\n\n"),
    };
  }

  const details: string[] = [];
  let messageStatus: "success" | "warning" = "success";

  if (existsCount > 0) {
    const existingProjects = results
      .filter((r) => r.status === "exists")
      .map((r) => r.projectTitle);
    details.push(
      `Projects already enabled (${existsCount}):\n${existingProjects.join(
        "\n"
      )}`
    );
    messageStatus = "warning";
  }

  if (failedResults.length > 0) {
    const errorDetails = failedResults
      .map((r) => `${r.projectTitle}:\n${r.details}`)
      .join("\n\n");
    details.push(`Failed projects (${failedResults.length}):\n${errorDetails}`);
    messageStatus = "warning";
  }

  return {
    success: messageStatus === "success",
    message:
      messageStatus === "warning"
        ? `Completed with ${successCount} successful, ${existsCount} already exists, and ${failedResults.length} failed`
        : `Successfully enabled ${tenancyName} Mail for all ${successCount} projects`,
    details: details.length > 0 ? details.join("\n\n") : undefined,
  };
}
