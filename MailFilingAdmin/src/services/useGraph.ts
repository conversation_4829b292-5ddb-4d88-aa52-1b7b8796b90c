/* v8 ignore next 135 */
import { useEffect, useState, useCallback, useRef } from "react";
import { graph<PERSON>, Graph<PERSON><PERSON>er, Graph<PERSON> } from "@pnp/graph";
import { MSAL, MSALOptions } from "@pnp/msaljsclient";
import { ClientId, RedirectUri, Scopes } from "../utils/Config";
import { msalInstance } from "./useSharePoint";

interface GraphState {
  graph: GraphFI | undefined;
  loginError: boolean;
  isInitialized: boolean;
}

const REFRESH_INTERVAL = 45 * 60 * 1000; // 45 minutes in milliseconds
let graphInstance: GraphFI | undefined;
let lastInitTime: number = 0;

export const useGraph = () => {
  const [state, setState] = useState<GraphState>({
    graph: graphInstance,
    loginError: false,
    isInitialized: !!graphInstance,
  });

  const timerRef = useRef<NodeJS.Timeout>();

  const initializeGraph = useCallback(async (force: boolean = false) => {
    const now = Date.now();
    if (!force && graphInstance && now - lastInitTime < REFRESH_INTERVAL) {
      setState({
        graph: graphInstance,
        loginError: false,
        isInitialized: true,
      });
      return;
    }

    const options: MSALOptions = {
      configuration: {
        auth: {
          authority: "https://login.microsoftonline.com/common",
          clientId: ClientId(),
          redirectUri: RedirectUri(),
        },
        cache: {
          claimsBasedCachingEnabled: true,
        },
      },
      authParams: {
        forceRefresh: true, // Force token refresh
        scopes: Scopes(),
      },
    };

    try {
      graphInstance = graphfi().using(GraphBrowser(), MSAL(options));
      lastInitTime = now;

      setState({
        graph: graphInstance,
        loginError: false,
        isInitialized: true,
      });
    } catch (error) {
      console.error("Graph initialization failed:", error);
      setState((prev) => ({
        ...prev,
        loginError: true,
        isInitialized: true,
      }));
    }
  }, []);

  const setupRefreshTimer = useCallback(() => {
    timerRef.current = setInterval(() => {
      console.log("Refreshing Graph instance");
      initializeGraph(true);
    }, REFRESH_INTERVAL);
  }, [initializeGraph]);

  useEffect(() => {
    initializeGraph();
    setupRefreshTimer();

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [initializeGraph, setupRefreshTimer]);

  const handleLogout = useCallback(async () => {
    try {
      const account = msalInstance.getAllAccounts()[0];
      if (account) {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
        graphInstance = undefined;
        lastInitTime = 0;

        setState((prev) => ({
          ...prev,
          graph: undefined,
          loginError: true,
          isInitialized: true,
        }));

        await msalInstance.logoutPopup({
          account,
          postLogoutRedirectUri: window.location.origin,
          mainWindowRedirectUri: window.location.origin,
        });

        window.location.reload();
      }
    } catch (error) {
      console.error("Logout failed:", error);
      window.location.reload();
    }
  }, []);

  const manualLogin = useCallback(() => {
    initializeGraph(true);
  }, [initializeGraph]);

  return {
    graph: state.graph,
    loginError: state.loginError,
    isInitialized: state.isInitialized,
    manualLogin,
    handleLogout,
  };
};
