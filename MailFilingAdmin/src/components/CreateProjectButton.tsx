import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogTrigger,
  DialogSurface,
  DialogBody,
  DialogTitle,
  DialogContent,
  DialogActions,
  Input,
  makeStyles,
  Text,
  ProgressBar,
  Spinner,
  Avatar,
  Option,
  Combobox,
  Label,
  SelectionEvents,
} from "@fluentui/react-components";
import { Add16Filled, Person16Regular } from "@fluentui/react-icons";
import StatusMessage from "./StatusMessage";
import {
  CreateProjectFunction,
  Hubsite,
  Project,
  ProjectResponse,
} from "../types";
import { getProjectList } from "../services/getProjects";
import { SPFI } from "@pnp/sp";
import {
  containsOnlySpaces,
  containsSpecialCharacters,
} from "../utils/textFieldUtils";

interface CreateProjectButtonProps {
  sharepoint: SPFI | undefined;
  hubsite: Hubsite | undefined;
  onCreateProject: CreateProjectFunction;
  onProjectUrlChange: (url: string) => void;
  onOpenUsersModal: () => void;
}

const useStyles = makeStyles({
  inputRoot: {
    // Stack the label above the field
    display: "flex",
    flexDirection: "column",
    // Use 2px gap below the label (per the design system)
    gap: "2px",
  },
  dialogContent: {
    display: "flex",
    flexDirection: "column",
    gap: "20px",
  },
  progressContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "10px",
  },
  statusMessage: {
    display: "flex",
    flexDirection: "row",
    gap: "10px",
  },
  search: {
    width: "100%",
  },
  searchContainer: {
    maxHeight: "300px",
  },
  label: {
    fontSize: "10px",
    marginLeft: "2px",
  },
  avatar: {
    marginRight: "8px",
  },
});

const CreateProjectButton: React.FC<CreateProjectButtonProps> = ({
  sharepoint,
  hubsite,
  onCreateProject,
  onProjectUrlChange,
  onOpenUsersModal,
}) => {
  const styles = useStyles();
  const [projectTitle, setProjectTitle] = useState("");
  const [projectCode, setProjectCode] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState("");
  const [message, setMessage] = useState<{
    type: "error" | "success" | "info" | "warning";
    text: string;
    details?: string;
  } | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [selectedProject, setSelectedProject] = useState<Project | undefined>(
    undefined
  );
  const [flatProjects, setFlatProjects] = useState<Project[]>([]);
  const [newProjectUrl, setNewProjectUrl] = useState<string>("");
  const [hasModifiedFields, setHasModifiedFields] = useState(false);
  const [cancelToken] = useState({ cancelled: false });
  const [validationMessage, setValidationMessage] = useState<{
    type: "warning" | "error";
    text: string;
    preview?: string;
  } | null>(null);
  const [hasCodeSpecialChars, setHasCodeSpecialChars] = useState(false);

  useEffect(() => {
    const fetchProjectList = async (): Promise<void> => {
      if (sharepoint && hubsite) {
        if (inputValue) {
          const response: ProjectResponse = await getProjectList(
            sharepoint,
            hubsite,
            10,
            0,
            "code",
            "desc",
            inputValue
          );

          setFlatProjects(response.projects);
        } else {
          setFlatProjects([]);
        }
      }
    };
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    fetchProjectList();
  }, [sharepoint, hubsite, inputValue]);

  const handleSubmit = async (): Promise<void> => {
    const sanitizedCode = projectCode.replace(/\s+/g, "_");

    setIsCreating(true);
    setMessage(null);
    setProgress(0);
    setStatusMessage("Initializing project creation...");

    try {
      cancelToken.cancelled = false;
      const result = await onCreateProject(
        projectTitle,
        sanitizedCode,
        undefined,
        (progress: number, message: string) => {
          setProgress(progress);
          setStatusMessage(message);
        },
        selectedProject,
        cancelToken
      );

      if (result.success) {
        setProgress(1);
        setStatusMessage(`Project ${sanitizedCode} created successfully`);
        setMessage({
          type: "success",
          text: result.message,
          details: result.details,
        });

        if (result.projectURL) {
          setNewProjectUrl(result.projectURL);
          onProjectUrlChange(result.projectURL);
        }

        resetForm();
      } else {
        setMessage({
          type: "error",
          text: result.message,
          details: result.details,
        });
      }
    } catch (error) {
      setMessage({
        type: "error",
        text: "An unexpected error occurred while creating the project.",
        details: error instanceof Error ? error.message : String(error),
      });
    } finally {
      setIsCreating(false);
    }
  };

  const closeDialog = (): void => {
    setIsDialogOpen(false);
    resetAllFormState();
  };

  const resetAllFormState = (): void => {
    setProjectTitle("");
    setProjectCode("");
    setMessage(null);
    setProgress(0);
    setStatusMessage("");
    setInputValue("");
    setSelectedProject(undefined);
    setIsCreating(false);
    setNewProjectUrl("");
    setValidationMessage(null);
  };

  const resetForm = (): void => {
    setSelectedProject(undefined);
    setInputValue("");
    setProjectTitle("");
    setProjectCode("");
    setHasModifiedFields(false);
    setValidationMessage(null);
  };

  const handleProjectTitleChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ): void => {
    const value = e.target.value;
    setProjectTitle(value);
    setHasModifiedFields(true);

    if (value === "") {
      if (!hasCodeSpecialChars && !projectCode.includes(" ")) {
        setValidationMessage(null);
      }
      return;
    }

    if (containsOnlySpaces(value)) {
      setValidationMessage({
        type: "error",
        text: "Project title cannot be only spaces",
      });
      return;
    }
  };

  const handleProjectCodeChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ): void => {
    const value = e.target.value;
    setProjectCode(value);
    setHasModifiedFields(true);

    if (value === "") {
      setValidationMessage(null);
      setHasCodeSpecialChars(false);
      return;
    }

    if (value.length > 255) {
      setValidationMessage({
        type: "error",
        text: "Project code cannot exceed 255 characters",
      });
      setHasCodeSpecialChars(true);
      return;
    }

    if (containsSpecialCharacters(value)) {
      setValidationMessage({
        type: "error",
        text: "Special characters and emojis are not allowed in the project code",
      });
      setHasCodeSpecialChars(true);
    } else {
      setHasCodeSpecialChars(false);
      if (value.includes(" ")) {
        updateSpacesValidation(value);
      } else {
        setValidationMessage(null);
      }
    }

    if (containsOnlySpaces(value)) {
      setValidationMessage({
        type: "error",
        text: "Project code cannot be only spaces",
      });
      return;
    }
  };

  const updateSpacesValidation = (code: string): void => {
    if (code.includes(" ")) {
      setValidationMessage({
        type: "warning",
        text: "Spaces will be replaced with underscores",
        preview: `Project Code: ${code.replace(/\s+/g, "_")}`,
      });
    } else {
      if (!hasCodeSpecialChars) {
        setValidationMessage(null);
      }
    }
  };

  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setInputValue(event.target.value);
    },
    []
  );

  const handleManageUsers = () => {
    onOpenUsersModal();
    closeDialog();
  };

  const handleOptionSelect = useCallback(
    (
      _event: SelectionEvents,
      data: { optionValue?: string; optionText?: string }
    ) => {
      const selectedProject = flatProjects.find(
        (project) => project.id === data.optionValue
      );
      if (selectedProject) {
        setSelectedProject(selectedProject);
        setInputValue(selectedProject.title || selectedProject.code);
      }
    },
    [flatProjects]
  );

  const renderComboboxOptions = (): JSX.Element[] => {
    return flatProjects.map((project, index) => (
      <Option
        key={project.id + index}
        value={project.id}
        text={`${project.code} - ${project.title}`}
        checkIcon={null}
      >
        <Avatar
          className={styles.avatar}
          name={project.title ?? project.code}
          size={32}
          color="colorful"
          shape="square"
        />
        <Text>
          {project.code} - {project.title}
        </Text>
      </Option>
    ));
  };

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(_, { open }) => setIsDialogOpen(open)}
    >
      <DialogTrigger disableButtonEnhancement>
        <Button
          icon={<Add16Filled />}
          iconPosition="after"
          onClick={() => setIsDialogOpen(true)}
          size="medium"
        >
          Create Project
        </Button>
      </DialogTrigger>
      <DialogSurface>
        <DialogBody>
          <DialogTitle>Create New Project</DialogTitle>
          <DialogContent className={styles.dialogContent}>
            <div>
              <div className={styles.inputRoot}>
                <Label htmlFor="project-code-input" className={styles.label}>
                  Internal project code for the project
                </Label>
                <Input
                  id="project-code-input"
                  placeholder="Project Code"
                  value={projectCode}
                  onChange={handleProjectCodeChange}
                  disabled={isCreating}
                />
              </div>
              <div className={styles.inputRoot}>
                <Label htmlFor="project-title-input" className={styles.label}>
                  Project Title (visible to users)
                </Label>
                <Input
                  id="project-title-input"
                  placeholder="Project Title"
                  value={projectTitle}
                  onChange={handleProjectTitleChange}
                  disabled={isCreating}
                />
              </div>
            </div>
            <div>
              <Label className={styles.label}>
                Import members from existing project:
              </Label>
              <Combobox
                placeholder="Search projects..."
                onChange={handleInputChange}
                onOptionSelect={handleOptionSelect}
                value={inputValue}
                className={styles.search}
                disabled={isCreating}
              >
                <div className={styles.searchContainer}>
                  {renderComboboxOptions()}
                </div>
              </Combobox>
            </div>

            {validationMessage && (
              <StatusMessage
                message={validationMessage.text}
                type={validationMessage.type}
                details={validationMessage.preview}
              />
            )}

            {isCreating && (
              <div className={styles.progressContainer}>
                <ProgressBar value={progress} />
                <div className={styles.statusMessage}>
                  <Spinner size="extra-tiny" />
                  <Text>{statusMessage}</Text>
                </div>
              </div>
            )}
            {message && (
              <StatusMessage
                message={message.text}
                type={message.type}
                details={message.details}
              />
            )}
          </DialogContent>
          <DialogActions>
            {/* 
            This button is commented out for now until we have project removal functionality
            in place to delete a canceled project in Sharepoint.
            
            <Button
              appearance="secondary"
              onClick={isCreating ? handleCancel : closeDialog}
            >
              {isCreating ? "Cancel" : "Close"}
            </Button> */}
            <Button
              appearance="secondary"
              onClick={closeDialog}
              disabled={isCreating}
            >
              Close
            </Button>
            {newProjectUrl && !hasModifiedFields ? (
              <Button
                appearance="primary"
                onClick={handleManageUsers}
                icon={<Person16Regular />}
              >
                Manage Users
              </Button>
            ) : (
              <Button
                appearance="primary"
                onClick={handleSubmit}
                disabled={
                  isCreating ||
                  !projectTitle ||
                  !projectCode ||
                  hasCodeSpecialChars ||
                  containsOnlySpaces(projectTitle) ||
                  containsOnlySpaces(projectCode)
                }
              >
                Create
              </Button>
            )}
          </DialogActions>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};

export default CreateProjectButton;
