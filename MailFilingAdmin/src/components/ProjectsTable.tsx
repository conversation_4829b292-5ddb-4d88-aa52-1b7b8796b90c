import React from "react";
import {
  Column,
  CreateProjectFunction,
  Project,
  RepartProjectFunction,
  SortDirection,
  User,
} from "../types";
import {
  makeStyles,
  Table,
  TableBody,
  TableColumnId,
  TableHeader,
  TableHeaderCell,
  TableRow,
} from "@fluentui/react-components";
import RenderRow, { generateShimmerRows } from "./RenderRow/RenderRow";
import AutoSizer, { Size } from "react-virtualized-auto-sizer";
import InfiniteLoader from "react-window-infinite-loader";
import { FixedSizeList as List } from "react-window";

interface ProjectsTableProps {
  projects: Project[];
  selectedProjects: Project[];
  columns: Column[];
  loading: boolean;
  hasNextPage: boolean;
  loadNextPage: (startIndex: number, endIndex: number) => void;
  isNextPageLoading: boolean;
  onSortChange: (columnKey: string, direction: SortDirection) => void;
  currentSort: { columnKey: string; direction: SortDirection };
  onFetchUsers: (projectUrl: string) => Promise<User[]>;
  onRepairProject: RepartProjectFunction;
  onCreateProject: CreateProjectFunction;
  onProjectSelect: (email: Project) => void;
  hasCMapProjects: boolean;
}

const useStyles = makeStyles({
  tableContainer: {
    height: "100%",
    display: "flex",
    flexDirection: "column",
    flex: "1 1 auto",
    overflow: "hidden",
    minHeight: "500px",
  },
  tableScroll: {
    overflow: "hidden",
    flex: "1 1 auto",
    overflowY: "auto",
    minHeight: "150px",
    minWidth: "510px",
    width: "100%",
    "& ::-webkit-scrollbar": {
      width: "8px",
    },
    "& ::-webkit-scrollbar-track": {
      backgroundColor: "#f0f0f0",
    },
    "& ::-webkit-scrollbar-thumb": {
      backgroundColor: "#cccccc",
      borderRadius: "10px",
    },
  },
});

const ProjectsTable: React.FC<ProjectsTableProps> = ({
  projects,
  loading,
  hasNextPage,
  loadNextPage,
  isNextPageLoading,
  onSortChange,
  currentSort,
  columns,
  onFetchUsers,
  onRepairProject,
  onCreateProject,
  selectedProjects,
  onProjectSelect,
  hasCMapProjects,
}) => {
  const styles = useStyles();
  const itemCount = hasNextPage ? projects.length + 1 : projects.length;
  const isItemLoaded = (index: number): boolean =>
    !hasNextPage || index < projects.length;
  const loadMoreItemsCallback = isNextPageLoading ? () => {} : loadNextPage;

  const handleSort = (columnId: TableColumnId): void => {
    const newDirection =
      currentSort.columnKey === columnId &&
      currentSort.direction === SortDirection.Ascending
        ? SortDirection.Descending
        : SortDirection.Ascending;
    onSortChange(columnId as string, newDirection);
  };

  return (
    <Table
      noNativeElements={true}
      aria-label="Custom table"
      className={styles.tableContainer}
      sortable
    >
      <TableHeader>
        <TableRow>
          {columns.map((column) => (
            <TableHeaderCell
              key={column.columnKey}
              onClick={() =>
                column.sortable ? handleSort(column.columnKey) : undefined
              }
              sortDirection={
                column.sortable && currentSort.columnKey === column.columnKey
                  ? currentSort.direction
                  : undefined
              }
              aria-sort={column.sortable ? undefined : "none"}
            >
              {column.label}
            </TableHeaderCell>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody className={styles.tableScroll}>
        {loading ? (
          <>{generateShimmerRows(15)}</>
        ) : (
          <AutoSizer>
            {({ height, width }: Size) => (
              <InfiniteLoader
                isItemLoaded={isItemLoaded}
                itemCount={itemCount}
                loadMoreItems={loadMoreItemsCallback}
              >
                {({ onItemsRendered, ref }) => (
                  <List
                    className="List"
                    height={height}
                    itemCount={itemCount}
                    itemSize={50}
                    onItemsRendered={onItemsRendered}
                    ref={ref}
                    width={width}
                  >
                    {({ index, style }) => (
                      <RenderRow
                        index={index}
                        style={style}
                        projects={projects}
                        selectedProjects={selectedProjects}
                        isItemLoaded={isItemLoaded}
                        onFetchUsers={onFetchUsers}
                        onRepairProject={onRepairProject}
                        onCreateProject={onCreateProject}
                        onClick={onProjectSelect}
                        hasCMapProjects={hasCMapProjects}
                      />
                    )}
                  </List>
                )}
              </InfiniteLoader>
            )}
          </AutoSizer>
        )}
      </TableBody>
    </Table>
  );
};

export default ProjectsTable;
