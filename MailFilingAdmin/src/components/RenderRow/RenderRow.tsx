import React from "react";
import {
  TableRow,
  TableCell,
  SkeletonItem,
  Avatar,
  TableSelectionCell,
} from "@fluentui/react-components";

import UsersModal from "../UsersModal";
import ProjectSettings from "../ProjectSettings";
import { useStyles } from "./RenderRow.styles";
import { IRenderRowProps } from "./IRenderRowProps";
import { EmailBadge } from "./EmailBadge";
import { CMapProjectStatus } from "../../types";

export const ShimmerRow: React.FC<{ style: React.CSSProperties }> = ({
  style,
}) => (
  <TableRow
    style={style}
    className={useStyles().tableRow}
    data-testid="skeleton-item"
  >
    <TableCell>
      <SkeletonItem shape="rectangle" size={24} />
    </TableCell>
    <TableCell>
      <SkeletonItem shape="rectangle" size={24} />
    </TableCell>
  </TableRow>
);

export const generateShimmerRows = (count: number): JSX.Element[] => {
  const rows = [];
  for (let i = 0; i < count; i++) {
    rows.push(<ShimmerRow key={i} style={{}} />);
  }
  return rows;
};

const RenderRow: React.FC<IRenderRowProps> = ({
  index,
  style,
  projects,
  selectedProjects,
  isItemLoaded,
  onFetchUsers,
  onRepairProject,
  onCreateProject,
  onClick,
  hasCMapProjects,
}) => {
  const styles = useStyles();
  const item = projects[index];
  const selected = selectedProjects.some((project) => {
    if (project.cMapId && item.cMapId) {
      return project.cMapId === item.cMapId;
    }
    return project.id === item.id;
  });

  return (
    <>
      {!isItemLoaded(index) ? (
        <div>
          {index === 0 ? (
            <div className={styles.centeredMessage} data-testid="loading-item">
              <p>Loading your projects.</p>
            </div>
          ) : (
            <ShimmerRow style={style} />
          )}
        </div>
      ) : (
        <TableRow
          style={style}
          key={index ?? item.id}
          className={styles.tableRow}
          aria-selected={selected}
          appearance={selected ? "brand" : "none"}
        >
          <TableSelectionCell
            checked={selected}
            checkboxIndicator={{
              "aria-label": selected ? "Deselect row" : "Select row",
            }}
            onClick={() => {
              onClick(item);
            }}
          />

          <TableCell>
            <span>
              <Avatar
                shape="square"
                name={item.title ?? "Untitled Project"}
                color={item.webUrl === "" ? "neutral" : "colorful"}
                size={36}
                className={styles.avatar}
              />
              <EmailBadge project={item} />
            </span>
            <span className={styles.projectCode}>{item.code}</span>
          </TableCell>
          <TableCell>{item.title}</TableCell>
          <TableCell>
            <UsersModal
              disabled={
                !(
                  item.cmapProject === CMapProjectStatus.CMapCreated ||
                  undefined === item.cMapId
                )
              }
              projectUrl={item.webUrl}
              onFetchUsers={onFetchUsers}
            />

            <ProjectSettings
              project={item}
              showSync={!(item.cmapProject === CMapProjectStatus.CMapCreated)}
              onRepairProject={onRepairProject}
              onCreateProject={onCreateProject}
              hasCMapProjects={hasCMapProjects}
            />
          </TableCell>
        </TableRow>
      )}
    </>
  );
};

export default RenderRow;
