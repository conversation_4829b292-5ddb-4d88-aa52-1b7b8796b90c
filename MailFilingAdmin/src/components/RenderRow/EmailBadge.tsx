import { Badge } from "@fluentui/react-components";
import { useStyles } from "./RenderRow.styles";
import { MailReadMultipleRegular } from "@fluentui/react-icons";
import { CMapProjectStatus, Project } from "../../types";

type IEmailBadge = { project: Project };

export const EmailBadge = ({ project }: IEmailBadge): JSX.Element | null => {
  const styles = useStyles();

  if (CMapProjectStatus.CMapCreated === project.cmapProject) {
    return (
      <Badge
        className={styles.emailBadge}
        size="medium"
        icon={<MailReadMultipleRegular />}
        color="informative"
      />
    );
  } else {
    return null;
  }
};
