import { makeStyles } from "@fluentui/react-components";

export const useStyles = makeStyles({
  tableRow: {
    "&:hover": {
      cursor: "pointer",
    },
  },
  projectCode: {
    float: "left",
  },
  centeredMessage: {
    textAlign: "center",
    padding: "50px",
    color: "#888",
    fontSize: "16px",
  },
  avatar: {
    marginRight: "8px",
  },
  emailBadge: {
    position: "absolute",
    marginTop: "-5px",
    marginLeft: "-18px",
    marginRight: "15px", //Correct the spacing of the negagtive margin
  },
});
