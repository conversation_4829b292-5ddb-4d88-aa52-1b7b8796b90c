import {
  CreateProjectFunction,
  Project,
  RepartProjectFunction,
  User,
} from "../../types";

export interface IRenderRowProps {
  index: number;
  style: React.CSSProperties;
  projects: Project[];
  selectedProjects: Project[];
  isItemLoaded: (index: number) => boolean;
  onFetchUsers: (projectUrl: string) => Promise<User[]>;
  onRepairProject: RepartProjectFunction;
  onCreateProject: CreateProjectFunction;
  onClick: (item: Project) => void;
  hasCMapProjects: boolean;
}
