import React from "react";
import {
  makeStyles,
  Text,
  MessageBar,
  MessageBarBody,
} from "@fluentui/react-components";

interface StatusMesssageProps {
  message: string;
  type: "error" | "success" | "info" | "warning";
  details?: string;
}

const useStyles = makeStyles({
  button: {
    outline: "none",
  },
  message: {
    marginLeft: "12px",
  },
  messageBar: {
    width: "100%",
    padding: "10px",
  },
  detailsContainer: {
    marginTop: "8px",
  },
  details: {
    fontSize: "small",
    marginLeft: "12px",
    display: "block",
    marginTop: "8px",
    whiteSpace: "pre-wrap",
    wordBreak: "break-word",
  },
});

const StatusMessage: React.FC<StatusMesssageProps> = ({
  message,
  type,
  details,
}) => {
  const styles = useStyles();

  return (
    <MessageBar
      data-testid="status-message"
      className={styles.messageBar}
      intent={type}
    >
      <MessageBarBody>
        <Text className={styles.message}>{message}</Text>
        {details && (
          <div className={styles.detailsContainer}>
            <Text className={styles.details}>{details}</Text>
          </div>
        )}
      </MessageBarBody>
    </MessageBar>
  );
};

export default StatusMessage;
