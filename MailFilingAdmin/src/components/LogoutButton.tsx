import React, { useState } from "react";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogTrigger,
  DialogSurface,
  DialogBody,
  DialogTitle,
  DialogContent,
  DialogActions,
  makeStyles,
} from "@fluentui/react-components";
import { ArrowExitFilled } from "@fluentui/react-icons";

const useStyles = makeStyles({
  logoutContainer: {
    marginTop: "auto",
    alignSelf: "center",
    padding: "20px 0",
  },
  logoutButton: {
    width: "100%",
  },
  dialogActions: {
    display: "flex",
    gap: "10px",
    justifyContent: "flex-end",
  },
});

interface LogoutButtonProps {
  onLogout: () => Promise<void>;
}

export const LogoutButton: React.FC<LogoutButtonProps> = ({ onLogout }) => {
  const styles = useStyles();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleConfirmLogout = async () => {
    try {
      setIsLoggingOut(true);
      await onLogout();
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLoggingOut(false);
      setIsDialogOpen(false);
    }
  };

  return (
    <div className={styles.logoutContainer}>
      <Dialog
        open={isDialogOpen}
        onOpenChange={(_, { open }) => setIsDialogOpen(open)}
      >
        <DialogTrigger disableButtonEnhancement>
          <Button
            className={styles.logoutButton}
            icon={<ArrowExitFilled />}
            appearance="secondary"
          >
            Log Out
          </Button>
        </DialogTrigger>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>Confirm Logout</DialogTitle>
            <DialogContent>Are you sure you want to log out?</DialogContent>
            <DialogActions className={styles.dialogActions}>
              <Button
                appearance="secondary"
                onClick={() => setIsDialogOpen(false)}
                disabled={isLoggingOut}
              >
                Cancel
              </Button>
              <Button
                appearance="primary"
                onClick={handleConfirmLogout}
                disabled={isLoggingOut}
              >
                {isLoggingOut ? "Logging out..." : "Log Out"}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </div>
  );
};
