import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogTrigger,
  DialogSurface,
  DialogBody,
  DialogTitle,
  DialogContent,
  DialogActions,
  makeStyles,
  Text,
  ProgressBar,
  Spinner,
} from "@fluentui/react-components";
import { ArrowSync16Filled, Wrench20Regular } from "@fluentui/react-icons";
import {
  CMapProjectStatus,
  CreateProjectFunction,
  Environment,
  Project,
  RepartProjectFunction,
} from "../types";
import StatusMessage from "./StatusMessage";
import { ProjectStatus } from "./ProjectStatus";
import { CurrentEnvironment } from "../utils/Config";

interface ProjectSettingsButtonProps {
  project: Project;
  showSync: boolean;
  onRepairProject: RepartProjectFunction;
  onCreateProject: CreateProjectFunction;
  hasCMapProjects: boolean;
}

const useStyles = makeStyles({
  button: {
    marginLeft: "10px",
  },
  dialogContent: {
    display: "flex",
    flexDirection: "column",
    gap: "20px",
  },
  progressContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "10px",
  },
  statusMessage: {
    display: "flex",
    flexDirection: "row",
    gap: "10px",
    alignItems: "center",
  },
});

const ProjectSettings: React.FC<ProjectSettingsButtonProps> = ({
  project,
  showSync,
  onRepairProject,
  onCreateProject,
  hasCMapProjects,
}) => {
  const styles = useStyles();
  const [isRepairing, setIsRepairing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [message, setMessage] = useState<{
    type: "error" | "success" | "info" | "warning";
    text: string;
    details?: string;
  } | null>(null);

  const handleRepair = async (): Promise<void> => {
    setIsRepairing(true);
    setProgress(0);
    setStatusMessage("Starting project repair...");
    setMessage(null);

    try {
      const result = await onRepairProject(project, (progress, message) => {
        setProgress(progress);
        setStatusMessage(message);
      });

      if (result.success) {
        setMessage({
          type: "success",
          text: result.message,
        });
      } else {
        setMessage({
          type: "error",
          text: result.message,
          details: result.details,
        });
      }
    } catch (err) {
      console.error("Unexpected error repairing project:", err);
      setMessage({
        type: "error",
        text: "An unexpected error occurred while repairing the project.",
        details: err instanceof Error ? err.message : String(err),
      });
    } finally {
      setIsRepairing(false);
    }
  };

  const handleCreate = async (): Promise<void> => {
    setIsRepairing(true);
    setProgress(0);
    setStatusMessage("Starting project repair...");
    setMessage(null);

    try {
      const result = await onCreateProject(
        project.title ?? project.code,
        project.code,
        project.cMapId,
        (progress, message) => {
          setProgress(progress);
          setStatusMessage(message);
        },
        project
      );

      if (result.success) {
        setMessage({
          type: "success",
          text: result.message,
        });
      } else {
        setMessage({
          type: "error",
          text: result.message,
          details: result.details,
        });
      }
    } catch (err) {
      console.error("Unexpected error repairing project:", err);
      setMessage({
        type: "error",
        text: "An unexpected error occurred while repairing the project.",
        details: err instanceof Error ? err.message : String(err),
      });
    } finally {
      setIsRepairing(false);
    }
  };

  const closeDialog = (): void => {
    setIsDialogOpen(false);
    setProgress(0);
    setStatusMessage("");
    setMessage(null);
  };

  let actionButtons;

  const dialogTrigger =
    CurrentEnvironment() !== Environment.Production || showSync ? (
      <DialogTrigger disableButtonEnhancement>
        <Button
          className={styles.button}
          icon={hasCMapProjects ? <ArrowSync16Filled /> : <Wrench20Regular />}
          iconPosition="after"
          onClick={() => setIsDialogOpen(true)}
          size="medium"
        />
      </DialogTrigger>
    ) : (
      <DialogTrigger disableButtonEnhancement></DialogTrigger>
    );

  switch (project.cmapProject) {
    case CMapProjectStatus.NotInCmap:
      actionButtons = (
        <Button
          data-testId="repairButton"
          appearance="primary"
          onClick={handleRepair}
          disabled={isRepairing}
        >
          Repair
        </Button>
      );

      break;

    case CMapProjectStatus.CMapNotCreated:
      {
        if (project.cMapId === undefined) {
          actionButtons = (
            <Button
              data-testId="repairButton"
              appearance="primary"
              onClick={handleRepair}
              disabled={isRepairing}
            >
              Repair
            </Button>
          );
        } else {
          actionButtons = (
            <Button
              data-testId="createProjectButton"
              appearance="primary"
              onClick={handleCreate}
              disabled={isRepairing}
            >
              Enable
            </Button>
          );
        }
      }
      break;
    case CMapProjectStatus.CMapCreated:
      actionButtons = (
        <Button
          data-testId="repairButton"
          appearance="primary"
          onClick={handleRepair}
          disabled={isRepairing}
        >
          Repair
        </Button>
      );

      break;
    default: {
      actionButtons = (
        <Button
          data-testId="repairButton"
          appearance="primary"
          onClick={handleRepair}
          disabled={isRepairing}
        >
          Sync CMap with Atvero
        </Button>
      );
    }
  }

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(_, { open }) => setIsDialogOpen(open)}
    >
      {dialogTrigger}
      <DialogSurface>
        <DialogBody>
          <DialogTitle>
            <div>
              <ProjectStatus project={project} />
              {project.code} - {project.title}
            </div>
          </DialogTitle>
          <DialogContent className={styles.dialogContent}>
            {isRepairing ? (
              <div className={styles.progressContainer}>
                <ProgressBar value={progress} />
                <div className={styles.statusMessage}>
                  <Spinner size="extra-tiny" />
                  <Text>{statusMessage}</Text>
                </div>
              </div>
            ) : (
              <Text></Text>
            )}
            {message && (
              <StatusMessage
                message={message.text}
                type={message.type}
                details={message.details}
              />
            )}
          </DialogContent>
          <DialogActions>
            <Button
              appearance="secondary"
              onClick={closeDialog}
              disabled={isRepairing}
            >
              Cancel
            </Button>

            {actionButtons}
          </DialogActions>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};

export default ProjectSettings;
