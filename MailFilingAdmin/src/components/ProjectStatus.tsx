import React from "react";
import { CMapProjectStatus, Project } from "../types";
import { makeStyles } from "@fluentui/react-components";
import { CMapLogo } from "./CMapLogo";

type IProjectStatus = {
  project: Project;
};

const useStyles = makeStyles({
  noLogo: {
    width: "16px",
    height: "16px",
    marginRight: "12px",
  },
});

export const ProjectStatus: React.FC<IProjectStatus> = ({ project }) => {
  const styles = useStyles();
  switch (project.cmapProject) {
    case CMapProjectStatus.NotInCmap:
      return <div className={styles.noLogo}></div>;
    case CMapProjectStatus.CMapNotCreated:
      return <CMapLogo />;

    case CMapProjectStatus.CMapCreated:
      return <CMapLogo />;
    default:
      return null;
  }
};
