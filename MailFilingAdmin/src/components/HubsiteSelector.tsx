import React from "react";
import {
  Combobox,
  Option,
  makeStyles,
  useId,
  Spinner,
  tokens,
  Avatar,
} from "@fluentui/react-components";
import { Hubsite } from "../types";

interface HubSiteSelectorProps {
  hubsites?: Hubsite[];
  onHubsiteSelect: (selectedHubsite: Hubsite | undefined) => void;
  isLoading?: boolean;
}

const useStyles = makeStyles({
  root: {
    display: "grid",
    gridTemplateRows: "repeat(1fr)",
    justifyItems: "start",
    gap: "2px",
    maxWidth: "300px",
    backgroundColor: tokens.colorNeutralBackground1,
    scrollbarWidth: "thin",
    scrollbarColor: `${tokens.colorNeutralBackground4} ${tokens.colorNeutralBackground1}`,
  },
  spinner: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100px",
  },
  optionContent: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    width: "100%",
  },
  avatar: {
    flexShrink: 0,
  },
});

const HubSiteSelector: React.FC<HubSiteSelectorProps> = ({
  hubsites = [],
  onHubsiteSelect,
  isLoading = false,
}) => {
  const styles = useStyles();
  const comboId = useId("hubsite-selector");
  const [selectedHubsite, setSelectedHubsite] = React.useState<string>("");

  const handleHubsiteSelect = (
    _event: any,
    data: { optionValue?: string; selectedOptions: string[] }
  ) => {
    const selected = data.selectedOptions[0];
    setSelectedHubsite(selected);

    const selectedHub = hubsites.find((hub) => hub.displayName === selected);
    onHubsiteSelect(selectedHub);
  };

  if (isLoading) {
    return (
      <div className={styles.spinner}>
        <Spinner size="small" />
      </div>
    );
  }

  const renderOption = (hubsite: Hubsite) => (
    <Option
      key={hubsite.url}
      value={hubsite.displayName}
      text={hubsite.displayName}
    >
      <div className={styles.optionContent}>
        <Avatar
          shape="square"
          name={hubsite.displayName}
          color={hubsite.url === "" ? "neutral" : "colorful"}
          size={28}
          className={styles.avatar}
        />
        {hubsite.displayName}
      </div>
    </Option>
  );

  return (
    <Combobox
      data-testid="hubsite-combobox"
      aria-labelledby={comboId}
      placeholder="Select a Hubsite"
      value={selectedHubsite}
      selectedOptions={selectedHubsite ? [selectedHubsite] : []}
      className={styles.root}
      onOptionSelect={handleHubsiteSelect}
    >
      {hubsites?.map(renderOption)}
    </Combobox>
  );
};

export default HubSiteSelector;
