import React, { useState } from "react";
import {
  Column,
  CreateProjectFunction,
  EnableAtveroMailFunction,
  Hubsite,
  Project,
  SortDirection,
  Tenancy,
  User,
} from "../types";
import {
  Input,
  makeStyles,
  mergeClasses,
  MessageBar,
  Switch,
  tokens,
  Tooltip,
  useId,
} from "@fluentui/react-components";
import ProjectsTable from "./ProjectsTable";
import CreateProjectButton from "./CreateProjectButton";
import UsersModal from "./UsersModal";
import { SPFI } from "@pnp/sp";
import EnableAtveroMail from "./EnableAtveroMail";
import { Info16Regular } from "@fluentui/react-icons";
import HubSiteSelector from "./HubsiteSelector";

interface ProjectsProps {
  hubsites: Hubsite[] | undefined;
  onHubsiteSelect: (hubsite: Hubsite | undefined) => void;
  clearProjects: () => void;
  sharepoint: SPFI | undefined;
  hubsite: Hubsite | undefined;
  projects: Project[];
  loading: boolean;
  hasNextPage: boolean;
  loadNextPage: (startIndex: number, endIndex: number) => void;
  isNextPageLoading: boolean;
  onSortChange: (columnKey: string, direction: SortDirection) => void;
  currentSort: { columnKey: string; direction: SortDirection };
  columns: Column[];
  onSearch: (searchValue: string) => void;
  searchString: string;
  onCreateProject: CreateProjectFunction;
  onFetchUsers: (projectUrl: string) => Promise<User[]>;
  onRepairProject: (
    project: Project,
    onProgressUpdate: (progress: number, message: string) => void
  ) => Promise<{ success: boolean; message: string; details?: string }>;
  hasCMapProjects: boolean;
  selectedProjects: Project[];
  onProjectSelect: (email: Project) => void;
  onEnableAtveroMail: EnableAtveroMailFunction;
  onToggleAtveroProjects: (checked: boolean) => void;
  showAtveroProjects: boolean;
  isAdmin: boolean | undefined;
  tenancy: Tenancy;
}

const useStyles = makeStyles({
  wrapper: { height: "100%" },
  topContainer: {
    height: "36px",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "20px",
  },
  searchInput: {
    minWidth: "300px",
    marginLeft: "12px",
  },
  switchContainer: {
    display: "flex",
    alignItems: "center",
  },
  switchWrapper: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    marginLeft: "16px",
  },
  messageBar: {
    marginBottom: "12px",
  },
  switchLabel: {
    marginRight: "5px",
  },
  selectorSearchContainer: {
    display: "flex",
    flexDirection: "row",
  },
  visible: {
    color: tokens.colorNeutralForeground2BrandSelected,
  },
  switchTooltipWrapper: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
});

const Projects: React.FC<ProjectsProps> = ({
  hubsites,
  onHubsiteSelect,
  clearProjects,
  sharepoint,
  hubsite,
  projects,
  loading,
  hasNextPage,
  loadNextPage,
  isNextPageLoading,
  onSortChange,
  currentSort,
  columns,
  onSearch,
  searchString,
  onCreateProject,
  onFetchUsers,
  onRepairProject,
  hasCMapProjects,
  selectedProjects,
  onProjectSelect,
  onEnableAtveroMail,
  onToggleAtveroProjects,
  showAtveroProjects,
  isAdmin,
  tenancy,
}) => {
  const styles = useStyles();
  const contentId = useId("content");
  const [visible, setVisible] = React.useState(false);
  const [projectUrl, setProjectUrl] = useState<string>("");
  const [shouldOpenUsersModal, setShouldOpenUsersModal] = useState(false);
  const tenancyText =
    tenancy === "cmap" ? "Not CMap Mail Enabled" : "Not Atvero Mail Enabled";

  const handleSearchChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    onSearch(event.target.value);
  };

  const handleSwitchChange = (
    _ev?: React.ChangeEvent<HTMLInputElement>,
    data?: { checked: boolean }
  ) => {
    onToggleAtveroProjects?.(data?.checked ?? false);
  };

  const handleProjectUrlChange = (url: string): void => {
    setProjectUrl(url);
  };

  const handleOpenUsersModal = (): void => {
    setShouldOpenUsersModal(true);
  };

  const handleCloseUsersModal = (): void => {
    setShouldOpenUsersModal(false);
  };

  return (
    <div className={styles.wrapper} data-testid="projects-table">
      <div className={styles.topContainer}>
        <div className={styles.selectorSearchContainer}>
          {hubsites && hubsites.length > 1 ? (
            <HubSiteSelector
              hubsites={hubsites}
              onHubsiteSelect={(hubsite) => {
                onHubsiteSelect(hubsite);
                clearProjects();
              }}
            />
          ) : null}
          <Input
            value={searchString}
            onChange={handleSearchChange}
            placeholder="Search projects..."
            className={styles.searchInput}
          />
        </div>
        <div className={styles.switchContainer}>
          {!hasCMapProjects && isAdmin && (
            <CreateProjectButton
              sharepoint={sharepoint}
              hubsite={hubsite}
              onCreateProject={onCreateProject}
              onProjectUrlChange={handleProjectUrlChange}
              onOpenUsersModal={handleOpenUsersModal}
            />
          )}
          {hasCMapProjects && isAdmin && (
            <>
              <div>
                <EnableAtveroMail
                  selectedProjects={selectedProjects}
                  onEnableAtveroMail={onEnableAtveroMail}
                  tenancy={tenancy}
                />
              </div>

              <div className={styles.switchWrapper}>
                <div className={styles.switchTooltipWrapper}>
                  <span className={styles.switchLabel}>{tenancyText}</span>
                  <Tooltip
                    content={{
                      children:
                        "Only show CMap projects that don't have a Atvero project.",
                      id: contentId,
                    }}
                    positioning="above-start"
                    withArrow
                    relationship="label"
                    onVisibleChange={(_e, data) => setVisible(data.visible)}
                  >
                    <Info16Regular
                      tabIndex={0}
                      className={mergeClasses(visible && styles.visible)}
                    />
                  </Tooltip>
                </div>
                <Switch
                  checked={showAtveroProjects}
                  onChange={handleSwitchChange}
                />
              </div>
            </>
          )}
        </div>
      </div>
      {projectUrl && (
        <UsersModal
          projectUrl={projectUrl}
          disabled={false}
          onFetchUsers={onFetchUsers}
          showButton={false}
          initialOpen={shouldOpenUsersModal}
          onClose={handleCloseUsersModal}
        />
      )}
      {!hubsite ? (
        <MessageBar className={styles.messageBar}>
          Please select a hubsite to view your projects.
        </MessageBar>
      ) : (
        <ProjectsTable
          projects={projects}
          selectedProjects={selectedProjects}
          loading={loading}
          hasNextPage={hasNextPage}
          loadNextPage={loadNextPage}
          isNextPageLoading={isNextPageLoading}
          onSortChange={onSortChange}
          currentSort={currentSort}
          columns={columns}
          onFetchUsers={onFetchUsers}
          onRepairProject={onRepairProject}
          onCreateProject={onCreateProject}
          onProjectSelect={onProjectSelect}
          hasCMapProjects={hasCMapProjects}
        />
      )}
    </div>
  );
};

export default Projects;
