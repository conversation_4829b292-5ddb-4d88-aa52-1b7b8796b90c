import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogTrigger,
  DialogSurface,
  DialogBody,
  DialogTitle,
  DialogContent,
  Text,
  DialogActions,
  ProgressBar,
  Spinner,
  makeStyles,
} from "@fluentui/react-components";
import { EnableAtveroMailFunction, Project, Tenancy } from "../types";
import { ArrowSync16Filled } from "@fluentui/react-icons";
import { List, ListItem } from "@fluentui/react-list-preview";
import StatusMessage from "./StatusMessage";

interface EnableAtveroMailProps {
  selectedProjects: Project[];
  onEnableAtveroMail: EnableAtveroMailFunction;
  tenancy: Tenancy;
}

const useStyles = makeStyles({
  progressContainer: {
    marginTop: "10px",
    display: "flex",
    flexDirection: "column",
    gap: "10px",
  },
  statusMessage: {
    display: "flex",
    flexDirection: "row",
    gap: "10px",
  },
  statusContainer: {
    marginTop: "10px",
  },
});

const EnableAtveroMail: React.FC<EnableAtveroMailProps> = ({
  selectedProjects,
  onEnableAtveroMail,
  tenancy,
}) => {
  const styles = useStyles();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const showButton = selectedProjects.length > 0;
  const [progress, setProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState("");
  const [isEnabling, setIsEnabling] = useState(false);
  const [message, setMessage] = useState<{
    type: "error" | "success" | "info" | "warning";
    text: string;
    details?: string;
  } | null>(null);

  const tenancyText =
    tenancy === "cmap" ? "Enable CMap Mail" : "Enable Atvero Mail";

  const handleStartSync = async (): Promise<void> => {
    setMessage(null);
    setProgress(0);
    setStatusMessage("Initializing project creation...");
    setIsEnabling(true);
    try {
      const result = await onEnableAtveroMail(
        selectedProjects,
        (progress: number, message: string) => {
          setProgress(progress);
          setStatusMessage(message);
        }
      );

      if (result.success) {
        setProgress(1);
        setStatusMessage(`All Projects were succesfully synced`);
        setMessage({
          type: "success",
          text: result.message,
          details: result.details,
        });
      } else {
        setMessage({
          type: "error",
          text: result.message,
          details: result.details,
        });
      }
    } catch (error) {
      setMessage({
        type: "error",
        text: "An unexpected error occurred while enabling Atvero for the selected projects.",
        details: error instanceof Error ? error.message : String(error),
      });
    } finally {
      setIsEnabling(false);
    }
  };

  useEffect(() => {
    if (!isModalOpen) {
      closeDialog();
    }
  }, [isModalOpen]);

  const closeDialog = (): void => {
    setIsModalOpen(false);
    setMessage(null);
    setProgress(0);
    setStatusMessage("");
  };

  return (
    <>
      {showButton && (
        <Dialog
          open={isModalOpen}
          onOpenChange={(_e, data) => setIsModalOpen(data.open)}
        >
          <DialogTrigger disableButtonEnhancement>
            <Button
              icon={<ArrowSync16Filled />}
              iconPosition="after"
              onClick={() => setIsModalOpen(true)}
              size="medium"
            >
              {tenancyText}
            </Button>
          </DialogTrigger>
          <DialogSurface>
            <DialogBody>
              <DialogTitle>{tenancyText}</DialogTitle>
              <DialogContent>
                <List>
                  {selectedProjects.map((project) => (
                    <ListItem key={project.cMapId || project.id}>
                      <Text>{project.title}</Text>
                    </ListItem>
                  ))}
                </List>
                {isEnabling && (
                  <div className={styles.progressContainer}>
                    <ProgressBar value={progress} />
                    <div className={styles.statusMessage}>
                      <Spinner size="extra-tiny" />
                      <Text>{statusMessage}</Text>
                    </div>
                  </div>
                )}
                {message && (
                  <div className={styles.statusContainer}>
                    <StatusMessage
                      message={message.text}
                      type={message.type}
                      details={message.details}
                    />
                  </div>
                )}
              </DialogContent>
              {!isEnabling && (
                <DialogActions>
                  <Button appearance="secondary" onClick={closeDialog}>
                    Close
                  </Button>
                  {!message && (
                    <Button appearance="primary" onClick={handleStartSync}>
                      Enable
                    </Button>
                  )}
                </DialogActions>
              )}
            </DialogBody>
          </DialogSurface>
        </Dialog>
      )}
    </>
  );
};

export default EnableAtveroMail;
