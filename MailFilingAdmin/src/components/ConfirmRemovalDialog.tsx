import React from "react";
import {
  Dialog,
  DialogSurface,
  DialogBody,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from "@fluentui/react-components";

interface ConfirmRemovalDialogProps {
  isOpen: boolean;
  userToDelete: { name: string } | null;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmRemovalDialog: React.FC<ConfirmRemovalDialogProps> = ({
  isOpen,
  userToDelete,
  onConfirm,
  onCancel,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={(_, { open }) => !open && onCancel()}>
      <DialogSurface>
        <DialogBody>
          <DialogTitle>Confirm Removal</DialogTitle>
          <DialogContent>
            Are you sure you want to remove {userToDelete?.name} from this
            project?
          </DialogContent>
          <DialogActions>
            <Button appearance="secondary" onClick={onCancel}>
              Close
            </Button>
            <Button appearance="primary" onClick={onConfirm}>
              Confirm
            </Button>
          </DialogActions>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};

export default ConfirmRemovalDialog;
