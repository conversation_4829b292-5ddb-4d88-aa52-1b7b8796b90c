import { makeStyles } from "@fluentui/react-components";

const useStyles = makeStyles({
  cmapLogo: {
    backgroundImage:
      "url('data:image/svg+xml;base64,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')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "contain",
    width: "16px",
    height: "16px",
    marginRight: "12px",
  },
});

export const CMapLogo = (): JSX.Element => {
  const styles = useStyles();
  return <div className={styles.cmapLogo}></div>;
};
