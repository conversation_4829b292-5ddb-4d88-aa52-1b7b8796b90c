import React, { useCallback, useEffect, useState } from "react";
import {
  makeStyles,
  shorthands,
  <PERSON><PERSON><PERSON><PERSON>,
  Tab,
  SelectTabData,
  SelectTabEvent,
  MessageBar,
} from "@fluentui/react-components";
import { AtveroLogo } from "./assets/AtveroLogo";
import { useGraph } from "./services/useGraph";
import "@pnp/graph/users";
import LoginButton from "./components/LoginButton";
import { getProjectList } from "./services/getProjects";
import {
  CMapProjectStatus,
  Column,
  CreateProjectPromise,
  EnableAtveroMailFunction,
  Hubsite,
  Project,
  ProjectResponse,
  SortDirection,
  Tenancy,
  User,
} from "./types";
import { fetchShareRootURL } from "./services/graphHelpers";
import Projects from "./components/Projects";
import {
  createProject,
  getProjectDesign,
  repairProject,
} from "./services/createProject";
import { fetchProjectUsers } from "./services/getProjectUsers";
import { handleProjectSelect } from "./services/projectSelection";
import { enableAtveroMail } from "./services/enableAtveroService";
import { checkIfUserIsAdmin } from "./services/checkAdminUser";
import { Adapters } from "./adapters/AdapterFactory";
import { isSortableColumn } from "./utils/sortingUtils";
import CMapLogo from "./assets/CMap PIM & CMap Mail/CMap Mail/Black_Text/SVG/CMap_Mail_black_text_svg.svg";
import {
  getFallbackSiteDesignName,
  getSiteDesignName,
} from "./utils/siteDesignUtils";

const useStyles = makeStyles({
  loginContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
    width: "100%",
    textAlign: "center",
    gap: "20px",
  },
  tabText: {
    color: "#242424",
  },
  container: {
    display: "flex",
    height: "98.5vh",
  },
  sidebar: {
    width: "200px",
    backgroundColor: "#f0f0f0",
    ...shorthands.padding("20px"),
    display: "flex",
    flexDirection: "column",
  },
  content: {
    flex: 1,
    ...shorthands.padding("20px"),
    overflowY: "auto",
  },
  logo: {
    width: "169px",
  },
});

interface AppProps {
  readonly adapter: Adapters;
  readonly hubsites: Hubsite[] | undefined;
  readonly selectedHubsite: Hubsite | undefined;
  readonly hubsiteError: string | undefined;
  readonly onHubsiteSelect: (hubsite: Hubsite | undefined) => void;
  readonly tenancy: Tenancy;
}
const App: React.FC<AppProps> = ({
  adapter,
  hubsites,
  selectedHubsite,
  hubsiteError,
  onHubsiteSelect,
  tenancy,
}) => {
  const styles = useStyles();
  const tableSize = 200;
  const [selectedTab, setSelectedTab] = useState<string>("projects");
  const { loginError, manualLogin } = useGraph();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMoreItems, setHasMoreItems] = useState(false);
  const [page, setPage] = useState(0); //set page to 0 when we make any kind of search/change to projects list. pass page to search/column sorting
  const [isNextPageLoading, setIsNextPageLoading] = useState(false);
  const [columnCode, setColumnCode] = useState<string | undefined>("code");
  const [sortDirection, setSortDirection] = useState<SortDirection>(
    SortDirection.Descending
  );
  const [appendEmailData, setAppendEmailData] = useState<boolean>(false);
  const [sortColumn, setSortColumn] = useState<string>("descending");
  const [searchString, setSearchString] = useState<string>("");
  const [hasCMapProjects, setHasCMapProjects] = useState(false);
  const [selectedProjects, setSelectedProjects] = useState<Project[]>([]);
  const [showAtveroProjects, setShowAtveroProjecs] = useState<boolean>(false);
  const [isAdmin, setIsAdmin] = useState<boolean | undefined>(undefined);
  const tenancyName = tenancy === Tenancy.Atvero ? "Atvero" : "CMap";
  const siteDesignName = getSiteDesignName(tenancy);
  const fallbackSiteDesignName = getFallbackSiteDesignName(tenancy);

  // Change these to the correct sort column keys once site search
  // managed props have been updated to be sortable.
  const columns = [
    {
      columnKey: "ProjectCode",
      label: "Project Code",
      sortable: true,
    },
    {
      columnKey: "Description",
      label: "Project Title",
      sortable: true,
    },
    {
      columnKey: "",
      label: "Actions",
      sortable: false,
    },
  ] as Column[];

  useEffect(() => {
    const checkForAdmin = async () => {
      const isAdmin = await checkIfUserIsAdmin(
        adapter.legacyAdapter.getGraph()
      );

      setIsAdmin(isAdmin);
    };
    checkForAdmin();
  }, [adapter.legacyAdapter.getGraph()]);

  useEffect(() => {
    const resetProjectsAndLoading = (): void => {
      setLoading(true);
      setProjects([]);
    };

    const fetchProjectList = async (): Promise<ProjectResponse | undefined> => {
      if (adapter.legacyAdapter.getSharepoint() && selectedHubsite) {
        const sortDirectionSearch =
          sortDirection === SortDirection.Descending ? "desc" : "asc";

        const validColumnCode = isSortableColumn(columnCode)
          ? columnCode
          : undefined;

        return await getProjectList(
          adapter.legacyAdapter.getSharepoint(),
          selectedHubsite,
          tableSize,
          page,
          validColumnCode,
          sortDirectionSearch,
          searchString,
          showAtveroProjects
        );
      }
    };

    const updateProjects = (newProjects: Project[]): void => {
      setProjects((prevProjects) =>
        appendEmailData ? [...prevProjects, ...newProjects] : newProjects
      );
    };

    const handleEmptyResponse = (): void => {
      if (!appendEmailData) {
        setProjects([]);
      }
      setHasMoreItems(false);
    };

    const updateProjectsBasedOnResponse = (
      response: ProjectResponse | undefined
    ): void => {
      if (response && response.projects.length > 0) {
        updateProjects(response.projects);
        setAppendEmailData(true);
        setHasMoreItems(
          response.moreResultsAvailable ?? response.nextLink !== undefined
        );
      } else {
        handleEmptyResponse();
      }
    };

    const handleFetchError = (error: unknown): void => {
      if (error instanceof Error) {
        console.error("Error fetching projects: ", error.message);
      } else {
        console.error("Unknown error fetching projects: ", error);
      }
    };
    const fetchAndSetProjects = async (): Promise<void> => {
      try {
        const response = await fetchProjectList();
        if (response) {
          const projectsWithCMap = response.projects.some(
            (project) =>
              project.cmapProject === CMapProjectStatus.CMapCreated ||
              project.cmapProject === CMapProjectStatus.CMapNotCreated
          );
          setHasCMapProjects(projectsWithCMap);
          updateProjectsBasedOnResponse(response);
        }
      } catch (error: unknown) {
        handleFetchError(error);
      }
    };
    const getProjects = async (): Promise<void> => {
      if (!appendEmailData) {
        resetProjectsAndLoading();
      }
      setIsNextPageLoading(true);

      if (
        adapter.legacyAdapter.getSharepoint() &&
        adapter.legacyAdapter.getGraph() &&
        selectedHubsite
      ) {
        await fetchAndSetProjects();
      }

      setIsNextPageLoading(false);
      setLoading(false);
    };

    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    if (isAdmin) {
      getProjects();
    }
  }, [
    selectedHubsite,
    page,
    sortColumn,
    sortDirection,
    searchString,
    adapter.legacyAdapter.getGraph(),
    adapter.legacyAdapter.getSharepoint(),
    columnCode,
    showAtveroProjects,
    isAdmin,
  ]);

  const handleSearch = (searchValue: string): void => {
    setSearchString(searchValue);
    setPage(0);
    setAppendEmailData(false);
  };

  const loadMoreItems = useCallback(() => {
    if (hasMoreItems ?? page !== 0) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [hasMoreItems, page]);

  const onTabSelect = (_event: SelectTabEvent, data: SelectTabData): void => {
    setSelectedTab(data.value as string);
  };

  const handleSortChange = (
    columnKey: string,
    direction: SortDirection
  ): void => {
    if (columnKey === "ProjectCode") {
      setColumnCode("code");
    } else if (columnKey === "Description") {
      setColumnCode("title");
    }

    setSortColumn(columnKey);
    setAppendEmailData(false);
    setSortDirection(direction);
    setPage(0);
  };

  const onProjectSelect = (project: Project) => {
    handleProjectSelect(project, setSelectedProjects);
  };

  const onToggleAtveroProjects = (checked: boolean) => {
    setAppendEmailData(false);
    setShowAtveroProjecs(checked);
  };

  /* v8 ignore next 187 */
  const handleCreateProject = async (
    projectTitle: string,
    projectCode: string,
    cmapProjectId: number | undefined,
    onProgressUpdate: (progress: number, message: string) => void,
    selectedProject: Project | undefined,
    cancelToken?: { cancelled: boolean }
  ): Promise<CreateProjectPromise> => {
    if (!adapter.legacyAdapter.getGraph()) {
      return {
        success: false,
        message: "Graph API connection not available",
        details: `Unable to establish connection to Microsoft Graph API. Please check your permissions and have signed into the correct account. If youre still hving issues please contact your ${tenancyName} Mail administrator.`,
      };
    }

    if (!selectedHubsite) {
      return {
        success: false,
        message: "Hub site configuration missing",
        details: `Your Hubsite could not be found. Please ensure you have a hubsite selected or you're logged into the correct account and your account is using ${tenancyName} Mail as your main Hubsite.`,
      };
    }

    if (!adapter.legacyAdapter.getSharepoint()) {
      return {
        success: false,
        message: "SharePoint connection not available",
        details:
          "Unable to establish connection to SharePoint. Please verify your SharePoint access and try again.",
      };
    }

    if (!adapter.legacyAdapter.getSharepointHostName()) {
      return {
        success: false,
        message: "We couldnt find you SharePoint Host Name",
        details:
          "The SharePoint host name is not properly configured. Please contact your system administrator.",
      };
    }

    try {
      if (cancelToken?.cancelled) {
        onProgressUpdate(0.1, "Cancelling...");
        return {
          success: true,
          message: "Operation cancelled",
          details: "Project creation was cancelled.",
        };
      }

      onProgressUpdate(0.1, `Setting up ${tenancyName} Mail...`);

      onProgressUpdate(
        0.1,
        `Setting up ${tenancy === Tenancy.CMap ? "CMap Mail" : "Atvero"}...`
      );

      const siteDesign = await getProjectDesign(
        adapter.legacyAdapter.getSharepoint(),
        siteDesignName,
        fallbackSiteDesignName
      );

      if (!siteDesign) {
        return {
          success: false,
          message: "Failed to create the project.",
          details: `Neither '${siteDesignName}' nor '${fallbackSiteDesignName}' site designs were found. Please double check the ${tenancyName} Install Process was correctly run.`,
        };
      }

      onProgressUpdate(
        0.2,
        `${tenancyName} setup found. Preparing to create project...`
      );

      if (cancelToken?.cancelled) {
        onProgressUpdate(0.1, "Cancelling...");
        return {
          success: true,
          message: "Operation cancelled",
          details: "Project creation was cancelled.",
        };
      }
      const sharePointUrl = await fetchShareRootURL(
        adapter.legacyAdapter.getGraph()
      );

      if (cancelToken?.cancelled) {
        onProgressUpdate(0.1, "Cancelling...");
        return {
          success: true,
          message: "Operation cancelled",
          details: "Project creation was cancelled.",
        };
      }
      let cancelled = cancelToken ? cancelToken : { cancelled: false };
      const newProjectUrl = await createProject(
        adapter.legacyAdapter.getGraph(),
        adapter.legacyAdapter.getSharepoint(),
        adapter.backendAdapter,
        tenancyName,
        sharePointUrl,
        siteDesign.Id,
        projectTitle,
        projectCode,
        cmapProjectId,
        selectedHubsite,
        selectedProject,
        (progress, message) => {
          if (!cancelToken?.cancelled) {
            onProgressUpdate(progress, message);
          }
        },
        cancelled
      );

      if (cancelToken?.cancelled) {
        onProgressUpdate(0.1, "Cancelling...");
        return {
          success: true,
          message: "Operation cancelled",
          details: "Project creation was cancelled.",
        };
      }

      if (newProjectUrl) {
        if (
          typeof newProjectUrl === "string" &&
          newProjectUrl.includes("already exists")
        ) {
          return {
            success: false,
            message: "A project with this code already exists.",
            details: newProjectUrl,
          };
        }

        // Refresh the projects list
        setPage(0);
        setAppendEmailData(false);
        return {
          success: true,
          message: `Project ${projectCode} created successfully!`,
          details: `To create another project, start typing in the project code/title fields or you can now manage the users for ${projectTitle}.`,
          projectURL: newProjectUrl,
        };
      }

      return {
        success: false,
        message: "Failed to create the project.",
        details:
          "The project creation process did not return a valid project URL.",
      };
    } catch (error) {
      if (cancelToken?.cancelled) {
        onProgressUpdate(0.1, "Cancelling...");
        return {
          success: true,
          message: "Operation cancelled",
          details: "Project creation was cancelled.",
        };
      }

      console.error("Error creating project: ", error);
      return {
        success: false,
        message: "An error occurred while creating the project.",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  };

  const clearProjects = () => {
    setPage(0);
    setSelectedProjects([]);
    setHasMoreItems(false);
    setAppendEmailData(false);
  };

  const handleEnableAtveroMail: EnableAtveroMailFunction = (
    projects,
    onProgressUpdate
  ) => {
    return enableAtveroMail({
      adapter,
      selectedHubsite,
      projects,
      onProgressUpdate,
      tenancyName,
      siteDesignName,
      fallbackSiteDesignName,
    });
  };

  const handleRepairProject = async (
    project: Project,
    onProgressUpdate: (progress: number, message: string) => void
  ): Promise<{ success: boolean; message: string; details?: string }> => {
    if (
      !adapter.legacyAdapter.getGraph() ||
      !selectedHubsite ||
      !adapter.legacyAdapter.getSharepoint()
    ) {
      return {
        success: false,
        message: "Failed to repair the project.",
        details: "Missing dependencies for repairing projects.",
      };
    } else {
      return repairProject(
        adapter.legacyAdapter.getSharepoint(),
        tenancyName,
        siteDesignName,
        selectedHubsite,
        project,
        onProgressUpdate,
        fallbackSiteDesignName
      );
    }
  };

  const fetchUsers = async (projectUrl: string): Promise<User[]> => {
    try {
      const users = await fetchProjectUsers(
        adapter.sharepointAdapter,
        projectUrl
      );
      return users;
    } catch (error) {
      console.error("Failed to fetch users:", error);
      throw error;
    }
  };

  const renderContent = (): JSX.Element => {
    switch (selectedTab) {
      case "projects":
        return (
          <>
            {isAdmin === false && (
              <MessageBar
                data-testid="admin-access-error-message"
                intent="error"
                style={{ marginBottom: "10px" }}
              >
                You are not a member of the administrators group. You must be
                added to the {tenancyName} Mail Admins group to access this
                page.
              </MessageBar>
            )}
            {hubsiteError && (
              <MessageBar
                data-testid="hubsite-error-message"
                intent="warning"
                style={{ marginBottom: "10px" }}
              >
                No hubsites were found for your account. Please contact your
                administrator to get access to the required hubsites.
              </MessageBar>
            )}
            <Projects
              hubsites={hubsites}
              onHubsiteSelect={onHubsiteSelect}
              clearProjects={clearProjects}
              sharepoint={adapter.legacyAdapter.getSharepoint()}
              hubsite={selectedHubsite}
              projects={projects}
              loading={loading}
              hasNextPage={hasMoreItems}
              loadNextPage={loadMoreItems}
              isNextPageLoading={isNextPageLoading}
              onSortChange={handleSortChange}
              currentSort={{
                columnKey: sortColumn ?? "",
                direction: sortDirection,
              }}
              columns={columns}
              onSearch={handleSearch}
              searchString={searchString}
              onCreateProject={handleCreateProject}
              onFetchUsers={fetchUsers}
              onRepairProject={handleRepairProject}
              hasCMapProjects={hasCMapProjects}
              selectedProjects={selectedProjects}
              onProjectSelect={onProjectSelect}
              onEnableAtveroMail={handleEnableAtveroMail}
              onToggleAtveroProjects={onToggleAtveroProjects}
              showAtveroProjects={showAtveroProjects}
              isAdmin={isAdmin}
              tenancy={tenancy}
            />
          </>
        );
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <div className={styles.container}>
      {loginError ? (
        <div className={styles.loginContainer}>
          {tenancy === Tenancy.CMap ? (
            <img src={CMapLogo} alt="CMap Mail" className={styles.logo} />
          ) : (
            <AtveroLogo />
          )}
          <h2>Login Failed</h2>
          <p>We couldn't log you in automatically. Please try again.</p>
          <LoginButton onLogin={manualLogin} />
        </div>
      ) : (
        <>
          <div className={styles.sidebar}>
            {tenancy === Tenancy.CMap ? (
              <img src={CMapLogo} alt="CMap Mail" className={styles.logo} />
            ) : (
              <AtveroLogo />
            )}
            <TabList
              vertical
              selectedValue={selectedTab}
              onTabSelect={onTabSelect}
            >
              <Tab className={styles.tabText} value="projects">
                Projects{" "}
              </Tab>
            </TabList>
            {/* <LogoutButton onLogout={handleLogout} /> */}
          </div>
          <div className={styles.content}>{renderContent()}</div>
        </>
      )}
    </div>
  );
};

export default App;
