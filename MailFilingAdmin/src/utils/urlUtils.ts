export const extractSharepointDomain = (url: string): string => {
  try {
    const { hostname } = new URL(url);
    return hostname;
  } catch (error) {
    console.error("Invalid URL:", error);
    return "";
  }
};

export const extractSiteId = (
  fullSiteId: string | undefined
): string | undefined => {
  if (!fullSiteId) return undefined;

  const parts = fullSiteId.split(",");
  return parts.length >= 2 ? parts[1] : undefined;
};
