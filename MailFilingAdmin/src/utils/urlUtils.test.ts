import { describe, it, expect } from "vitest";
import { extractSharepointDomain, extractSiteId } from "./urlUtils";

describe("extractSharepointDomain", () => {
  it("returns the hostname for a valid SharePoint URL", () => {
    const url = "https://test.sharepoint.com/sites/example";
    const expectedHostname = "test.sharepoint.com";

    const result = extractSharepointDomain(url);

    expect(result).toBe(expectedHostname);
  });

  it("returns an empty string for an invalid URL", () => {
    const url = "invalid-url";
    const expectedHostname = "";

    const result = extractSharepointDomain(url);

    expect(result).toBe(expectedHostname);
  });

  it("returns the hostname for a valid URL with a different domain", () => {
    const url = "https://example.com/path";
    const expectedHostname = "example.com";

    const result = extractSharepointDomain(url);

    expect(result).toBe(expectedHostname);
  });

  it("returns an empty string for an empty URL", () => {
    const url = "";
    const expectedHostname = "";

    const result = extractSharepointDomain(url);

    expect(result).toBe(expectedHostname);
  });

  it("returns the hostname for a URL with a subdomain", () => {
    const url = "https://subdomain.test.sharepoint.com/sites/example";
    const expectedHostname = "subdomain.test.sharepoint.com";

    const result = extractSharepointDomain(url);

    expect(result).toBe(expectedHostname);
  });
  describe("extractSiteId", () => {
    it("extracts site ID from full site ID string", () => {
      expect(extractSiteId("sites,abc123,web")).toBe("abc123");
    });

    it("returns undefined for missing input", () => {
      expect(extractSiteId(undefined)).toBeUndefined();
    });

    it("returns undefined for invalid format", () => {
      expect(extractSiteId("invalid-format")).toBeUndefined();
    });
  });
});
