import config from "../../config.json";

import { Environment } from "../types";

export const Product = (): string => {
  return config.product;
};

export const ClientId = (): string => {
  return config.appid;
};

export const RedirectUri = (): string => {
  return config.redirectUrl;
};
export const BackendUrl = () => {
  return config.backendUrl;
};

export const Scopes = (): string[] => {
  return [
    "User.Read",
    "Sites.Read.All",
    "Contacts.Read",
    "Files.ReadWrite",
    "Group.Read.All",
    "Mail.ReadWrite",
    "ProfilePhoto.Read.All",
    "Sites.FullControl.All",
  ];
};

export const DefaultHubsitePath = (): string => {
  return `/sites/${Product()}Mail`;
};

export const BackendScope = () => {
  return `api://${config.backendScope}/${config.appid}/access_as_user`;
};

export const CurrentEnvironment = (): Environment => {
  const mode = import.meta.env.MODE;

  switch (mode) {
    case "development":
      return Environment.Development;
    case "production":
      return Environment.Production;
    default:
      return Environment.Production; // Default to production for safety
  }
};
