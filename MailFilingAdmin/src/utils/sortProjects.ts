import { Project, SortableProjectKeys } from "../types";

export const sortProjects = (
  projects: Project[],
  sortField?: SortableProjectKeys,
  sortDirection?: "asc" | "desc"
): Project[] => {
  if (!sortField || !projects.length) return projects;

  if (sortDirection !== "asc" && sortDirection !== "desc") {
    return projects;
  }

  return [...projects].sort((a, b) => {
    const valA = a[sortField] ?? "";
    const valB = b[sortField] ?? "";

    if (valA === "" && valB !== "") return 1;
    if (valB === "" && valA !== "") return -1;

    const isNumeric = (str: string) => /^\d+$/.test(str);

    const aIsNum = isNumeric(valA.toString());
    const bIsNum = isNumeric(valB.toString());

    if (aIsNum && bIsNum) {
      const numA = parseInt(valA.toString(), 10);
      const numB = parseInt(valB.toString(), 10);
      return sortDirection === "asc" ? numA - numB : numB - numA;
    }

    // fallback to string comparison (preserves leading zeros & mixed types)
    return sortDirection === "asc"
      ? valA.toString().localeCompare(valB.toString())
      : valB.toString().localeCompare(valA.toString());
  });
};
