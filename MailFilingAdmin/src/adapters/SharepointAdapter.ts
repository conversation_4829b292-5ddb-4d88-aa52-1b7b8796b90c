import { <PERSON><PERSON> } from "@pnp/sp";
import { ISharepointAdapter } from "./ISharepointAdapter";
import { User } from "../types";
import { Web } from "@pnp/sp/webs";
import { ISiteUserInfo } from "@pnp/sp/site-users/types";

export class SharepointAdapter implements ISharepointAdapter {
  sharepoint: SPFI;
  sharepointHostName: string;

  constructor(spclient: SPFI, sharepointHost: string) {
    this.sharepoint = spclient;
    this.sharepointHostName = sharepointHost;
  }

  private async getUsersFromGroup(
    projectUrl: string,
    groupIdentifier: {
      type: "id" | "name";
      value: number | string;
    }
  ): Promise<User[] | undefined> {
    try {
      const web = Web([this.sharepoint.web, projectUrl]);
      const group =
        groupIdentifier.type === "id"
          ? web.siteGroups.getById(groupIdentifier.value as number)
          : web.siteGroups.getByName(groupIdentifier.value as string);

      const users = await group.users();

      const mappedUsers: User[] = users.map((member: ISiteUserInfo) => {
        const email = member.Email ?? member.UserPrincipalName;
        return {
          loginName: member.LoginName,
          id: member.Id.toString(),
          name: member.Title,
          email: email ?? "Group",
        };
      });

      return mappedUsers;
    } catch (error: unknown) {
      console.error(`Failed to get users from group. Error:`, error);
      return undefined;
    }
  }

  async getMemberUsers(projectUrl: string): Promise<User[] | undefined> {
    try {
      const web = Web([this.sharepoint.web, projectUrl]);
      const memberGroup = await web.associatedMemberGroup();
      return this.getUsersFromGroup(projectUrl, {
        type: "id",
        value: memberGroup.Id,
      });
    } catch (error: unknown) {
      console.error("Failed to get member users. Error:", error);
      return undefined;
    }
  }

  async getConfidentialUsers(projectUrl: string): Promise<User[] | undefined> {
    try {
      return await this.getUsersFromGroup(projectUrl, {
        type: "name",
        value: "Confidential Users",
      });
    } catch (error: unknown) {
      console.error("Failed to get confidential users. Error:", error);
      return undefined;
    }
  }
}
