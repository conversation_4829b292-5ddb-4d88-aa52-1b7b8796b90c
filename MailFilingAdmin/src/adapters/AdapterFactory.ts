import { <PERSON>raph<PERSON> } from "@pnp/graph";
import { SPFI } from "@pnp/sp";
import { LegacyAdapter } from "./LegacyAdapter";
import { IBackendAdapter } from "./IBackendAdapters";
import { BackendAdapter } from "./BackendAdapter";
import { ILegacyAdapter } from "./ILegacyAdapter";
import { ISharepointAdapter } from "./ISharepointAdapter";
import { SharepointAdapter } from "./SharepointAdapter";

export type Adapters = {
  legacyAdapter: ILegacyAdapter;
  backendAdapter: IBackendAdapter;
  sharepointAdapter: ISharepointAdapter;
};

export const adapterFactory = async (
  graph: GraphFI,
  sharepoint: SPFI,
  sharepointHost: string
) => {
  const adapters: Adapters = {
    legacyAdapter: new LegacyAdapter(graph, sharepoint, sharepointHost),
    backendAdapter: new BackendAdapter(),
    sharepointAdapter: new SharepointAdapter(sharepoint, sharepointHost),
  };
  return adapters;
};
