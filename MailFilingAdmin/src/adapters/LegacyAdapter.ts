import { Graph<PERSON> } from "@pnp/graph";
import { SPFI } from "@pnp/sp";
import { ILegacyAdapter } from "./ILegacyAdapter";

// Do not add anything to this
// This was implemented for backwards compatability.
export class LegacyAdapter implements ILegacyAdapter {
  graph: GraphFI;
  sharepoint: SPFI;
  sharepointHostName: string;

  constructor(client: GraphFI, spclient: SPFI, sharepointHost: string) {
    this.graph = client;
    this.sharepoint = spclient;
    this.sharepointHostName = sharepointHost;
  }

  getGraph(): GraphFI {
    return this.graph;
  }
  getSharepoint(): SPFI {
    return this.sharepoint;
  }
  getSharepointHostName(): string {
    return this.sharepointHostName;
  }

  setSharepointHostName(host: string): void {
    this.sharepointHostName = host;
  }
}
