import {
  atveroMailBackend,
  atveroMailBackendPost,
} from "../services/backendCall/backendCall";
import { Hubsite } from "../types";
import { IBackendAdapter } from "./IBackendAdapters";

interface HubsiteResponse {
  IsSuccess: boolean;
  Message: string;
  Data: Array<{
    Name: string;
    SiteUrl: string;
    DisplayName: string;
  }>;
}

interface SettingResponse {
  IsSuccess: boolean;
  Message: string;
}

export class BackendAdapter implements IBackendAdapter {
  async createSite(
    hubsiteurl: string,
    name: string,
    description: string | undefined,
    hubsiteid: string | undefined
  ): Promise<boolean> {
    const request = {
      hubsite: hubsiteurl,
      site: name,
      description: description,
      hubsiteid: hubsiteid,
    };

    try {
      const siteInfo = await atveroMailBackendPost(
        "/api/CreateSiteNoGroup",
        JSON.stringify(request)
      );

      if (siteInfo) {
        return true;
      } else {
        console.error("Failed creating a site");
        return false;
      }
    } catch (error: any) {
      console.error(error);
      return false;
    }
  }

  async getHubsites(): Promise<{
    hubsites: Hubsite[] | undefined;
    error?: string;
  }> {
    try {
      const response = await atveroMailBackend("/api/Hubsites", "GET");

      if (response) {
        const hubsiteResponse = response as HubsiteResponse;

        if (hubsiteResponse.IsSuccess) {
          const mappedHubsites: Hubsite[] = hubsiteResponse.Data.map(
            (site) => ({
              displayName: site.DisplayName || "No Hubsite Name",
              name: site.Name,
              url: site.SiteUrl,
            })
          );

          return { hubsites: mappedHubsites };
        } else {
          return { hubsites: undefined, error: hubsiteResponse.Message };
        }
      }

      return { hubsites: undefined, error: "Failed to fetch hubsites" };
    } catch (error) {
      console.error("An error occurred while fetching hubsites");
      return {
        hubsites: undefined,
        error: "An error occurred while fetching hubsites",
      };
    }
  }

  async getSetting(setting: string): Promise<string | undefined> {
    try {
      const response = await atveroMailBackend(
        "/api/AppSettings?setting=" + setting,
        "GET"
      );

      const settingVal = response as SettingResponse;

      if (settingVal.Message === "") return undefined;

      return settingVal.Message;
    } catch (error: unknown) {
      if (error instanceof Error && error.message !== null) {
        console.error(error.message);
        return undefined;
      } else {
        return undefined;
      }
    }
  }
}
