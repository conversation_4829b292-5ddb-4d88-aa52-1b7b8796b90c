import { User } from "../types";
import { ISharepointAdapter } from "./ISharepointAdapter";

export class TestSharepointAdapter implements ISharepointAdapter {
  getMemberUsers(projectUrl: string): Promise<User[]> {
    console.log(projectUrl);
    throw new Error("Method not implemented.");
  }
  getConfidentialUsers(projectUrl: string): Promise<User[] | undefined> {
    console.log(projectUrl);
    throw new Error("Method not implemented.");
  }
}
