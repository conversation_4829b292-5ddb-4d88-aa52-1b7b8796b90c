import ReactDOM from "react-dom";
import "./index.css";
import { AuthenticationResult, EventType } from "@azure/msal-browser";
import { msalInstance } from "./services/useSharePoint";
import Root from "./AppInitializer";

const accounts = msalInstance.getAllAccounts();
if (accounts.length > 0) {
  msalInstance.setActiveAccount(accounts[0]);
}

msalInstance.addEventCallback((event) => {
  if (
    event.eventType === EventType.LOGIN_SUCCESS &&
    (event.payload as AuthenticationResult).account
  ) {
    const account = (event.payload as AuthenticationResult).account;
    msalInstance.setActiveAccount(account);
  }
});

ReactDOM.render(<Root />, document.getElementById("root"));
