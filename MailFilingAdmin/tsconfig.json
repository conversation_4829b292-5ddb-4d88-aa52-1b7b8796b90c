{
  "compilerOptions": {
    "esModuleInterop": true,
    "pretty": true,
    "outDir": "dist",
    "forceConsistentCasingInFileNames": true,
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable", "es7", "dom"],
    "module": "esnext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    "types": ["node", "vite/client", "vitest/globals"]
  },
  "include": [
    "src",
    "../shared/**/*",
    "**/*.tsx",
    "tests/graphCalls.test.ts",
    "tests/Config.test.ts"
  ],
  "references": [{ "path": "./tsconfig.node.json" }],
  "exclude": ["node_modules"],
  "compileOnSave": false,
  "buildOnSave": false,
  "ts-node": {
    "files": true
  }
}
