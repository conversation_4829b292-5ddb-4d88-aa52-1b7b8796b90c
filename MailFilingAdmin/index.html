<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/AtveroLogo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/assets/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/assets/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/assets/favicon-16x16.png"
    />
    <link rel="manifest" href="/assets/site.webmanifest" />
    <link
      rel="mask-icon"
      href="/assets/safari-pinned-tab.svg"
      color="#5bbad5"
    />
    <link rel="shortcut icon" href="/assets/favicon.ico" />
    <meta name="msapplication-TileColor" content="#da532c" />
    <meta name="msapplication-config" content="/assets/browserconfig.xml" />
    <meta name="theme-color" content="#ffffff" />
    <title>Atvero Mail Admin</title>
    <script>
      // This can be removed once all customers have been moved over to use
      // CMap mail tenancy.

      // Script to determine tenancy from URL and update title accordingly
      document.addEventListener("DOMContentLoaded", function () {
        // Get the current URL
        const currentUrl = window.location.href.toLowerCase();

        // Check if URL contains tenancy identifiers
        const isAtveroTenancy = currentUrl.includes("atveromail");
        const isCMapTenancy =
          currentUrl.includes("cmapmail") || !isAtveroTenancy; // Default to CMap if not Atvero

        // Update the document title based on tenancy
        if (isAtveroTenancy) {
          document.title = "Atvero Mail Admin";

          // Optional: Update favicon for Atvero
          const faviconLink = document.querySelector(
            'link[rel="icon"][type="image/svg+xml"]'
          );
          if (faviconLink) {
            faviconLink.href = "/AtveroLogo.svg";
          }
        } else {
          document.title = "CMap Mail Admin";
          // Update favicon for CMap (default)
          const faviconLink = document.querySelector(
            'link[rel="icon"][type="image/svg+xml"]'
          );
          if (faviconLink) {
            faviconLink.href = "/CMapLogo.svg";
          }
        }

        // You can also store the tenancy information for use elsewhere in your application
        window.appTenancy = isAtveroTenancy ? "atvero" : "cmap";

        // Optional: Log the detected tenancy for debugging
        console.log(`Detected tenancy: ${window.appTenancy}`);
      });
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
