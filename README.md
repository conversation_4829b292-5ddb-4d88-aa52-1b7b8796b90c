## Filing Plugin

[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=cmap-vsts_atvero-atveromailfrontend-filing&metric=alert_status&token=ffb08a2da701cd239961fe7727d6ab0d3a1be563)](https://sonarcloud.io/summary/new_code?id=cmap-vsts_atvero-atveromailfrontend-filing)

[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=cmap-vsts_atvero-atveromailfrontend-filing&metric=coverage&token=ffb08a2da701cd239961fe7727d6ab0d3a1be563)](https://sonarcloud.io/summary/new_code?id=cmap-vsts_atvero-atveromailfrontend-filing)

## Discovery

[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=cmap-vsts_atvero-atveromailfrontend-discovery&metric=alert_status&token=7196227da69787a8563e65065ba5d8825250eb81)](https://sonarcloud.io/summary/new_code?id=cmap-vsts_atvero-atveromailfrontend-discovery)

[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=cmap-vsts_atvero-atveromailfrontend-discovery&metric=coverage&token=7196227da69787a8563e65065ba5d8825250eb81)](https://sonarcloud.io/summary/new_code?id=cmap-vsts_atvero-atveromailfrontend-discovery)

# Stetting up Snyk

1. Sign up to Snyk

2. `npm install -g snyk`

3. `snyk auth`

4. `cd <project-directory>`

5. `snyk test`

6. `snyk monitor`

## Atvero Mailing Frontend

# Branch Naming Convention

To ensure a consistent and understandable branch structure in our repository, please follow the naming conventions outlined below when creating new branches.

# Branch Types and Naming

## Branch Naming:

Naming format: `{branch-type}/{ticket-number}/{short-description}`

Example: `feature/53039/progress-bar`

Description:

    feature: Indicates that the branch is for a new feature.

    53039: The ticket or issue number associated with this feature.

    progress-bar: A brief, descriptive name for the feature being developed.

## Branch Types:

Feature Branches:

    Used for developing new features or enhancements

    `feature/{ticket-number}/{short-description}`

Bugfix Branches:

    Used for fixing bugs

    `bug-fix/{ticket-number}/{short-description}`

Release Branches:

    Used for preparing a new production release

    `release/{version-number}/{short-description}`

Documentation Branches:

    Used for making changes to the documentation

    `docs/{short-description}`

Research Branches:

    Used for experimental features or spikes

    `research/{ticket-number}/{short-description}`

Support Branches:

    Used for support-related tasks

    `support/{ticket-number}/{short-description}`

Refactor Branches:

    Used for code refactoring tasks

    `refactor/{ticket-number}/{short-description}`

Test Branches:

    Used for creating or updating tests

    `test/{ticket-number}/{short-description}`