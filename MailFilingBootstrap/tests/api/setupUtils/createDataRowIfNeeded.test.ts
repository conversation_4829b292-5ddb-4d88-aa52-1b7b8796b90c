import { describe, it, expect, vi } from "vitest";
import { createDataRowIfNeeded } from "../../../src/api/setupUtils";
import { TestSiteAdmin } from "../../../src/adapters/TestSiteAdmin";

describe("createDataRowIfNeeded", () => {
  it("should create a data row ", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteFromUrl = vi
      .fn()
      .mockResolvedValue({ Id: "site-id" });

    adapters.siteAdmin.getSiteList = vi
      .fn()
      .mockResolvedValue({ id: "list-id" });

    adapters.siteAdmin.getDrives = vi
      .fn()
      .mockResolvedValue([{ id: "drive-id", name: "Filed Email Content" }]);

    adapters.siteAdmin.uploadFile = vi
      .fn()
      .mockResolvedValue({ id: "drtive-item-id" });
    adapters.siteAdmin.getListItemByDriveItem = vi
      .fn()
      .mockResolvedValue({ id: "list-item-id" });

    adapters.siteAdmin.updateListItem = vi
      .fn()
      .mockResolvedValue({ id: "list-item-id" });

    const result = await createDataRowIfNeeded(adapters, "/hubsitePath");

    expect(result).toEqual({ id: "list-item-id" });
  });

  it("the hubsite doesn't exist", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteFromUrl = vi.fn().mockResolvedValue(null);

    const result = await createDataRowIfNeeded(adapters, "/hubsitePath");

    expect(result).toEqual(null);
  });

  it("the hubsite doesn't have a Filed Email Content library", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteFromUrl = vi
      .fn()
      .mockResolvedValue({ Id: "site-id" });

    adapters.siteAdmin.getSiteList = vi.fn().mockResolvedValue(null);

    const result = await createDataRowIfNeeded(adapters, "/hubsitePath");

    expect(result).toEqual(null);
  });

  it("can't get the filed email content drive", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteFromUrl = vi
      .fn()
      .mockResolvedValue({ Id: "site-id" });

    adapters.siteAdmin.getSiteList = vi
      .fn()
      .mockResolvedValue({ id: "list-id" });

    adapters.siteAdmin.getDrives = vi.fn().mockResolvedValue([]);

    const result = await createDataRowIfNeeded(adapters, "/hubsitePath");

    expect(result).toEqual(null);
  });

  it("the test data fails to upload", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteFromUrl = vi
      .fn()
      .mockResolvedValue({ Id: "site-id" });

    adapters.siteAdmin.getSiteList = vi
      .fn()
      .mockResolvedValue({ id: "list-id" });

    adapters.siteAdmin.getDrives = vi
      .fn()
      .mockResolvedValue([{ id: "drive-id", name: "Filed Email Content" }]);

    adapters.siteAdmin.uploadFile = vi.fn().mockResolvedValue(null);

    const result = await createDataRowIfNeeded(adapters, "/hubsitePath");

    expect(result).toEqual(null);
  });

  it("can't get the list item from the drive item of the uploaded data", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteFromUrl = vi
      .fn()
      .mockResolvedValue({ Id: "site-id" });

    adapters.siteAdmin.getSiteList = vi
      .fn()
      .mockResolvedValue({ id: "list-id" });

    adapters.siteAdmin.getDrives = vi
      .fn()
      .mockResolvedValue([{ id: "drive-id", name: "Filed Email Content" }]);

    adapters.siteAdmin.uploadFile = vi
      .fn()
      .mockResolvedValue({ id: "drtive-item-id" });
    adapters.siteAdmin.getListItemByDriveItem = vi.fn().mockResolvedValue(null);

    const result = await createDataRowIfNeeded(adapters, "/hubsitePath");

    expect(result).toEqual(null);
  });

  it("can't get the list item throws an error", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteFromUrl = vi
      .fn()
      .mockResolvedValue({ Id: "site-id" });

    adapters.siteAdmin.getSiteList = vi
      .fn()
      .mockResolvedValue({ id: "list-id" });

    adapters.siteAdmin.getDrives = vi
      .fn()
      .mockResolvedValue([{ id: "drive-id", name: "Filed Email Content" }]);

    adapters.siteAdmin.uploadFile = vi
      .fn()
      .mockResolvedValue({ id: "drtive-item-id" });
    adapters.siteAdmin.getListItemByDriveItem = vi
      .fn()
      .mockRejectedValue(new Error("error"));

    const result = await createDataRowIfNeeded(adapters, "/hubsitePath");

    expect(result).toEqual(null);
  });

  it("test data metadata cannot be set", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteFromUrl = vi
      .fn()
      .mockResolvedValue({ Id: "site-id" });

    adapters.siteAdmin.getSiteList = vi
      .fn()
      .mockResolvedValue({ id: "list-id" });

    adapters.siteAdmin.getDrives = vi
      .fn()
      .mockResolvedValue([{ id: "drive-id", name: "Filed Email Content" }]);

    adapters.siteAdmin.uploadFile = vi
      .fn()
      .mockResolvedValue({ id: "drtive-item-id" });
    adapters.siteAdmin.getListItemByDriveItem = vi
      .fn()
      .mockResolvedValue({ id: "list-item-id" });

    adapters.siteAdmin.updateListItem = vi.fn().mockResolvedValue(null);

    const result = await createDataRowIfNeeded(adapters, "/hubsitePath");

    expect(result).toEqual(null);
  });
});
