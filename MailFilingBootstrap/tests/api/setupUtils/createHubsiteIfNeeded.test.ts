import { describe, it, expect, vi } from "vitest";
import { createHubsiteIfNeeded } from "../../../src/api/setupUtils";
import { TestSiteAdmin } from "../../../src/adapters/TestSiteAdmin";
import { ISiteCreationResponse } from "@pnp/sp/sites/types";

describe("createHubsiteIfNeeded", () => {
  it("should create the hubsite and register it", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi
      .fn()
      .mockResolvedValueOnce(null)
      .mockResolvedValueOnce({ Id: "hubsite-id" });

    adapters.siteAdmin.createCommunicationSite = vi.fn().mockResolvedValue({
      SiteStatus: 2,
      SiteUrl: "https://example.com/hubsitepath",
    } as ISiteCreationResponse);

    adapters.siteAdmin.getHubsites = vi.fn().mockResolvedValue([]);

    adapters.siteAdmin.registerHubsite = vi.fn().mockResolvedValue(true);

    const result = await createHubsiteIfNeeded(
      adapters,
      "/hubsitePath",
      "site-design-id"
    );

    expect(result).toEqual({ Id: "hubsite-id" });
  });

  it("site creation fails with an exception", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi
      .fn()
      .mockResolvedValueOnce(null);

    adapters.siteAdmin.createCommunicationSite = vi
      .fn()
      .mockRejectedValue("Error");

    const result = await createHubsiteIfNeeded(
      adapters,
      "/hubsitePath",
      "site-design-id"
    );

    expect(result).toEqual(null);
  });

  it("site creation fails with an error code", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi
      .fn()
      .mockResolvedValueOnce(null);

    adapters.siteAdmin.createCommunicationSite = vi
      .fn()
      .mockResolvedValue({ SiteStatus: 1 });

    const result = await createHubsiteIfNeeded(
      adapters,
      "/hubsitePath",
      "site-design-id"
    );

    expect(result).toEqual(null);
  });

  it("site already exists, but its not a hubsite", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi
      .fn()
      .mockResolvedValueOnce({ id: "site-id", webUrl: "https://example.com" })
      .mockResolvedValueOnce({ id: "site-id", webUrl: "https://example.com" });

    const spy = vi.spyOn(adapters.siteAdmin, "createCommunicationSite");
    adapters.siteAdmin.getHubsites = vi.fn().mockResolvedValue([]);

    adapters.siteAdmin.registerHubsite = vi.fn().mockResolvedValue(true);

    const result = await createHubsiteIfNeeded(
      adapters,
      "/hubsitePath",
      "site-design-id"
    );

    expect(spy).not.toHaveBeenCalled();

    expect(result).toEqual({ id: "site-id", webUrl: "https://example.com" });
  });

  it("site already exists, and its a hubsite", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi
      .fn()
      .mockResolvedValueOnce({ id: "site-id", webUrl: "https://example.com" });

    const createCommunicationSiteSpy = vi.spyOn(
      adapters.siteAdmin,
      "createCommunicationSite"
    );

    adapters.siteAdmin.getHubsites = vi
      .fn()
      .mockResolvedValue([{ Id: "site-id", SiteUrl: "https://example.com" }]);
    const registerHubsiteSpy = vi.spyOn(adapters.siteAdmin, "registerHubsite");

    const result = await createHubsiteIfNeeded(
      adapters,
      "/hubsitePath",
      "site-design-id"
    );

    expect(createCommunicationSiteSpy).not.toHaveBeenCalled();
    expect(registerHubsiteSpy).not.toHaveBeenCalled();

    expect(result).toEqual({ id: "site-id", webUrl: "https://example.com" });
  });

  it("register hubsite fails", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi
      .fn()
      .mockResolvedValueOnce(null)
      .mockResolvedValueOnce({ Id: "hubsite-id" });

    adapters.siteAdmin.createCommunicationSite = vi.fn().mockResolvedValue({
      SiteStatus: 2,
      SiteUrl: "https://example.com/hubsitepath",
    } as ISiteCreationResponse);

    adapters.siteAdmin.getHubsites = vi.fn().mockResolvedValue([]);

    adapters.siteAdmin.registerHubsite = vi.fn().mockResolvedValue(false);

    const result = await createHubsiteIfNeeded(
      adapters,
      "/hubsitePath",
      "site-design-id"
    );

    expect(result).toEqual(null);
  });
});
