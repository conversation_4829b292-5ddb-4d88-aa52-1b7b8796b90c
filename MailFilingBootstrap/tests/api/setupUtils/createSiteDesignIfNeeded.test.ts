import { describe, it, expect, vi } from "vitest";
import { createSiteDesignIfNeeded } from "../../../src/api/setupUtils";
import { TestSiteAdmin } from "../../../src/adapters/TestSiteAdmin";

describe("createSiteDesignIfNeeded", () => {
  it("should create a new site design if not exists", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteDesigns = vi.fn().mockResolvedValue([]);
    adapters.siteAdmin.createSiteScript = vi
      .fn()
      .mockResolvedValue({ Id: "test-site-script-id" });

    adapters.siteAdmin.createSiteDesign = vi
      .fn()
      .mockResolvedValue({ Id: "test-site-design-id" });

    const result = await createSiteDesignIfNeeded(
      adapters,
      "Test Site Design",
      "Test Site Script Content"
    );

    expect(adapters.siteAdmin.createSiteDesign).toHaveBeenCalledWith(
      ["test-site-script-id"],
      "Test Site Design",
      "hubsite site design"
    );

    expect(result?.Id).toEqual("test-site-design-id");
  });

  it("should not create a new site script if it already exists", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteDesigns = vi
      .fn()
      .mockResolvedValue([
        { Id: "test-site-design-id", Title: "Test Site Design" },
      ]);

    const spy = vi.spyOn(adapters.siteAdmin, "createSiteDesign");

    const result = await createSiteDesignIfNeeded(
      adapters,
      "Test Site Design",
      "Test Site Script Content"
    );

    expect(spy).not.toHaveBeenCalled();
    expect(result?.Id).toEqual("test-site-design-id");
  });

  it("createSiteScript throws an error", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteDesigns = vi.fn().mockResolvedValue([]);
    adapters.siteAdmin.createSiteScript = vi.fn().mockRejectedValue("error");

    adapters.siteAdmin.createSiteDesign = vi
      .fn()
      .mockResolvedValue({ Id: "test-site-design-id" });

    const result = await createSiteDesignIfNeeded(
      adapters,
      "Test Site Design",
      "Test Site Script Content"
    );

    expect(result).toEqual(null);
  });
  it("createSiteDesign throws an error", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getSiteDesigns = vi.fn().mockResolvedValue([]);
    adapters.siteAdmin.createSiteScript = vi
      .fn()
      .mockResolvedValue({ Id: "test-site-script-id" });
    adapters.siteAdmin.createSiteDesign = vi.fn().mockRejectedValue("error");

    const result = await createSiteDesignIfNeeded(
      adapters,
      "Test Site Design",
      "Test Site Script Content"
    );

    expect(result).toEqual(null);
  });
});
