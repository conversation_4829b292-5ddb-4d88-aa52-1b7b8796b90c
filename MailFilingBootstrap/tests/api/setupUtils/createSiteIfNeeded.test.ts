import { describe, it, expect, vi } from "vitest";
import { createSiteIfNeeded } from "../../../src/api/setupUtils";
import { TestSiteAdmin } from "../../../src/adapters/TestSiteAdmin";

describe("createSiteIfNeeded", () => {
  it("should create a new hubsite if not exists", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi
      .fn()
      .mockResolvedValueOnce(null)
      .mockResolvedValueOnce({ Id: "hubsite-id" });
    adapters.siteAdmin.createCommunicationSite = vi
      .fn()
      .mockResolvedValue({ SiteStatus: 2 });

    const result = await createSiteIfNeeded(
      adapters,
      "https://example.com/hubsitePath",
      "site-design-id"
    );

    expect(adapters.siteAdmin.createCommunicationSite).toHaveBeenCalledWith(
      "https://example.com/hubsitePath",
      "site-design-id"
    );

    expect(result).toEqual({ Id: "hubsite-id" });
  });

  it("should not create a hubsite if it exists", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi
      .fn()
      .mockResolvedValue({ Id: "hubsite-id" });

    const spy = vi.spyOn(adapters.siteAdmin, "createCommunicationSite");

    const result = await createSiteIfNeeded(
      adapters,
      "https://example.com/hubsitePath",
      "site-design-id"
    );
    expect(spy).not.toHaveBeenCalled();

    expect(result).toEqual({ Id: "hubsite-id" });
  });

  it("the create site fails", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi.fn().mockResolvedValue(null);
    adapters.siteAdmin.createCommunicationSite = vi
      .fn()
      .mockRejectedValue("error");

    const result = await createSiteIfNeeded(
      adapters,
      "https://example.com/hubsitePath",
      "site-design-id"
    );

    expect(result).toEqual(null);
  });
  it("the create site fails with an error code", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };

    adapters.siteAdmin.getAtveroMailHubsite = vi.fn().mockResolvedValue(null);
    adapters.siteAdmin.createCommunicationSite = vi
      .fn()
      .mockResolvedValue({ SiteStatus: 1 });

    const result = await createSiteIfNeeded(
      adapters,
      "https://example.com/hubsitePath",
      "site-design-id"
    );

    expect(result).toEqual(null);
  });
});
