import { describe, it, expect, vi } from "vitest";
import {
  createManagementGroupIfNeeded,
  GroupCreated,
} from "../../../src/api/setupUtils";
import { TestSiteAdmin } from "../../../src/adapters/TestSiteAdmin";

describe("createManagementGroupsIfNeeded", () => {
  it("should create management groups if not exists", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };
    adapters.siteAdmin.getGroups = vi.fn().mockResolvedValue([]);
    adapters.siteAdmin.createGroup = vi
      .fn()
      .mockResolvedValue({ Id: "test-group-id" });

    const result: GroupCreated = await createManagementGroupIfNeeded(
      adapters,
      "Atvero Mail All Users"
    );

    expect(adapters.siteAdmin.createGroup).toHaveBeenCalledWith(
      "Atvero Mail All Users"
    );

    expect(result).toEqual(GroupCreated.Created);
  });
  it("should not create management group if it exists", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };
    adapters.siteAdmin.getGroups = vi
      .fn()
      .mockResolvedValue([{ Id: "test-id" }]);

    const spy = vi.spyOn(adapters.siteAdmin, "createGroup");
    const result: GroupCreated = await createManagementGroupIfNeeded(
      adapters,
      "Atvero Mail All Users"
    );

    expect(result).toEqual(GroupCreated.AlreadyExists);

    expect(spy).not.toHaveBeenCalled();
  });
  it("get groups fails", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };
    adapters.siteAdmin.getGroups = vi.fn().mockRejectedValue("error");
    adapters.siteAdmin.createGroup = vi
      .fn()
      .mockResolvedValue({ Id: "test-group-id" });

    const result: GroupCreated = await createManagementGroupIfNeeded(
      adapters,
      "Atvero Mail All Users"
    );
    expect(result).toEqual(GroupCreated.Failed);
  });

  it("create groups throws an error", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };
    adapters.siteAdmin.getGroups = vi.fn().mockResolvedValue([]);
    adapters.siteAdmin.createGroup = vi.fn().mockRejectedValue("error");

    const result: GroupCreated = await createManagementGroupIfNeeded(
      adapters,
      "Atvero Mail All Users"
    );
    expect(result).toEqual(GroupCreated.Failed);
  });
  it("create groups returns an error", async () => {
    const adapters = { siteAdmin: new TestSiteAdmin() };
    adapters.siteAdmin.getGroups = vi.fn().mockResolvedValue([]);
    adapters.siteAdmin.createGroup = vi.fn().mockResolvedValue(null);

    const result: GroupCreated = await createManagementGroupIfNeeded(
      adapters,
      "Atvero Mail All Users"
    );
    expect(result).toEqual(GroupCreated.Failed);
  });
});
