import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { Configure } from "../../src/components/Configure";
import { Adapters } from "../../src/adapters/AdapterFactory";
import { Site } from "@microsoft/microsoft-graph-types";

describe("Configure Component", () => {
  const createMockAdapters = (mockResponses: {
    hubsiteExists: boolean;
    adminGroupExists: boolean;
    confUsersGroupExists: boolean;
    allUsersGroupExists: boolean;
  }): Adapters => {
    return {
      siteAdmin: {
        getAtveroMailHubsite: vi.fn().mockImplementation(async () => {
          if (mockResponses.hubsiteExists) {
            return {
              id: "site-123",
              webUrl: "https://example.com/site",
            } as Site;
          }
          return null;
        }),
        getGroups: vi.fn().mockImplementation(async (groupName: string) => {
          if (
            groupName === `CMap Mail Admins` &&
            mockResponses.adminGroupExists
          ) {
            return [{ id: "group-1", displayName: groupName }];
          }
          if (
            groupName === `CMap Mail All Confidential Users` &&
            mockResponses.confUsersGroupExists
          ) {
            return [{ id: "group-2", displayName: groupName }];
          }
          if (
            groupName === `CMap Mail All Users` &&
            mockResponses.allUsersGroupExists
          ) {
            return [{ id: "group-3", displayName: groupName }];
          }
          return [];
        }),
        getSite: vi.fn(),
        getSiteFromUrl: vi.fn(),
        getSiteDesignRuns: vi.fn(),
        getHubsites: vi.fn(),
        getSiteDesigns: vi.fn(),
        createSiteScript: vi.fn(),
        createSiteDesign: vi.fn(),
        registerHubsite: vi.fn(),
        createCommunicationSite: vi.fn(),
        applySiteDesign: vi.fn(),
        assignMailAdminsGroup: vi.fn(),
        assignEveryoneGroup: vi.fn(),
        assignGroupPermissions: vi.fn(),
        createGroup: vi.fn(),
        getDrives: vi.fn(),
        getSiteList: vi.fn(),
        getListItemByDriveItem: vi.fn(),
        uploadFile: vi.fn(),
        updateListItem: vi.fn(),
        searchUsers: vi.fn(),
        addUserToGroupById: vi.fn(),
        getUserByEmail: vi.fn(),
        getUsersFromGroup: vi.fn(),
        removeUsersFromGroupById: vi.fn(),
      },
    };
  };

  it("should show loading message when adapters are not available", () => {
    render(
      <Configure
        //@ts-ignore
        adapters={undefined}
        hubsiteUrl="https://test.sharepoint.com/test-path"
      />
    );

    const loadingMessageElement = screen.getByTestId("loading-message");
    expect(loadingMessageElement).toBeDefined();
  });

  it("should show loading state initially", async () => {
    const adapters = createMockAdapters({
      hubsiteExists: true,
      adminGroupExists: true,
      confUsersGroupExists: true,
      allUsersGroupExists: true,
    });

    render(
      <Configure
        adapters={adapters}
        hubsiteUrl="https://test.sharepoint.com/test-path"
      />
    );

    expect(screen.getByText(/Verifying required components/i)).toBeDefined();

    await waitFor(() => {
      expect(screen.queryByText(/Verifying required components/i)).toBeNull();
    });
  });

  it("should show success when all verifications pass", async () => {
    const adapters = createMockAdapters({
      hubsiteExists: true,
      adminGroupExists: true,
      confUsersGroupExists: true,
      allUsersGroupExists: true,
    });

    render(
      <Configure
        adapters={adapters}
        hubsiteUrl="https://test.sharepoint.com/test-path"
      />
    );

    await waitFor(() => {
      expect(screen.getByText(/All verification checks passed/i)).toBeDefined();
    });

    const comboboxes = screen.getAllByTestId("add-user-combobox");

    expect(comboboxes.length).toBe(3);
    // Check that none of them are disabled
    comboboxes.forEach((combobox) => {
      expect(combobox.hasAttribute("disabled")).toBe(false);
    });
  });

  it("should show error when hub site verification fails", async () => {
    const adapters = createMockAdapters({
      hubsiteExists: false,
      adminGroupExists: true,
      confUsersGroupExists: true,
      allUsersGroupExists: true,
    });

    render(
      <Configure
        adapters={adapters}
        hubsiteUrl="https://test.sharepoint.com/test-path"
      />
    );

    await waitFor(() => {
      const errorText = screen.getByText(
        'Hub site at URL "https://test.sharepoint.com/test-path" does not exist.'
      );
      expect(errorText).toBeDefined();
    });
  });

  it("should show error when admin group verification fails", async () => {
    const adapters = createMockAdapters({
      hubsiteExists: true,
      adminGroupExists: false,
      confUsersGroupExists: true,
      allUsersGroupExists: true,
    });

    render(
      <Configure
        adapters={adapters}
        hubsiteUrl="https://test.sharepoint.com/test-path"
      />
    );

    await waitFor(() => {
      const errorText = screen.getByText(
        `Security group "CMap Mail Admins" does not exist.`
      );
      expect(errorText).toBeDefined();
    });
  });

  it("should show error when confidential users group verification fails", async () => {
    const adapters = createMockAdapters({
      hubsiteExists: true,
      adminGroupExists: true,
      confUsersGroupExists: false,
      allUsersGroupExists: true,
    });

    render(
      <Configure
        adapters={adapters}
        hubsiteUrl="https://test.sharepoint.com/test-path"
      />
    );

    await waitFor(() => {
      const errorText = screen.getByText(
        `Security group "CMap Mail All Confidential Users" does not exist.`
      );
      expect(errorText).toBeDefined();
    });
  });

  it("should show error when all users group verification fails", async () => {
    const adapters = createMockAdapters({
      hubsiteExists: true,
      adminGroupExists: true,
      confUsersGroupExists: true,
      allUsersGroupExists: false,
    });

    render(
      <Configure
        adapters={adapters}
        hubsiteUrl="https://test.sharepoint.com/test-path"
      />
    );

    await waitFor(() => {
      const errorText = screen.getByText(
        `Security group "CMap Mail All Users" does not exist.`
      );
      expect(errorText).toBeDefined();
    });
  });

  it("should disable configuration buttons when any verification fails", async () => {
    const adapters = createMockAdapters({
      hubsiteExists: false,
      adminGroupExists: true,
      confUsersGroupExists: true,
      allUsersGroupExists: true,
    });

    render(
      <Configure
        adapters={adapters}
        hubsiteUrl="https://test.sharepoint.com/test-path"
      />
    );

    const comboboxes = screen.getAllByTestId("add-user-combobox");
    // Check that all of them are disabled
    comboboxes.forEach((combobox) => {
      expect(combobox.hasAttribute("disabled")).toBe(true);
    });
  });

  it("should enable configuration buttons when all verifications pass", async () => {
    const adapters = createMockAdapters({
      hubsiteExists: true,
      adminGroupExists: true,
      confUsersGroupExists: true,
      allUsersGroupExists: true,
    });

    render(
      <Configure
        adapters={adapters}
        hubsiteUrl="https://test.sharepoint.com/test-path"
      />
    );

    await waitFor(() => {
      expect(screen.getByTestId("success-message")).toBeDefined();
    });

    const comboboxes = screen.getAllByTestId("add-user-combobox");
    // Check that none of them are disabled
    comboboxes.forEach((combobox) => {
      expect(combobox.hasAttribute("disabled")).toBe(false);
    });
  });
});
