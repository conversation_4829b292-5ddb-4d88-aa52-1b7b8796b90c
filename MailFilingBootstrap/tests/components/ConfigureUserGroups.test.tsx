import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { ConfigureUserGroups } from "../../src/components/ConfigureUserGroups";
import { User } from "../../src/types/User";
import * as UserManagementHelpers from "../../src/utils/UserManagementHelpers";

vi.mock("lodash", () => ({
  debounce: (fn: any) => fn,
}));

vi.mock("@sentry/react", () => ({
  captureException: vi.fn(),
}));

describe("ConfigureUserGroups", () => {
  const mockUsers = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      principalName: "<EMAIL>",
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      principalName: "<EMAIL>",
    },
  ];

  const mockCurrentUsers = [
    { id: "3", displayName: "Bob Admin", email: "<EMAIL>" },
    { id: "4", displayName: "Alice Admin", email: "<EMAIL>" },
  ];

  const createMockAdapters = (overrides = {}) => ({
    siteAdmin: {
      searchUsers: vi.fn().mockResolvedValue(mockUsers),
      getUsersFromGroup: vi.fn().mockResolvedValue(mockCurrentUsers),
      removeUsersFromGroupById: vi.fn().mockResolvedValue(true),
      getGroups: vi.fn().mockResolvedValue([{ id: "group-id" }]),
      getUserByEmail: vi.fn().mockImplementation((email) => {
        const user = mockUsers.find((u) => u.email === email);
        return Promise.resolve(
          user
            ? {
                id: user.id,
                displayName: user.name,
                email: user.email,
              }
            : null
        );
      }),
      addUserToGroupById: vi.fn().mockResolvedValue({
        success: true,
        message: "User added successfully",
      }),
      ...overrides,
    },
  });

  const testProps = {
    groupName: "CMap Mail Test Group",
    title: "Test Group Users",
    descriptions: {
      flair: `Add users to the test group.`,
      details: `This will add users to the test group for this test.`,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders without crashing", () => {
    const mockAdapters = createMockAdapters();
    expect(() => {
      render(
        <ConfigureUserGroups
          adapters={mockAdapters as any}
          disabled={false}
          groupName={testProps.groupName}
          descriptions={testProps.descriptions}
        />
      );
    }).not.toThrow();
  });

  it("renders the correct text elements", () => {
    const mockAdapters = {
      siteAdmin: {
        searchUsers: vi.fn(),
        addUsersToGroup: vi.fn(),
        getUsersFromGroup: vi.fn().mockResolvedValue([]),
      },
    };

    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={false}
        groupName={testProps.groupName}
        descriptions={testProps.descriptions}
      />
    );

    expect(screen.getByText(testProps.descriptions.flair)).toBeDefined();
    expect(screen.getByText(testProps.descriptions.details)).toBeDefined();
  });

  it("disables the add button when no users are selected", () => {
    const mockAdapters = {
      siteAdmin: {
        searchUsers: vi.fn(),
        addUsersToGroup: vi.fn(),
        getUsersFromGroup: vi.fn().mockResolvedValue([]),
      },
    };

    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={false}
        groupName={testProps.groupName}
      />
    );

    const addButton = screen.getByRole("button", {
      name: /Add Users/,
    });
    expect(addButton.hasAttribute("disabled")).toBe(true);
  });

  it("renders the search input with correct placeholder", () => {
    const mockAdapters = {
      siteAdmin: {
        searchUsers: vi.fn(),
        addUsersToGroup: vi.fn(),
        getUsersFromGroup: vi.fn().mockResolvedValue([]),
      },
    };

    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={false}
        groupName={testProps.groupName}
      />
    );

    const searchInput = screen.getByPlaceholderText(
      "Start typing a name or email (minimum 3 characters)"
    );
    expect(searchInput).toBeDefined();
    expect(searchInput.hasAttribute("disabled")).toBe(false);
  });

  it("disables all inputs when disabled prop is true", () => {
    const mockAdapters = {
      siteAdmin: {
        searchUsers: vi.fn(),
        addUsersToGroup: vi.fn(),
        getUsersFromGroup: vi.fn().mockResolvedValue([]),
      },
    };

    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={true}
        groupName={testProps.groupName}
      />
    );

    const searchInput = screen.getByPlaceholderText(
      "Start typing a name or email (minimum 3 characters)"
    );
    const addButton = screen.getByRole("button", {
      name: /Add Users/,
    });

    expect(searchInput.hasAttribute("disabled")).toBe(true);
    expect(addButton.hasAttribute("disabled")).toBe(true);
  });

  it("does not show selected users section when no users are selected", () => {
    const mockAdapters = {
      siteAdmin: {
        searchUsers: vi.fn(),
        addUsersToGroup: vi.fn(),
        getUsersFromGroup: vi.fn().mockResolvedValue([]),
      },
    };

    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={false}
        groupName={testProps.groupName}
      />
    );

    expect(screen.queryByText(/Selected Users/)).toBeNull();
  });

  it("calls searchUsers when typing 3 or more characters", () => {
    const mockSearchUsers = vi.fn().mockResolvedValue([]);
    const mockAdapters = {
      siteAdmin: {
        searchUsers: mockSearchUsers,
        addUsersToGroup: vi.fn(),
        getUsersFromGroup: vi.fn().mockResolvedValue([]),
      },
    };

    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={false}
        groupName={testProps.groupName}
      />
    );

    const searchInput = screen.getByPlaceholderText(
      "Start typing a name or email (minimum 3 characters)"
    );

    fireEvent.change(searchInput, { target: { value: "joh" } });

    expect(mockSearchUsers).toHaveBeenCalledWith("joh");
  });
});

describe("ConfigureUserGroups additional functionality", () => {
  const mockUsers = [
    {
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
      principalName: "<EMAIL>",
    },
    {
      id: "2",
      name: "Jane Smith",
      email: "<EMAIL>",
      principalName: "<EMAIL>",
    },
  ];

  const mockCurrentUsers: User[] = [
    { id: "3", displayName: "Bob Admin", email: "<EMAIL>" },
    { id: "4", displayName: "Alice Admin", email: "<EMAIL>" },
  ];

  const mockAdapters = {
    siteAdmin: {
      searchUsers: vi.fn().mockResolvedValue(mockUsers),
      addUsersToGroup: vi.fn(),
      getGroups: vi.fn().mockResolvedValue([{ id: "group-id" }]),
      getUserByEmail: vi.fn().mockImplementation((email) => {
        const user = mockUsers.find((u) => u.email === email);
        return Promise.resolve(
          user
            ? {
                id: user.id,
                displayName: user.name,
                email: user.email,
              }
            : null
        );
      }),
      addUserToGroupById: vi.fn().mockResolvedValue({
        success: true,
        message: "User added successfully",
      }),
      getUsersFromGroup: vi.fn().mockResolvedValue(mockCurrentUsers),
      removeUsersFromGroupById: vi.fn().mockResolvedValue(true),
    },
  };

  const testProps = {
    groupName: "Atvero Mail Test Group",
    title: "Test Group Users",
    description: "Add users to the test group.",
    descriptions: {
      flair: `Add users to the test group.`,
      details: `This will add users to the test group for this test.`,
    },
  };

  it("should handle option selection correctly", async () => {
    mockAdapters.siteAdmin.searchUsers.mockResolvedValue(mockUsers);

    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={false}
        groupName={testProps.groupName}
        descriptions={testProps.descriptions}
      />
    );

    const searchInput = screen.getByPlaceholderText(
      "Start typing a name or email (minimum 3 characters)"
    );

    // Trigger search
    fireEvent.change(searchInput, { target: { value: "joh" } });

    await waitFor(() => {
      expect(mockAdapters.siteAdmin.searchUsers).toHaveBeenCalledWith("joh");
    });

    const addButton = screen.getByRole("button", {
      name: /Add Users/,
    });

    expect(addButton.hasAttribute("disabled")).toBe(true);
  });

  it("should call removeUsersFromGroupById when removing a user from the group", async () => {
    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={false}
        groupName={testProps.groupName}
        descriptions={testProps.descriptions}
      />
    );

    await waitFor(() => {
      expect(
        screen.getByText(`Users in ${testProps.groupName} (2)`)
      ).toBeDefined();
      expect(screen.getByText("Bob Admin")).toBeDefined();
    });

    const removeButtons = screen.getAllByTitle("Remove user");

    const bobAdminButton = removeButtons[0];

    fireEvent.click(bobAdminButton);

    await waitFor(() => {
      expect(
        mockAdapters.siteAdmin.removeUsersFromGroupById
      ).toHaveBeenCalledWith(
        testProps.groupName,
        "3" // The ID for Bob Admin
      );
    });

    expect(mockAdapters.siteAdmin.getUsersFromGroup).toHaveBeenCalledWith(
      testProps.groupName
    );
  });

  // New test for handleAddUsersToGroup without interacting with combobox
  it("should call addUsersToGroup when Add Users button is clicked", async () => {
    // Spy on UserManagementHelpers.addUsersToGroup
    const addUsersToGroupSpy = vi.spyOn(
      UserManagementHelpers,
      "addUsersToGroup"
    );
    addUsersToGroupSpy.mockResolvedValue({
      success: true,
      results: {
        "<EMAIL>": {
          success: true,
          message: "User added successfully",
        },
      },
    });

    // Spy on other helper functions
    const processUserAdditionResultsSpy = vi.spyOn(
      UserManagementHelpers,
      "processUserAdditionResults"
    );
    processUserAdditionResultsSpy.mockReturnValue({
      successfulUsers: ["John Doe"],
      alreadyInGroup: [],
      failedUsers: [],
      usersToRemove: [mockUsers[0]],
      groupName: testProps.groupName,
    });

    const formatUserAdditionStatusMessageSpy = vi.spyOn(
      UserManagementHelpers,
      "formatUserAdditionStatusMessage"
    );
    formatUserAdditionStatusMessageSpy.mockReturnValue(
      `Successfully added 1 user to ${testProps.groupName}.`
    );

    // Directly render the component with selectedUsers prop
    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={false}
        groupName={testProps.groupName}
        descriptions={testProps.descriptions}
        selectedUsers={[mockUsers[0]]} // Inject users directly
      />
    );

    // Verify that the selected user appears in the UI
    await waitFor(() => {
      expect(screen.getByText("John Doe")).toBeDefined();
    });

    // Find and click the Add Users button
    const addButton = screen.getByRole("button", { name: /Add Users/ });
    expect(addButton.hasAttribute("disabled")).toBe(false);

    fireEvent.click(addButton);

    // Verify the addUsersToGroup function was called with the correct arguments
    await waitFor(() => {
      expect(addUsersToGroupSpy).toHaveBeenCalledWith(
        expect.anything(),
        testProps.groupName,
        ["<EMAIL>"]
      );
    });

    // Verify the processing and formatting functions were called
    expect(processUserAdditionResultsSpy).toHaveBeenCalled();
    expect(formatUserAdditionStatusMessageSpy).toHaveBeenCalled();

    // Verify success message is displayed
    await waitFor(() => {
      const successMessage = screen.queryByText(
        `Successfully added 1 user to ${testProps.groupName}.`
      );
      expect(successMessage).toBeDefined();
    });

    // Restore the original implementations
    addUsersToGroupSpy.mockRestore();
    processUserAdditionResultsSpy.mockRestore();
    formatUserAdditionStatusMessageSpy.mockRestore();
  });

  // New test for error handling without interacting with combobox
  it("should handle errors when adding users fails", async () => {
    // Spy on UserManagementHelpers.addUsersToGroup and make it fail
    const addUsersToGroupSpy = vi.spyOn(
      UserManagementHelpers,
      "addUsersToGroup"
    );
    addUsersToGroupSpy.mockRejectedValue(new Error("Network error"));

    // Directly render the component with selectedUsers prop
    render(
      <ConfigureUserGroups
        adapters={mockAdapters as any}
        disabled={false}
        groupName={testProps.groupName}
        descriptions={testProps.descriptions}
        selectedUsers={[mockUsers[0]]} // Inject users directly
      />
    );

    // Verify that the selected user appears in the UI
    await waitFor(() => {
      expect(screen.getByText("John Doe")).toBeDefined();
    });

    // Find and click the Add Users button
    const addButton = screen.getByRole("button", { name: /Add Users/ });
    expect(addButton.hasAttribute("disabled")).toBe(false);

    fireEvent.click(addButton);

    // Verify error message is displayed (the component shows "Network connection error" for "Network error")
    await waitFor(() => {
      const errorMessage = screen.queryByText(/Network connection error/);
      expect(errorMessage).toBeDefined();
    });

    // Restore the original implementation
    addUsersToGroupSpy.mockRestore();
  });
});
