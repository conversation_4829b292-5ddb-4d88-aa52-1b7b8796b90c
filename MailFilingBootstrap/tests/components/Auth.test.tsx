import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { Auth } from "../../src/components/Auth";
import * as useGraphAPI from "../../src/hooks/useGraphAPI";
import * as useSharePointAPI from "../../src/hooks/useSharePointAPI";
import { adapterFactory } from "../../src/adapters/AdapterFactory";
import * as TanstackRouter from "@tanstack/react-router";

vi.mock("../../src/hooks/useGraphAPI");
vi.mock("../../src/hooks/useSharePointAPI");
vi.mock("../../src/adapters/AdapterFactory");

vi.mock("@tanstack/react-router", async () => {
  const actual = await vi.importActual("@tanstack/react-router");
  return {
    ...actual,
    useRouter: vi.fn(),
  };
});

describe("Auth Component", () => {
  beforeEach(() => {
    vi.mocked(useGraphAPI.useGraph).mockReturnValue({} as any);

    vi.mocked(useSharePointAPI.useSharePointApi).mockReturnValue({
      sharepoint: {} as any,
      sharepointHostName: "https://test.sharepoint.com",
    });

    vi.mocked(adapterFactory).mockResolvedValue({
      siteAdmin: {
        getAtveroMailHubsite: vi.fn(),
        getGroups: vi.fn(),
        getSite: vi.fn(),
        getSiteFromUrl: vi.fn(),
        getSiteDesignRuns: vi.fn(),
        getHubsites: vi.fn(),
        getSiteDesigns: vi.fn(),
        createSiteScript: vi.fn(),
        createSiteDesign: vi.fn(),
        registerHubsite: vi.fn(),
        createCommunicationSite: vi.fn(),
        applySiteDesign: vi.fn(),
        assignMailAdminsGroup: vi.fn(),
        assignEveryoneGroup: vi.fn(),
        assignGroupPermissions: vi.fn(),
        createGroup: vi.fn(),
        getDrives: vi.fn(),
        getSiteList: vi.fn(),
        getListItemByDriveItem: vi.fn(),
        uploadFile: vi.fn(),
        updateListItem: vi.fn(),
      },
    });

    vi.spyOn(TanstackRouter, "useRouter").mockImplementation(
      () =>
        ({
          state: {
            location: {
              pathname: "/",
            },
          },
        }) as any
    );
  });

  it("renders LoginHelper when adapters are not available", async () => {
    //@ts-ignore - easiest way to ignore type issues when forcing no adapter
    vi.mocked(adapterFactory).mockResolvedValueOnce(undefined);

    render(<Auth hubsiteUrl="https://test.sharepoint.com/test-path" />);

    await waitFor(() => {
      expect(screen.getByTestId("login-helper")).toBeDefined();
    });
  });

  it("renders SetupHubsite for regular routes when adapters are available", async () => {
    vi.spyOn(TanstackRouter, "useRouter").mockImplementation(
      () =>
        ({
          state: {
            location: {
              pathname: "/register_hubsite",
            },
          },
        }) as any
    );

    render(<Auth hubsiteUrl="https://test.sharepoint.com/test-path" />);

    await waitFor(() => {
      expect(screen.getByTestId("setup-hubsite")).toBeDefined();
    });
  });

  it("renders Configure component for /configure route when adapters are available", async () => {
    vi.spyOn(TanstackRouter, "useRouter").mockImplementation(
      () =>
        ({
          state: {
            location: {
              pathname: "/configure",
            },
          },
        }) as any
    );

    render(<Auth hubsiteUrl="https://test.sharepoint.com/test-path" />);

    await waitFor(() => {
      expect(screen.getByTestId("configure")).toBeDefined();
    });
  });
});
