import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import '@testing-library/jest-dom';
import React from 'react';
import { Validate } from '../../src/components/Validate';
import * as useGraphHook from '../../src/hooks/useGraphAPI';

// Mocks
vi.mock('../../src/adapters/BackendAdapter', () => ({
  BackendAdapter: vi.fn().mockImplementation(() => ({
    getHubsites: vi.fn().mockResolvedValue({
      Data: [{ url: 'https://tenant.sharepoint.com/sites/hubsite1', id: '1' }],
    }),
    getHubsiteRights: vi.fn().mockResolvedValue({
      IsSuccess: true,
      Data: { UsersWithRights: [{ Name: '<PERSON>' }] },
    }),
  })),
}));

vi.mock('../../src/adapters/AdapterFactory', () => ({
  Adapters: vi.fn(),
}));

vi.spyOn(useGraphHook, 'useGraph').mockReturnValue({} as any);

vi.mock('../../src/api/sharepointUtils', async (importOriginal) => {
  const actual = await importOriginal;
  return {
    ...actual,
    SharePointAdapterHook: ({ onAdaptersCreated }) => {
      onAdaptersCreated({
        siteAdmin: {
          getAtveroMailHubsite: vi.fn().mockResolvedValue({
            displayName: 'Test Hubsite',
          }),
          getSiteVisitors: vi.fn().mockResolvedValue({
            visitors: ['Alice'],
            count: 1,
          }),
          getSiteOwners: vi.fn().mockResolvedValue({
            owners: ['Bob'],
            count: 1,
          }),
          getUsersFromGroup: vi.fn().mockResolvedValue([{ displayName: 'Admin User' }]),
        },
      });
      return null;
    },
  };
});

describe('Validate Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('clicks button and shows a known validation result', async () => {
    render(<Validate />);

    const button = screen.getByRole('button', { name: /start validation/i });
    fireEvent.click(button);

    // Wait for one known result to appear
    await waitFor(() => {
      expect(screen.getByText(/Security Groups created/i)).toBeInTheDocument();
    });
  });

  it('renders the main heading', () => {
    render(<Validate />);
    expect(
      screen.getByText('CMap Mail Installation Validation')
    ).toBeInTheDocument();
  });

  it('shows the start validation button', () => {
    render(<Validate />);
    const button = screen.getByRole('button', { name: /start validation/i });
    expect(button).toBeInTheDocument();
  });

  it('renders the description paragraph', () => {
    render(<Validate />);
    expect(
      screen.getByText(/validates your CMap Mail installation/i)
    ).toBeInTheDocument();
  });

  it('start button is enabled on first render', () => {
    render(<Validate />);
    const button = screen.getByRole('button', { name: /start validation/i });
    expect(button).toBeEnabled();
  });

  it('shows Security Groups Configuration after validation', async () => {
    render(<Validate />);
    fireEvent.click(screen.getByRole('button', { name: /start validation/i }));

    await waitFor(() => {
      expect(
        screen.getByText(/Security Groups Configuration/i)
      ).toBeInTheDocument();
    });
  });

  it('disables button after click to prevent re-validation', async () => {
    render(<Validate />);
    const button = screen.getByRole('button', { name: /start validation/i });
    fireEvent.click(button);

    await waitFor(() => {
      expect(button).toBeDisabled();
    });
  });

  it('shows success icon for a successful validation step', async () => {
    render(<Validate />);
    fireEvent.click(screen.getByRole('button', { name: /start validation/i }));

    await waitFor(() => {
      const successIcons = screen.getAllByRole('img', { hidden: true });
      expect(successIcons.length).toBeGreaterThan(0);
    });
  });

});

