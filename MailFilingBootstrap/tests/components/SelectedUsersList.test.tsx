import React from "react";
import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { SelectedUsersList } from "../../src/components/SelectedUsersList";

describe("SelectedUsersList Component", () => {
  // Sample user data for testing
  const mockUsers = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      principalName: "<EMAIL>",
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      principalName: "<EMAIL>",
    },
  ];

  it("renders nothing when users array is empty", () => {
    const { container } = render(
      <SelectedUsersList users={[]} onRemoveUser={() => {}} disabled={false} />
    );

    expect(container.firstChild).toBeNull();
  });

  it("renders the correct number of users", () => {
    render(
      <SelectedUsersList
        users={mockUsers}
        onRemoveUser={() => {}}
        disabled={false}
      />
    );

    expect(screen.getByText("Selected Users (2)")).toBeDefined();
    expect(screen.getAllByTestId("user-item")).toHaveLength(2);
  });

  it("displays user information correctly", () => {
    render(
      <SelectedUsersList
        users={mockUsers}
        onRemoveUser={() => {}}
        disabled={false}
      />
    );

    expect(screen.getByText("John Doe")).toBeDefined();
    expect(screen.getByText("<EMAIL>")).toBeDefined();
    expect(screen.getByText("Jane Smith")).toBeDefined();
    expect(screen.getByText("<EMAIL>")).toBeDefined();
  });

  it("calls onRemoveUser with the correct userId when remove button is clicked", () => {
    const mockRemoveUser = vi.fn();

    render(
      <SelectedUsersList
        users={mockUsers}
        onRemoveUser={mockRemoveUser}
        disabled={false}
      />
    );

    const removeButtons = screen.getAllByTestId("remove-user-button");
    fireEvent.click(removeButtons[0]); // Click the first remove button

    expect(mockRemoveUser).toHaveBeenCalledWith("1");
  });

  it("disables remove buttons when disabled prop is true", () => {
    render(
      <SelectedUsersList
        users={mockUsers}
        onRemoveUser={() => {}}
        disabled={true}
      />
    );

    const removeButtons = screen.getAllByTestId("remove-user-button");
    removeButtons.forEach((button) => {
      expect(button.hasAttribute("disabled")).toBe(true);
    });
  });

  it("disables remove buttons when isProcessing prop is true", () => {
    render(
      <SelectedUsersList
        users={mockUsers}
        onRemoveUser={() => {}}
        disabled={false}
        isProcessing={true}
      />
    );

    const removeButtons = screen.getAllByTestId("remove-user-button");
    removeButtons.forEach((button) => {
      expect(button.hasAttribute("disabled")).toBe(true);
    });
  });

  it("uses custom title when provided", () => {
    render(
      <SelectedUsersList
        users={mockUsers}
        onRemoveUser={() => {}}
        disabled={false}
        title="Custom Title"
      />
    );

    expect(screen.getByText("Custom Title (2)")).toBeDefined();
  });
});
