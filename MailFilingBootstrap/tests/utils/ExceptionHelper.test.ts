import { describe, it, expect } from "vitest";
import { errorMessage } from "../../src/utils/ExceptionHelper";

describe("errorMessage", () => {
  it("should return the error message if error is an instance of Error", () => {
    const error = new Error("Test error message");
    const fallback = "Fallback message";
    const result = errorMessage(error, fallback);
    expect(result).toBe("Test error message");
  });

  it("should return the fallback message if error is not an instance of Error", () => {
    const error = "Not an error instance";
    const fallback = "Fallback message";
    const result = errorMessage(error, fallback);
    expect(result).toBe(fallback);
  });

  it("should return the fallback message if error is undefined", () => {
    const error = undefined;
    const fallback = "Fallback message";
    const result = errorMessage(error, fallback);
    expect(result).toBe(fallback);
  });

  it("should return the fallback message if error is null", () => {
    const error = null;
    const fallback = "Fallback message";
    const result = errorMessage(error, fallback);
    expect(result).toBe(fallback);
  });

  it("should return the fallback message if error is an object but not an instance of Error", () => {
    const error = { message: "Some error" };
    const fallback = "Fallback message";
    const result = errorMessage(error, fallback);
    expect(result).toBe(fallback);
  });
});
