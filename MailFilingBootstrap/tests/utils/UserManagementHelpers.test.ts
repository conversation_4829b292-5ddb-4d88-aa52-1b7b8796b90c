import { describe, it, expect, beforeEach } from "vitest";
import {
  processUserAdditionResults,
  formatUserAdditionStatusMessage,
  formatUserAdditionErrorMessage,
  type User,
  addUsersToGroup,
} from "../../src/utils/UserManagementHelpers";
import { vi } from "vitest";
import { ISiteAdmin } from "../../src/adapters/ISiteAdmin";
import * as Sentry from "@sentry/react";

vi.mock("../shared/TenancyService", () => ({
  productName: () => "Atvero Mail",
}));

vi.mock("@sentry/react", () => ({
  captureException: vi.fn(),
}));

console.error = vi.fn();

describe("processUserAdditionResults", () => {
  // Test group names
  const testAdminGroup = "CMap Mail Admins";
  const testConfGroup = "CMap Mail All Confidential Users";
  const testAllUsersGroup = "CMap Mail All Users";

  it("should correctly categorize successful additions", () => {
    const users: User[] = [
      { id: "1", name: "<PERSON>", email: "<EMAIL>" },
      { id: "2", name: "<PERSON>", email: "<EMAIL>" },
    ];

    const results = {
      "<EMAIL>": { success: true, message: "User added successfully" },
      "<EMAIL>": { success: true, message: "User added successfully" },
    };

    const processed = processUserAdditionResults(
      users,
      results,
      testAdminGroup
    );

    expect(processed.successfulUsers).toHaveLength(2);
    expect(processed.successfulUsers).toContain("John Doe");
    expect(processed.successfulUsers).toContain("Jane Smith");
    expect(processed.alreadyInGroup).toHaveLength(0);
    expect(processed.failedUsers).toHaveLength(0);
    expect(processed.usersToRemove).toHaveLength(2);
    expect(processed.groupName).toBe(testAdminGroup);
  });

  it("should correctly identify users already in the group", () => {
    const users: User[] = [
      { id: "1", name: "John Doe", email: "<EMAIL>" },
      { id: "2", name: "Jane Smith", email: "<EMAIL>" },
    ];

    const results = {
      "<EMAIL>": {
        success: true,
        message: "User was already in the group",
      },
      "<EMAIL>": { success: true, message: "User added successfully" },
    };

    const processed = processUserAdditionResults(users, results, testConfGroup);

    expect(processed.successfulUsers).toHaveLength(1);
    expect(processed.successfulUsers).toContain("Jane Smith");
    expect(processed.alreadyInGroup).toHaveLength(1);
    expect(processed.alreadyInGroup).toContain("John Doe");
    expect(processed.failedUsers).toHaveLength(0);
    expect(processed.usersToRemove).toHaveLength(2);
    expect(processed.groupName).toBe(testConfGroup);
  });

  it("should handle failure cases correctly", () => {
    const users: User[] = [
      { id: "1", name: "John Doe", email: "<EMAIL>" },
      { id: "2", name: "Jane Smith", email: "<EMAIL>" },
      { id: "3", name: "Bob Brown", email: "<EMAIL>" },
    ];

    const results = {
      "<EMAIL>": { success: true, message: "User added successfully" },
      "<EMAIL>": { success: false, message: "User not found" },
      "<EMAIL>": { success: false, message: "Permission denied" },
    };

    const processed = processUserAdditionResults(
      users,
      results,
      testAllUsersGroup
    );

    expect(processed.successfulUsers).toHaveLength(1);
    expect(processed.successfulUsers).toContain("John Doe");
    expect(processed.alreadyInGroup).toHaveLength(0);
    expect(processed.failedUsers).toHaveLength(2);
    expect(processed.failedUsers[0].name).toBe("Jane Smith");
    expect(processed.failedUsers[0].error).toBe("User not found in the system");
    expect(processed.failedUsers[1].name).toBe("Bob Brown");
    expect(processed.failedUsers[1].error).toBe(
      "Failed to add user - please try again"
    );
    expect(processed.usersToRemove).toHaveLength(1);
    expect(processed.usersToRemove[0].name).toBe("John Doe");
    expect(processed.groupName).toBe(testAllUsersGroup);
  });

  it("should recognize specific error patterns that mean user is already in group", () => {
    const users: User[] = [
      { id: "1", name: "John Doe", email: "<EMAIL>" },
      { id: "2", name: "Jane Smith", email: "<EMAIL>" },
    ];

    const results = {
      "<EMAIL>": {
        success: false,
        message:
          "One or more added object references already exist for the following modified properties: 'members'",
      },
      "<EMAIL>": {
        success: false,
        message: "The object reference already exists",
      },
    };

    const processed = processUserAdditionResults(
      users,
      results,
      testAdminGroup
    );

    expect(processed.successfulUsers).toHaveLength(0);
    expect(processed.alreadyInGroup).toHaveLength(2);
    expect(processed.alreadyInGroup).toContain("John Doe");
    expect(processed.alreadyInGroup).toContain("Jane Smith");
    expect(processed.failedUsers).toHaveLength(0);
    expect(processed.usersToRemove).toHaveLength(2);
    expect(processed.groupName).toBe(testAdminGroup);
  });
});

describe("formatUserAdditionStatusMessage", () => {
  const testGroup = "Atvero Mail Test Group";

  it("should format message for only successful additions", () => {
    const processedResults = {
      successfulUsers: ["John", "Jane"],
      alreadyInGroup: [],
      groupName: testGroup,
    };

    const message = formatUserAdditionStatusMessage(processedResults);

    expect(message).toBe(`Successfully added 2 users to ${testGroup}.`);
  });

  it("should format message for only already-in-group users", () => {
    const processedResults = {
      successfulUsers: [],
      alreadyInGroup: ["John", "Jane"],
      groupName: testGroup,
    };

    const message = formatUserAdditionStatusMessage(processedResults);

    expect(message).toBe(`2 users were already in the group ${testGroup}.`);
  });

  it("should format message for a mix of outcomes", () => {
    const processedResults = {
      successfulUsers: ["John"],
      alreadyInGroup: ["Jane"],
      groupName: testGroup,
    };

    const message = formatUserAdditionStatusMessage(processedResults);

    expect(message).toBe(
      `Successfully added 1 user to ${testGroup}. 1 user was already in the group ${testGroup}.`
    );
  });
});

describe("formatUserAdditionErrorMessage", () => {
  const testGroup = "Atvero Mail Test Group";

  it("should return empty string for no failed users", () => {
    const message = formatUserAdditionErrorMessage([], testGroup);

    expect(message).toBe("");
  });

  it("should format message for a single error type", () => {
    const failedUsers = [
      { name: "John", error: "User not found in the system" },
      { name: "Jane", error: "User not found in the system" },
    ];

    const message = formatUserAdditionErrorMessage(failedUsers, testGroup);

    expect(message).toBe(
      `We ran into an issue adding some of the users you selected to ${testGroup}. Error: User not found in the system`
    );
  });

  it("should format message for multiple error types", () => {
    const failedUsers = [
      { name: "John", error: "User not found in the system" },
      { name: "Jane", error: "Insufficient permissions to add user" },
    ];

    const message = formatUserAdditionErrorMessage(failedUsers, testGroup);

    expect(message).toBe(
      `We ran into an issue adding some of the users you selected to ${testGroup}. Error: Multiple users couldn't be added due to different errors`
    );
  });
});

describe("addUsersToGroup", () => {
  let mockSiteAdmin: ISiteAdmin;
  const testGroup = "Atvero Mail Test Group";

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();

    // Create a mock site admin implementation
    mockSiteAdmin = {
      getGroups: vi.fn(),
      getUserByEmail: vi.fn(),
      addUserToGroupById: vi.fn(),
    } as unknown as ISiteAdmin;
  });

  it("should add multiple users to a group successfully", async () => {
    // Setup mocks
    mockSiteAdmin.getGroups = vi.fn().mockResolvedValue([{ id: "group-123" }]);
    mockSiteAdmin.getUserByEmail = vi.fn().mockImplementation((email) => {
      return Promise.resolve({
        id: email === "<EMAIL>" ? "user-1" : "user-2",
        displayName: email === "<EMAIL>" ? "John Doe" : "Jane Smith",
        email,
      });
    });
    mockSiteAdmin.addUserToGroupById = vi.fn().mockResolvedValue({
      success: true,
      message: "User added successfully",
    });

    // Call the function
    const result = await addUsersToGroup(mockSiteAdmin, testGroup, [
      "<EMAIL>",
      "<EMAIL>",
    ]);

    // Verify results
    expect(result.success).toBe(true);
    expect(result.results["<EMAIL>"].success).toBe(true);
    expect(result.results["<EMAIL>"].success).toBe(true);

    // Verify function calls
    expect(mockSiteAdmin.getGroups).toHaveBeenCalledWith(testGroup);
    expect(mockSiteAdmin.getUserByEmail).toHaveBeenCalledTimes(2);
    expect(mockSiteAdmin.getUserByEmail).toHaveBeenCalledWith(
      "<EMAIL>"
    );
    expect(mockSiteAdmin.getUserByEmail).toHaveBeenCalledWith(
      "<EMAIL>"
    );
    expect(mockSiteAdmin.addUserToGroupById).toHaveBeenCalledTimes(2);
    expect(mockSiteAdmin.addUserToGroupById).toHaveBeenCalledWith(
      "group-123",
      "user-1"
    );
    expect(mockSiteAdmin.addUserToGroupById).toHaveBeenCalledWith(
      "group-123",
      "user-2"
    );
  });

  it("should handle users that are already in the group", async () => {
    // Setup mocks
    mockSiteAdmin.getGroups = vi.fn().mockResolvedValue([{ id: "group-123" }]);
    mockSiteAdmin.getUserByEmail = vi.fn().mockImplementation((email) => {
      return Promise.resolve({
        id: "user-1",
        displayName: "John Doe",
        email,
      });
    });
    mockSiteAdmin.addUserToGroupById = vi.fn().mockResolvedValue({
      success: true,
      message: "User was already in the group",
    });

    // Call the function
    const result = await addUsersToGroup(mockSiteAdmin, testGroup, [
      "<EMAIL>",
    ]);

    // Verify results
    expect(result.success).toBe(true);
    expect(result.results["<EMAIL>"].success).toBe(true);
    expect(result.results["<EMAIL>"].message).toBe(
      "User was already in the group"
    );

    // Verify function calls
    expect(mockSiteAdmin.getGroups).toHaveBeenCalledWith(testGroup);
    expect(mockSiteAdmin.getUserByEmail).toHaveBeenCalledTimes(1);
    expect(mockSiteAdmin.addUserToGroupById).toHaveBeenCalledTimes(1);
  });

  it("should handle users that don't exist", async () => {
    // Setup mocks
    mockSiteAdmin.getGroups = vi.fn().mockResolvedValue([{ id: "group-123" }]);
    mockSiteAdmin.getUserByEmail = vi.fn().mockResolvedValue(null);

    // Call the function
    const result = await addUsersToGroup(mockSiteAdmin, testGroup, [
      "<EMAIL>",
    ]);

    // Verify results
    expect(result.success).toBe(false);
    expect(result.results["<EMAIL>"].success).toBe(false);
    expect(result.results["<EMAIL>"].message).toBe(
      "User not found"
    );

    // Verify function calls
    expect(mockSiteAdmin.getGroups).toHaveBeenCalledWith(testGroup);
    expect(mockSiteAdmin.getUserByEmail).toHaveBeenCalledTimes(1);
    expect(mockSiteAdmin.addUserToGroupById).not.toHaveBeenCalled();
  });

  it("should handle when the security group is not found", async () => {
    // Setup mocks - empty array means no groups found
    mockSiteAdmin.getGroups = vi.fn().mockResolvedValue([]);

    // Call the function
    const result = await addUsersToGroup(mockSiteAdmin, "Nonexistent Group", [
      "<EMAIL>",
      "<EMAIL>",
    ]);

    // Verify results
    expect(result.success).toBe(false);
    expect(result.results["<EMAIL>"].success).toBe(false);
    expect(result.results["<EMAIL>"].success).toBe(false);
    expect(result.results["<EMAIL>"].message).toContain(
      "Security group Nonexistent Group not found"
    );

    // Verify function calls
    expect(mockSiteAdmin.getGroups).toHaveBeenCalledWith("Nonexistent Group");
    expect(mockSiteAdmin.getUserByEmail).not.toHaveBeenCalled();
    expect(mockSiteAdmin.addUserToGroupById).not.toHaveBeenCalled();
  });

  it("should correctly format non-Error objects as strings in error handling", async () => {
    // Setup mock to throw a non-Error object
    mockSiteAdmin.getGroups = vi.fn().mockImplementation(() => {
      // Throw a string or object that isn't an Error instance
      throw "Server unavailable";
    });

    // Call the function with multiple email addresses to test the reduce logic
    const userEmails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ];
    const result = await addUsersToGroup(mockSiteAdmin, testGroup, userEmails);

    // Verify that the error was logged
    expect(console.error).toHaveBeenCalled();
    expect(Sentry.captureException).toHaveBeenCalledWith("Server unavailable");

    // Verify structure of the returned object
    expect(result).toEqual({
      success: false,
      results: {
        "<EMAIL>": { success: false, message: "Server unavailable" },
        "<EMAIL>": { success: false, message: "Server unavailable" },
        "<EMAIL>": { success: false, message: "Server unavailable" },
      },
    });

    // Verify that the error message is correctly propagated to all emails
    for (const email of userEmails) {
      expect(result.results[email]).toBeDefined();
      expect(result.results[email].success).toBe(false);
      expect(result.results[email].message).toBe("Server unavailable");
    }
  });

  it("should handle mixed success and failure cases", async () => {
    // Setup mocks
    mockSiteAdmin.getGroups = vi.fn().mockResolvedValue([{ id: "group-123" }]);

    mockSiteAdmin.getUserByEmail = vi.fn().mockImplementation((email) => {
      if (email === "<EMAIL>") {
        return Promise.resolve({
          id: "user-1",
          displayName: "John Doe",
          email,
        });
      } else if (email === "<EMAIL>") {
        return Promise.resolve(null); // User not found
      }
    });

    mockSiteAdmin.addUserToGroupById = vi.fn().mockResolvedValue({
      success: true,
      message: "User added successfully",
    });

    // Call the function
    const result = await addUsersToGroup(mockSiteAdmin, testGroup, [
      "<EMAIL>",
      "<EMAIL>",
    ]);

    // Verify results
    expect(result.success).toBe(false); // Overall success should be false since one user failed
    expect(result.results["<EMAIL>"].success).toBe(true);
    expect(result.results["<EMAIL>"].success).toBe(false);

    // Verify function calls
    expect(mockSiteAdmin.getGroups).toHaveBeenCalledWith(testGroup);
    expect(mockSiteAdmin.getUserByEmail).toHaveBeenCalledTimes(2);
    expect(mockSiteAdmin.addUserToGroupById).toHaveBeenCalledTimes(1);
    expect(mockSiteAdmin.addUserToGroupById).toHaveBeenCalledWith(
      "group-123",
      "user-1"
    );
  });
});
