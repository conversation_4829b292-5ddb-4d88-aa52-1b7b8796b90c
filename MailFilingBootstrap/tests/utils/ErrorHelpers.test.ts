import { describe, it, expect } from "vitest";
import {
  extractErrorValue,
  createSharePointError,
} from "../../src/utils/ErrorHelpers";

describe("ErrorHelper", () => {
  describe("extractErrorValue", () => {
    it("should extract value from SharePoint odata.error format", () => {
      const error = new Error(
        'Error making HttpClient request in queryable [404] ::> {"odata.error":{"code":"-2130575276, Microsoft.SharePoint.SPException","message":{"lang":"en-US","value":"The user does not exist or is not unique."}}}'
      );

      expect(extractErrorValue(error)).toBe(
        "The user does not exist or is not unique."
      );
    });

    it("should extract value from alternative error format", () => {
      const error = new Error(
        'Request failed with status 400 {"error":{"code":"InvalidRequest","message":{"lang":"en-US","value":"Invalid site collection URL."}}}'
      );

      expect(extractErrorValue(error)).toBe("Invalid site collection URL.");
    });

    it("should handle errors with regex when JSON parsing fails", () => {
      const error = new Error(
        'Some complex error with embedded "value":"Access denied" in the message'
      );

      expect(extractErrorValue(error)).toBe(
        `Some complex error with embedded "value":"Access denied" in the message`
      );
    });

    it("should return original message when no value can be extracted", () => {
      const error = new Error("Simple error message");

      expect(extractErrorValue(error)).toBe("Simple error message");
    });

    it("should handle non-Error objects", () => {
      expect(extractErrorValue("String error")).toBe("String error");
      expect(extractErrorValue(404)).toBe("404");
      expect(extractErrorValue({ custom: "error" })).toBe("[object Object]");
    });

    it("should handle null and undefined", () => {
      expect(extractErrorValue(null)).toBe("null");
      expect(extractErrorValue(undefined)).toBe("undefined");
    });

    it("should handle malformed JSON gracefully", () => {
      const error = new Error('Invalid JSON {"incomplete": true');

      expect(extractErrorValue(error)).toBe('Invalid JSON {"incomplete": true');
    });

    it("should handle nested JSON with complex structure", () => {
      const error = new Error(
        'Complex error {"odata.error":{"code":"BadRequest","message":{"lang":"en-US","value":"Site already exists","details":"Additional details"}}}'
      );

      expect(extractErrorValue(error)).toBe("Site already exists");
    });

    it("should handle errors with special characters in value", () => {
      const error = new Error(
        '{"odata.error":{"message":{"value":"Error with special chars: \\"quotes\\", \'apostrophes\', and unicode: 🚨"}}}'
      );

      expect(extractErrorValue(error)).toBe(
        "Error with special chars: \"quotes\", 'apostrophes', and unicode: 🚨"
      );
    });

    it("should handle exception during processing", () => {
      // Create an error that will throw when trying to access its properties
      const thrower = {
        get message() {
          throw new Error("Property access failed");
        },
      };

      // Cast to Error to satisfy the instanceof check
      const badError = Object.create(Error.prototype);
      Object.defineProperty(badError, "message", {
        get() {
          throw new Error("Cannot access message");
        },
      });

      expect(extractErrorValue(badError)).toBe("Unknown error occurred");
    });
  });

  describe("createSharePointError", () => {
    it("should create formatted error message", () => {
      const error = new Error(
        '{"odata.error":{"message":{"value":"The user does not exist"}}}'
      );

      expect(createSharePointError("assign admin group", error)).toBe(
        "Failed to assign admin group: The user does not exist"
      );
    });

    it("should work with simple error messages", () => {
      const error = new Error("Network timeout");

      expect(createSharePointError("create site", error)).toBe(
        "Failed to create site: Network timeout"
      );
    });

    it("should handle non-Error inputs", () => {
      expect(createSharePointError("upload file", "Permission denied")).toBe(
        "Failed to upload file: Permission denied"
      );
    });
  });
});
