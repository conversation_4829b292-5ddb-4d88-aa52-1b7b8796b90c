import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: "./src/setupTests.ts",
    include: ["tests/**/*.ts", "tests/**/*.tsx"],
    coverage: {
      reporter: ["text", "json", "html", "lcov"],
      include: ["src/**/*.ts", "src/**/*.tsx"],
      exclude: [
        "src/main.tsx",
        "src/routeTree.gen.ts",
        "src/routes/**/*",
        "**/*.d.ts",
        "tests/**/*",
        "*.config.js",
        "src/adapters/**/*",
      ],
    },
  },
});
