{
  "compilerOptions": {
    "esModuleInterop": true,
    "pretty": true,
    "outDir": "dist",
    "forceConsistentCasingInFileNames": true,
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["es2021", "DOM", "DOM.Iterable", "es7", "dom"],
    "module": "CommonJS",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "node",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": [
    "src",
    "tests/setupUtils.test.ts",
    "tests/utils/ExceptionHelper.test.ts",
    "tests/utils/Config.test.ts",
    "tests/components/SearchSchema/apiCall.test.ts",
    "tests/components/SearchSchema/SearchSchemaImport.test.tsx"
  ],
  "references": [{ "path": "./tsconfig.node.json" }],
  "exclude": ["node_modules"],
  "compileOnSave": false,
  "buildOnSave": false,
  "ts-node": {
    "files": true
  }
}
