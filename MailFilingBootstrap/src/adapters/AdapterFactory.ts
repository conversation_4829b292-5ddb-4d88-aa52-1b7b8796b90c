import { GraphFI } from "@pnp/graph";
import { SiteAdmin } from "./SiteAdmin";
import { SPFI } from "@pnp/sp";
import { ISiteAdmin } from "./ISiteAdmin";

export type Adapters = {
  siteAdmin: ISiteAdmin;
};

export const adapterFactory = async (
  graph: GraphFI,
  sharepoint: SPFI,
  sharepointHost: string
) => {
  const adapters: Adapters = {
    siteAdmin: new SiteAdmin(graph, sharepoint, sharepointHost),
  };
  return adapters;
};
