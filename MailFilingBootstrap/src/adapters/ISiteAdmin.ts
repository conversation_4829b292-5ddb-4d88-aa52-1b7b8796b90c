import {
  Drive,
  DriveItem,
  Group,
  Site as ISiteType,
  List as IListEntity,
  ListItem,
  Site,
} from "@microsoft/microsoft-graph-types";
import { IFileUploadOptions } from "@pnp/graph/files";
import { IHubSiteInfo } from "@pnp/sp/hubsites";
import { ISiteDesignInfo, ISiteDesignRun } from "@pnp/sp/site-designs";
import { ISiteScriptInfo } from "@pnp/sp/site-scripts";
import { ISiteUser } from "@pnp/sp/site-users/types";
import { ISite } from "@pnp/sp/sites";
import { ISiteCreationResponse, ISiteInfo } from "@pnp/sp/sites/types";
import { IWeb } from "@pnp/sp/webs";
import { User } from "../types/User";

export interface ISiteAdmin {
  getSite(): ISite;
  getSiteFromUrl(url: string): Promise<ISiteInfo | undefined>;

  getAtveroMailHubsite(hubsiteUrl: string): Promise<ISiteType | null>;

  createSiteScript(
    title: string,
    description: string,
    scriptContent: string
  ): Promise<ISiteScriptInfo>;

  createSiteDesign(
    scriptIds: string[],
    title: string,
    description: string
  ): Promise<ISiteDesignInfo>;

  registerHubsite(hubsiteUrl: string): Promise<boolean>;
  getHubsites(): Promise<IHubSiteInfo[]>;

  createCommunicationSite(
    hubsiteUrl: string,
    siteDesignId: string
  ): Promise<ISiteCreationResponse>;
  getSiteDesigns(): Promise<ISiteDesignInfo[]>;

  getSiteDesignRuns(hubsiteUrl: string): Promise<ISiteDesignRun[]>;
  applySiteDesign(hubsiteUrl: string, siteDesignId: string): Promise<boolean>;
  // Check if the site design is applied to the hubsite
  assignGroupPermissions(hubsite: Site): Promise<boolean>;
  assignMailAdminsGroup(web: IWeb): Promise<ISiteUser>;
  getGroups(group: string): Promise<Group[]>;
  assignEveryoneGroup(web: IWeb): Promise<ISiteUser>;
  createGroup(group: string): Promise<Group>;

  getListItemByDriveItem(
    siteId: string,
    driveId: string,
    path: string
  ): Promise<ListItem | null>;

  getDrives(siteId: string): Promise<Drive[]>;
  uploadFile(
    siteId: string,
    driveId: string,
    fileData: IFileUploadOptions
  ): Promise<DriveItem>;

  getSiteList(siteId: string, listName: string): Promise<IListEntity | null>;
  updateListItem(
    siteId: string,
    listId: string,
    listItemId: string,
    fields: any
  ): Promise<ListItem | null>;
  searchUsers(searchTerm: string): Promise<any[]>;
  getUsersFromGroup(groupName: string): Promise<User[] | undefined>;
  removeUsersFromGroupById(groupName: string, userId: string): Promise<boolean>;
  getUserByEmail(email: string): Promise<User | null>;
  addUserToGroupById(
    groupId: string,
    userId: string
  ): Promise<{ success: boolean; message: string }>;
  getSiteVisitors(
    hubsiteUrl: string
  ): Promise<{ count: number; visitors: string[] }>;
  getSiteOwners(
    hubsiteUrl: string
  ): Promise<{ count: number; owners: string[] }>;
}
