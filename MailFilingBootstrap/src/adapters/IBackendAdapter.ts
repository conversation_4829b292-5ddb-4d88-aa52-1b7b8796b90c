import { ApiResponse } from "../api/backendUtils";
import {
  HubsiteRightsData,
  SetSearchSchemaResponse,
} from "../adapters/BackendAdapter";
import { Hubsite } from "../types/Hubsite";

export interface IBackendAdapter {
  deploySearchSchema(
    hubsiteUrl: string
  ): Promise<ApiResponse<SetSearchSchemaResponse>>;
  getHubsiteRights(sitePath: string): Promise<ApiResponse<HubsiteRightsData>>;
  setHubsiteRights(sitePath: string): Promise<ApiResponse<HubsiteRightsData>>;
  getHubsites(): Promise<ApiResponse<Hubsite[]>>;

  makeGenericCall<T = any>(
    endpoint: string,
    method: "GET" | "POST" | "PUT" | "DELETE",
    body?: any
  ): Promise<ApiResponse<T>>;
}
