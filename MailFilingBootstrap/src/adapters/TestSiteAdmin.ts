import {
  Site,
  Group,
  Drive,
  DriveItem,
  List,
  ListItem,
} from "@microsoft/microsoft-graph-types";
import { IFileUploadOptions } from "@pnp/graph/files";
import { ISiteDesignInfo, ISiteDesignRun } from "@pnp/sp/site-designs";
import { ISiteScriptInfo } from "@pnp/sp/site-scripts";
import { ISite } from "@pnp/sp/sites";
import { ISiteCreationResponse, ISiteInfo } from "@pnp/sp/sites/types";
import { ISiteAdmin } from "./ISiteAdmin";
import { IHubSiteInfo } from "@pnp/sp/hubsites";
import { IWeb } from "@pnp/sp/webs";
import { ISiteUser } from "@pnp/sp/site-users/types";
import { User } from "../types/User";

export class TestSiteAdmin implements ISiteAdmin {
  getSiteVisitors(
    _hubsiteUrl: string
  ): Promise<{ count: number; visitors: string[] }> {
    throw new Error("Method not implemented.");
  }
  getSiteOwners(
    _hubsiteUrl: string
  ): Promise<{ count: number; owners: string[] }> {
    throw new Error("Method not implemented.");
  }
  assignGroupPermissions(_hubsite: Site): Promise<boolean> {
    throw new Error("Method not implemented.");
  }
  assignMailAdminsGroup(_web: IWeb): Promise<ISiteUser> {
    throw new Error("Method not implemented.");
  }
  assignEveryoneGroup(_web: IWeb): Promise<ISiteUser> {
    throw new Error("Method not implemented.");
  }
  getSite(): ISite {
    throw new Error("Method not implemented.");
  }
  getSiteFromUrl(_url: string): Promise<ISiteInfo | undefined> {
    throw new Error("Method not implemented.");
  }
  getAtveroMailHubsite(_hubsitePath: string): Promise<Site | null> {
    throw new Error("Method not implemented.");
  }
  createSiteScript(
    _title: string,
    _description: string,
    _scriptContent: string
  ): Promise<ISiteScriptInfo> {
    throw new Error("Method not implemented.");
  }
  createSiteDesign(
    _scriptIds: string[],
    _title: string,
    _description: string
  ): Promise<ISiteDesignInfo> {
    throw new Error("Method not implemented.");
  }
  registerHubsite(_hubsiteUrl: any): Promise<boolean> {
    throw new Error("Method not implemented.");
  }
  getHubsites(): Promise<IHubSiteInfo[]> {
    throw new Error("Method not implemented.");
  }
  createCommunicationSite(
    _hubsitePath: string,
    _siteDesignId: string
  ): Promise<ISiteCreationResponse> {
    throw new Error("Method not implemented.");
  }
  getSiteDesigns(): Promise<ISiteDesignInfo[]> {
    throw new Error("Method not implemented.");
  }
  getSiteDesignRuns(_hubsiteUrl: string): Promise<ISiteDesignRun[]> {
    throw new Error("Method not implemented.");
  }

  applySiteDesign(
    _hubsiteUrl: string,
    _siteDesignId: string
  ): Promise<boolean> {
    throw new Error("Method not implemented.");
  }

  getGroups(_group: string): Promise<Group[]> {
    throw new Error("Method not implemented.");
  }
  createGroup(_group: string): Promise<Group> {
    throw new Error("Method not implemented.");
  }
  getListItemByDriveItem(
    _siteId: string,
    _driveId: string,
    _path: string
  ): Promise<ListItem | null> {
    throw new Error("Method not implemented.");
  }
  getDrives(_siteId: string): Promise<Drive[]> {
    throw new Error("Method not implemented.");
  }
  uploadFile(
    _siteId: string,
    _driveId: string,
    _fileData: IFileUploadOptions
  ): Promise<DriveItem> {
    throw new Error("Method not implemented.");
  }
  getSiteList(_siteId: string, _listName: string): Promise<List | null> {
    throw new Error("Method not implemented.");
  }
  updateListItem(
    _siteId: string,
    _listId: string,
    _listItemId: string,
    _fields: any
  ): Promise<List | null> {
    throw new Error("Method not implemented.");
  }
  addUsersToGroup(
    _groupName: string,
    _userEmails: string[]
  ): Promise<{
    success: boolean;
    results: { [email: string]: { success: boolean; message: string } };
  }> {
    throw new Error("Method not implemented.");
  }

  searchUsers(_searchTerm: string): Promise<any[]> {
    throw new Error("Method not implemented.");
  }
  getUsersFromGroup(_groupName: string): Promise<User[] | undefined> {
    throw new Error("Method not implemented.");
  }
  removeUsersFromGroupById(
    _groupName: string,
    _userId: string
  ): Promise<boolean> {
    throw new Error("Method not implemented.");
  }
  getUserByEmail(_email: string): Promise<User | null> {
    throw new Error("Method not implemented.");
  }
  addUserToGroupById(
    _groupId: string,
    _userId: string
  ): Promise<{ success: boolean; message: string }> {
    throw new Error("Method not implemented.");
  }
}
