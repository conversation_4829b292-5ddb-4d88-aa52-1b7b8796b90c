import { <PERSON>raph<PERSON> } from "@pnp/graph";
import { ISiteAdmin } from "./ISiteAdmin";
import { SPFI } from "@pnp/sp";
import {
  Drive,
  DriveItem,
  Group,
  Site,
  List as IListEntity,
  List,
  ListItem,
} from "@microsoft/microsoft-graph-types";
import { ISiteDesignInfo, ISiteDesignRun } from "@pnp/sp/site-designs";
import { ISite, Site as SharepointSite } from "@pnp/sp/sites";
import * as Sentry from "@sentry/react";
import { IWeb, Web } from "@pnp/sp/webs";
import { GroupType } from "@pnp/graph/groups";
import { IFileUploadOptions } from "@pnp/graph/files";
import { ISiteScriptInfo } from "@pnp/sp/site-scripts";
import { ISiteCreationResponse, ISiteInfo } from "@pnp/sp/sites/types";
import "@pnp/graph/groups";
import "@pnp/graph/users";
import "@pnp/graph/members";
import { IHubSiteInfo } from "@pnp/sp/hubsites";
import "@pnp/sp/hubsites";
import { HttpRequestError } from "@pnp/queryable";
import { productName } from "../shared/TenancyService";
import "@pnp/sp/site-groups/web";
import { ISiteUser } from "@pnp/sp/site-users/types";
import "@pnp/graph/admin";
import "@pnp/sp/profiles";
import { User } from "../types/User";
import {
  createSharePointError,
  extractErrorValue,
} from "../utils/ErrorHelpers";

export class SiteAdmin implements ISiteAdmin {
  graph: GraphFI;
  sharepoint: SPFI;
  sharepointHostName: string;

  constructor(client: GraphFI, spclient: SPFI, sharepointHost: string) {
    this.graph = client;
    this.sharepoint = spclient;
    this.sharepointHostName = sharepointHost;
  }

  sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  getSite(): ISite {
    return SharepointSite([this.sharepoint.web, this.sharepointHostName]);
  }

  getSiteFromUrl(siteUrl: string): Promise<ISiteInfo | undefined> {
    try {
      const site = SharepointSite([this.sharepoint.web, siteUrl]);
      return site();
    } catch (error) {
      console.error("Invalid URL format:", siteUrl, error);
      return Promise.resolve(undefined);
    }
  }

  async getAtveroMailHubsite(hubsiteUrl: string): Promise<Site | null> {
    try {
      const url = new URL(hubsiteUrl);
      const sharepointHost = url.host;
      const hubsitePath = url.pathname;

      const sitePromise = await this.graph.sites.getByUrl(
        sharepointHost,
        hubsitePath
      );
      const site = await sitePromise();
      return site;
    } catch (error) {
      return null;
    }
  }
  async getSiteDesignRuns(hubsiteUrl: string): Promise<ISiteDesignRun[]> {
    const url = new URL(hubsiteUrl);
    const hubsitePath = url.pathname;
    return this.sharepoint.siteDesigns.getSiteDesignRun(hubsitePath);
  }

  async getHubsites(): Promise<IHubSiteInfo[]> {
    return this.sharepoint.hubSites();
  }

  async getSiteDesigns(): Promise<ISiteDesignInfo[]> {
    return await this.sharepoint.siteDesigns.getSiteDesigns();
  }
  async createSiteScript(
    title: string,
    description: string,
    scriptContent: string
  ): Promise<ISiteScriptInfo> {
    return this.sharepoint.siteScripts.createSiteScript(
      title,
      description,
      scriptContent
    );
  }
  async createSiteDesign(
    scriptIds: string[],
    title: string,
    description: string
  ): Promise<ISiteDesignInfo> {
    return await this.sharepoint.siteDesigns.createSiteDesign({
      SiteScriptIds: scriptIds,
      Title: title,
      Description: description,
      WebTemplate: "68", //68 comms site design
    });
  }

  async registerHubsite(hubsitePath: string): Promise<boolean> {
    try {
      const hubsite = SharepointSite([this.sharepoint.web, hubsitePath]);
      await hubsite.registerHubSite();

      return true;
    } catch (e) {
      if (e instanceof HttpRequestError) {
        // we can read the json from the response
        const json = await (<HttpRequestError>e).response.text();
        if (json.indexOf("This site is already a HubSite.") >= 0) {
          return true;
        } else {
          Sentry.captureException(e);
        }
      } else {
        // not an HttpRequestError so we just log message
        console.log(e);
        Sentry.captureException(e);
        return false;
      }
    }
    return false;
  }

  async createCommunicationSite(
    hubsiteUrl: string,
    siteDesignId: string
  ): Promise<ISiteCreationResponse> {
    const urll = new URL(hubsiteUrl);
    const hubsitePath = urll.pathname;
    const url = `${this.sharepointHostName}/${hubsitePath}`;
    return await this.sharepoint.site.createCommunicationSiteFromProps({
      Title: productName(),
      ShareByEmailEnabled: false,
      Url: url,
      Description: productName() + " Filing hubsite",
      SiteDesignId: siteDesignId,
    });
  }

  async applySiteDesign(
    hubsiteUrl: string,
    siteDesignId: string
  ): Promise<boolean> {
    const runs = await this.sharepoint.siteDesigns.getSiteDesignRun(hubsiteUrl);
    if (runs.some((r) => r.SiteDesignID === siteDesignId)) {
      // Already applied
      return true;
    } else {
      // apply
      try {
        const web = Web([this.sharepoint.web, hubsiteUrl]);
        const task = await web.addSiteDesignTask(siteDesignId);

        let complete = false;
        while (!complete) {
          try {
            const runningTask = await web.getSiteDesignRunStatus(task.ID);
            complete = !!runningTask;
          } catch (error) {
            //ignore this error
          }
          await this.sleep(5000);
        }

        return true;
      } catch (error) {
        Sentry.captureException(error);
        console.error(error);
        return false;
      }
    }
  }

  async assignMailAdminsGroup(web: IWeb): Promise<ISiteUser> {
    const [ownerGroup, adminSecurityGroups] = await Promise.all([
      web.associatedOwnerGroup(),
      this.getGroups(productName() + " Admins"),
    ]);

    if (!adminSecurityGroups?.length) {
      throw new Error(
        "Admin security group not found. Searching from security group: " +
          productName() +
          " Admins"
      );
    }

    return await web.siteGroups
      .getByName(ownerGroup.Title)
      .users.add(`c:0t.c|tenant|${adminSecurityGroups[0].id}`);
  }

  async assignEveryoneGroup(web: IWeb): Promise<ISiteUser> {
    try {
      const visitorGroup = await web.associatedVisitorGroup();

      const everyoneGroup =
        await this.sharepoint.profiles.clientPeoplePickerSearchUser({
          AllowEmailAddresses: true,
          AllowMultipleEntities: false,
          MaximumEntitySuggestions: 50,
          QueryString: "Everyone except external users",
        });

      const everyoneExceptExternal = everyoneGroup.find(
        (user) => user.DisplayText === "Everyone except external users"
      );

      if (!everyoneExceptExternal) {
        throw new Error("Everyone except external users group not found");
      }

      console.log(
        "Found Everyone except external users group: ",
        everyoneExceptExternal.Key
      );

      return await web.siteGroups
        .getById(visitorGroup.Id)
        .users.add(everyoneExceptExternal.Key);
    } catch (error: unknown) {
      const cleanError = createSharePointError(
        "assign Everyone except external users group",
        error
      );
      console.error(cleanError);
      Sentry.captureException(error);
      throw new Error(extractErrorValue(error));
    }
  }

  async assignGroupPermissions(hubsite: Site): Promise<boolean> {
    if (!hubsite.webUrl) {
      throw new Error("Hubsite WebURL is missing or undefined");
    }

    try {
      const web = Web([this.sharepoint.web, hubsite.webUrl]);

      try {
        await this.assignMailAdminsGroup(web);
        console.log("Successfully assigned admin group");
      } catch (error: unknown) {
        const cleanError = createSharePointError("assign admin group", error);
        console.error(cleanError);
        throw new Error(cleanError);
      }

      try {
        await this.assignEveryoneGroup(web);
        console.log(
          "Successfully assigned Everyone except external users group"
        );
      } catch (error: unknown) {
        throw error;
      }

      return true;
    } catch (error: unknown) {
      throw error;
    }
  }

  async getGroups(group: string): Promise<Group[]> {
    return await this.graph.groups.filter(
      `mailEnabled eq false and securityEnabled eq true and DisplayName eq '${group}'`
    )();
  }

  async createGroup(group: string): Promise<Group> {
    return await this.graph.groups.add(
      group,
      group.replaceAll(" ", "_"),
      GroupType.Security
    );
  }
  async getDrives(siteId: string): Promise<Drive[]> {
    return await this.graph.sites.getById(siteId).drives();
  }

  async getSiteList(
    siteId: string,
    listName: string
  ): Promise<IListEntity | null> {
    const lists = await this.graph.sites.getById(siteId).lists();
    const list: IListEntity = lists.filter((l) => l.displayName == listName)[0];
    return list;
  }

  async getListItemByDriveItem(
    siteId: string,
    driveId: string,
    path: string
  ): Promise<ListItem | null> {
    const driveItem = await this.graph.sites
      .getById(siteId)
      .drives.getById(driveId)
      .getItemByPath(path)
      .expand("ListItem")();

    if (driveItem?.listItem) {
      return driveItem.listItem;
    } else {
      return null;
    }
  }

  async uploadFile(
    siteId: string,
    driveId: string,
    fileData: IFileUploadOptions
  ): Promise<DriveItem> {
    return await this.graph.sites
      .getById(siteId)
      .drives.getById(driveId)
      .root.upload(fileData);
  }

  async updateListItem(
    siteId: string,
    listId: string,
    listItemId: string,
    fields: any
  ): Promise<List | null> {
    return await this.graph.sites
      .getById(siteId)
      .lists.getById(listId)
      .items.getById(listItemId)
      .update({
        fields: fields,
      });
  }

  /**
   * Retrieves a user by their email address
   * @param email The email address of the user to find
   * @returns Promise containing the user object or null if not found
   */
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      const users = await this.graph.users.filter(
        `mail eq '${email}' or userPrincipalName eq '${email}'`
      )();

      if (!users || users.length === 0) {
        return null;
      }

      const user = users[0];
      return {
        id: user.id || "",
        displayName: user.displayName || "",
        email: user.mail || user.userPrincipalName || "",
      };
    } catch (error) {
      console.error(`Error finding user by email ${email}:`, error);
      Sentry.captureException(error);
      return null;
    }
  }

  /**
   * Adds a single user to a group by their IDs
   * @param groupId The ID of the group to add the user to
   * @param userId The ID of the user to add
   * @returns Promise with the result of the operation
   */
  async addUserToGroupById(
    groupId: string,
    userId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.graph.groups
        .getById(groupId)
        .members.add(
          `https://graph.microsoft.com/v1.0/directoryObjects/${userId}`
        );
      return {
        success: true,
        message: "User added successfully",
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      if (
        errorMessage.includes("already exists") ||
        errorMessage.includes(
          "already exist for the following modified properties: 'members'"
        )
      ) {
        return {
          success: true,
          message: "User was already in the group",
        };
      } else {
        console.error(`Error adding user to group:`, error);
        return {
          success: false,
          message: errorMessage || "Failed to add user",
        };
      }
    }
  }

  async searchUsers(searchTerm: string): Promise<any[]> {
    try {
      if (!searchTerm || searchTerm.length < 3) {
        return [];
      }

      const users = await this.sharepoint.profiles.clientPeoplePickerSearchUser(
        {
          AllowEmailAddresses: true,
          AllowMultipleEntities: false,
          MaximumEntitySuggestions: 10,
          QueryString: searchTerm,
        }
      );

      return users.slice(0, 10).map((user) => ({
        id: user.Key,
        name: user.DisplayText,
        email: user.EntityData.Email || "",
        principalName: user.EntityData.Email || "",
      }));
    } catch (error) {
      console.error("Error searching for users:", error);
      Sentry.captureException(error);
      return [];
    }
  }

  async getUsersFromGroup(groupName: string): Promise<User[] | undefined> {
    const securityGroup = await this.getGroups(groupName);

    const group = securityGroup[0];
    if (!group || !group.id) {
      console.error(`Group ${groupName} found but has no ID`);
      return undefined;
    }

    const groupId: string = group.id;

    const users = await this.graph.groups.getById(groupId).members();

    return users.map((user) => ({
      id: user.id || "",
      displayName: user.displayName || "",
      email: user.mail || "",
    }));
  }

  async removeUsersFromGroupById(
    groupName: string,
    userId: string
  ): Promise<boolean> {
    try {
      const securityGroup = await this.getGroups(groupName);
      const group = securityGroup[0];

      if (!group || !group.id) {
        console.error(`Group ${groupName} found but has no ID`);
        return false;
      }

      const groupId: string = group.id;

      await this.graph.groups.getById(groupId).members.getById(userId).remove();

      return true;
    } catch (error) {
      console.error(`Error removing user from group ${groupName}:`, error);
      Sentry.captureException(error);
      return false;
    }
  }

  async getSiteVisitors(
    hubsiteUrl: string
  ): Promise<{ count: number; visitors: string[] }> {
    try {
      const web = Web([this.sharepoint.web, hubsiteUrl]);
      const visitorGroup = await web.associatedVisitorGroup();
      const visitors = await web.siteGroups.getById(visitorGroup.Id).users();

      return {
        count: visitors.length,
        visitors: visitors.map(
          (visitor) => visitor.Title || visitor.LoginName || "Unknown"
        ),
      };
    } catch (error) {
      console.error("Error getting site visitors:", error);
      return { count: 0, visitors: [] };
    }
  }

  async getSiteOwners(
    hubsiteUrl: string
  ): Promise<{ count: number; owners: string[] }> {
    try {
      const web = Web([this.sharepoint.web, hubsiteUrl]);
      const ownerGroup = await web.associatedOwnerGroup();
      const owners = await web.siteGroups.getById(ownerGroup.Id).users();

      return {
        count: owners.length,
        owners: owners.map(
          (owner) => owner.Title || owner.LoginName || "Unknown"
        ),
      };
    } catch (error) {
      console.error("Error getting site owners:", error);
      return { count: 0, owners: [] };
    }
  }
}
