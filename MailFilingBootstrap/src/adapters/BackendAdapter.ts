import { makeBackendCall, ApiResponse } from "../api/backendUtils";
import { Hubsite } from "../types/Hubsite";
import { getSitePath } from "../utils/SiteUtils";

export type HubsiteRightsData = {
  SitePath: string;
  UsersWithRights: Array<{
    Id: string;
    Name: string;
  }>;
};

export type SetSearchSchemaResponse = {
  IsSuccess: boolean;
  Message: string;
};

interface HubsiteResponse {
  Name: string;
  SiteUrl: string;
  DisplayName: string;
}

export class BackendAdapter {
  async deploySearchSchema(
    hubsiteUrl: string
  ): Promise<ApiResponse<SetSearchSchemaResponse>> {
    const endpoint = `/api/SetSearchSchema?hubsiteUrl=${encodeURIComponent(hubsiteUrl)}`;
    return await makeBackendCall<SetSearchSchemaResponse>(endpoint, "POST");
  }

  async getHubsiteRights(
    sitePath: string
  ): Promise<ApiResponse<HubsiteRightsData>> {
    const endpoint = `/api/GetHubsiteRights?hubsiteUrl=${encodeURIComponent(sitePath)}`;
    return await makeBackendCall<HubsiteRightsData>(endpoint, "GET");
  }

  async setHubsiteRights(
    sitePath: string
  ): Promise<ApiResponse<HubsiteRightsData>> {
    const endpoint = `/api/SetHubsiteRights?hubsiteUrl=${encodeURIComponent(sitePath)}`;
    return await makeBackendCall<HubsiteRightsData>(endpoint, "POST");
  }

  async makeGenericCall<T = any>(
    endpoint: string,
    method: "GET" | "POST" | "PUT" | "DELETE" = "GET",
    body?: any
  ): Promise<ApiResponse<T>> {
    return await makeBackendCall<T>(endpoint, method, body);
  }

  async getHubsites(): Promise<ApiResponse<Hubsite[]>> {
    try {
      const endpoint = `/api/Hubsites`;

      const response = await makeBackendCall<HubsiteResponse[]>(
        endpoint,
        "GET"
      );
      console.log("Hubsites response:", response);
      if (response && response.IsSuccess && response.Data) {
        console.log("Hubsites data:", response.Data);
        const mappedHubsites: Hubsite[] = response.Data.map((site) => ({
          displayName: site.DisplayName || "No Hubsite Name",
          name: site.Name,
          url: site.SiteUrl,
          path: getSitePath(site.SiteUrl) ?? "/",
        }));

        return {
          IsSuccess: response.IsSuccess,
          Message: response.Message,
          Data: mappedHubsites,
        };
      } else {
        return {
          IsSuccess: response.IsSuccess,
          Message: response.Message,
          Data: undefined,
        };
      }
    } catch (error) {
      return {
        IsSuccess: false,
        Message:
          "An error occurred while fetching hubsites: " +
          (error as Error).message,
        Data: undefined,
      };
    }
  }
}
