import { ISiteDesignInfo } from "@pnp/sp/site-designs/types";
import {
  List as IListEntity,
  Site as ISiteType,
  ListItem,
} from "@microsoft/microsoft-graph-types";

import "@pnp/graph/users";
import "@pnp/graph/lists";
import "@pnp/graph/list-item";
import "@pnp/graph/sites";
import "@pnp/graph/files";
import "@pnp/sp/hubsites";
import "@pnp/sp/hubsites/web";
import { IFileUploadOptions } from "@pnp/graph/files";
import * as Sentry from "@sentry/react";
import { Adapters } from "../adapters/AdapterFactory";
import { ISiteCreationResponse } from "@pnp/sp/sites/types";
import { IHubSiteInfo } from "@pnp/sp/hubsites";

export enum GroupCreated {
  Created,
  AlreadyExists,
  Failed,
}

export const createSiteDesignIfNeeded = async (
  adapters: Adapters,
  siteDesignName: string,
  sitescriptContent: string
): Promise<ISiteDesignInfo | null> => {
  const allSiteDesigns = await adapters.siteAdmin.getSiteDesigns();

  const siteDesign = allSiteDesigns.filter((s) => s.Title == siteDesignName)[0];
  try {
    if (null == siteDesign) {
      console.log(siteDesignName, "Creating missing site design - script");

      const siteScript = await adapters.siteAdmin.createSiteScript(
        siteDesignName,
        "Creates the filed email content library, for filing email into",
        sitescriptContent
      );

      console.log(siteDesignName, "Creating missing site design - design");
      const newSiteDesign: ISiteDesignInfo =
        await adapters.siteAdmin.createSiteDesign(
          [siteScript.Id],
          siteDesignName,
          "hubsite site design"
        );
      return newSiteDesign;
    } else {
      return siteDesign;
    }
  } catch (error) {
    console.error(error);
    Sentry.captureException(error);
    return null;
  }
};

export const createHubsiteIfNeeded = async (
  adapters: Adapters,
  hubsiteUrl: string,
  siteDesignId: string
): Promise<ISiteType | null> => {
  const site = await adapters.siteAdmin.getAtveroMailHubsite(hubsiteUrl);
  let siteUrl = site?.webUrl;

  if (!site) {
    try {
      const site: ISiteCreationResponse =
        await adapters.siteAdmin.createCommunicationSite(
          hubsiteUrl,
          siteDesignId
        );
      if (site?.SiteUrl) {
        siteUrl = site.SiteUrl;
      }
    } catch (error) {
      console.error("Failed to create hubsite", error);
      Sentry.captureException(error);
      return null;
    }
  }

  if (siteUrl) {
    const hubsites: IHubSiteInfo[] = await adapters.siteAdmin.getHubsites();
    if (hubsites.some((h) => h.SiteUrl == siteUrl)) {
      return site;
    } else {
      const registered = await adapters.siteAdmin.registerHubsite(siteUrl);
      if (registered) {
        return await adapters.siteAdmin.getAtveroMailHubsite(hubsiteUrl);
      } else {
        return null;
      }
    }
  }
  return null;
};

export const createSiteIfNeeded = async (
  adapters: Adapters,
  hubsiteUrl: string,
  siteDesignId: string
): Promise<ISiteType | null> => {
  const hubsite = await adapters.siteAdmin.getAtveroMailHubsite(hubsiteUrl);

  if (null == hubsite) {
    try {
      const newHubsite = await adapters.siteAdmin.createCommunicationSite(
        hubsiteUrl,
        siteDesignId
      );

      console.log("Hubsite created: ", newHubsite);

      if (2 == newHubsite.SiteStatus) {
        return await adapters.siteAdmin.getAtveroMailHubsite(hubsiteUrl);
      } else {
        console.log("Hubsite not created return: ", newHubsite);
        Sentry.captureMessage("Hubsite not created");
        return null;
      }
    } catch (error) {
      Sentry.captureException(error);
      return null;
    }
  } else {
    return hubsite;
  }
};

export const createManagementGroupIfNeeded = async (
  adapters: Adapters,
  group: string
) => {
  try {
    const groups = await adapters.siteAdmin.getGroups(group);

    if (groups.length > 0) {
      // don't create
      return GroupCreated.AlreadyExists;
    } else {
      try {
        const createdGroup = await adapters.siteAdmin.createGroup(group);

        if (createdGroup) {
          return GroupCreated.Created;
        } else {
          return GroupCreated.Failed;
        }
      } catch (error) {
        console.error("Failed to create group " + group, error);
        Sentry.captureException(error);
        return GroupCreated.Failed;
      }
    }
  } catch (error) {
    console.error("Failed to get group " + group, error);
    Sentry.captureException(error);
    return GroupCreated.Failed;
  }
};

export const createDataRowIfNeeded = async (
  adapters: Adapters,
  hubsiteUrl: string
): Promise<ListItem | null> => {
  const site = await adapters.siteAdmin.getSiteFromUrl(hubsiteUrl);

  if (!site?.Id) {
    return null;
  }

  const list: IListEntity | null = await adapters.siteAdmin.getSiteList(
    site.Id,
    "Filed Email Content"
  );
  if (!list?.id) {
    return null;
  }

  const fileOptions: IFileUploadOptions = {
    content: "This is some test content",
    filePathName: "data.eml",
    contentType: "message/rfc822;charset=utf-8",
  };

  const drives = await adapters.siteAdmin.getDrives(site.Id);
  const drive = drives.filter((d) => d.name == "Filed Email Content")[0];
  try {
    if (!drive?.id) {
      return null;
    }

    const driveItem = await adapters.siteAdmin.uploadFile(
      site.Id,
      drive.id,
      fileOptions
    );

    if (!driveItem?.id) {
      return null;
    }

    // Can't expand the list item on upload,
    // so I need to get it to update
    // the metadata
    const listItem = await adapters.siteAdmin.getListItemByDriveItem(
      site.Id,
      drive.id,
      "data.eml"
    );

    if (listItem?.id) {
      return await adapters.siteAdmin.updateListItem(
        site.Id,
        list.id,
        listItem.id,
        {
          EmailSubject: "Install test",
          EmailFrom: "Install test",
          EmailTo: "Install test",
          EmailCC: "Install test",
          EmailReceivedOn: "Install test",
          EmailTextSummary: "Install test",
          ATVMessageId: "Install test",
          ATVConversationId: "Install test",
          ATVSearchRefiner: "Email",
          EmailReceived: "2019-09-10T12:00:00Z", // Happy birthday to Atvero
          ReceivedOn: "2019-09-10T12:00:00Z",
          AttachmentCount: 1,
          EmailTags: "Install test",
          EmailImportant: "Yes",
        }
      );
    } else {
      return null;
    }
  } catch (error) {
    console.error("Failed to upload test data", error);
    Sentry.captureException(error);
    return null;
  }
};
