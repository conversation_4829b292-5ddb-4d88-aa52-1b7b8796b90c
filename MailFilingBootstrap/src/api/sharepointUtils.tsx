import React, { useState, useEffect } from "react";
import { adapterFactory, Adapters } from "../adapters/AdapterFactory";
import { useSharePointApi } from "../hooks/useSharePointAPI";

type SharePointAdapterHookProps = {
  hubsiteUrl: string;
  graph: any;
  onAdaptersCreated: (adapters: Adapters | null) => void;
};

export const SharePointAdapterHook: React.FC<SharePointAdapterHookProps> = ({
  hubsiteUrl,
  graph,
  onAdaptersCreated,
}) => {
  const { sharepoint, sharepointHostName } = useSharePointApi(hubsiteUrl);
  const [hasProcessed, setHasProcessed] = useState(false);

  useEffect(() => {
    const createAdapters = async () => {
      if (graph && sharepoint && sharepointHostName && !hasProcessed) {
        try {
          const url = new URL(hubsiteUrl);
          const sharepointHostUrl = url.origin;

          const adapters = await adapterFactory(
            graph,
            sharepoint,
            sharepointHostUrl
          );

          onAdaptersCreated(adapters);
          setHasProcessed(true);
        } catch (error) {
          console.error(`Failed to create adapters for ${hubsiteUrl}:`, error);
          onAdaptersCreated(null);
          setHasProcessed(true);
        }
      }
    };

    createAdapters();
  }, [
    graph,
    sharepoint,
    sharepointHostName,
    hubsiteUrl,
    onAdaptersCreated,
    hasProcessed,
  ]);

  return null;
};
