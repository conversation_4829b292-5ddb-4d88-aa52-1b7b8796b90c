import {
  BackendUrl,
  BackendScope,
  ClientId,
  RedirectUri,
} from "../utils/Config";
import {
  AuthenticationResult,
  EventType,
  PublicClientApplication,
} from "@azure/msal-browser";

export const msalInstance = new PublicClientApplication({
  auth: {
    clientId: ClientId(),
    authority: "https://login.microsoftonline.com/common",
    redirectUri: RedirectUri() + "/search_schema", // Ensure this matches your app's redirect URI
  },
  cache: {
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: false,
  },
});

const accounts = msalInstance.getAllAccounts();
if (accounts.length > 0) {
  msalInstance.setActiveAccount(accounts[0]);
}

msalInstance.addEventCallback((event) => {
  if (
    event.eventType === EventType.LOGIN_SUCCESS &&
    (event.payload as AuthenticationResult).account
  ) {
    const account = (event.payload as AuthenticationResult).account;
    msalInstance.setActiveAccount(account);
  }
});

export type ApiResponse<T = any> = {
  IsSuccess: boolean;
  Message: string;
  Data?: T;
};

export const getApiRequest = (endpoint: string) => {
  return {
    url: `${BackendUrl()}${endpoint}`,
    scopes: [BackendScope()],
  };
};

export async function getBearerToken() {
  const account = msalInstance.getActiveAccount();
  if (!account) {
    throw Error(
      "No active account! Verify a user has been signed in and setActiveAccount has been called."
    );
  }

  const apiRequest = getApiRequest("");

  const response = await msalInstance.acquireTokenSilent({
    ...apiRequest,
    account: account,
  });

  return response.accessToken;
}

export async function makeBackendCall<T = any>(
  endpoint: string,
  method: "GET" | "POST" | "PUT" | "DELETE" = "GET",
  body?: any
): Promise<ApiResponse<T>> {
  try {
    const token = await getBearerToken();

    const headers = new Headers();
    headers.append("Authorization", `Bearer ${token}`);

    if (body) {
      headers.append("Content-Type", "application/json");
    }

    const url = `${BackendUrl()}${endpoint}`;

    const options: RequestInit = {
      method,
      headers,
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);
    return await response.json();
  } catch (error) {
    return {
      IsSuccess: false,
      Message: `API call failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}
