import { Tenancy } from "../types/Tenancy";
import { Product } from "../utils/Config";

/**
 * Detects which tenancy the application is running in based on the hostname
 * @returns The detected tenancy: "cmap" or "atvero"
 */
export const detectTenancy = (): Tenancy => {
  switch (Product()?.toLowerCase()) {
    case "cmap":
      return Tenancy.CMap;
    case "atvero":
      return Tenancy.Atvero;
    default:
      return Tenancy.CMap; // Default to CMap for safety
  }
};

export const productName = () => {
  const tenancy = detectTenancy();
  switch (tenancy) {
    case Tenancy.CMap:
      return "CMap Mail";
    case Tenancy.Atvero:
      return "Atvero Mail";
    default:
      return "Atvero Mail";
  }
};
