import React from "react";
import { PrimaryButton } from "@fluentui/react";
import { Text } from "@fluentui/react/lib/Text";
import { detectTenancy, productName } from "../shared/TenancyService";
import { Tenancy } from "../types/Tenancy";

export const Introduction = () => {
  return (
    <div className="ms-Grid" dir="ltr">
      <div className="ms-Grid-row">
        <div className="ms-Grid-col">
          <Text variant="xxLarge">Install</Text>
          <br />
          <br />

          <Text>
            This process will install {productName()}on your SharePoint tenancy.
            <br />
            <br />
            There are three parts to the installation process:
            <ol>
              <li>
                Install {productName()}, this process requires consent to the
                {" " + productName()} application as a Global Admin in Microsoft
                365.
                <br />
                <br />
                <PrimaryButton href="/consents">
                  Install {productName()}
                </PrimaryButton>
                <br />
                <br />
              </li>
              <li>
                Install {productName()} Outlook Plugin, this needs to be
                completed by a Microsoft Global Admin
                <br /> <br />
                <PrimaryButton
                  href={
                    detectTenancy() == Tenancy.CMap
                      ? "https://filing.cmapmail.com/cmapmail.html"
                      : "https://filing.atveromail.com"
                  }
                >
                  Install {productName()} Plugin
                </PrimaryButton>
                <br />
                <br />
              </li>
              <li>
                Wait 24 hours for the {productName()} initial search to be
                completed
                <br />
                <br />
              </li>
              <li>
                Install {productName()} Search
                <br /> <br />
                <PrimaryButton href="/search_schema">
                  Install {productName()} Search
                </PrimaryButton>
                <br />
                <br />
              </li>
            </ol>
          </Text>
        </div>
      </div>
    </div>
  );
};
