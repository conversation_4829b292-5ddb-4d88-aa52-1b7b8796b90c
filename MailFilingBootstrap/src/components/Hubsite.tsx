import React, { useState } from "react";

import { Text } from "@fluentui/react/lib/Text";
import { Input, Label, makeStyles } from "@fluentui/react-components";
import { PrimaryButton } from "@fluentui/react";
import { productName } from "../shared/TenancyService";
import { getDefaultHubsitePath } from "../shared/HubsiteService";

export const Hubsite = () => {
  const styles = useStyles();
  const [hubsiteName, setHubsiteName] = useState(
    "https://yourcompany.sharepoint.com" + getDefaultHubsitePath()
  );
  const handleHubsiteNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHubsiteName(e.target.value);
  };
  return (
    <>
      <Text variant="xxLarge">Management Hub</Text>
      <p>
        <Text>
          Here we create the managment hub for {productName()}. This is location
          for all shared information in {productName()}
        </Text>
      </p>
      <p>
        <Text>
          <strong>To proceed you will need to be a SharePoint Admin.</strong>
          <br />
          <br />
          Pressing the button below will ask you to login with your SharePoint
          Admin account and then create the management hubsite.
        </Text>
      </p>
      <div>
        <p>
          <Text>
            Projects reference a central site where {productName()} manages
            users, licenses, and settings.
            <br /> If your organisation has a specific scheme for naming sites
            then you can set your own name below.
            <br />
            <br /> The default is <strong>{getDefaultHubsitePath()}</strong>
            <br />
            This will create the management site in your SharePoint tenant.
            <br />
            <br />
            <b>Please provide the full URL of the site</b>
            <br />
            <br />
            {hubsiteName}
          </Text>
        </p>
        <div>
          <Label size="large" htmlFor="hubiste-name">
            Management Site Path - full URL
          </Label>
          <br />
          <Input
            style={{ width: "500px" }}
            placeholder="Managment Site Name"
            value={hubsiteName}
            onChange={handleHubsiteNameChange}
            id="hubsite-name"
          />
        </div>
        <PrimaryButton
          className={styles.createButton}
          data-testid="atvermail-create-hubsite-button"
          href={`/register_hubsite?hubsiteUrl=${hubsiteName}`}
        >
          Create Site
        </PrimaryButton>
      </div>
    </>
  );
};
const useStyles = makeStyles({
  createButton: {
    marginTop: "20px",
  },
});
