import React, { useEffect, useState } from "react";
import { SetupHubsite } from "./SetupHubsite";
import { LoginHelper } from "./LoginHelper";
import { adapterFactory, Adapters } from "../adapters/AdapterFactory";
import { useGraph } from "../hooks/useGraphAPI";
import { useSharePointApi } from "../hooks/useSharePointAPI";
import { Configure } from "./Configure";
import { useRouter } from "@tanstack/react-router";

type IAuthProps = {
  hubsiteUrl: string;
};

export const Auth = ({ hubsiteUrl }: IAuthProps) => {
  const [adapters, setAdapters] = useState<Adapters | undefined>(undefined);
  const graph = useGraph();

  // this one we want to point at the top level

  const url = new URL(hubsiteUrl);
  const sharepointHostUrl = url.origin;

  const { sharepoint, sharepointHostName } =
    useSharePointApi(sharepointHostUrl);
  const router = useRouter();

  const createAdapters = async () => {
    try {
      if (graph && sharepoint && sharepointHostName) {
        const adapters = await adapterFactory(
          graph,
          sharepoint,
          sharepointHostUrl
        );
        if (adapters) {
          setAdapters(adapters);
        } else {
          console.log("Adapters are null");
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    createAdapters();
  }, [graph, sharepoint, sharepointHostName]);

  if (!adapters) {
    return <LoginHelper />;
  }

  const currentPath = router.state.location.pathname;
  if (currentPath === "/configure") {
    return <Configure adapters={adapters} hubsiteUrl={hubsiteUrl} />;
  }
  // Default to the hub site setup page
  return <SetupHubsite hubsiteUrl={hubsiteUrl} adapters={adapters} />;
};
