import React, { useEffect } from "react";

import { Site as ISiteType, ListItem } from "@microsoft/microsoft-graph-types";

import { Text } from "@fluentui/react/lib/Text";
import "@pnp/sp/sites";

import "@pnp/sp/site-scripts";
import "@pnp/sp/site-designs";
import { IStackProps, PrimaryButton, Stack } from "@fluentui/react";
import { Task, TaskStatus, CreatingStatus } from "./InstalledStatus";
import {
  createHubsiteIfNeeded,
  createSiteDesignIfNeeded,
  createDataRowIfNeeded,
  GroupCreated,
  createManagementGroupIfNeeded,
} from "../api/setupUtils";
import {
  hubsiteSiteScript,
  projectSiteScript,
} from "../siteDesigns/FiledEmailContent";

import { errorMessage } from "../utils/ExceptionHelper";

import { Adapters } from "../adapters/AdapterFactory";
type ISetupHubsiteProps = {
  adapters: Adapters;
  hubsiteUrl: string;
};
import * as Sentry from "@sentry/react";
import { detectTenancy, productName } from "../shared/TenancyService";
import { Tenancy } from "../types/Tenancy";

export const projectSiteDesignName = () => {
  // Cover a typo in the Atvero Mail project site design name
  if (Tenancy.Atvero == detectTenancy()) {
    return "AtveroMail - Project";
  } else {
    return `${productName()} - Project`;
  }
};
export const hubsiteSiteDesignName = () => {
  // Cover a typo in the Atvero Mail project site design name
  if (Tenancy.Atvero == detectTenancy()) {
    return "AtveroMail - Hubsite";
  } else {
    return `${productName()} - Hubsite`;
  }
};
export const applySiteDesignEffect = async (
  adapters: Adapters,
  hubsite: ISiteType | undefined,
  siteDesignId: string | undefined,
  siteDesignApplied: Task,
  setSiteDesignApplied: React.Dispatch<React.SetStateAction<Task>>
): Promise<boolean> => {
  if (hubsite?.webUrl && siteDesignId) {
    try {
      setSiteDesignApplied({
        ...siteDesignApplied,
        status: TaskStatus.Creating,
      });
      await adapters.siteAdmin.applySiteDesign(hubsite.webUrl, siteDesignId);
      setSiteDesignApplied({
        ...siteDesignApplied,
        status: TaskStatus.Created,
      });
      return true;
    } catch (error) {
      setSiteDesignApplied({
        status: TaskStatus.Failed,
        error: errorMessage(error, "Problem applying site design"),
      });
      return false;
    }
  } else {
    return false;
  }
};

export const SetupHubsite = ({ hubsiteUrl, adapters }: ISetupHubsiteProps) => {
  const [installing, setInstalling] = React.useState<boolean>(false);
  const [hubsiteCreated, setHubsiteCreated] = React.useState<Task>({
    status: TaskStatus.Waiting,
    error: undefined,
  });
  const [siteHubsiteSiteDesignCreated, setHubsiteSiteDesignCreated] =
    React.useState<Task>({
      status: TaskStatus.Waiting,
      error: undefined,
    });
  const [siteProjectSiteDesignCreated, setProjectSiteDesignCreated] =
    React.useState<Task>({
      status: TaskStatus.Waiting,
      error: undefined,
    });
  const [siteDesignApplied, setSiteDesignApplied] = React.useState<Task>({
    status: TaskStatus.Waiting,
    error: undefined,
  });
  const [createManagementGroups, setCreateManagementGroups] =
    React.useState<Task>({
      status: TaskStatus.Waiting,
      error: undefined,
    });
  const [addDataRow, setAddDataRow] = React.useState<Task>({
    status: TaskStatus.Waiting,
    error: undefined,
  });
  const [assignGroupPerms, setAssignGroupPerms] = React.useState<Task>({
    status: TaskStatus.Waiting,
    error: undefined,
  });

  const createGroups = async (): Promise<boolean> => {
    try {
      setCreateManagementGroups({
        ...createManagementGroups,
        status: TaskStatus.Creating,
      });

      let created = await createManagementGroupIfNeeded(
        adapters,
        productName() + " All Users"
      );
      if (GroupCreated.Failed != created) {
        created = await createManagementGroupIfNeeded(
          adapters,
          productName() + " All Confidential Users"
        );
        if (GroupCreated.Failed != created) {
          created = await createManagementGroupIfNeeded(
            adapters,
            productName() + " Admins"
          );
        }
      }

      if (GroupCreated.Failed === created) {
        setCreateManagementGroups({
          status: TaskStatus.Failed,
          error: errorMessage(undefined, "Creating management groups failed"),
        });
        return false;
      } else {
        setCreateManagementGroups({
          ...createManagementGroups,
          status: TaskStatus.Created,
        });
        return true;
      }
    } catch (error) {
      setCreateManagementGroups({
        status: TaskStatus.Failed,
        error: errorMessage(error, "Problem creating management groups"),
      });
      return false;
    }
  };

  const createHubsiteSiteDesign = async (): Promise<string | undefined> => {
    if (siteHubsiteSiteDesignCreated.status == TaskStatus.Creating) {
      return undefined;
    }
    try {
      setHubsiteSiteDesignCreated({
        ...siteHubsiteSiteDesignCreated,
        status: TaskStatus.Creating,
      });

      const siteDesign = await createSiteDesignIfNeeded(
        adapters,
        hubsiteSiteDesignName(),
        JSON.parse(hubsiteSiteScript())
      );

      if (siteDesign) {
        setHubsiteSiteDesignCreated({
          ...siteHubsiteSiteDesignCreated,
          status: TaskStatus.Created,
        });

        return siteDesign.Id;
      } else {
        Sentry.captureMessage("Site design was not created");
        return undefined;
      }
    } catch (error) {
      setHubsiteSiteDesignCreated({
        error: errorMessage(error, "Problem creating hubsite site design"),
        status: TaskStatus.Failed,
      });
      return undefined;
    }
  };

  const createProjectSiteDesign = async (): Promise<boolean> => {
    try {
      setProjectSiteDesignCreated({
        ...siteProjectSiteDesignCreated,
        status: TaskStatus.Creating,
      });
      const rawProjectJson = projectSiteScript();
      if (rawProjectJson) {
        await createSiteDesignIfNeeded(
          adapters,
          projectSiteDesignName(),
          JSON.parse(rawProjectJson)
        );
      }
      setProjectSiteDesignCreated({
        ...siteProjectSiteDesignCreated,
        status: TaskStatus.Created,
      });
      return true;
    } catch (error) {
      setProjectSiteDesignCreated({
        error: errorMessage(error, "Problem creating project site design"),
        status: TaskStatus.Failed,
      });
      return false;
    }
  };

  const createHubsiteEffect = async (
    siteDesignId: string | undefined
  ): Promise<ISiteType | undefined> => {
    if (hubsiteCreated.status == TaskStatus.Creating) {
      console.log("Already creating hubsite");
      return undefined;
    }
    if (siteDesignId) {
      setHubsiteCreated({
        ...hubsiteCreated,
        status: TaskStatus.Creating,
      });
      try {
        const hubsite = await createHubsiteIfNeeded(
          adapters,
          hubsiteUrl,
          siteDesignId
        );
        if (hubsite) {
          setHubsiteCreated({
            ...hubsiteCreated,
            status: TaskStatus.Created,
          });
          return hubsite;
        } else {
          console.log("Hubsite not created");
          setHubsiteCreated({
            ...hubsiteCreated,
            error: errorMessage(undefined, "Hubsite was not created"),
            status: TaskStatus.Failed,
          });

          return undefined;
        }
      } catch (error) {
        setHubsiteCreated({
          error: errorMessage(error, "Problem creating hubsite"),
          status: TaskStatus.Failed,
        });

        return undefined;
      }
    } else {
      return undefined;
    }
  };

  const createDataRow = async (
    hubsite: ISiteType | undefined
  ): Promise<boolean> => {
    if (hubsite?.webUrl) {
      setAddDataRow({ ...addDataRow, status: TaskStatus.Creating });
      try {
        const dataRowItem: ListItem | null = await createDataRowIfNeeded(
          adapters,
          hubsite.webUrl
        );
        if (dataRowItem) {
          setAddDataRow({ ...addDataRow, status: TaskStatus.Created });
          return true;
        } else {
          setAddDataRow({ ...addDataRow, status: TaskStatus.Failed });
          return false;
        }
      } catch (error) {
        setAddDataRow({
          error: errorMessage(error, "Problem creating data row"),
          status: TaskStatus.Failed,
        });
        return false;
      }
    } else {
      return false;
    }
  };

  const assignGroupPermissions = async (
    hubsite: ISiteType
  ): Promise<boolean> => {
    if (!hubsite?.webUrl) {
      setAssignGroupPerms({
        error: errorMessage(
          undefined,
          "Problem Assigning Group Permissions: The Hubsites Web URL is missing."
        ),
        status: TaskStatus.Failed,
      });
      return false;
    }

    try {
      setAssignGroupPerms({
        ...assignGroupPerms,
        status: TaskStatus.Creating,
      });
      await adapters.siteAdmin.assignGroupPermissions(hubsite);
      setAssignGroupPerms({
        ...assignGroupPerms,
        status: TaskStatus.Created,
      });
      return true;
    } catch (error) {
      setAssignGroupPerms({
        status: TaskStatus.Failed,
        error: errorMessage(
          error,
          "Problem Assigning Group Permissions: A call within assignGroupPermissions failed."
        ),
      });
      return false;
    }
  };

  useEffect(() => {
    const installMail = async () => {
      if (!installing) {
        setInstalling(true);
        console.log("Installing " + productName());
        let success = await createGroups();
        if (!success) {
          return;
        }
        const siteDesignId = await createHubsiteSiteDesign();
        if (!siteDesignId) {
          return;
        }
        success = await createProjectSiteDesign();
        if (!success) {
          return;
        }
        const hubsite = await createHubsiteEffect(siteDesignId);
        if (!hubsite) {
          return;
        }
        success = await applySiteDesignEffect(
          adapters,
          hubsite,
          siteDesignId,
          siteDesignApplied,
          setSiteDesignApplied
        );
        if (!success) {
          return;
        }
        success = await createDataRow(hubsite);
        if (!success) {
          return;
        }
        console.log("failed to assign group perms");
        success = await assignGroupPermissions(hubsite);
      }
    };

    if (!installing) {
      installMail();
    }
  }, []);

  // This is just for laying out the label and spinner (spinners don't have to be inside a Stack)
  const rowProps: IStackProps = { horizontal: true, verticalAlign: "center" };

  const tokens = {
    sectionStack: {
      childrenGap: 10,
    },
    spinnerStack: {
      childrenGap: 20,
    },
  };

  return (
    <div className="ms-Grid" dir="ltr" data-testid="setup-hubsite">
      <div className="ms-Grid-row">
        <div className="ms-Grid-col">
          <Text variant="xxLarge">Creating Management Hub</Text>
          <br />
          <br />
          <Stack tokens={tokens.sectionStack}>
            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus
                task={createManagementGroups}
                thing="Management Groups"
              />
            </Stack>
            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus
                task={siteHubsiteSiteDesignCreated}
                thing="Create Hubsite Site Design"
              />
            </Stack>{" "}
            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus
                task={siteProjectSiteDesignCreated}
                thing="Create Project Site Design"
              />
            </Stack>
            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus task={hubsiteCreated} thing="Hubsite" />
            </Stack>
            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus
                task={siteDesignApplied}
                thing="Apply Site Design"
              />
            </Stack>
            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus task={addDataRow} thing="Testing Site Design" />
            </Stack>
            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus
                task={assignGroupPerms}
                thing="Assigning Group Permissions"
              />
            </Stack>
            {TaskStatus.Created == addDataRow.status &&
            TaskStatus.Created == siteDesignApplied.status &&
            TaskStatus.Created == hubsiteCreated.status &&
            TaskStatus.Created == assignGroupPerms.status &&
            TaskStatus.Created == siteHubsiteSiteDesignCreated.status &&
            TaskStatus.Created == siteProjectSiteDesignCreated.status &&
            TaskStatus.Created == createManagementGroups.status ? (
              <>
                <p>
                  <Text>{productName()} install completed!</Text>
                </p>

                <p>
                  <strong>Next Steps:</strong>

                  <br />
                  <br />
                  <PrimaryButton href="/">
                    Start Outlook Plugin Install
                  </PrimaryButton>
                </p>
              </>
            ) : null}
          </Stack>
        </div>
      </div>
    </div>
  );
};
