import React, { useState, useEffect, useCallback } from "react";
import {
  Text,
  Spinner,
  SpinnerSize,
  MessageBar,
  MessageBarType,
  PrimaryButton,
  Stack,
} from "@fluentui/react";
import {
  makeStyles,
  shorthands,
  Combobox,
  Option,
  Persona,
} from "@fluentui/react-components";
import { PersonRegular } from "@fluentui/react-icons";
import { debounce } from "lodash";
import { Adapters } from "../adapters/AdapterFactory";
import { User } from "../types/User";
import {
  addUsersToGroup,
  formatUserAdditionErrorMessage,
  formatUserAdditionStatusMessage,
  processUserAdditionResults,
} from "../utils/UserManagementHelpers";
import { SelectedUsersList } from "./SelectedUsersList";

interface IUser {
  id: string;
  name: string;
  email: string;
  principalName: string;
}

interface Descriptions {
  flair: string;
  details: string;
}

interface IConfigureUserGroupsProps {
  adapters: Adapters;
  disabled: boolean;
  groupName: string;
  descriptions?: Descriptions;
  // This prop is only for testing purposes
  selectedUsers?: IUser[];
}

export const ConfigureUserGroups: React.FC<IConfigureUserGroupsProps> = ({
  adapters,
  disabled,
  groupName,
  descriptions = {
    flair: "Add users to the security group to grant them access.",
    details: "",
  },
  selectedUsers: initialSelectedUsers,
}) => {
  const styles = useStyles();
  const [selectedUsers, setSelectedUsers] = useState<IUser[]>(
    initialSelectedUsers || []
  );
  const [searchResults, setSearchResults] = useState<IUser[]>([]);
  const [currentGroupUsers, setCurrentGroupUsers] = useState<
    User[] | undefined
  >([]);

  const [inputValue, setInputValue] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isAddingUsers, setIsAddingUsers] = useState(false);
  const [status, setStatus] = useState<{
    type: MessageBarType | null;
    message: string;
  }>({
    type: null,
    message: "",
  });

  // Update selectedUsers if initialSelectedUsers changes (for testing)
  useEffect(() => {
    if (initialSelectedUsers) {
      setSelectedUsers(initialSelectedUsers);
    }
  }, [initialSelectedUsers]);

  useEffect(() => {
    if (disabled) {
      setStatus({ type: null, message: "" });
      if (!initialSelectedUsers) {
        setSelectedUsers([]);
      }
      setInputValue("");
      setSearchResults([]);
      setCurrentGroupUsers([]);
    } else {
      handleFetchGroupUsers();
    }
  }, [disabled, groupName]);

  const debouncedSearch = useCallback(
    debounce(async (term: string) => {
      if (!term || term.length < 3 || !adapters) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        const results = await adapters.siteAdmin.searchUsers(term);

        const filteredResults = results.filter(
          (user) => !selectedUsers.some((selected) => selected.id === user.id)
        );

        setSearchResults(filteredResults);
      } catch (error) {
        console.error("Error searching for users:", error);
        setStatus({
          type: MessageBarType.error,
          message: "Failed to search for users. Please try again.",
        });
      } finally {
        setIsSearching(false);
      }
    }, 300),
    [adapters, selectedUsers]
  );

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);

    if (newValue.length >= 3) {
      debouncedSearch(newValue);
    } else {
      setSearchResults([]);
    }
  };

  const handleOptionSelect = (
    _event: React.SyntheticEvent,
    data: { optionValue?: string }
  ) => {
    if (!data.optionValue) return;

    const selectedUser = searchResults.find(
      (user) => user.id === data.optionValue
    );
    if (!selectedUser) return;

    // Add user to selected users
    setSelectedUsers((prev) => [...prev, selectedUser]);

    // Clear search
    setInputValue("");
    setSearchResults([]);
  };

  const handleFetchGroupUsers = async () => {
    try {
      const results = await adapters.siteAdmin.getUsersFromGroup(groupName);

      if (results === undefined) {
        setStatus({
          type: MessageBarType.error,
          message: `Failed to fetch current users from ${groupName}. Please try again.`,
        });
      }

      setCurrentGroupUsers(results);
    } catch (error) {
      console.error(`Error fetching users from ${groupName}:`, error);
      setStatus({
        type: MessageBarType.error,
        message: `Failed to fetch current users from ${groupName}. Please try again.`,
      });
    }
  };

  const handleRemoveGroupUser = async (userId: string) => {
    try {
      const userToRemove = currentGroupUsers?.find(
        (user) => user.id === userId
      );
      if (!userToRemove) return;

      await adapters.siteAdmin.removeUsersFromGroupById(
        groupName,
        userToRemove.id
      );
    } catch (error) {
      console.error(`Error removing selected user from ${groupName}:`, error);
      setStatus({
        type: MessageBarType.error,
        message: `Failed remove the selected user from ${groupName}. Please try again.`,
      });
    } finally {
      handleFetchGroupUsers();
    }
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUsers((users) => users.filter((user) => user.id !== userId));
  };

  const handleAddUsersToGroup = async () => {
    if (selectedUsers.length === 0) {
      setStatus({
        type: MessageBarType.warning,
        message: "Please select at least one user to add to the group.",
      });
      return;
    }

    setIsAddingUsers(true);
    setStatus({ type: null, message: "" });

    try {
      // Prepare data for the API call
      const userEmails = selectedUsers.map((user) => user.email);

      // Make the API call
      const result = await addUsersToGroup(
        adapters.siteAdmin,
        groupName,
        userEmails
      );

      // Process the results using our pure function
      const processedResults = processUserAdditionResults(
        selectedUsers,
        result.results,
        groupName
      );

      // Update UI based on processed results
      setSelectedUsers((prev) =>
        prev.filter((user) => !processedResults.usersToRemove.includes(user))
      );

      // Display appropriate messages
      if (
        processedResults.successfulUsers.length > 0 ||
        processedResults.alreadyInGroup.length > 0
      ) {
        setStatus({
          type: MessageBarType.success,
          message: formatUserAdditionStatusMessage(processedResults),
        });
      }

      if (processedResults.failedUsers.length > 0) {
        setStatus({
          type: MessageBarType.error,
          message: formatUserAdditionErrorMessage(
            processedResults.failedUsers,
            processedResults.groupName
          ),
        });
      }
    } catch (error) {
      console.error(`Error adding users to group ${groupName}:`, error);
      setStatus({
        type: MessageBarType.error,
        message: `We ran into an issue adding users to ${groupName}. Error: ${
          error instanceof Error
            ? error.message.includes("permission")
              ? "Insufficient permissions"
              : error.message.includes("network")
                ? "Network connection error"
                : "System error - please try again"
            : "Unexpected error occurred"
        }`,
      });
    } finally {
      setIsAddingUsers(false);
      handleFetchGroupUsers();
    }
  };

  return (
    <div className={styles.container}>
      <Text>{descriptions.flair.replace("{groupName}", groupName)}</Text>

      <Text>{descriptions.details.replace("{groupName}", groupName)}</Text>
      <div className={styles.searchContainer}>
        <Combobox
          placeholder="Start typing a name or email (minimum 3 characters)"
          onChange={handleInputChange}
          onOptionSelect={handleOptionSelect}
          value={inputValue}
          disabled={disabled ?? isAddingUsers}
          className={styles.combobox}
          appearance="outline"
          freeform={true}
          data-testid="add-user-combobox"
        >
          {isSearching ? (
            <Option text="Searching..." disabled>
              <div className={styles.searchingOption}>
                <Spinner size={SpinnerSize.small} />
                <span>Searching...</span>
              </div>
            </Option>
          ) : searchResults.length > 0 ? (
            searchResults.map((user) => (
              <Option key={user.id} value={user.id} text={user.name}>
                <Persona
                  name={user.name}
                  primaryText={user.name}
                  secondaryText={user.email}
                  size="medium"
                  textAlignment="center"
                  avatar={{
                    icon: <PersonRegular />,
                    color: "colorful",
                  }}
                />
              </Option>
            ))
          ) : inputValue.length >= 3 ? (
            <Option disabled text="No users found">
              No users found
            </Option>
          ) : null}
        </Combobox>
      </div>

      <SelectedUsersList
        users={selectedUsers}
        onRemoveUser={handleRemoveUser}
        disabled={disabled ?? false}
        isProcessing={isAddingUsers}
        title="Selected Users"
      />

      <Stack
        horizontal
        tokens={{ childrenGap: 10 }}
        className={styles.actionButtons}
      >
        <PrimaryButton
          text={isAddingUsers ? "Adding Users..." : "Add Users"}
          onClick={handleAddUsersToGroup}
          disabled={disabled || isAddingUsers || selectedUsers.length === 0}
          iconProps={{ iconName: "PersonAdd" }}
        >
          {isAddingUsers && (
            <Spinner size={SpinnerSize.small} style={{ marginRight: 8 }} />
          )}
        </PrimaryButton>
      </Stack>

      {status.type !== null && (
        <MessageBar
          messageBarType={status.type}
          isMultiline
          onDismiss={() => setStatus({ type: null, message: "" })}
          className={styles.statusMessage}
        >
          {status.message}
        </MessageBar>
      )}

      {currentGroupUsers && currentGroupUsers.length > 0 && (
        <SelectedUsersList
          users={currentGroupUsers.map((user) => ({
            id: user.id,
            name: user.displayName,
            email: user.email,
          }))}
          onRemoveUser={handleRemoveGroupUser}
          disabled={disabled ?? false}
          isProcessing={isAddingUsers}
          title={`Users in ${groupName}`}
        />
      )}
    </div>
  );
};

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    ...shorthands.gap("16px"),
  },
  searchContainer: {
    marginTop: "8px",
    maxWidth: "626px",
    position: "relative",
  },
  combobox: {
    width: "100%",
  },
  searchingOption: {
    display: "flex",
    alignItems: "center",
    ...shorthands.gap("8px"),
  },
  statusMessage: {
    marginTop: "8px",
    maxWidth: "626px",
  },
  actionButtons: {
    marginTop: "16px",
  },
});
