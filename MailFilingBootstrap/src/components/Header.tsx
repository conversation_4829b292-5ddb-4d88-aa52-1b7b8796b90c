import React from "react";
import AtveroLogo from "../../assets/AtveroMail.svg";
import CMapMailLogo from "../../assets/CMap PIM & CMap Mail/CMap Mail/Black_Text/SVG/CMap_Mail_black_text_svg.svg";
import { makeStyles } from "@fluentui/react-components";
import { detectTenancy, productName } from "../shared/TenancyService";
import { Tenancy } from "../types/Tenancy";

export const Header = () => {
  const classes = useStyles();
  switch (detectTenancy()) {
    case Tenancy.Atvero:
      return (
        <img src={AtveroLogo} alt={productName()} className={classes.logo} />
      );

    case Tenancy.CMap:
    default:
      return (
        <img src={CMapMailLogo} alt={productName()} className={classes.logo} />
      );
  }
};
const useStyles = makeStyles({
  logo: {
    width: "200px",
  },
});
