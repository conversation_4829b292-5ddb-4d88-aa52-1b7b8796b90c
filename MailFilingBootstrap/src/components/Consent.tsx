import React from "react";
import { ClientId, RedirectUri } from "../utils/Config";
import { PrimaryButton } from "@fluentui/react";
import { Text } from "@fluentui/react/lib/Text";
import { productName } from "../shared/TenancyService";

type ConsentSearchParams = {
  admin_consent: boolean | undefined;
  tenant: string | undefined;
};

export const Consent = ({ admin_consent, tenant }: ConsentSearchParams) => {
  if (!admin_consent && !tenant) {
    const consent = `https://login.microsoftonline.com/common/adminconsent?client_id=${ClientId()}&redirect_uri=${RedirectUri()}/consents&state=12345`;

    return (
      <div className="ms-Grid" dir="ltr">
        <div className="ms-Grid-row">
          <div className="ms-Grid-col">
            <Text variant="xxLarge">
              Consent to the {productName()} App Registration
            </Text>
            <br />
            <br />
            <p>
              <Text>
                This process will:
                <ol>
                  <li>
                    Allow you to consent to the {productName()} application,
                    allowing its use in your SharePoint Tenant
                  </li>
                  <li>
                    Create a managment hubsite in SharePoint for {productName()}
                  </li>
                  <li>
                    Add SharePoint groups for user managment in {productName()}
                  </li>
                  <li>Apply the {productName()} search schema</li>
                </ol>
              </Text>
            </p>
            <p>
              <Text>
                By clicking the button below, you will be taken to the Microsoft
                login page to consent to the {productName()} application.
              </Text>
            </p>
            <p>
              <Text>
                The application asks for only <strong>Delegate</strong> level
                permissions, this means that access is only granted to the
                resources if the user themselves have access to that resource
              </Text>
            </p>

            <p>
              <Text>
                <strong>
                  You will need to login as a Global Admin to consent to the
                  application
                </strong>
              </Text>
            </p>

            <PrimaryButton
              data-testid="atvermail-consent-button"
              href={consent}
            >
              Consent to {productName()}
            </PrimaryButton>
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="ms-Grid" dir="ltr">
        <div className="ms-Grid-row">
          <div className="ms-Grid-col">
            <p>
              <Text variant="xxLarge">
                {productName()} registered successfully
              </Text>
            </p>
            <p>
              <Text>
                The next step of the process will create the management hubsite.
              </Text>
            </p>{" "}
            <PrimaryButton
              data-testid="atvermail-hubsite-button"
              href={`/hubsite`}
            >
              Create Management Site
            </PrimaryButton>
          </div>
        </div>
      </div>
    );
  }
};
