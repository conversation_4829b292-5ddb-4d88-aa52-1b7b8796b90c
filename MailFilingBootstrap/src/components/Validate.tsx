import React, { useState, useCallback, useRef } from "react";
import { makeStyles } from "@fluentui/react-components";
import { BackendAdapter } from "../adapters/BackendAdapter";
import { useGraph } from "../hooks/useGraphAPI";
import { Adapters } from "../adapters/AdapterFactory";
import { SharePointAdapterHook } from "../api/sharepointUtils";
import {
  DataGrid,
  DataGridBody,
  DataGridRow,
  DataGridCell,
  DataGridHeader,
  DataGridHeaderCell,
  TableCellLayout,
  TableColumnDefinition,
  createTableColumn,
  Spinner,
  Button,
  Accordion,
  AccordionHeader,
  AccordionItem,
  AccordionPanel,
  AvatarGroup,
  AvatarGroupItem,
  AvatarGroupPopover,
  partitionAvatarGroupItems,
  Tooltip,
} from "@fluentui/react-components";
import {
  CheckmarkCircle24Filled,
  ErrorCircle24Filled,
  Info24Filled,
} from "@fluentui/react-icons";
import { Hubsite } from "../types/Hubsite";

type ValidationStatus = "success" | "error" | "info";

type ValidationTableItem = ValidationItem & {
  hubsiteName: string;
  isProcessing?: boolean;
};

type ValidationItem = {
  name: string;
  status: ValidationStatus;
  details: string;
  message?: string;
  users?: string[];
  count?: number;
};

type HubsiteValidation = {
  hubsiteUrl: string;
  hubsiteName: string;
  validations: ValidationItem[];
  isComplete: boolean;
};

export const Validate = () => {
  const styles = useStyles();
  const graph = useGraph();
  const [backendAdapter] = useState(new BackendAdapter());
  const [hubsiteValidations, setHubsiteValidations] = useState<
    HubsiteValidation[]
  >([]);
  const [currentProcessingHubsite, setCurrentProcessingHubsite] = useState<
    string | null
  >(null);
  const [isValidating, setIsValidating] = useState(false);
  const [currentValidationStep, setCurrentValidationStep] = useState<
    string | null
  >(null);
  const adaptersRef = useRef<Adapters | null>(null);

  const getCMapMailHubsites = async (): Promise<Hubsite[] | undefined> => {
    const hubsites = await backendAdapter.getHubsites();
    return hubsites.Data;
  };

  const handleAdaptersCreated = useCallback((adapters: Adapters | null) => {
    adaptersRef.current = adapters;
  }, []);

  const [securityGroupValidations, setSecurityGroupValidations] = useState<
    ValidationItem[]
  >([]);

  const validateHubsiteSequentially = async (
    hubsiteUrl: string,
    adapters: Adapters
  ): Promise<HubsiteValidation> => {
    const validations: ValidationItem[] = [];
    let hubsiteName = "Communications Hub Site";

    try {
      // Step 1: Validate Hub Site
      setCurrentValidationStep("Hub Site created");
      const hubsite = await adapters.siteAdmin.getAtveroMailHubsite(hubsiteUrl);
      hubsiteName = hubsite?.displayName || "Communications Hub Site";

      if (hubsite) {
        validations.push({
          name: "Hub site created",
          status: "success",
          details: hubsiteName,
        });
      } else {
        validations.push({
          name: "Hub site created",
          status: "error",
          details: "The hub site could not be found or accessed.",
          message:
            "Please ensure it exists and that you have permission to access it. If the issue persists, contact Support.",
        });
      }

      // Step 2: Validate Site Visitors
      setCurrentValidationStep(`Site visitors has users`);
      const visitorsResult =
        await adapters.siteAdmin.getSiteVisitors(hubsiteUrl);

      if (visitorsResult.count === 0) {
        validations.push({
          name: "Site visitors has users",
          status: "error",
          details:
            "0 visitors found. The SharePoint 'Visitors' group must contain at least one user.",
          message: "Contact Support",
          count: 0,
        });
      } else {
        validations.push({
          name: "Site visitors has users",
          status: "success",
          details: ``,
          users: visitorsResult.visitors,
          count: visitorsResult.count,
        });
      }

      // Step 3: Validate Site Owners
      setCurrentValidationStep(`Site owners has users`);
      const ownersResult = await adapters.siteAdmin.getSiteOwners(hubsiteUrl);

      if (ownersResult.count === 0) {
        validations.push({
          name: "Site owners has users",
          status: "error",
          details:
            "0 owners found. The SharePoint 'Owners' group must contain at least one user.",
          message: "Contact Support",
          count: 0,
        });
      } else {
        validations.push({
          name: "Site owners has users",
          status: "success",
          details: ``,
          users: ownersResult.owners,
          count: ownersResult.count,
        });
      }

      // Step 4: Validate Hubsite Rights
      setCurrentValidationStep(`Hub site rights check`);
      const rightsResult = await backendAdapter.getHubsiteRights(hubsiteUrl);

      if (rightsResult.IsSuccess && rightsResult.Data) {
        const userNames = rightsResult.Data.UsersWithRights.map(
          (user) => user.Name
        );
        validations.push({
          name: "Hub site rights check",
          status: "success",
          details: ``,
          users: userNames,
          count: rightsResult.Data.UsersWithRights.length,
        });
      } else {
        validations.push({
          name: `Hub site rights check`,
          status: "error",
          details: "Failed to retrieve hub site rights.",
          message: rightsResult.Message || "Contact Support",
          count: 0,
        });
      }

      return {
        hubsiteUrl,
        hubsiteName,
        validations,
        isComplete: true,
      };
    } catch (error) {
      console.error("Error validating hub site:", error);
      return {
        hubsiteUrl,
        hubsiteName,
        validations: [
          {
            name: "Hub site validation",
            status: "error",
            details: "Failed to validate hub site",
            message: "Contact Support",
          },
        ],
        isComplete: true,
      };
    }
  };

  const getHubsiteStatus = (
    validations: ValidationItem[]
  ): ValidationStatus => {
    const hasError = validations.some((v) => v.status === "error");
    const hasInfo = validations.some((v) => v.status === "info");

    if (hasError) return "error";
    if (hasInfo) return "info";
    return "success";
  };

  const startValidation = async () => {
    setIsValidating(true);
    setHubsiteValidations([]);
    setSecurityGroupValidations([]);
    setCurrentValidationStep(null);

    const hubsites = await getCMapMailHubsites();

    if (!hubsites) {
      console.error("No hubsites found");
      setIsValidating(false);
      return;
    }

    // Validate security groups once (shared across all hubsites)
    if (hubsites.length > 0) {
      setCurrentProcessingHubsite(hubsites[0].url);
      adaptersRef.current = null;

      const adapters = await new Promise<Adapters | null>((resolve) => {
        const checkAdapters = setInterval(() => {
          if (adaptersRef.current !== null) {
            clearInterval(checkAdapters);
            resolve(adaptersRef.current);
          }
        }, 100);
      });

      if (adapters) {
        await validateSecurityGroups(adapters);
      }
    }

    // Then validate each hubsite individually
    for (const hubsite of hubsites) {
      setCurrentProcessingHubsite(hubsite.url);
      adaptersRef.current = null;

      // Only wait for new adapters if this is a different hubsite
      let adapters: Adapters | null = null;

      if (hubsite.url === hubsites[0].url) {
        // Use the existing adapters from security group validation
        adapters = adaptersRef.current;
      } else {
        // Wait for new adapters for different hubsite
        adapters = await new Promise<Adapters | null>((resolve) => {
          const checkAdapters = setInterval(() => {
            if (adaptersRef.current !== null) {
              clearInterval(checkAdapters);
              resolve(adaptersRef.current);
            }
          }, 100);
        });
      }

      if (adapters) {
        const validation = await validateHubsiteSequentially(
          hubsite.url,
          adapters
        );
        setHubsiteValidations((prev) => [...prev, validation]);
      }
    }

    setCurrentProcessingHubsite(null);
    setCurrentValidationStep(null);
    setIsValidating(false);
  };

  const validateSecurityGroups = async (adapters: Adapters) => {
    // Step 2: Validate Security Groups exist
    setCurrentValidationStep("Security Groups created");
    const requiredGroups = [
      "CMap Mail Admins",
      "CMap Mail All Confidential Users",
      "CMap Mail All Users",
    ];
    const groupResults = await Promise.all(
      requiredGroups.map(async (groupName) => {
        try {
          const users = await adapters.siteAdmin.getUsersFromGroup(groupName);
          return { groupName, users: users || [] };
        } catch (error) {
          return { groupName, users: [] };
        }
      })
    );

    const validations: ValidationItem[] = [];

    const missingGroups = groupResults
      .filter((result) => result.users.length === 0)
      .map((r) => r.groupName);
    if (missingGroups.length === 0) {
      validations.push({
        name: "Security Groups created",
        status: "success",
        details: "All security groups found",
      });
    } else {
      validations.push({
        name: "Security Groups created",
        status: "error",
        details: `Missing groups: ${missingGroups.join(", ")}`,
        message: "Contact Support",
      });
    }

    // Step 3-5: Validate each security group individually
    for (const result of groupResults) {
      const { groupName, users } = result;
      setCurrentValidationStep(`${groupName} has users`);

      const userNames = users.map((user) => user.displayName || user.email);

      if (groupName === "CMap Mail Admins") {
        if (users.length === 0) {
          validations.push({
            name: "CMap Mail Admins has users",
            status: "error",
            details: "0 users",
            message: "Admins must be configured - step 2 above",
            users: userNames,
            count: users.length,
          });
        } else {
          validations.push({
            name: "CMap Mail Admins has users",
            status: "success",
            details: ``,
            users: userNames,
            count: users.length,
          });
        }
      } else if (groupName === "CMap Mail All Confidential Users") {
        validations.push({
          name: "CMap Mail All Confidential Users has users",
          status: users.length === 0 ? "info" : "success",
          details: ``,
          message:
            users.length === 0
              ? "Remember to configure your Confidential Users - step 3 above"
              : undefined,
          users: userNames,
          count: users.length,
        });
      } else if (groupName === "CMap Mail All Users") {
        validations.push({
          name: "CMap Mail All Users has users",
          status: users.length === 0 ? "info" : "success",
          details: ``,
          message:
            users.length === 0
              ? "Remember to configure your All Users - step 4 above"
              : undefined,
          users: userNames,
          count: users.length,
        });
      }
    }

    setSecurityGroupValidations(validations);
  };

  const getStatusIcon = (status: ValidationStatus, isProcessing?: boolean) => {
    if (isProcessing) {
      return <Spinner size="small" />;
    }

    switch (status) {
      case "success":
        return <CheckmarkCircle24Filled style={{ color: "#107C10" }} />;
      case "error":
        return <ErrorCircle24Filled style={{ color: "#D13438" }} />;
      case "info":
        return <Info24Filled style={{ color: "#0078D4" }} />;
      default:
        return null;
    }
  };

  const columns: TableColumnDefinition<ValidationTableItem>[] = [
    createTableColumn<ValidationTableItem>({
      columnId: "validation",
      renderHeaderCell: () => "Validation Check",
      renderCell: (item) => (
        <TableCellLayout>
          <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
            {getStatusIcon(item.status, item.isProcessing)}
            {item.name}
          </div>
        </TableCellLayout>
      ),
    }),
    createTableColumn<ValidationTableItem>({
      columnId: "details",
      renderHeaderCell: () => "Details",
      renderCell: (item) => {
        const { inlineItems, overflowItems } = partitionAvatarGroupItems({
          items: item.users || [],
        });
        return (
          <TableCellLayout>
            <div>
              <div>{item.details}</div>
              {item.message && (
                <div
                  style={{
                    fontStyle: "italic",
                    color: "#666",
                    marginTop: "5px",
                  }}
                >
                  Msg: {item.message}
                </div>
              )}
              {item.users && item.users.length > 0 && (
                <AvatarGroup {...{ inlineItems, overflowItems }}>
                  {inlineItems.map((name) => (
                    <Tooltip content={name} relationship="label" key={name}>
                      <AvatarGroupItem name={name} />
                    </Tooltip>
                  ))}
                  {overflowItems && (
                    <AvatarGroupPopover>
                      {overflowItems.map((name) => (
                        <AvatarGroupItem name={name} key={name} />
                      ))}
                    </AvatarGroupPopover>
                  )}
                </AvatarGroup>
              )}
            </div>
          </TableCellLayout>
        );
      },
    }),
  ];

  return (
    <div className={styles.container}>
      <h1>CMap Mail Installation Validation</h1>
      <p>
        This page validates your CMap Mail installation across all configured
        hub sites.
      </p>

      {isValidating && currentValidationStep && (
        <div
          style={{
            padding: "10px",
            backgroundColor: "#f0f0f0",
            borderRadius: "4px",
            marginBottom: "20px",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
            <Spinner size="small" />
            <span>Processing: {currentValidationStep}</span>
          </div>
        </div>
      )}

      {/* Security Groups Table - Static for all hubsites */}
      {securityGroupValidations.length > 0 && (
        <div style={{ marginBottom: "30px" }}>
          <div
            style={{
              marginBottom: "30px",
              marginLeft: "10px",
              maxWidth: "100%",
            }}
          >
            <h2
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                marginBottom: "15px",
                display: "flex",
                alignItems: "center",
                gap: "10px",
              }}
            >
              {getStatusIcon(getHubsiteStatus(securityGroupValidations))}
              Security Groups Configuration
            </h2>
            <div
              style={{
                marginTop: "10px",
                border: "2px solid #e0e0e0",
                borderRadius: "8px",
                overflow: "hidden",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                maxWidth: "calc(100% - 20px)",
              }}
            >
              <DataGrid
                items={securityGroupValidations.map((v) => ({
                  ...v,
                  hubsiteName: "Security Groups",
                }))}
                columns={columns}
                getRowId={(item) => `security-${item.name}`}
              >
                <DataGridHeader>
                  <DataGridRow
                    style={{
                      backgroundColor: "#f8f9fa",
                      borderBottom: "2px solid #e0e0e0",
                    }}
                  >
                    {({ renderHeaderCell }) => (
                      <DataGridHeaderCell
                        style={{
                          fontWeight: "bold",
                          padding: "12px",
                          borderRight: "1px solid #e0e0e0",
                        }}
                      >
                        {renderHeaderCell()}
                      </DataGridHeaderCell>
                    )}
                  </DataGridRow>
                </DataGridHeader>
                <DataGridBody<ValidationTableItem>>
                  {({ item, rowId }) => (
                    <DataGridRow<ValidationTableItem> key={rowId}>
                      {({ renderCell }) => (
                        <DataGridCell style={{ padding: "12px" }}>
                          {renderCell(item)}
                        </DataGridCell>
                      )}
                    </DataGridRow>
                  )}
                </DataGridBody>
              </DataGrid>
            </div>
          </div>
        </div>
      )}
      {/* Single hubsite - no accordion */}
      {hubsiteValidations.length === 1 &&
        hubsiteValidations.map((hubsiteValidation) => (
          <div
            key={hubsiteValidation.hubsiteUrl}
            style={{
              marginBottom: "30px",
              marginLeft: "10px",
              maxWidth: "100%",
            }}
          >
            <h2
              style={{
                marginBottom: "15px",
                color: "#333",
                display: "flex",
                alignItems: "center",
                gap: "10px",
              }}
            >
              {getStatusIcon(getHubsiteStatus(hubsiteValidation.validations))}
              <span>
                <strong>Hub site:</strong> {hubsiteValidation.hubsiteName}
              </span>
            </h2>
            <div
              style={{
                border: "2px solid #e0e0e0",
                borderRadius: "8px",
                overflow: "hidden",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                maxWidth: "calc(100% - 20px)",
              }}
            >
              <DataGrid
                items={hubsiteValidation.validations.map((v) => ({
                  ...v,
                  hubsiteName: hubsiteValidation.hubsiteName,
                }))}
                columns={columns}
                getRowId={(item) =>
                  `${hubsiteValidation.hubsiteName}-${item.name}`
                }
              >
                <DataGridHeader>
                  <DataGridRow
                    style={{
                      backgroundColor: "#f8f9fa",
                      borderBottom: "2px solid #e0e0e0",
                    }}
                  >
                    {({ renderHeaderCell }) => (
                      <DataGridHeaderCell
                        style={{
                          fontWeight: "bold",
                          padding: "12px",
                          borderRight: "1px solid #e0e0e0",
                        }}
                      >
                        {renderHeaderCell()}
                      </DataGridHeaderCell>
                    )}
                  </DataGridRow>
                </DataGridHeader>
                <DataGridBody<ValidationTableItem>>
                  {({ item, rowId }) => (
                    <DataGridRow<ValidationTableItem> key={rowId}>
                      {({ renderCell }) => (
                        <DataGridCell
                          style={{
                            padding: "12px",
                          }}
                        >
                          {renderCell(item)}
                        </DataGridCell>
                      )}
                    </DataGridRow>
                  )}
                </DataGridBody>
              </DataGrid>
            </div>
          </div>
        ))}

      {/* Multiple hubsites - accordion */}
      {hubsiteValidations.length > 1 && (
        <Accordion collapsible multiple style={{ marginBottom: "20px" }}>
          {hubsiteValidations.map((hubsiteValidation, index) => (
            <AccordionItem key={index} value={`hubsite-${index}`}>
              <AccordionHeader style={{ fontSize: "18px", fontWeight: "bold" }}>
                <h2
                  style={{
                    margin: 0,
                    display: "flex",
                    alignItems: "center",
                    gap: "10px",
                    color: "#333",
                    fontSize: "18px",
                  }}
                >
                  {getStatusIcon(
                    getHubsiteStatus(hubsiteValidation.validations)
                  )}
                  <span>
                    <strong>Hub site:</strong> {hubsiteValidation.hubsiteName}
                  </span>
                </h2>
              </AccordionHeader>
              <AccordionPanel>
                <div
                  style={{
                    border: "2px solid #e0e0e0",
                    borderRadius: "8px",
                    overflow: "hidden",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    marginTop: "10px",
                  }}
                >
                  <DataGrid
                    items={hubsiteValidation.validations.map((v) => ({
                      ...v,
                      hubsiteName: hubsiteValidation.hubsiteName,
                    }))}
                    columns={columns}
                    getRowId={(item) =>
                      `${hubsiteValidation.hubsiteName}-${item.name}`
                    }
                  >
                    <DataGridHeader>
                      <DataGridRow
                        style={{
                          backgroundColor: "#f8f9fa",
                          borderBottom: "2px solid #e0e0e0",
                        }}
                      >
                        {({ renderHeaderCell }) => (
                          <DataGridHeaderCell
                            style={{
                              fontWeight: "bold",
                              padding: "12px",
                              borderRight: "1px solid #e0e0e0",
                            }}
                          >
                            {renderHeaderCell()}
                          </DataGridHeaderCell>
                        )}
                      </DataGridRow>
                    </DataGridHeader>
                    <DataGridBody<ValidationTableItem>>
                      {({ item, rowId }) => (
                        <DataGridRow<ValidationTableItem> key={rowId}>
                          {({ renderCell }) => (
                            <DataGridCell
                              style={{
                                padding: "12px",
                              }}
                            >
                              {renderCell(item)}
                            </DataGridCell>
                          )}
                        </DataGridRow>
                      )}
                    </DataGridBody>
                  </DataGrid>
                </div>
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      )}

      {/* Show processing table for current hubsite */}
      {currentProcessingHubsite && isValidating && (
        <div style={{ marginBottom: "30px" }}>
          <h2
            style={{
              marginBottom: "15px",
              color: "#333",
              display: "flex",
              alignItems: "center",
              gap: "10px",
              fontSize: "18px",
              fontWeight: "bold",
            }}
          >
            <Spinner size="small" />
            <span>{currentProcessingHubsite}</span>
            <span
              style={{ fontSize: "18px", color: "#666", fontWeight: "normal" }}
            >
              (Processing...)
            </span>
          </h2>
          <div
            style={{
              border: "2px solid #e0e0e0",
              borderRadius: "8px",
              overflow: "hidden",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            <DataGrid
              items={[
                "Hub Site created",
                "Site visitors has users",
                "Site owners has users",
                "Hub Site rights check",
              ].map((item) => ({
                name: item,
                status: "info" as ValidationStatus,
                details: "Processing...",
                hubsiteName: currentProcessingHubsite,
                isProcessing: true,
              }))}
              columns={columns}
              getRowId={(item) => `processing-${item.name}`}
            >
              <DataGridHeader>
                <DataGridRow
                  style={{
                    backgroundColor: "#f8f9fa",
                    borderBottom: "2px solid #e0e0e0",
                  }}
                >
                  {({ renderHeaderCell }) => (
                    <DataGridHeaderCell
                      style={{
                        fontWeight: "bold",
                        padding: "12px",
                        borderRight: "1px solid #e0e0e0",
                      }}
                    >
                      {renderHeaderCell()}
                    </DataGridHeaderCell>
                  )}
                </DataGridRow>
              </DataGridHeader>
              <DataGridBody<ValidationTableItem>>
                {({ item, rowId }) => (
                  <DataGridRow<ValidationTableItem> key={rowId}>
                    {({ renderCell }) => (
                      <DataGridCell
                        style={{
                          padding: "12px",
                        }}
                      >
                        {renderCell(item)}
                      </DataGridCell>
                    )}
                  </DataGridRow>
                )}
              </DataGridBody>
            </DataGrid>
          </div>
        </div>
      )}

      {/* Show security group processing */}
      {isValidating && securityGroupValidations.length === 0 && (
        <div style={{ marginBottom: "30px" }}>
          <h2
            style={{
              marginBottom: "15px",
              color: "#333",
              display: "flex",
              alignItems: "center",
              gap: "10px",
              fontSize: "18px",
              fontWeight: "bold",
            }}
          >
            <Spinner size="small" />
            <span>{currentProcessingHubsite}</span>
            <span
              style={{ fontSize: "18px", color: "#666", fontWeight: "normal" }}
            >
              (Processing...)
            </span>
          </h2>
          <div
            style={{
              border: "2px solid #e0e0e0",
              borderRadius: "8px",
              overflow: "hidden",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            <DataGrid
              items={[
                "Security Groups created",
                "CMap Mail Admins has users",
                "CMap Mail All Confidential Users has users",
                "CMap Mail All Users has users",
              ].map((item) => ({
                name: item,
                status: "info" as ValidationStatus,
                details: "Processing...",
                hubsiteName: "Security Groups",
                isProcessing: true,
              }))}
              columns={columns}
              getRowId={(item) => `security-processing-${item.name}`}
            >
              <DataGridHeader>
                <DataGridRow
                  style={{
                    backgroundColor: "#f8f9fa",
                    borderBottom: "2px solid #e0e0e0",
                  }}
                >
                  {({ renderHeaderCell }) => (
                    <DataGridHeaderCell
                      style={{
                        fontWeight: "bold",
                        padding: "12px",
                        borderRight: "1px solid #e0e0e0",
                      }}
                    >
                      {renderHeaderCell()}
                    </DataGridHeaderCell>
                  )}
                </DataGridRow>
              </DataGridHeader>
              <DataGridBody<ValidationTableItem>>
                {({ item, rowId }) => (
                  <DataGridRow<ValidationTableItem> key={rowId}>
                    {({ renderCell }) => (
                      <DataGridCell
                        style={{
                          padding: "12px",
                        }}
                      >
                        {renderCell(item)}
                      </DataGridCell>
                    )}
                  </DataGridRow>
                )}
              </DataGridBody>
            </DataGrid>
          </div>
        </div>
      )}

      <Button
        appearance="primary"
        onClick={startValidation}
        disabled={isValidating}
        style={{ marginBottom: "20px" }}
      >
        {isValidating ? "Validating..." : "Start Validation"}
      </Button>

      {/* Render SharePoint adapter hooks for each hubsite being processed */}
      {currentProcessingHubsite && graph && (
        <SharePointAdapterHook
          key={currentProcessingHubsite}
          hubsiteUrl={currentProcessingHubsite}
          graph={graph}
          onAdaptersCreated={handleAdaptersCreated}
        />
      )}
    </div>
  );
};

const useStyles = makeStyles({
  container: {
    padding: "20px",
    maxWidth: "1400px",
  },
  validateButton: {
    padding: "10px 20px",
    marginBottom: "20px",
    backgroundColor: "#0078d4",
    color: "white",
    border: "none",
    borderRadius: "4px",
    cursor: "pointer",
    fontSize: "16px",
  },
  processingStatus: {
    padding: "10px",
    backgroundColor: "#f0f0f0",
    borderRadius: "4px",
    marginBottom: "20px",
  },
  hubsiteSection: {
    marginBottom: "30px",
    border: "1px solid #e0e0e0",
    borderRadius: "8px",
    padding: "20px",
  },
  tableContainer: {
    overflowX: "auto",
  },
  validationTable: {
    width: "100%",
    borderCollapse: "collapse",
    border: "1px solid #e0e0e0",
  },
  successRow: {
    backgroundColor: "#f0f8f0",
  },
  errorRow: {
    backgroundColor: "#fff0f0",
  },
  infoRow: {
    backgroundColor: "#f0f8ff",
  },
  successIcon: {
    color: "#28a745",
    fontSize: "18px",
  },
  errorIcon: {
    color: "#dc3545",
    fontSize: "18px",
  },
  infoIcon: {
    color: "#007bff",
    fontSize: "18px",
  },
  message: {
    fontStyle: "italic",
    color: "#666",
    marginTop: "5px",
  },
  usersList: {
    marginTop: "10px",
    "& ul": {
      margin: "5px 0",
      paddingLeft: "20px",
    },
    "& li": {
      margin: "2px 0",
    },
  },
});
