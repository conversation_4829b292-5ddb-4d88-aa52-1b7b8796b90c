import React, { useEffect, useState } from "react";
import { Text } from "@fluentui/react/lib/Text";
import { IStackProps, PrimaryButton, Spinner, Stack } from "@fluentui/react";
import { Input, Label } from "@fluentui/react-components";

import { TaskStatus } from "./InstalledStatus";
import { productName } from "../shared/TenancyService";
import { BackendAdapter } from "../adapters/BackendAdapter";

const tokens = {
  sectionStack: {
    childrenGap: 10,
  },
  spinnerStack: {
    childrenGap: 20,
  },
};
const rowProps: IStackProps = { horizontal: true, verticalAlign: "center" };

export const SearchSchemaImport = () => {
  const [searchSchemaDeployed, setSearchSchemaDeployed] =
    React.useState<TaskStatus>(TaskStatus.Waiting);
  const [triggerSearchSchema, setTriggerSearchSchema] =
    React.useState<boolean>(false);

  const [tenancyUrl, setTenancyUrl] = useState("");
  const [backendAdapter] = useState(new BackendAdapter());

  const handleTenancyNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTenancyUrl(e.target.value);
  };

  useEffect(() => {
    const callApi = async () => {
      if (triggerSearchSchema) {
        setTriggerSearchSchema(false);
        setSearchSchemaDeployed(TaskStatus.Creating);

        const response = await backendAdapter.deploySearchSchema(tenancyUrl);

        if (response?.IsSuccess) {
          setSearchSchemaDeployed(TaskStatus.Created);
        } else {
          setSearchSchemaDeployed(TaskStatus.Failed);
        }
      }
    };
    callApi();
  }, [triggerSearchSchema, backendAdapter, tenancyUrl]);

  const deployingSchema = () => {
    switch (searchSchemaDeployed) {
      case TaskStatus.Waiting: {
        return (
          <PrimaryButton
            data-testid="deploy-search-schema"
            type="button"
            className="btn btn-primary"
            onClick={() => setTriggerSearchSchema(true)}
          >
            Deploy Search Schema
          </PrimaryButton>
        );
      }

      case TaskStatus.Created: {
        return (
          <div>
            <p>Search Schema Deployed</p>
            <p>
              You can administer {productName()} from admin site. Please click
              the link below and bookmark it for future reference
              <br />
              <br />
              <PrimaryButton
                type="button"
                className="btn btn-primary"
                href="https://admin.atveromail.com"
                target="_blank"
              >
                Admin Site
              </PrimaryButton>
            </p>
          </div>
        );
      }

      case TaskStatus.Failed: {
        return <p>Search Schema Deployment Failed</p>;
      }

      case TaskStatus.Creating: {
        return <Spinner label={"Deploying Schema"} labelPosition="right" />;
      }
      default: {
        return <p>Unknown Status</p>;
      }
    }
  };

  return (
    <div className="ms-Grid" dir="ltr">
      <div className="ms-Grid-row">
        <div className="ms-Grid-col">
          <Text variant="xxLarge">Deploy Search Schema</Text>
          <br />
          <p>
            This makes the structures in {productName()} discoverable by the
            Search Engine
          </p>
          <p>
            This stage may need to be completed some hours after the initial
            install
          </p>
          <p>You can return to this page at any time to try again</p>
          <p>Please specify the tenancy URL</p>
          <div>
            <Label size="large" htmlFor="tenancy-name">
              Tenancy URL
            </Label>
            <br />
            <Input
              style={{ width: "500px" }}
              placeholder="https://company.sharepoint.com"
              value={tenancyUrl}
              onChange={handleTenancyNameChange}
              id="hubsite-name"
            />
          </div>
          <br />
          <br />
          <Stack tokens={tokens.sectionStack}>
            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <div>{deployingSchema()}</div>
            </Stack>
          </Stack>
        </div>
      </div>
    </div>
  );
};
