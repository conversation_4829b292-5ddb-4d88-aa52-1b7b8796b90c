import React from "react";
import {
  Label,
  makeStyles,
  shorthands,
  Persona,
} from "@fluentui/react-components";
import { DismissSquare20Regular, PersonRegular } from "@fluentui/react-icons";

interface IUser {
  id: string;
  name: string;
  email: string;
  principalName?: string;
}

interface ISelectedUsersListProps {
  users: IUser[];
  onRemoveUser: (userId: string) => void;
  disabled: boolean;
  isProcessing?: boolean;
  title?: string;
}

export const SelectedUsersList: React.FC<ISelectedUsersListProps> = ({
  users,
  onRemoveUser,
  disabled,
  isProcessing = false,
  title = "Selected Users",
}) => {
  const styles = useStyles();

  if (users.length === 0) {
    return null;
  }

  return (
    <div
      className={styles.selectedUsersContainer}
      data-testid="selected-users-list"
    >
      <Label>
        {title} ({users.length})
      </Label>
      <div className={styles.usersList}>
        {users.map((user) => (
          <div
            key={user.id}
            className={styles.userItem}
            data-testid="user-item"
          >
            <Persona
              name={user.name || user.principalName}
              primaryText={user.name || user.principalName}
              secondaryText={user.email}
              size="medium"
              textAlignment="center"
              avatar={{
                icon: <PersonRegular />,
                color: "colorful",
              }}
            />
            <button
              onClick={() => onRemoveUser(user.id)}
              disabled={disabled || isProcessing}
              className={styles.removeButton}
              title="Remove user"
              aria-label="Remove user"
              data-testid="remove-user-button"
            >
              <DismissSquare20Regular />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

const useStyles = makeStyles({
  selectedUsersContainer: {
    ...shorthands.border("1px", "solid", "#e0e0e0"),
    ...shorthands.borderRadius("4px"),
    ...shorthands.padding("12px"),
    backgroundColor: "#f9f9f9",
    maxWidth: "600px",
    maxHeight: "300px",
    overflowY: "auto",
  },
  usersList: {
    display: "flex",
    flexDirection: "column",
    ...shorthands.gap("8px"),
  },
  userItem: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    ...shorthands.padding("4px", "8px"),
    ...shorthands.borderRadius("4px"),
    ":hover": {
      backgroundColor: "#f0f0f0",
    },
  },
  removeButton: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    ...shorthands.padding("4px"),
    backgroundColor: "transparent",
    border: "none",
    borderRadius: "4px",
    cursor: "pointer",
    color: "#a4262c",
    ":hover": {
      backgroundColor: "#fdf2f2",
    },
    ":disabled": {
      cursor: "not-allowed",
      opacity: 0.5,
    },
  },
});
