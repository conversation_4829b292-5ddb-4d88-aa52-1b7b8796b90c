import React, { useEffect, useState } from "react";
import { Text } from "@fluentui/react/lib/Text";
import { makeStyles, shorthands, tokens } from "@fluentui/react-components";
import { Adapters } from "../adapters/AdapterFactory";
import { Task, TaskStatus, CreatingStatus } from "./InstalledStatus";
import {
  MessageBar,
  MessageBarType,
  Stack,
  IStackProps,
} from "@fluentui/react";
import { Site } from "@microsoft/microsoft-graph-types";
import { ConfigureUserGroups } from "./ConfigureUserGroups";

interface IConfigureProps {
  adapters: Adapters;
  hubsiteUrl: string;
}

export const Configure = ({ adapters, hubsiteUrl }: IConfigureProps) => {
  const styles = useStyles();
  const [isVerifying, setIsVerifying] = useState<boolean>(true);
  const [allChecksPass, setAllChecksPass] = useState<boolean>(false);
  const [specificError, setSpecificError] = useState<string>("");

  const [hubsiteVerification, setHubsiteVerification] = useState<Task>({
    status: TaskStatus.Waiting,
    error: undefined,
  });

  const [adminGroupVerification, setAdminGroupVerification] = useState<Task>({
    status: TaskStatus.Waiting,
    error: undefined,
  });

  const [confUsersGroupVerification, setConfUsersGroupVerification] =
    useState<Task>({
      status: TaskStatus.Waiting,
      error: undefined,
    });

  const [allUsersGroupVerification, setAllUsersGroupVerification] =
    useState<Task>({
      status: TaskStatus.Waiting,
      error: undefined,
    });

  useEffect(() => {
    // Reset verification states when hubsitePath changes
    setHubsiteVerification({
      status: TaskStatus.Waiting,
      error: undefined,
    });

    setAdminGroupVerification({
      status: TaskStatus.Waiting,
      error: undefined,
    });

    setConfUsersGroupVerification({
      status: TaskStatus.Waiting,
      error: undefined,
    });

    setAllUsersGroupVerification({
      status: TaskStatus.Waiting,
      error: undefined,
    });

    setIsVerifying(true);
    setAllChecksPass(false);
    setSpecificError("");

    const performVerification = async () => {
      // Verify hub site
      const hubsiteExists = await verifyHubsite();

      if (hubsiteExists) {
        // Only proceed with group verification if hubsite exists
        const adminGroupExists = await verifySecurityGroup(`CMap Mail Admins`);
        const confUsersGroupExists = await verifySecurityGroup(
          `CMap Mail All Confidential Users`
        );
        const allUsersGroupExists =
          await verifySecurityGroup(`CMap Mail All Users`);

        // All checks pass only if every verification succeeded
        const allPassed =
          hubsiteExists &&
          adminGroupExists &&
          confUsersGroupExists &&
          allUsersGroupExists;
        setAllChecksPass(allPassed);
      }

      setIsVerifying(false);
    };

    if (adapters && hubsiteUrl) {
      performVerification();
    }
  }, [adapters, hubsiteUrl]);

  // Verify hub site exists
  const verifyHubsite = async (): Promise<boolean> => {
    if (!adapters || !hubsiteUrl) {
      return false;
    }

    try {
      setHubsiteVerification({
        ...hubsiteVerification,
        status: TaskStatus.Creating,
      });

      const hubsite: Site | null =
        await adapters.siteAdmin.getAtveroMailHubsite(hubsiteUrl);

      if (hubsite) {
        setHubsiteVerification({
          ...hubsiteVerification,
          status: TaskStatus.Created,
        });
        return true;
      } else {
        const errorMsg = `Hub site at URL "${hubsiteUrl}" does not exist.`;
        setHubsiteVerification({
          status: TaskStatus.Failed,
          error: errorMsg,
        });
        setSpecificError(errorMsg);
        return false;
      }
    } catch (error) {
      const errorMsg = `Error verifying hub site: ${error instanceof Error ? error.message : String(error)}`;
      setHubsiteVerification({
        status: TaskStatus.Failed,
        error: errorMsg,
      });
      setSpecificError(errorMsg);
      return false;
    }
  };

  // Verify security group exists
  const verifySecurityGroup = async (groupName: string): Promise<boolean> => {
    if (!adapters) {
      return false;
    }

    let setVerificationStatus = setAdminGroupVerification;
    if (groupName === `CMap Mail All Confidential Users`) {
      setVerificationStatus = setConfUsersGroupVerification;
    } else if (groupName === `CMap Mail All Users`) {
      setVerificationStatus = setAllUsersGroupVerification;
    }

    try {
      setVerificationStatus({
        status: TaskStatus.Creating,
        error: undefined,
      });

      const groups = await adapters.siteAdmin.getGroups(groupName);

      if (groups && groups.length > 0) {
        setVerificationStatus({
          status: TaskStatus.Created,
          error: undefined,
        });
        return true;
      } else {
        const errorMsg = `Security group "${groupName}" does not exist.`;
        setVerificationStatus({
          status: TaskStatus.Failed,
          error: errorMsg,
        });
        setSpecificError(errorMsg);
        return false;
      }
    } catch (error) {
      const errorMsg = `Error verifying security group "${groupName}": ${error instanceof Error ? error.message : String(error)}`;
      setVerificationStatus({
        status: TaskStatus.Failed,
        error: errorMsg,
      });
      setSpecificError(errorMsg);
      return false;
    }
  };

  const rowProps: IStackProps = { horizontal: true, verticalAlign: "center" };
  const tokens = {
    sectionStack: {
      childrenGap: 10,
    },
    spinnerStack: {
      childrenGap: 20,
    },
  };

  return (
    <div className={styles.container} data-testid="configure">
      <Text variant="xxLarge">Configuration Page</Text>
      <div className={styles.verificationSection}>
        <Text variant="xLarge">System Verification</Text>
        {isVerifying && (
          <Text>
            Verifying required components before proceeding with
            configuration...
          </Text>
        )}

        {!adapters ? (
          <MessageBar
            className={styles.errorMessage}
            messageBarType={MessageBarType.warning}
          >
            Loading authentication and API connections...
          </MessageBar>
        ) : (
          <Stack
            tokens={tokens.sectionStack}
            className={styles.checksContainer}
          >
            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus
                task={hubsiteVerification}
                thing="Hub Site Verification"
              />
            </Stack>

            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus
                task={adminGroupVerification}
                thing={`"CMap Mail Admins" Security Group Verification`}
              />
            </Stack>

            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus
                task={confUsersGroupVerification}
                thing={`"CMap Mail All Confidential Users" Security Group Verification`}
              />
            </Stack>

            <Stack {...rowProps} tokens={tokens.spinnerStack}>
              <CreatingStatus
                task={allUsersGroupVerification}
                thing={`"CMap Mail All Users" Security Group Verification`}
              />
            </Stack>
          </Stack>
        )}

        {!isVerifying && !allChecksPass && (
          <MessageBar
            className={styles.errorMessage}
            messageBarType={MessageBarType.error}
            isMultiline={true}
          >
            <Text variant="medium">
              Oops, something went wrong with your initial installation, please
              contact Support before proceeding any further.
              <br />
              <br />
              <strong>Details for Support:</strong> {specificError}
            </Text>
          </MessageBar>
        )}

        {!isVerifying && allChecksPass && (
          <MessageBar
            className={styles.successMessage}
            messageBarType={MessageBarType.success}
            data-testid="success-message"
          >
            <Text variant="medium">
              All verification checks passed! You can now proceed with
              configuration.
            </Text>
          </MessageBar>
        )}
      </div>

      {!adapters ? (
        <MessageBar
          className={styles.errorMessage}
          messageBarType={MessageBarType.warning}
          data-testid="loading-message"
        >
          Loading configuration options...
        </MessageBar>
      ) : (
        <>
          <div className={styles.userGroupContainer}>
            <Text variant="large" className={styles.groupHeader}>
              CMap Mail Admins
            </Text>
            <ConfigureUserGroups
              adapters={adapters}
              disabled={!allChecksPass}
              groupName={"CMap Mail Admins"}
              descriptions={{
                flair: `Add users to the "CMap Mail Admins" security group.`,
                details: `This will grant the selected users Administrative access to all projects in CMap Mail.`,
              }}
            />
          </div>

          <div className={styles.userGroupContainer}>
            <Text variant="large" className={styles.groupHeader}>
              CMap Mail All Confidential Users
            </Text>
            <ConfigureUserGroups
              adapters={adapters}
              disabled={!allChecksPass}
              groupName={"CMap Mail All Confidential Users"}
              descriptions={{
                flair: `Add users to the "CMap Mail All Confidential Users" security group.`,
                details: `This will grant the selected users the ability to file and view confidential projects emails within CMap Mail.`,
              }}
            />
          </div>

          <div className={styles.userGroupContainer}>
            <Text variant="large" className={styles.groupHeader}>
              CMap Mail All Users
            </Text>
            <ConfigureUserGroups
              adapters={adapters}
              disabled={!allChecksPass}
              groupName={"CMap Mail All Users"}
              descriptions={{
                flair: `Add users to the "CMap Mail All Users" security group.`,
                details: `This will grant the selected users basic user access to assigned projects in CMap Mail.`,
              }}
            />
          </div>
        </>
      )}
    </div>
  );
};

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    ...shorthands.gap("24px"),
    ...shorthands.padding("20px"),
  },
  verificationSection: {
    display: "flex",
    flexDirection: "column",
    ...shorthands.gap("16px"),
    ...shorthands.padding("16px"),
    ...shorthands.borderRadius("4px"),
    ...shorthands.border("1px", "solid", tokens.colorNeutralStroke1),
    marginTop: "20px",
  },
  checksContainer: {
    marginTop: "16px",
  },
  errorMessage: {
    marginTop: "16px",
    maxWidth: "626px",
  },
  successMessage: {
    marginTop: "16px",
    maxWidth: "626px",
  },
  configSection: {
    display: "flex",
    flexDirection: "column",
    ...shorthands.gap("16px"),
    ...shorthands.padding("16px"),
    ...shorthands.borderRadius("4px"),
    ...shorthands.border("1px", "solid", tokens.colorNeutralStroke1),
    marginTop: "20px",
  },
  configOptions: {
    marginTop: "16px",
  },
  configButton: {
    minWidth: "200px",
  },
  sectionSeparator: {
    marginTop: "20px",
  },
  advancedOptions: {
    marginTop: "16px",
  },
  configBlock: {
    backgroundColor: tokens.colorNeutralBackground1,
  },
  userGroupContainer: {
    display: "flex",
    flexDirection: "column",
    ...shorthands.gap("12px"),
    ...shorthands.padding("16px"),
    ...shorthands.borderRadius("4px"),
    ...shorthands.border("1px", "solid", tokens.colorNeutralStroke1),
    backgroundColor: tokens.colorNeutralBackground1,
    marginTop: "16px",
  },

  groupHeader: {
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
});
