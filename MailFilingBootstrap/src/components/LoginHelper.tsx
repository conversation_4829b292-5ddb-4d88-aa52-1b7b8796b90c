import React from "react";
import { Text } from "@fluentui/react/lib/Text";

export const LoginHelper = () => {
  return (
    <div className="ms-Grid" dir="ltr" data-testid="login-helper">
      <div className="ms-Grid-row">
        <div className="ms-Grid-col">
          <Text variant="xLarge">Login to continue</Text>
          <br />
          <br />

          <p>Not currently logged in. You might need to enable your popups</p>
          <p>
            To enable popups in your browser, select the icon in the web address
            bar
          </p>
          <img
            width="250"
            src="/assets/show_popups.png"
            alt="An image showing a web browser, to enable popup select the icon, that looks like a window with a line through it"
          ></img>
          <p>
            Always allow pop-ups and redirects for
            https://install.atveromail.com{" "}
          </p>
          <img
            width="250"
            src="/assets/popups.png"
            alt="Shows the popup settings for the browser, with the option to allow all popups from atveromail.com"
          ></img>

          <p>
            Refresh this page to continue and then you will be able to Login
          </p>
        </div>
      </div>
    </div>
  );
};
