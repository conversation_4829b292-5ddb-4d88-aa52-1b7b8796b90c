import { Label } from "@fluentui/react";
import { Spinner } from "@fluentui/react/lib/Spinner";
import React from "react";
import {
  CheckboxUnchecked20Regular,
  CheckboxChecked20Regular,
} from "@fluentui/react-icons";

export enum TaskStatus {
  Waiting,
  Creating,
  Created,
  Failed,
}

export type Task = {
  status: TaskStatus;
  error: string | undefined;
};

type ICreatingStatusProps = {
  task: Task;
  thing: string;
};

export const CreatingStatus = (props: ICreatingStatusProps) => {
  switch (props.task.status) {
    case TaskStatus.Waiting:
      return (
        <>
          <CheckboxUnchecked20Regular className="waiting-icon" />
          <Label>{props.thing}</Label>
        </>
      );
    case TaskStatus.Creating:
      return <Spinner label={`${props.thing}...`} labelPosition="right" />;
    case TaskStatus.Created:
      return (
        <>
          <CheckboxChecked20Regular className="completed-icon" />
          <Label>{props.thing}</Label>
        </>
      );
    case TaskStatus.Failed:
      return (
        <>
          <Label>{props.thing}</Label>
          <p>{props.task.error}</p>
        </>
      );
  }
};
