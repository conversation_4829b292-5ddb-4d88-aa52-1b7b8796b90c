import React from "react";
import ReactDOM from "react-dom/client";
import * as Sen<PERSON> from "@sentry/react";
import { RouterProvider, createRouter } from "@tanstack/react-router";

import { App } from "./App";
import "./index.css";
// Import the generated route tree
import { routeTree } from "./routeTree.gen";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { Version } from "./utils/Config";

// Create a new router instance
const router = createRouter({ routeTree });

Sentry.init({
  dsn: "https://<EMAIL>/4508177173118976",
  integrations: [Sentry.tanstackRouterBrowserTracingIntegration(router)],
  release: Version(),

  // Setting a sample rate is required for sending performance data.
  // We recommend adjusting this value in production, or using tracesSampler
  // for finer control.
  tracesSampleRate: 1.0,

  // Capture Replay for 10 % of all sessions,
  // plus for 100% of sessions with an error
  // Learn more at
  // https://docs.sentry.io/platforms/javascript/session-replay/configuration/#general-integration-configuration
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
});

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <FluentProvider theme={webLightTheme}>
      {/* @ts-expect-error */}
      <RouterProvider router={router}>
        <App />
      </RouterProvider>{" "}
    </FluentProvider>
  </React.StrictMode>
);
