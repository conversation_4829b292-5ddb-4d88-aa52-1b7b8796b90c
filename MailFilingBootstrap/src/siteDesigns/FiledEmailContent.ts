export const projectSiteScript = () => {
  return `
{
  "$schema": "schema.json",
  "actions": [
 
    {
      "verb": "createSPList",
      "listName": "Filed Email Content",
      "templateType": 101,
      "subactions": [
  {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailSubject\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailSubject\\" Name=\\"EmailSubject\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailFrom\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailFrom\\" Name=\\"EmailFrom\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTo\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTo\\" Name=\\"EmailTo\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailCC\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailCC\\" Name=\\"EmailCC\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailReceivedOn\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailReceivedOn\\" Name=\\"EmailReceivedOn\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTextSummary\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTextSummary\\" Name=\\"EmailTextSummary\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVMessageId\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVMessageId\\" Name=\\"ATVMessageId\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVConversationId\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVConversationId\\" Name=\\"ATVConversationId\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVSearchRefiner\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVSearchRefiner\\" Name=\\"ATVSearchRefiner\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailReceived\\" Type=\\"DateTime\\" Required=\\"FALSE\\" StaticName=\\"EmailReceived\\" Name=\\"EmailReceived\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ReceivedOn\\" Type=\\"DateTime\\" Required=\\"FALSE\\" StaticName=\\"ReceivedOn\\" Name=\\"ReceivedOn\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"AttachmentCount\\" Type=\\"Number\\" Required=\\"FALSE\\" StaticName=\\"AttachmentCount\\" Name=\\"AttachmentCount\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTags\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTags\\" Name=\\"EmailTags\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailImportant\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailImportant\\" Name=\\"EmailImportant\\" Indexed=\\"TRUE\\"/>"
        }
      ]
    },
    {
      "verb": "createSPList",
      "listName": "Confidential Filed Email Content",
      "templateType": 101,
      "subactions": [
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailSubject\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailSubject\\" Name=\\"EmailSubject\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailFrom\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailFrom\\" Name=\\"EmailFrom\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTo\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTo\\" Name=\\"EmailTo\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailCC\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailCC\\" Name=\\"EmailCC\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailReceivedOn\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailReceivedOn\\" Name=\\"EmailReceivedOn\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTextSummary\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTextSummary\\" Name=\\"EmailTextSummary\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVMessageId\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVMessageId\\" Name=\\"ATVMessageId\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVConversationId\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVConversationId\\" Name=\\"ATVConversationId\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVSearchRefiner\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVSearchRefiner\\" Name=\\"ATVSearchRefiner\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailReceived\\" Type=\\"DateTime\\" Required=\\"FALSE\\" StaticName=\\"EmailReceived\\" Name=\\"EmailReceived\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ReceivedOn\\" Type=\\"DateTime\\" Required=\\"FALSE\\" StaticName=\\"ReceivedOn\\" Name=\\"ReceivedOn\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"AttachmentCount\\" Type=\\"Number\\" Required=\\"FALSE\\" StaticName=\\"AttachmentCount\\" Name=\\"AttachmentCount\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTags\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTags\\" Name=\\"EmailTags\\" Indexed=\\"TRUE\\"/>"
        },        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailImportant\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailImportant\\" Name=\\"EmailImportant\\" Indexed=\\"TRUE\\"/>"
        }
      ]
    },
    {
      "verb": "createSPList",
      "listName": "Filed Email Content Writable",
      "templateType": 101,
      "subactions": [
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailSubject\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailSubject\\" Name=\\"EmailSubject\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailFrom\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailFrom\\" Name=\\"EmailFrom\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTo\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTo\\" Name=\\"EmailTo\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailCC\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailCC\\" Name=\\"EmailCC\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailReceivedOn\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailReceivedOn\\" Name=\\"EmailReceivedOn\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTextSummary\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTextSummary\\" Name=\\"EmailTextSummary\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVMessageId\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVMessageId\\" Name=\\"ATVMessageId\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVConversationId\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVConversationId\\" Name=\\"ATVConversationId\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVSearchRefiner\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVSearchRefiner\\" Name=\\"ATVSearchRefiner\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailReceived\\" Type=\\"DateTime\\" Required=\\"FALSE\\" StaticName=\\"EmailReceived\\" Name=\\"EmailReceived\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ReceivedOn\\" Type=\\"DateTime\\" Required=\\"FALSE\\" StaticName=\\"ReceivedOn\\" Name=\\"ReceivedOn\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"AttachmentCount\\" Type=\\"Number\\" Required=\\"FALSE\\" StaticName=\\"AttachmentCount\\" Name=\\"AttachmentCount\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTags\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTags\\" Name=\\"EmailTags\\" />"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailImportant\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailImportant\\" Name=\\"EmailImportant\\" />"
        }
      ]
    }
  ],
  "bindata": {},
  "version": 1
}
`;
};

export const hubsiteSiteScript = () => {
  return `
{
  "$schema": "schema.json",
  "actions": [
    {
      "verb": "createSPList",
      "listName": "Filed Email Content",
      "templateType": 101,
      "subactions": [
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailSubject\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailSubject\\" Name=\\"EmailSubject\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailFrom\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailFrom\\" Name=\\"EmailFrom\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTo\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTo\\" Name=\\"EmailTo\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailCC\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailCC\\" Name=\\"EmailCC\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailReceivedOn\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailReceivedOn\\" Name=\\"EmailReceivedOn\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTextSummary\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTextSummary\\" Name=\\"EmailTextSummary\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVMessageId\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVMessageId\\" Name=\\"ATVMessageId\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVConversationId\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVConversationId\\" Name=\\"ATVConversationId\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVSearchRefiner\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVSearchRefiner\\" Name=\\"ATVSearchRefiner\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailReceived\\" Type=\\"DateTime\\" Required=\\"FALSE\\" StaticName=\\"EmailReceived\\" Name=\\"EmailReceived\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ReceivedOn\\" Type=\\"DateTime\\" Required=\\"FALSE\\" StaticName=\\"ReceivedOn\\" Name=\\"ReceivedOn\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"AttachmentCount\\" Type=\\"Number\\" Required=\\"FALSE\\" StaticName=\\"AttachmentCount\\" Name=\\"AttachmentCount\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailTags\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailTags\\" Name=\\"EmailTags\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"EmailImportant\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"EmailImportant\\" Name=\\"EmailImportant\\"  Indexed=\\"TRUE\\"/>"
        }
      ]
    },
    {
      "verb": "createSPList",
      "listName": "EmailProjectLookup",
      "templateType": 101,
      "subactions": [
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"Email\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"Email\\" Name=\\"Email\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ProjectCode\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ProjectCode\\" Name=\\"ProjectCode\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ProjectNumber\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ProjectNumber\\" Name=\\"ProjectNumber\\" Indexed=\\"TRUE\\"/>"
        }
      ]
    },
    {
      "verb": "createSPList",
      "listName": "Projects",
      "templateType": 100,
      "subactions": [
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ProjectCode\\" Type=\\"Text\\" Required=\\"TRUE\\" StaticName=\\"ProjectCode\\" Name=\\"ProjectCode\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"Description\\" Type=\\"Text\\" Required=\\"TRUE\\" StaticName=\\"Description\\" Name=\\"Description\\" Indexed=\\"TRUE\\"/>"
        },
        {
          "verb": "addSPFieldXml",
          "schemaXml": "<Field DisplayName=\\"ATVImportedSourceID\\" Type=\\"Text\\" Required=\\"FALSE\\" StaticName=\\"ATVImportedSourceID\\" Name=\\"ATVImportedSourceID\\" Indexed=\\"TRUE\\"/>"
        }
      ]
    }
  ],
  "bindata": {},
  "version": 1
}
`;
};
