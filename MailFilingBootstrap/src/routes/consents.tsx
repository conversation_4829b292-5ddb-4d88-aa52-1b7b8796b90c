import React from "react";
import { createFileRoute } from "@tanstack/react-router";
import { Consent } from "../components/Consent";
type ConsentSearchParams = {
  admin_consent: boolean | undefined;
  tenant: string | undefined;
  state: string | undefined;
};

const ConsentWrapper = () => {
  const { admin_consent, tenant } = Route.useSearch();

  return <Consent admin_consent={admin_consent} tenant={tenant} />;
};

export const Route = createFileRoute("/consents")({
  validateSearch: (search: Record<string, unknown>): ConsentSearchParams => {
    // validate and parse the search params into a typed state
    return {
      admin_consent: Boolean(search.admin_consent),
      tenant: (search.tenant as string) || "",
      state: (search.state as string) || "",
    };
  },
  component: ConsentWrapper,
});
