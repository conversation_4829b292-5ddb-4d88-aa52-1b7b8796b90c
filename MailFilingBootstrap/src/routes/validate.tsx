import React from "react";
import { createFileRoute } from "@tanstack/react-router";
import { MsalAuthenticationTemplate, MsalProvider } from "@azure/msal-react";
import { InteractionType } from "@azure/msal-browser";
import { msalInstance } from "../api/backendUtils";
import { Validate } from "../components/Validate";

const ValidateWrapper = () => {
  return (
    <MsalProvider instance={msalInstance}>
      <MsalAuthenticationTemplate interactionType={InteractionType.Redirect}>
        <Validate />
      </MsalAuthenticationTemplate>
    </MsalProvider>
  );
};

export const Route = createFileRoute("/validate")({
  component: ValidateWrapper,
});
