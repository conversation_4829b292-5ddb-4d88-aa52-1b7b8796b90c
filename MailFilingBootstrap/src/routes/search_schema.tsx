import React from "react";
import { createFileRoute } from "@tanstack/react-router";
import { SearchSchemaImport } from "../components/SearchSchemaImport";
import { MsalAuthenticationTemplate, MsalProvider } from "@azure/msal-react";
import { InteractionType } from "@azure/msal-browser";
import { msalInstance } from "../api/backendUtils";

const SearchSchema = () => {
  return (
    <MsalProvider instance={msalInstance}>
      <MsalAuthenticationTemplate interactionType={InteractionType.Redirect}>
        <SearchSchemaImport />
      </MsalAuthenticationTemplate>
    </MsalProvider>
  );
};

export const Route = createFileRoute("/search_schema")({
  component: SearchSchema,
});
