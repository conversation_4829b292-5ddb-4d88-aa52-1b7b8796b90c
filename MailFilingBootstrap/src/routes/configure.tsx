import { createFileRoute } from "@tanstack/react-router";
import React from "react";
import { Auth } from "../components/Auth";

type SearchParams = {
  hubsiteUrl: string;
};

const ConfigureWrapper = () => {
  const { hubsiteUrl } = Route.useSearch();

  return <Auth hubsiteUrl={hubsiteUrl} />;
};

export const Route = createFileRoute("/configure")({
  validateSearch: (search: Record<string, unknown>): SearchParams => {
    // validate and parse the search params into a typed state
    return {
      hubsiteUrl: (search.hubsiteUrl as string) || "",
    };
  },
  component: ConfigureWrapper,
});
