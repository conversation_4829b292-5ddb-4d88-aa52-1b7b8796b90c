export interface ODataError {
  "odata.error": {
    code: string;
    message: {
      lang: string;
      value: string;
    };
  };
}

export interface SharePointErrorResponse {
  error?: ODataError["odata.error"];
  "odata.error"?: ODataError["odata.error"];
}

/**
 * Extracts the meaningful error message from SharePoint/Graph API errors
 * @param error - The error object to extract the message from
 * @returns A clean, user-friendly error message
 */
export function extractErrorValue(error: unknown): string {
  try {
    if (error instanceof Error) {
      try {
        // Look for JSON content in the error message
        const jsonMatch = error.message.match(/(\{.*\})/);
        if (jsonMatch) {
          const errorObj = JSON.parse(jsonMatch[1]) as SharePointErrorResponse;

          if (errorObj["odata.error"]?.message?.value) {
            return errorObj["odata.error"].message.value;
          }

          if (errorObj.error?.message?.value) {
            return errorObj.error.message.value;
          }
        }
      } catch (parseError) {
        // If JSON parsing fails, try regex extraction
        const valueMatch = error.message.match(/"value"\s*:\s*"([^"]+)"/);
        if (valueMatch) {
          return valueMatch[1];
        }
      }

      // Return the original error message if we can't extract a value
      return error.message;
    }

    // Handle non-Error objects
    return String(error);
  } catch {
    return "Unknown error occurred";
  }
}

/**
 * Creates a standardized error message for SharePoint operations
 * @param operation - The operation that failed (e.g., "assign admin group")
 * @param error - The error that occurred
 * @returns A formatted error message
 */
export function createSharePointError(
  operation: string,
  error: unknown
): string {
  const cleanError = extractErrorValue(error);
  return `Failed to ${operation}: ${cleanError}`;
}
