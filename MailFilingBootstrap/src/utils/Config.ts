import config from "../../config.json";
export const Product = (): string => {
  return config.product;
};

export const ClientId = (): string => {
  return config.appid;
};

export const RedirectUri = () => {
  return config.redirectUrl;
};

export const BackendScope = () => {
  return `api://${config.backendScope}/${config.appid}/access_as_user`;
};
export const BackendUrl = () => {
  return config.backendUrl;
};

export const Version = () => {
  return config.version;
};

export const Scopes = () => {
  return ["User.Read", "Sites.ReadWrite.All", "Group.ReadWrite.All"];
};
