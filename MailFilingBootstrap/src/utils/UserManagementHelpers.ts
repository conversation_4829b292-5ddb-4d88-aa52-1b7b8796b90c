import * as Sentry from "@sentry/react";
import { ISiteAdmin } from "../adapters/ISiteAdmin";

export type User = {
  id: string;
  name: string;
  email: string;
  principalName?: string;
};

export type UserAdditionResult = {
  email: string;
  success: boolean;
  message: string;
};

export type ProcessedResults = {
  successfulUsers: string[];
  alreadyInGroup: string[];
  failedUsers: { name: string; error: string }[];
  usersToRemove: User[];
  groupName: string;
};

/**
 * Adds multiple users to a security group by their email addresses
 * @param siteAdmin The site admin service instance
 * @param groupName The name of the security group
 * @param userEmails Array of email addresses for users to add
 * @returns Promise with the results of the batch operation
 */
export async function addUsersToGroup(
  siteAdmin: ISiteAdmin,
  groupName: string,
  userEmails: string[]
): Promise<{
  success: boolean;
  results: { [email: string]: { success: boolean; message: string } };
}> {
  try {
    const securityGroups = await siteAdmin.getGroups(groupName);

    if (!securityGroups?.length || !securityGroups[0].id) {
      return {
        success: false,
        results: userEmails.reduce(
          (acc, email) => ({
            ...acc,
            [email]: {
              success: false,
              message: `Security group ${groupName} not found or has no ID`,
            },
          }),
          {}
        ),
      };
    }

    const groupId: string = securityGroups[0].id;
    const results: {
      [email: string]: { success: boolean; message: string };
    } = {};

    for (const email of userEmails) {
      const user = await siteAdmin.getUserByEmail(email);

      if (user) {
        // User exists, try to add them to the group
        const result = await siteAdmin.addUserToGroupById(groupId, user.id);
        results[email] = result;
      } else {
        // User not found, record the error
        results[email] = { success: false, message: "User not found" };
      }
    }

    const overallSuccess = Object.values(results).every(
      (result) => result.success
    );

    return { success: overallSuccess, results };
  } catch (error) {
    console.error(`Error adding users to group ${groupName}:`, error);
    Sentry.captureException(error);

    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      success: false,
      results: userEmails.reduce(
        (acc, email) => ({
          ...acc,
          [email]: {
            success: false,
            message: errorMessage || "Unexpected error occurred",
          },
        }),
        {}
      ),
    };
  }
}

/**
 * Processes the results of adding users to a security group
 * @param users List of users that were attempted to be added
 * @param results Results from the adapter operation
 * @param groupName The name of the security group
 * @returns Processed results categorized for UI consumption
 */
export function processUserAdditionResults(
  users: User[],
  results: { [email: string]: { success: boolean; message: string } },
  groupName: string
): ProcessedResults {
  const successfulUsers: string[] = [];
  const alreadyInGroup: string[] = [];
  const failedUsers: { name: string; error: string }[] = [];

  // Process each result from the adapter call
  Object.entries(results).forEach(([email, outcome]) => {
    const user = users.find((u) => u.email === email);
    const userName = user?.name || email;

    if (outcome.success) {
      if (outcome.message.includes("already in the group")) {
        alreadyInGroup.push(userName);
      } else {
        successfulUsers.push(userName);
      }
    } else {
      // Convert technical error to human-readable message
      let humanError = outcome.message;

      if (
        outcome.message.includes(
          "already exist for the following modified properties: 'members'"
        )
      ) {
        humanError = "User is already in the group";
        alreadyInGroup.push(userName);
        return;
      } else if (outcome.message.includes("already exists")) {
        humanError = "User is already in the group";
        alreadyInGroup.push(userName);
        return;
      } else if (outcome.message.includes("not found")) {
        humanError = "User not found in the system";
      } else if (
        outcome.message.includes("permission") ||
        outcome.message.includes("access")
      ) {
        humanError = "Insufficient permissions to add user";
      } else if (outcome.message.includes("invalid")) {
        humanError = "Invalid user account";
      } else {
        humanError = "Failed to add user - please try again";
      }

      failedUsers.push({
        name: userName,
        error: humanError,
      });
    }
  });

  // Determine which users to remove from selection
  const usersToRemove = users.filter((user) => {
    return (
      successfulUsers.includes(user.name) || alreadyInGroup.includes(user.name)
    );
  });

  return {
    successfulUsers,
    alreadyInGroup,
    failedUsers,
    usersToRemove,
    groupName,
  };
}

/**
 * Formats a user-friendly status message based on processed results
 * @param processedResults The processed results from user addition operation
 * @returns A formatted status message string
 */
export function formatUserAdditionStatusMessage(
  processedResults: Pick<
    ProcessedResults,
    "successfulUsers" | "alreadyInGroup" | "groupName"
  >
): string {
  const { successfulUsers, alreadyInGroup, groupName } = processedResults;
  let successMessage = "";

  if (successfulUsers.length > 0) {
    successMessage = `Successfully added ${successfulUsers.length} user${
      successfulUsers.length > 1 ? "s" : ""
    } to ${groupName}.`;
  }

  if (alreadyInGroup.length > 0) {
    if (successMessage) successMessage += " ";
    successMessage += `${alreadyInGroup.length} user${
      alreadyInGroup.length > 1 ? "s were" : " was"
    } already in the group ${groupName}.`;
  }

  return successMessage;
}

/**
 * Formats an error message based on failed user additions
 * @param failedUsers List of users that failed to be added with their errors
 * @param groupName The name of the group
 * @returns A formatted error message string
 */
export function formatUserAdditionErrorMessage(
  failedUsers: { name: string; error: string }[],
  groupName: string
): string {
  if (failedUsers.length === 0) return "";

  const primaryError = failedUsers[0].error;
  const allSameError = failedUsers.every((f) => f.error === primaryError);
  const errorMessage = allSameError
    ? primaryError
    : "Multiple users couldn't be added due to different errors";

  return `We ran into an issue adding some of the users you selected to ${groupName}. Error: ${errorMessage}`;
}
