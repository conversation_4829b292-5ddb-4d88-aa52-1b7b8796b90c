/* prettier-ignore-start */

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file is auto-generated by TanStack Router

// Import Routes

import { Route as rootRoute } from "./routes/__root";
import { Route as ValidateImport } from "./routes/validate";
import { Route as SearchschemaImport } from "./routes/search_schema";
import { Route as RegisterhubsiteImport } from "./routes/register_hubsite";
import { Route as HubsiteImport } from "./routes/hubsite";
import { Route as ConsentsImport } from "./routes/consents";
import { Route as ConfigureImport } from "./routes/configure";
import { Route as IndexImport } from "./routes/index";

// Create/Update Routes

const ValidateRoute = ValidateImport.update({
  path: "/validate",
  getParentRoute: () => rootRoute,
} as any);

const SearchschemaRoute = SearchschemaImport.update({
  path: "/search_schema",
  getParentRoute: () => rootRoute,
} as any);

const RegisterhubsiteRoute = RegisterhubsiteImport.update({
  path: "/register_hubsite",
  getParentRoute: () => rootRoute,
} as any);

const HubsiteRoute = HubsiteImport.update({
  path: "/hubsite",
  getParentRoute: () => rootRoute,
} as any);

const ConsentsRoute = ConsentsImport.update({
  path: "/consents",
  getParentRoute: () => rootRoute,
} as any);

const ConfigureRoute = ConfigureImport.update({
  path: "/configure",
  getParentRoute: () => rootRoute,
} as any);

const IndexRoute = IndexImport.update({
  path: "/",
  getParentRoute: () => rootRoute,
} as any);

// Populate the FileRoutesByPath interface

declare module "@tanstack/react-router" {
  interface FileRoutesByPath {
    "/": {
      id: "/";
      path: "/";
      fullPath: "/";
      preLoaderRoute: typeof IndexImport;
      parentRoute: typeof rootRoute;
    };
    "/configure": {
      id: "/configure";
      path: "/configure";
      fullPath: "/configure";
      preLoaderRoute: typeof ConfigureImport;
      parentRoute: typeof rootRoute;
    };
    "/consents": {
      id: "/consents";
      path: "/consents";
      fullPath: "/consents";
      preLoaderRoute: typeof ConsentsImport;
      parentRoute: typeof rootRoute;
    };
    "/hubsite": {
      id: "/hubsite";
      path: "/hubsite";
      fullPath: "/hubsite";
      preLoaderRoute: typeof HubsiteImport;
      parentRoute: typeof rootRoute;
    };
    "/register_hubsite": {
      id: "/register_hubsite";
      path: "/register_hubsite";
      fullPath: "/register_hubsite";
      preLoaderRoute: typeof RegisterhubsiteImport;
      parentRoute: typeof rootRoute;
    };
    "/search_schema": {
      id: "/search_schema";
      path: "/search_schema";
      fullPath: "/search_schema";
      preLoaderRoute: typeof SearchschemaImport;
      parentRoute: typeof rootRoute;
    };
    "/validate": {
      id: "/validate";
      path: "/validate";
      fullPath: "/validate";
      preLoaderRoute: typeof ValidateImport;
      parentRoute: typeof rootRoute;
    };
  }
}

// Create and export the route tree

export const routeTree = rootRoute.addChildren({
  IndexRoute,
  ConfigureRoute,
  ConsentsRoute,
  HubsiteRoute,
  RegisterhubsiteRoute,
  SearchschemaRoute,
  ValidateRoute,
});

/* prettier-ignore-end */

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/configure",
        "/consents",
        "/hubsite",
        "/register_hubsite",
        "/search_schema",
        "/validate"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/configure": {
      "filePath": "configure.tsx"
    },
    "/consents": {
      "filePath": "consents.tsx"
    },
    "/hubsite": {
      "filePath": "hubsite.tsx"
    },
    "/register_hubsite": {
      "filePath": "register_hubsite.tsx"
    },
    "/search_schema": {
      "filePath": "search_schema.tsx"
    },
    "/validate": {
      "filePath": "validate.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
