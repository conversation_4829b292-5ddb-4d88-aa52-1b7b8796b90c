import React, { useEffect } from "react";
import { graph<PERSON>, Graph<PERSON>rows<PERSON>, Graph<PERSON> } from "@pnp/graph";
import { MSAL, MSALOptions } from "@pnp/msaljsclient";
import { ClientId, RedirectUri, Scopes } from "../utils/Config";

export const useGraph = () => {
  const [graph, setGraph] = React.useState<GraphFI>();

  useEffect(() => {
    const options: MSALOptions = {
      configuration: {
        auth: {
          authority: "https://login.microsoftonline.com/common",
          clientId: ClientId(),
          redirectUri: RedirectUri(),
        },
        cache: {
          claimsBasedCachingEnabled: true, // in order to avoid network call to refresh a token every time claims are requested
        },
      },
      authParams: {
        forceRefresh: false,
        scopes: Scopes(),
      },
    };
    setGraph(graphfi().using(GraphBrowser(), MS<PERSON>(options)));
  }, []);

  return graph;
};
