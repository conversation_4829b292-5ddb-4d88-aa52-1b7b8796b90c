import { useEffect, useState } from "react";
import { MSAL, MSALOptions } from "@pnp/msaljsclient";
import { ClientId, RedirectUri } from "../utils/Config";
import { spfi, <PERSON><PERSON>rowser, SP<PERSON> } from "@pnp/sp";

export const useSharePointApi = (hubsiteUrl: string) => {
  const [sharepoint, setSharepoint] = useState<SPFI | undefined>(undefined);
  const [sharepointHostName, setSharepointHostName] = useState<
    string | undefined
  >(undefined);

  useEffect(() => {
    async function fetchData() {
      //  extract the hostname from the full URL

      let sharepointHost: string | undefined;

      try {
        const url = new URL(hubsiteUrl);
        sharepointHost = url.origin;
      } catch (error) {
        console.error("Invalid URL format:", hubsiteUrl, error);
        sharepointHost = undefined;
      }

      if (sharepointHost) {
        setSharepointHostName(sharepointHost);

        const options: MSALOptions = {
          configuration: {
            auth: {
              authority: "https://login.microsoftonline.com/common",
              clientId: ClientId(),
              redirectUri: RedirectUri(),
            },
            cache: {
              claimsBasedCachingEnabled: true, // in order to avoid network call to refresh a token every time claims are requested
            },
          },
          authParams: {
            forceRefresh: false,
            scopes: [sharepointHost + "/.default"],
          },
        };

        setSharepoint(spfi(hubsiteUrl).using(SPBrowser(), MSAL(options)));
      }
    }

    fetchData();
  }, []);

  return { sharepoint, sharepointHostName };
};
