import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import basicSsl from "@vitejs/plugin-basic-ssl";
import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import { viteStaticCopy } from "vite-plugin-static-copy";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    TanStackRouterVite(),
    react(),
    basicSsl(),

    viteStaticCopy({
      targets: [
        {
          src: "./staticwebapp.config.json",
          dest: "./",
        },
        {
          src: "./assets",
          dest: "./assets",
        },
      ],
    }),
  ],
  server: {
    host: "localhost",
    port: 3003,
  },
  esbuild: {
    target: "esnext",
  },
});
