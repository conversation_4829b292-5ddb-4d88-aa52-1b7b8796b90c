{"name": "mailfilingbootstrap", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm run dev-config && vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "npm run test-config && vitest", "build:prep": "npm run dev-config", "dev-config": "cp config/dev.json config.json", "test-config": "cp config/test.json config.json", "coverage": "npm run test-config && vitest run --coverage", "check:formatting": "npx prettier 'src/**/*.{js,ts,tsx,jsx}' --check"}, "dependencies": {"@azure/msal-browser": "^3.17.0", "@azure/msal-react": "^2.0.19", "@fluentui/react": "^8.118.9", "@fluentui/react-components": "^9.54.5", "@fluentui/react-icons": "^2.0.258", "@pnp/graph": "^4.2.0", "@pnp/msaljsclient": "^4.2.0", "@pnp/sp": "^4.2.0", "@sentry/react": "^8.42.0", "@tanstack/react-router": "^1.39.8", "build": "^0.1.4", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@azure/static-web-apps-cli": "^2.0.1", "@tanstack/router-devtools": "^1.39.8", "@tanstack/router-plugin": "^1.39.9", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "babel-jest": "^29.7.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "prettier": "3.5.3", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-plugin-static-copy": "^1.0.6", "vitest": "^2.0.5"}}