# Logs
logs
*.log
npm-debug.log*

# Dependency directories
node_modules

# Build generated files
dist
lib
solution
temp
*.sppkg

# OSX
.DS_Store

# Visual Studio files
.ntvs_analysis.dat
.vs
bin
obj

# Styles Generated Code
*.scss.ts

.env
bsconfig.json
.babel-cache

_build
_opam
junit.xml
coverage/

MailFilingPlugin/manifest.xml
MailFilingPlugin/manifest-file-on-send.xml
MailFilingPlugin/cmapmail-us1-file-on-send.xml
MailFilingPlugin/cmapmail-us1.xml
MailFilingPlugin/cmapmail.xml
MailFilingPlugin/cmapmail-file-on-send.xml


MailFilingPlugin/manifest/prod.json
MailFilingPlugin/config/prod.json
MailFilingPlugin/config.json
MailFilingDiscovery/config/prod.json
MailFilingDiscovery/config.json
MailFilingDiscovery/config/prod.json
MailFilingDiscovery/config.json
MailFilingAdmin/config/prod.json
MailFilingAdmin/config.json
MailFilingBootstrap/config/prod.json
MailFilingBootstrap/config.json
