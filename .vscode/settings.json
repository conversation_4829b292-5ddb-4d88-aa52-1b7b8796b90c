{"sonarlint.connectedMode.project": {"connectionId": "cmap-vsts", "projectKey": "Atvero-AtveroMailFrontend"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.configPath": ".prettierrc.json"}